import { createService } from './libs/cloud-run';
import { isPreprodStack } from './libs/resources';

const resources = isPreprodStack
  ? {
      cpu: '1',
      memory: '512Mi', // minimun,
      minInstance: 0,
      maxInstance: 1
    }
  : {
      cpu: '2',
      memory: '4Gi', // 2go,
      minInstance: 1,
      maxInstance: 2
    };

export const apiSignaturesService = createService({
  name: 'api-signatures',
  resources
});
