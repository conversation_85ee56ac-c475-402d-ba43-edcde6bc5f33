import * as gcp from '@pulumi/gcp';
import * as pulumi from '@pulumi/pulumi';
import { exec } from 'node:child_process';
import { promisify } from 'node:util';

export const gcpRegion = gcp.config.region as pulumi.Input<string>;

export const commitHash = promisify(exec)('git rev-parse --short=8 HEAD').then(({ stdout }) => stdout.trim());

type GcpProject = 'mynotary-preproduction' | 'mynotary-production';
export const getGcpProject = (): GcpProject => {
  const config = new pulumi.Config('gcp');
  const project = config.require('project');
  return project as GcpProject;
};
