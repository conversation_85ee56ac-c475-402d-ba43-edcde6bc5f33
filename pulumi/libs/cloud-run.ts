import * as gcp from '@pulumi/gcp';
import * as pulumi from '@pulumi/pulumi';
import { gcpRegion, commitHash, getGcpProject } from './common';
import { mynotaryDockerRepository } from './docker-repository';
import { linkToMyNotaryVpcConnector } from './vpc-connector';
import { existingMynotaryDatabase } from '../database';
import { Output } from '@pulumi/pulumi';

/**
 * Documentation https://www.pulumi.com/registry/packages/gcp/api-docs/cloudrun/service/
 */
export const activateCloudRun = new gcp.projects.Service('cloud-run', {
  disableDependentServices: false,
  service: 'run.googleapis.com'
});

const sqlAdminService = new gcp.projects.Service('sql-admin', {
  disableDependentServices: false,
  service: 'sqladmin.googleapis.com'
});

const computeEngineService = new gcp.projects.Service('compute-engine-service', {
  disableDependentServices: false,
  service: 'compute.googleapis.com'
});

const publicCloudRunPolicy = gcp.organizations.getIAMPolicy({
  bindings: [{ members: ['allUsers'], role: 'roles/run.invoker' }]
});

export interface ServiceOptions {
  /**
   * Relative path from workspace root
   */
  name: string;
  dockerImage?: string;
  vpcConnectorId?: Output<string>;
  resources: {
    cpu: string;
    memory: string;
    minInstance: number;
    maxInstance: number;
  };
}

export const createService = ({ name, vpcConnectorId, resources }: ServiceOptions) => {
  const revision = pulumi.interpolate`${name}-v${commitHash}`;
  const connectorId = vpcConnectorId ?? linkToMyNotaryVpcConnector();

  const imageName = pulumi.interpolate`${mynotaryDockerRepository.location}-docker.pkg.dev/mynotary-preproduction/${mynotaryDockerRepository.name}/${name}:latest`;

  revision.apply((r) => console.log(`------------------> Creating revision ${r}`));

  const cloudRunService = new gcp.cloudrunv2.Service(
    `cloud-run-${name}`,
    {
      name,
      location: gcpRegion,
      labels: {
        service_name: name
      },
      ingress: 'INGRESS_TRAFFIC_INTERNAL_LOAD_BALANCER',
      template: {
        executionEnvironment: 'EXECUTION_ENVIRONMENT_GEN2',
        vpcAccess: {
          connector: connectorId,
          egress: 'ALL_TRAFFIC'
        },
        scaling: {
          minInstanceCount: resources.minInstance,
          maxInstanceCount: resources.maxInstance
        },
        revision,
        volumes: [
          {
            name: 'cloudsql',
            cloudSqlInstance: {
              instances: [existingMynotaryDatabase.connectionName]
            }
          }
        ],
        containers: [
          {
            image: imageName,
            envs: [
              { name: 'GCP_PROJECT', value: getGcpProject() },
              { name: 'DB_USER', value: name },
              { name: 'NODE_OPTIONS', value: `--max-old-space-size=${getNodeMaxHeapSize(resources.memory)}` }
            ],
            resources: {
              startupCpuBoost: true,
              cpuIdle: true,
              limits: {
                cpu: resources.cpu,
                memory: resources.memory
              }
            },
            volumeMounts: [
              {
                name: 'cloudsql',
                mountPath: '/cloudsql'
              }
            ]
          }
        ]
      },
      traffics: [
        {
          type: 'TRAFFIC_TARGET_ALLOCATION_TYPE_REVISION',
          revision,
          percent: 100,
          /* Cloud run service tag must start with a letter and doesn't contain '.' */
          tag: pulumi.interpolate`v${commitHash}`
        }
      ]
    },
    {
      dependsOn: [activateCloudRun, computeEngineService, sqlAdminService]
    }
  );

  new gcp.cloudrun.IamPolicy(`cloud-run-policy-${name}`, {
    service: cloudRunService.name,
    location: gcpRegion,
    policyData: publicCloudRunPolicy.then((policy) => policy.policyData)
  });

  return {
    name: cloudRunService.name
  };
};

/**
 * Calculates the maximum heap size for Node.js based on the allocated memory for a Cloud Run service.
 * This function parses the memory string (e.g., '512Mi' or '2Gi'), converts it to megabytes,
 * and returns 90% of that value to ensure the heap size doesn't exceed available memory.
 *
 * The returned value is used in the NODE_OPTIONS environment variable to set --max-old-space-size.
 *
 * @param memory - The memory allocation string in the format of '<number>Mi' or '<number>Gi'
 * @returns The calculated maximum heap size in megabytes (as a number)
 * @throws Error if the memory string format is invalid
 * @see https://cloud.google.com/run/docs/configuring/memory-limits
 */
const getNodeMaxHeapSize = (memory: string) => {
  const memoryValue = memory.match(/^(\d+)(Mi|Gi)$/);

  if (memoryValue == null || memoryValue.length !== 3) {
    throw new Error(`Invalid memory value: ${memory}`);
  }

  const value = parseInt(memoryValue[1], 10);
  const unit = memoryValue[2];
  const memoryInMB = unit === 'Gi' ? value * 1024 : value;
  return Math.floor(memoryInMB * 0.9);
};
