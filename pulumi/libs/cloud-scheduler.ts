import { Input } from '@pulumi/pulumi';
import { gcpRegion, getGcpProject } from './common';
import * as pulumi from '@pulumi/pulumi';
import * as gcp from '@pulumi/gcp';
import { mynotaryDockerRepository } from './docker-repository';
import { activateCloudRun } from './cloud-run';

export const createJob = ({ jobArgs, name, imageName, database, resources, schedule, timeout }: JobOptions) => {
  const projectName = getGcpProject();

  if (projectName !== 'mynotary-production') {
    return;
  }

  const fullImageName = pulumi.interpolate`${mynotaryDockerRepository.location}-docker.pkg.dev/mynotary-preproduction/${mynotaryDockerRepository.name}/${imageName}:latest`;

  const cloudRunJob = new gcp.cloudrunv2.Job(
    `cloud-run-${name}`,
    {
      name,
      location: gcpRegion,
      deletionProtection: false,
      template: {
        template: {
          containers: [
            {
              image: fullImageName,
              args: jobArgs,
              commands: ['node', '--enable-source-maps', 'main.js'],
              envs: [
                { name: 'GCP_PROJECT', value: getGcpProject() },
                { name: 'DB_USER', value: 'jobs' }
              ],
              resources: {
                limits: {
                  cpu: resources.cpu,
                  memory: resources.memory
                }
              },
              volumeMounts: [
                {
                  name: 'cloudsql',
                  mountPath: '/cloudsql'
                }
              ]
            }
          ],
          maxRetries: 0,
          timeout: `${timeout}s`,
          volumes: [
            {
              name: 'cloudsql',
              cloudSqlInstance: {
                instances: [database.connectionName]
              }
            }
          ]
        }
      }
    },
    {
      dependsOn: [activateCloudRun]
    }
  );

  new gcp.cloudscheduler.Job(`job-${name}`, {
    name,
    /* Region is hardcoded to belgium here as the scheduler service is not generally available. */
    region: 'europe-west1',
    schedule,
    timeZone: 'Europe/Paris',
    httpTarget: {
      oauthToken: {
        serviceAccountEmail: gcp.compute.getDefaultServiceAccount({}).then(({ email }) => email)
      },
      uri: pulumi.interpolate`https://${mynotaryDockerRepository.location}-run.googleapis.com/apis/run.googleapis.com/v1/namespaces/${
        gcp.config.project
      }/jobs/${name}:run`
    }
  });

  return {
    name: cloudRunJob.name
  };
};

export interface JobOptions extends CloudRunOptions {
  /**
   * Name of the image in the artifact registry. It should be the same docker image for all the jobs
   */
  imageName: string;
  /**
   * Arguments to pass to the dockerfile's entrypoint.
   */
  jobArgs: string[];

  /**
   * Schedule in cron format.
   * e.g. '0 2 * * *' for everyday at 2am.
   */
  schedule: string;

  /**
   * Timeout in seconds.
   * Max allowed time duration the Task may be active before the system will actively try to mark it failed and kill associated containers.
   * This applies per attempt of a task, meaning each retry can run for the full timeout.
   */
  timeout: number;
}

interface CloudRunOptions {
  name: string;
  database: {
    connectionName: Input<string>;
  };
  envs?: { name: string; value: string | Input<string> }[];
  resources: {
    cpuIdle?: boolean;
    cpu: string;
    memory: string;
    minInstance: number;
  };
}
