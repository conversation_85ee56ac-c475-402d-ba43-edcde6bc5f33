import * as gcp from '@pulumi/gcp';
import { gcpRegion } from './common';

const artifactRepositoryService = new gcp.projects.Service(
  'artifact-repository',
  {
    disableDependentServices: false,
    service: 'artifactregistry.googleapis.com',
  }
);

export const mynotaryDockerRepository = new gcp.artifactregistry.Repository(
  'docker-repository',
  {
    description: 'MyNotary Docker repository',
    location: gcpRegion,
    format: 'DOCKER',
    repositoryId: 'docker-repository',
  },
  {
    dependsOn: [artifactRepositoryService],
  }
);
