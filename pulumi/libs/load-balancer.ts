import * as gcp from '@pulumi/gcp';
import * as cloudflare from '@pulumi/cloudflare';
import { getGcpProject } from './common';
import { GlobalAddress } from '@pulumi/gcp/compute';

// found in cloudflare overview page, safe to expose
const cloudFlareZoneId = '9ec1df71bc7be7f890ba473811047194';

/**
 * Cloud Armor Security Policy priority ranges
 * https://cloud.google.com/armor/docs/waf-rules
 *
 * 0-99: Critical emergency blocking rules (highest priority)
 * 1000-1999: Preconfigured WAF rules (OWASP SQLi, XSS, etc.)
 * 2000-2999: Rate limiting rules
 * 3000-3999: Geolocation rules
 * 4000-4999: Custom IP lists (Allow/Deny)
 */
const cloudArmorSecurityPolicy = new gcp.compute.SecurityPolicy('cloud-armor-security-policy', {
  description: "Security policy for mynotary api",
  rules: [
    {
      action: 'deny(403)',
      priority: 1000,
      description: 'SQL Injection (OWASP)',
      match: {
        expr: {
          expression: "evaluatePreconfiguredExpr('sqli-v33-stable')"
        }
      },
      preview: true
    },
    {
      action: 'deny(403)',
      priority: 1010,
      description: 'Cross-Site Scripting (OWASP)',
      match: {
        expr: {
          expression: "evaluatePreconfiguredExpr('xss-v33-stable')"
        }
      },
      preview: true
    },
    {
      action: 'deny(403)',
      priority: 1020,
      description: 'Local File Inclusion (OWASP)',
      match: {
        expr: {
          expression: "evaluatePreconfiguredExpr('lfi-v33-stable')"
        }
      },
      preview: true
    },
    {
      action: 'deny(403)',
      priority: 1030,
      description: 'Remote File Inclusion (OWASP)',
      match: {
        expr: {
          expression: "evaluatePreconfiguredExpr('rfi-v33-stable')"
        }
      },
      preview: true
    },
    {
      action: 'deny(403)',
      priority: 1040,
      description: 'Remote Code Execution (OWASP)',
      match: {
        expr: {
          expression: "evaluatePreconfiguredExpr('rce-v33-stable')"
        }
      },
      preview: true
    },
    {
      action: 'deny(403)',
      priority: 1050,
      description: 'Method enforcement (OWASP)',
      match: {
        expr: {
          expression: "evaluatePreconfiguredExpr('methodenforcement-v33-stable')"
        }
      },
      preview: true
    },
    {
      action: 'deny(403)',
      priority: 1060,
      description: 'Scanner detection (OWASP)',
      match: {
        expr: {
          expression: "evaluatePreconfiguredExpr('scannerdetection-v33-stable')"
        }
      },
      preview: true
    },
    {
      action: 'deny(403)',
      priority: 1070,
      description: 'Protocol attack (OWASP)',
      match: {
        expr: {
          expression: "evaluatePreconfiguredExpr('protocolattack-v33-stable')"
        }
      },
      preview: true
    },
    {
      action: 'deny(403)',
      priority: 1080,
      description: 'Java attack (OWASP)',
      match: {
        expr: {
          expression: "evaluatePreconfiguredExpr('java-v33-stable')"
        }
      },
      preview: true
    },
    {
      action: 'deny(403)',
      priority: 1090,
      description: 'NodeJS attack (OWASP)',
      match: {
        expr: {
          expression: "evaluatePreconfiguredExpr('nodejs-v33-stable')"
        }
      },
      preview: true
    },
    {
      action: "rate_based_ban",
      priority: 2000,
      description: "Limit requests to 200 per minute and ban for 5 minutes if exceeded too many times",
      match: {
        versionedExpr: "SRC_IPS_V1",
        config: {
          srcIpRanges: ["*"],
        },
      },
      rateLimitOptions: {
        rateLimitThreshold: {
          count: 200,
          intervalSec: 60,
        },
        conformAction: "allow",
        exceedAction: "deny(429)",
        enforceOnKey: "IP",
        banDurationSec: 300,
      },
      preview: true,
    },
    {
      action: "allow",
      priority: 2147483647,
      match: {
        versionedExpr: "SRC_IPS_V1",
        config: {
          srcIpRanges: ["*"],
        },
      },
      description: "Default rule (allow traffic not intercepted by other rules)",
    },
  ]
});

/**
 * Sets up the Java API configuration for the load balancer
 * @param domainEnv - Environment identifier ('production' or 'preproduction')
 * @param ipAddress - The global IP address resource for the load balancer
 * @returns Configuration object with domain names and backend service
 */
const setupApiJava = ({ domainEnv, ipAddress }: { domainEnv: string; ipAddress: GlobalAddress }) => {
  const domainJavaPrefixs: Record<string, string> = {
    production: 'api',
    preproduction: 'api-preprod'
  };

  // Remove this when java api is on gcp handle all traffic wiht api.mynotary.fr domain
  const domainJavaTmpPrefixs: Record<string, string> = {
    production: 'api-java-gcp',
    preproduction: 'api-preprod-java-gcp'
  };

  const domainJavaPrefix = domainJavaPrefixs[domainEnv];
  const domainJava = `${domainJavaPrefix}.mynotary.fr`;

  const domainJavaTmpPrefix = domainJavaTmpPrefixs[domainEnv];
  const domainJavaTmp = `${domainJavaTmpPrefix}.mynotary.fr`;

  new cloudflare.Record(
    'cloudflare-dns-record-java-tmp',
    {
      zoneId: cloudFlareZoneId,
      name: domainJavaTmpPrefix,
      content: ipAddress.address,
      type: 'A',
      ttl: 1 // 1 means automatic ttl
    },
    { deleteBeforeReplace: true }
  );

  const endpointGroupJava = new gcp.compute.RegionNetworkEndpointGroup('load-balancer-neg-java', {
    networkEndpointType: 'SERVERLESS',
    region: 'europe-west9',
    cloudRun: {
      service: 'api-java'
    }
  });

  const backendService = new gcp.compute.BackendService('load-balancer-backend-service-java', {
    enableCdn: false,
    connectionDrainingTimeoutSec: 10,
    securityPolicy: cloudArmorSecurityPolicy.id,
    backends: [
      {
        group: endpointGroupJava.id
      }
    ]
  });

  return {
    domain: domainJava,
    domainTmp: domainJavaTmp,
    backendService
  };
};

/**
 * Sets up the MyNotary API configuration for the load balancer
 * @param domainEnv - Environment identifier ('production' or 'preproduction')
 * @param ipAddress - The global IP address resource for the load balancer
 * @returns Configuration object with domain name and backend service
 */
export const setupApiMyNotary = ({ domainEnv, ipAddress }: { domainEnv: string; ipAddress: GlobalAddress }) => {
  const domain = `api.${domainEnv}.mynotary.fr`;

  new cloudflare.Record(
    'cloudflare-dns-record',
    {
      zoneId: cloudFlareZoneId,
      name: `api.${domainEnv}`,
      content: ipAddress.address,
      type: 'A',
      ttl: 1 // 1 means automatic ttl
    },
    { deleteBeforeReplace: true }
  );

  const endpointGroup = new gcp.compute.RegionNetworkEndpointGroup('load-balancer-neg', {
    networkEndpointType: 'SERVERLESS',
    region: 'europe-west9',
    cloudRun: {
      urlMask: `${domain}/<service>/`
    }
  });

  const backendService = new gcp.compute.BackendService('load-balancer-backend-service', {
    enableCdn: false,
    connectionDrainingTimeoutSec: 10,
    securityPolicy: cloudArmorSecurityPolicy.id,
    backends: [
      {
        group: endpointGroup.id
      }
    ]
  });

  return {
    domain,
    backendService
  };
};

/**
 * Creates and configures the main load balancer for MyNotary APIs
 * Sets up routing rules, SSL certificates, and forwarding rules for both Java and MyNotary APIs
 */
export const createLoadBalancer = (): void => {
  const domainEnv = getDomainEnv();

  const ipAddress = new gcp.compute.GlobalAddress('load-balancer-ip', { addressType: 'EXTERNAL' });
  const apiJava = setupApiJava({ domainEnv, ipAddress });
  const apiMyNotary = setupApiMyNotary({ domainEnv, ipAddress });

  const httpsPaths = new gcp.compute.URLMap('load-balancer-url-map', {
    defaultService: apiMyNotary.backendService.id,
    hostRules: [
      {
        hosts: [apiMyNotary.domain],
        pathMatcher: 'all-paths'
      },
      {
        hosts: [apiJava.domain, apiJava.domainTmp],
        pathMatcher: 'java-paths'
      }
    ],
    pathMatchers: [
      {
        name: 'all-paths',
        defaultService: apiMyNotary.backendService.id
      },
      {
        name: 'java-paths',
        defaultService: apiJava.backendService.id
      }
    ]
  });

  const certificate = new gcp.compute.ManagedSslCertificate(
    `ssl-certificate`,
    {
      managed: {
        domains: [apiMyNotary.domain, apiJava.domain, apiJava.domainTmp]
      }
    },
    {
      retainOnDelete: true
    }
  );

  const httpsProxy = new gcp.compute.TargetHttpsProxy('load-balancer-https-proxy', {
    urlMap: httpsPaths.selfLink,
    sslCertificates: [certificate.id]
  });

  new gcp.compute.GlobalForwardingRule('load-balancer-https-forward', {
    target: httpsProxy.selfLink,
    ipAddress: ipAddress.address,
    portRange: '443',
    loadBalancingScheme: 'EXTERNAL'
  });
};

const getDomainEnv = (): 'preproduction' | 'production' => {
  const project = getGcpProject();

  switch (project) {
    case 'mynotary-preproduction':
      return 'preproduction';
    case 'mynotary-production':
      return 'production';
  }
};
