import * as gcp from '@pulumi/gcp';

export interface LinkDatabaseOptions {
  databaseName: string;
  instanceName: string;
}

/**
 * https://www.pulumi.com/registry/packages/gcp/api-docs/sql/databaseinstance/
 */
export const linkToExistingDatabase = ({ instanceName, databaseName }: LinkDatabaseOptions) => {
  const instance = gcp.sql.DatabaseInstance.get(`database-instance-${instanceName}`, instanceName);

  return {
    connectionName: instance.connectionName
  };
}
