import { createJob } from './libs/cloud-scheduler';
import { existingMynotaryDatabase } from './database';
import { BackgroundJobType } from '../libs/backend/background-jobs/core';

const EVERY_DAY_AT_2AM = {
  schedule: '0 2 * * *',
  timeout: 30 * 60
};

const EVERY_DAY_AT_MIDNIGHT = {
  schedule: '0 0 * * *',
  timeout: 30 * 60
};

const EVERY_DAY_AT_8AM = {
  schedule: '0 8 * * *',
  timeout: 30 * 60
};

const sharedOptions = {
  database: { ...existingMynotaryDatabase },
  imageName: 'jobs'
};

export const gelAvoirJob = createJob({
  jobArgs: [BackgroundJobType.GEL_AVOIR],
  ...EVERY_DAY_AT_2AM,
  ...sharedOptions,
  name: 'gel-avoir',
  resources: {
    cpu: '1',
    memory: '512Mi',
    minInstance: 1
  }
});

export const documentRequestAnalyticsJob = createJob({
  jobArgs: [BackgroundJobType.ANALYTICS_DOCUMENT_REQUESTS],
  ...EVERY_DAY_AT_MIDNIGHT,
  ...sharedOptions,
  name: 'analytics-document-requests',
  resources: {
    cpu: '1',
    memory: '512Mi',
    minInstance: 1
  }
});

export const analyticsDriveJob = createJob({
  jobArgs: [BackgroundJobType.ANALYTICS_DRIVE],
  ...EVERY_DAY_AT_MIDNIGHT,
  ...sharedOptions,
  name: BackgroundJobType.ANALYTICS_DRIVE,
  resources: {
    cpu: '1',
    memory: '512Mi',
    minInstance: 1
  }
});

export const analyticsInvoicesJob = createJob({
  jobArgs: [BackgroundJobType.ANALYTICS_INVOICES],
  ...EVERY_DAY_AT_MIDNIGHT,
  ...sharedOptions,
  name: BackgroundJobType.ANALYTICS_INVOICES,
  resources: {
    cpu: '1',
    memory: '512Mi',
    minInstance: 1
  }
});

export const analyticsOperationsJob = createJob({
  jobArgs: [BackgroundJobType.ANALYTICS_OPERATIONS],
  ...EVERY_DAY_AT_MIDNIGHT,
  ...sharedOptions,
  name: BackgroundJobType.ANALYTICS_OPERATIONS,
  resources: {
    cpu: '1',
    memory: '512Mi',
    minInstance: 1
  }
});

export const analyticsOrganizationsJob = createJob({
  jobArgs: [BackgroundJobType.ANALYTICS_ORGANIZATIONS],
  ...EVERY_DAY_AT_MIDNIGHT,
  ...sharedOptions,
  name: BackgroundJobType.ANALYTICS_ORGANIZATIONS,
  resources: {
    cpu: '1',
    memory: '512Mi',
    minInstance: 1
  }
});

export const analyticsUsersJob = createJob({
  jobArgs: [BackgroundJobType.ANALYTICS_USERS],
  ...EVERY_DAY_AT_MIDNIGHT,
  ...sharedOptions,
  name: BackgroundJobType.ANALYTICS_USERS,
  resources: {
    cpu: '1',
    memory: '512Mi',
    minInstance: 1
  }
});

export const taskReminderJob = createJob({
  jobArgs: [BackgroundJobType.TASK_REMINDER],
  ...EVERY_DAY_AT_8AM,
  ...sharedOptions,
  name: BackgroundJobType.TASK_REMINDER,
  resources: {
    cpu: '1',
    memory: '512Mi',
    minInstance: 1
  }
});
  
