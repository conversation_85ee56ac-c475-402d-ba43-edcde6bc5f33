import { linkToExistingDatabase } from './libs/cloud-sql';
import { getGcpProject } from './libs/common';

const getIntanceName = () => {
  const project = getGcpProject();

  if (project === 'mynotary-production') {
    return 'database-production';
  } else if (project === 'mynotary-preproduction') {
    return 'database-preproduction';
  }

  throw new Error('Database instance name not found');
};

export const existingMynotaryDatabase = linkToExistingDatabase({
  instanceName: getIntanceName(),
  databaseName: 'mynotary'
});
