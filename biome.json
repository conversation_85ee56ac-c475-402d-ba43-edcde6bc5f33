{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": [], "maxSize": 5000000}, "formatter": {"enabled": true, "useEditorconfig": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 120, "attributePosition": "auto", "bracketSpacing": true, "ignore": ["apps/front-mynotary/src/features/emailTemplates/**/*.html", "**/*-dto.ts", "./dist", "./coverage", "libs/backend/prisma/src", "./.nx/cache", "./.nx/workspace-data"]}, "organizeImports": {"enabled": true}, "linter": {"enabled": false, "rules": {"recommended": true}}, "javascript": {"formatter": {"jsxQuoteStyle": "single", "quoteProperties": "asNeeded", "trailingCommas": "none", "semicolons": "always", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto", "bracketSpacing": true}}}