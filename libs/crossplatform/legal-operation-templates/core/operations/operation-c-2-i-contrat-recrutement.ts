// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationC2IContratRecrutement: LegalOperationTemplate = {
  config: {
    emailLabelPattern: '{{ prenom_agent }} {{ nom_agent }}',
    labelPattern: '{{ prenom_agent }} {{ nom_agent }}',
    tags: {
      prenom_agent: {
        link: 'OPERATION__RECRUTEMENT__AGENT__AGENT_COMMERCIAL',
        questionId: 'prenoms',
        max: 1,
        label: 'Prénom agent'
      },
      nom_agent: {
        link: 'OPERATION__RECRUTEMENT__AGENT__AGENT_COMMERCIAL',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom agent'
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'AGENCE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'AGENT_COMMERCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'AFFILIE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'GENERAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'C2I_CONTRAT_RECRUTEMENT_CONTRAT_MANDATAIRE',
        label: 'Contrat de Mandataire'
      },
      {
        id: 'C2I_CONTRAT_RECRUTEMENT_CONTRAT_LICENCE_MARQUE',
        label: 'Contrat de Licence de Marque - Groupe C2i'
      },
      {
        id: 'C2I_CONTRAT_RECRUTEMENT_CONTRAT_LICENCE_MARQUE_ASSOCIEE',
        label: 'Contrat de Licence de Marque - Groupe C2i Associée'
      }
    ],
    documentsToExclude: [],
    documentsToInclude: [
      'c2i_doc_cahier_charge_booster',
      'c2i_doc_deontologie',
      'c2i_doc_deontologie_groupe',
      'c2i_doc_remuneration',
      'c2i_doc_territoire',
      'c2i_marque_doc_certificat',
      'c2i_marque_doc_charte',
      'c2i_marque_doc_explicatif_licence',
      'c2i_marque_doc_image',
      'c2i_marque_doc_quotas',
      'c2i_marque_doc_territoire',
      'contrat_prestation',
      'performance_mandataire',
      'regle_collaboration_interconseiller'
    ]
  },
  id: 'OPERATION__C2I__CONTRAT_RECRUTEMENT',
  label: 'Recrutement - C2i',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__RECRUTEMENT__MYNOTARY__AGENT',
  specificTypes: ['C2I', 'CONTRAT_RECRUTEMENT'],
  type: 'OPERATION'
};
