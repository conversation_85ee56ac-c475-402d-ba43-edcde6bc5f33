// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationPvciSensImmobilierVente: LegalOperationTemplate = {
  config: {
    hiddenPages: {
      documents: true
    },
    labelPattern: '{{ numero_lot }} / {{ programme_nom }} /  {{ nom_acquereur }}',
    emailLabelPattern: '{{ biens_vendus }}',
    tags: {
      numero_lot: {
        link: 'OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_PVCI_SENS',
        questionId: ['numero_lot', 'numero_lot_commercialisation'],
        max: 1,
        label: 'Numéro du lot'
      },
      programme_nom: {
        link: 'OPERATION__IMMOBILIER__VENTES_PROGRAMME__GENERAL',
        questionId: 'programme_nom',
        max: 1,
        label: 'Nom du programme'
      },
      nom_acquereur: {
        link: 'OPERATION__IMMOBILIER__VENTE_NEUF__RESERVATAIRES',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_PVCI_SENS',
        order: 0,
        max: 1
      },
      commercialisateurs: {
        link: 'OPERATION__IMMOBILIER__VENTE_NEUF__REPRESENTANT_COMMERCIALISATEUR',
        order: 3,
        max: 5,
        group: 0
      },
      acquereurs: {
        link: 'OPERATION__IMMOBILIER__VENTE_NEUF__RESERVATAIRES',
        order: 2,
        max: 1
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'LOTS_PVCI_SENS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_NEUF', 'RESERVATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_NEUF', 'FICHES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_NEUF', 'NOTAIRE_RESERVATAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_NEUF', 'COMMERCIALISATEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_NEUF', 'REPRESENTANT_COMMERCIALISATEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'PROMOTEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'REPRESENTANT_PROMOTEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'GENERAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      }
    ],
    linkOverrides: [
      'OPERATION__IMMOBILIER__VENTES_PROGRAMME__GENERAL',
      'OPERATION__IMMOBILIER__VENTES_PROGRAMME__PROMOTEURS',
      'OPERATION__IMMOBILIER__VENTES_PROGRAMME__REPRESENTANT_PROMOTEUR',
      'OPERATION__IMMOBILIER__VENTES_PROGRAMME__NOTAIRE_PROGRAMME'
    ],
    operationLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'VENTE_VEFA_PVCI_SENS']
      }
    ],
    contracts: [
      {
        id: 'PVCI_SENS_IMMOBILIER_VENTE_CONTRAT_RESERVATION',
        label: 'Contrat préliminaire de Vente - Non meublé'
      },
      {
        id: 'PVCI_SENS_IMMOBILIER_VENTE_CONTRAT_RESERVATION_LMNP',
        label: 'Contrat préliminaire de Vente - LMNP'
      }
    ],
    isSubOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      'bon_commande',
      'contrat_licence_marque',
      'convention_type_disposition',
      'convention_type_exploitation',
      'convention_type_parties_communes',
      'convention_vacances',
      'descriptif_senioriales',
      'grille_prix',
      'intention_mandat',
      'kit_fiscal_mg',
      'lettre_garantie_notaire',
      'projet_bail_mandat'
    ]
  },
  id: 'OPERATION__PVCI_SENS__IMMOBILIER__VENTE',
  label: 'Pierre et Sens Développement - Vente',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__VENTE_NEUF',
  specificTypes: ['PVCI_SENS', 'IMMOBILIER', 'VENTE'],
  type: 'OPERATION'
};
