// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationUnisLocationCommercial: LegalOperationTemplate = {
  config: {
    labelPattern: '{{ adresse }} / {{ nom_bailleur }} / {{ nom_locataire }}',
    emailLabelPattern: '{{ adresse }}',
    tags: {
      adresse: {
        link: 'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BIENS_LOUES_COMMERCIAL',
        questionId: 'adresse',
        format: 'ADDRESS',
        max: 1,
        label: 'Adresse'
      },
      nom_bailleur: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom bailleur'
      },
      nom_locataire: {
        link: 'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom locataire'
      },
      biens_loues: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES',
        order: 0,
        max: 1
      },
      bailleurs: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
        order: 1,
        max: 1
      },
      locataires: {
        link: 'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
        order: 2,
        max: 1
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'BAILLEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'LOCATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'REPRESENTANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VISITEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'GARANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'OFFRANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_COMMERCIAL', 'BIENS_LOUES_COMMERCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_COMMERCIAL', 'BAIL_COMMERCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'AGENTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'MANDATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_DIP_COMMERCIAL',
        label: "Document d'informations précontractuelles"
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_MANDAT_COMMERCIAL',
        label: 'Mandat de location - Local Commercial'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_MANDAT_COMMERCIAL_AVENANT',
        label: 'Mandat de location - Local Commercial - Avenant'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_MANDAT_RECHERCHE_COMMERCIAL',
        label: 'Mandat de recherche - Local Commercial'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_MANDAT_RECHERCHE_COMMERCIAL_AVENANT',
        label: 'Mandat de recherche - Local Commercial - Avenant'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_MANDAT_PROFESSIONNEL',
        label: 'Mandat de location - Local Professionnel'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_MANDAT_PROFESSIONNEL_AVENANT',
        label: 'Mandat de location - Local Professionnel - Avenant'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_MANDAT_RECHERCHE_PROFESSIONNEL',
        label: 'Mandat de recherche - Local Professionnel'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_MANDAT_RECHERCHE_PROFESSIONNEL_AVENANT',
        label: 'Mandat de recherche - Local Professionnel - Avenant'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_MANDAT_GESTION',
        label: 'Mandat de gestion - Local Commercial'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_MANDAT_GESTION_PROFESSIONNEL',
        label: 'Mandat de Gestion - Local Professionnel'
      },
      {
        id: 'UNIS_LOCATION_COMMERCIAL_MANDAT_GESTION_TECHNIQUE',
        label: 'Mandat de Gestion Immobilière et Technique'
      },
      {
        id: 'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_MANDAT_CESSION_BAIL',
        label: 'Mandat - Cession de bail'
      },
      {
        id: 'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_MANDAT_CESSION_BAIL_AVENANT',
        label: 'Mandat  - Cession de bail - Avenant '
      },
      {
        id: 'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_MANDAT_RECHERCHE_CESSION_BAIL',
        label: 'Mandat de recherche - Cession de Bail'
      },
      {
        id: 'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_MANDAT_RECHERCHE_CESSION_BAIL_AVENANT',
        label: 'Mandat de recherche - Cession de Bail - Avenant '
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_OFFRE_LOCATION',
        label: 'Offre de location'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_ENGAGEMENT_PRISE_A_BAIL',
        label: 'Engagement de prise à bail'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_ETAT_DES_LIEUX',
        label: 'Etat des lieux'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_PROCURATION_BAILLEUR',
        label: 'Procuration - Bailleur'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_PROCURATION_LOCATAIRE',
        label: 'Procuration - Locataire'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BAIL_COMMERCIAL',
        label: 'Bail Commercial'
      },
      {
        id: 'UNIS_LOCATION_COMMERCIAL_BAIL_COMMERCIAL_ENTREPRISE',
        label: "Bail Commercial - Immobilier d'Entreprise"
      },
      {
        id: 'UNIS_LOCATION_COMMERCIAL_BAIL_COMMERCIAL_BUREAU',
        label: 'Bail Commercial - Bureau'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BAIL_COMMERCIAL_AVENANT',
        label: 'Bail Commercial - Avenant'
      },
      {
        id: 'UNIS_LOCATION_COMMERCIAL_AVENANT_BAIL_ECOENERGIE',
        label: 'Bail Commercial - Avenant  - Dispositif Eco Energie Tertiaire'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_CAUTIONNEMENT_COMMERCIAL',
        label: 'Cautionnement Commercial'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PROFESSIONNEL',
        label: 'Bail Professionnel'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PROFESSIONNEL_AVENANT',
        label: 'Bail Professionnel - Avenant'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_CAUTIONNEMENT_PROFESSIONNEL',
        label: 'Cautionnement Professionnel'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PRECAIRE',
        label: "Convention d'Occupation Précaire"
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BON_VISITE',
        label: 'Bon de Visite'
      },
      {
        id: 'UNIS_LOCATION_COMMERCIAL_TRAVAUX_BAILLEUR',
        label: 'Travaux par le Bailleur'
      },
      {
        id: 'UNIS_LOCATION_COMMERCIAL_INVENTAIRE_IMPOTS_CHARGE',
        label: 'Inventaire impôts et charges'
      },
      {
        id: 'UNIS_LOCATION_COMMERCIAL_NOTIFICATION_VENTE',
        label: 'Notification de la vente du local'
      }
    ],
    documentsToExclude: ['mandat_bail', 'taxe_fonciere'],
    documentsToInclude: [
      'assainissement_non_collectif_controle_libre',
      'attestation_diagnostiqueur',
      'autorisation_location',
      'cheminee_facture_ramonage',
      'contrat_attaches_list_contrat_attaches_document',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'declaration_prealable_location',
      'diagnostic_amiante',
      'diagnostic_amiante_libre',
      'diagnostic_electrique',
      'diagnostic_gaz',
      'diagnostic_merule',
      'diagnostic_parties_communes_amiante',
      'diagnostic_parties_communes_plomb',
      'diagnostic_plomb',
      'diagnostic_termites',
      'diagnostic_termites_libre',
      'dpe',
      'dpe_libre',
      'inventaire_impots_charges',
      'location_bail_liste_contrat_bail',
      'location_bail_liste_etat_des_lieux_document',
      'location_bail_liste_quittances_loyer',
      'mandat_bail',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'plan_lot',
      'pv_ag',
      'servitude_acte',
      'tableau_surface',
      'taxe_fonciere',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_autorisation_copropriete_travaux_global',
      'travaux_list_carnet_information_travaux_document',
      'travaux_list_ctravaux_particulier_assurance_dommage',
      'travaux_list_dossier_urbanisme',
      'travaux_list_facture_entreprise_travaux',
      'travaux_list_travaux_autorisation_pv_ag',
      'travaux_list_travaux_facture_doc'
    ]
  },
  id: 'OPERATION__UNIS__LOCATION__COMMERCIAL',
  label: 'UNIS - Gestion Locative - Commercial / Professionnel',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL',
  specificTypes: ['UNIS', 'LOCATION', 'COMMERCIAL'],
  type: 'OPERATION'
};
