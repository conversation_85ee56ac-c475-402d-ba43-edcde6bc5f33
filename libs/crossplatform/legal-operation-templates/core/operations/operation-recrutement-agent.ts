// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationRecrutementAgent: LegalOperationTemplate = {
  config: {
    emailLabelPattern: '{{ prenom_agent }} {{ nom_agent }}',
    labelPattern: '{{ prenom_agent }} {{ nom_agent }}',
    tags: {
      prenom_agent: {
        link: 'OPERATION__RECRUTEMENT__AGENT__AGENT_COMMERCIAL',
        questionId: 'prenoms',
        max: 1,
        label: 'Prénom agent'
      },
      nom_agent: {
        link: 'OPERATION__RECRUTEMENT__AGENT__AGENT_COMMERCIAL',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom agent'
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'AGENCE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'PARTENAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'AGENT_COMMERCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'GENERAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'BENEFICIAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'PRESTATAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'SPONSORING'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'SPONSOR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'REPRESENTANT_OPERIO'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'RECRUTEMENT_AGENT_MANDAT',
        label: 'Mandat - Agent Commercial'
      },
      {
        id: 'RECRUTEMENT_AGENT_AVENANT_MANDAT_AGENT_COMMERCIAL',
        label: 'Avenant au Mandat d’agent commercial '
      },
      {
        id: 'RECRUTEMENT_AGENT_DETERMINATION_COMMISSIONS',
        label: "Annexe 1 du Mandat d'agent commercial"
      },
      {
        id: 'RECRUTEMENT_AGENT_ANNEXE_AGENT_NON_CARTE',
        label: 'Annexe au mandat d’agent commercial - Mega Agent non carté'
      },
      {
        id: 'RECRUTEMENT_AGENT_CONTRAT_SPONSORING',
        label: 'Contrat de prestation de service GS'
      },
      {
        id: 'RECRUTEMENT_AGENT_CONTRAT_PRESTATION_SERVICE_MEGATEAM',
        label: 'Contrat de Prestation de Services - MegaTeam / MAO'
      },
      {
        id: 'RECRUTEMENT_AGENT_CONTRAT_PRESTATION_SERVICES_PC',
        label: 'Contrat de Prestation de Services - PC/Agent'
      },
      {
        id: 'RECRUTEMENT_AGENT_CONVENTION_PARTENARIAT_COMMERCIAL',
        label: 'Convention de partenariat commercial Immobilier'
      },
      {
        id: 'RECRUTEMENT_AGENT_CONVENTION_BSA',
        label: 'Convention de Sponsoring - Formulaire BSA'
      },
      {
        id: 'RECRUTEMENT_AGENT_PRELEVEMENT_SEPA',
        label: 'Mandat de Prélèvement SEPA'
      },
      {
        id: 'RECRUTEMENT_AGENT_REJET_MANDAT',
        label: 'Courrier échéancier'
      },
      {
        id: 'RECRUTEMENT_AGENT_AVENANT_CONTRAT_AGENT',
        label: 'Avenant Contrat Agent - Délégation de Paiement'
      },
      {
        id: 'RECRUTEMENT_AGENT_LETTRE_MISSION_OPERIO',
        label: 'Lettre de mission - OPERIO '
      }
    ],
    documentsToExclude: [],
    documentsToInclude: [
      'RIB_mandataire',
      'attestaiton_rcp',
      'bsa_us',
      'carte_collaborateur',
      'carte_vitale',
      'charte_culture',
      'cheque_cci',
      'contrat_bsa',
      'contrat_coaching',
      'demande_attestation_habilitation',
      'demande_rcp',
      'deontologie',
      'doc_lettre_mission_complementaires',
      'doc_lettre_mission_repartition_cgu',
      'doc_lettre_mission_repartition_travaux',
      'droit_image',
      'fiche_info_conseil',
      'immatriculation_rsac',
      'mandat_agent_commercial',
      'mandat_sepa',
      'mandat_sepa_kw',
      'securite_sociale_bsa',
      'tracfin3'
    ]
  },
  id: 'OPERATION__RECRUTEMENT__AGENT',
  label: 'Recrutement Agent - Keller Williams',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__RECRUTEMENT__MYNOTARY__AGENT',
  specificTypes: ['RECRUTEMENT', 'AGENT'],
  type: 'OPERATION'
};
