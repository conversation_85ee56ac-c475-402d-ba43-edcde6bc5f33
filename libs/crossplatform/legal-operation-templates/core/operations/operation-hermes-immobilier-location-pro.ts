// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationHermesImmobilierLocationPro: LegalOperationTemplate = {
  config: {
    labelPattern: '{{ adresse }} / {{ nom_bailleur }} / {{ nom_locataire }}',
    emailLabelPattern: '{{ adresse }}',
    tags: {
      adresse: {
        link: 'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BIENS_LOUES_COMMERCIAL',
        questionId: 'adresse',
        format: 'ADDRESS',
        max: 1,
        label: 'Adresse'
      },
      nom_bailleur: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom bailleur'
      },
      nom_locataire: {
        link: 'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom locataire'
      },
      biens_loues: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES',
        order: 0,
        max: 1
      },
      bailleurs: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
        order: 1,
        max: 1
      },
      locataires: {
        link: 'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
        order: 2,
        max: 1
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'APPORTEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'BAILLEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'LOCATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'REPRESENTANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VISITEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VISITE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'OFFRANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'OFFRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_COMMERCIAL', 'BIENS_LOUES_COMMERCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_COMMERCIAL', 'BAIL_COMMERCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'GARANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'AGENTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'MANDATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'AGENCE_DELEGATAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'HERMES_IMMOBILIER_LOCATION_PRO_MANDAT_LOCATION',
        label: 'Mandat de Location - Simple / Privilège / Exclusif'
      },
      {
        id: 'HERMES_IMMOBILIER_LOCATION_PRO_MANDAT_LOCATION_AVENANT',
        label: 'Mandat de Location - Avenant'
      },
      {
        id: 'HERMES_IMMOBILIER_LOCATION_PRO_MANDAT_RECHERCHE',
        label: 'Mandat de Recherche'
      },
      {
        id: 'HERMES_IMMOBILIER_LOCATION_PRO_OFFRE_LOCATION',
        label: 'Offre de Location'
      },
      {
        id: 'HERMES_IMMOBILIER_LOCATION_PRO_RECONNAISSANCE_HONORAIRES',
        label: "Reconnaissance d'honoraires"
      },
      {
        id: 'HERMES_IMMOBILIER_LOCATION_PRO_ACCORD_CONFIDENTIALITE',
        label: 'Accord de confidentialité'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_DELEGATION_COMMERCIAL',
        label: 'Délégation de mandat - commercial'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_DELEGATION_PROFESSIONNEL',
        label: 'Délégation de mandat - professionnel'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BON_VISITE',
        label: 'Bon de Visite'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_ETAT_DES_LIEUX',
        label: 'Etat des lieux'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_PROCURATION_BAILLEUR',
        label: 'Procuration - Bailleur'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_PROCURATION_LOCATAIRE',
        label: 'Procuration - Locataire'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PRECAIRE',
        label: "Convention d'Occupation Précaire"
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BAIL_COMMERCIAL',
        label: 'Bail Commercial'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BAIL_COMMERCIAL_AVENANT',
        label: 'Bail Commercial - Avenant'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_CAUTIONNEMENT_COMMERCIAL',
        label: 'Cautionnement Commercial'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PROFESSIONNEL',
        label: 'Bail Professionnel'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PROFESSIONNEL_AVENANT',
        label: 'Bail Professionnel - Avenant'
      },
      {
        id: 'IMMOBILIER_LOCATION_COMMERCIAL_CAUTIONNEMENT_PROFESSIONNEL',
        label: 'Cautionnement Professionnel'
      }
    ],
    documentsToExclude: [],
    documentsToInclude: [
      'assainissement_non_collectif_controle_libre',
      'attestation_diagnostiqueur',
      'autorisation_location',
      'cheminee_facture_ramonage',
      'contrat_attaches_list_contrat_attaches_document',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'declaration_prealable_location',
      'diagnostic_amiante',
      'diagnostic_electrique',
      'diagnostic_gaz',
      'diagnostic_merule',
      'diagnostic_parties_communes_amiante',
      'diagnostic_parties_communes_plomb',
      'diagnostic_plomb',
      'diagnostic_termites',
      'dpe',
      'location_bail_liste_contrat_bail',
      'location_bail_liste_etat_des_lieux_document',
      'location_bail_liste_quittances_loyer',
      'mandat_bail',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'pv_ag',
      'servitude_acte',
      'taxe_fonciere',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_autorisation_copropriete_travaux_global',
      'travaux_list_carnet_information_travaux_document',
      'travaux_list_ctravaux_particulier_assurance_dommage',
      'travaux_list_dossier_urbanisme',
      'travaux_list_facture_entreprise_travaux',
      'travaux_list_travaux_autorisation_pv_ag',
      'travaux_list_travaux_facture_doc'
    ]
  },
  id: 'OPERATION__HERMES__IMMOBILIER__LOCATION_PRO',
  label: 'Dossier de Location - Cabinet Hermès',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL',
  specificTypes: ['HERMES', 'IMMOBILIER', 'LOCATION_PRO'],
  type: 'OPERATION'
};
