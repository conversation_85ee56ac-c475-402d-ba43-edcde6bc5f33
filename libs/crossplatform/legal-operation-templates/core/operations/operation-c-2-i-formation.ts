// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationC2IFormation: LegalOperationTemplate = {
  config: {
    emailLabelPattern: '',
    tags: {
      OPERATION__AUTRE_INTERVENANT: {
        order: 0,
        max: 1,
        group: 0
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'AUTRE_LIBRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'AUTRE_INTERVENANT'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'AUTRE_LIBRE_VIDE',
        label: 'Contrat libre'
      }
    ],
    documentsToExclude: [],
    documentsToInclude: []
  },
  id: 'OPERATION__C2I__FORMATION',
  label: 'Formation - C2i',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__C2I__FORMATION',
  specificTypes: ['C2I', 'FORMATION'],
  type: 'OPERATION'
};
