// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationRecrutementMynotaryAgent: LegalOperationTemplate = {
  config: {
    emailLabelPattern: '{{ prenom_agent }} {{ nom_agent }}',
    labelPattern: '{{ prenom_agent }} {{ nom_agent }}',
    tags: {
      prenom_agent: {
        link: 'OPERATION__RECRUTEMENT__AGENT__AGENT_COMMERCIAL',
        questionId: 'prenoms',
        max: 1,
        label: 'Prénom agent'
      },
      nom_agent: {
        link: 'OPERATION__RECRUTEMENT__AGENT__AGENT_COMMERCIAL',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom agent'
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'AGENCE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'PARTENAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'AGENT_COMMERCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'GENERAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'BENEFICIAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'PRESTATAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'SPONSORING'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'SPONSOR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'RECRUTEMENT_MYNOTARY_AGENT_MANDAT_AGENT_COMMERCIAL',
        label: "Mandat d'agent commercial"
      }
    ],
    documentsToExclude: [],
    documentsToInclude: []
  },
  id: 'OPERATION__RECRUTEMENT__MYNOTARY__AGENT',
  label: "Recrutement - Contrat d'agent commercial",
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__RECRUTEMENT__MYNOTARY__AGENT',
  specificTypes: ['RECRUTEMENT', 'MYNOTARY', 'AGENT'],
  type: 'OPERATION'
};
