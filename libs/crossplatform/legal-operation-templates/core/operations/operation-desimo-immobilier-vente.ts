// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationDesimoImmobilierVente: LegalOperationTemplate = {
  config: {
    labelPattern: '{{ numero_lot }} / {{ programme_nom }} /  {{ nom_acquereur }}',
    emailLabelPattern: '{{ biens_vendus }}',
    tags: {
      numero_lot: {
        link: 'OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_DESIMO',
        questionId: ['numero_lot', 'numero_commercialisation_lot'],
        max: 1,
        label: 'Numéro du lot'
      },
      programme_nom: {
        link: 'OPERATION__IMMOBILIER__VENTES_PROGRAMME__GENERAL',
        questionId: 'programme_nom',
        max: 1,
        label: 'Nom du programme'
      },
      nom_acquereur: {
        link: 'OPERATION__IMMOBILIER__VENTE_NEUF__RESERVATAIRES',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_DESIMO',
        order: 0,
        max: 1
      },
      commercialisateurs: {
        link: 'OPERATION__IMMOBILIER__VENTE_NEUF__REPRESENTANT_COMMERCIALISATEUR',
        order: 3,
        max: 5,
        group: 0
      },
      acquereurs: {
        link: 'OPERATION__IMMOBILIER__VENTE_NEUF__RESERVATAIRES',
        order: 2,
        max: 1
      }
    },
    hiddenPages: {
      documents: true
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'LOTS_DESIMO'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_NEUF', 'RESERVATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_NEUF', 'FICHES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'PROMOTEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'REPRESENTANT_PROMOTEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'GENERAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      }
    ],
    linkOverrides: [
      'OPERATION__IMMOBILIER__VENTES_PROGRAMME__GENERAL',
      'OPERATION__IMMOBILIER__VENTES_PROGRAMME__PROMOTEURS',
      'OPERATION__IMMOBILIER__VENTES_PROGRAMME__REPRESENTANT_PROMOTEUR',
      'OPERATION__IMMOBILIER__VENTES_PROGRAMME__NOTAIRE_PROGRAMME'
    ],
    operationLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'VENTE_VEFA_DESIMO']
      }
    ],
    contracts: [
      {
        id: 'DESIMO_IMMOBILIER_VENTE_CONTRAT_RESERVATION',
        label: 'Contrat de Réservation'
      }
    ],
    isSubOperation: true,
    documentsToExclude: [],
    documentsToInclude: ['echeancier', 'infos_client_vefa', 'notice_descriptive']
  },
  id: 'OPERATION__DESIMO__IMMOBILIER__VENTE',
  label: 'Desimo - Vente',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__VENTE_NEUF',
  specificTypes: ['DESIMO', 'IMMOBILIER', 'VENTE'],
  type: 'OPERATION'
};
