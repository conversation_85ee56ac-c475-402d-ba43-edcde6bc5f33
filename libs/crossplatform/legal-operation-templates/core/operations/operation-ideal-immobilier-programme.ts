// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationIdealImmobilierProgramme: LegalOperationTemplate = {
  config: {
    hiddenPages: {
      documents: true
    },
    tags: {
      OPERATION__IMMOBILIER__VENTES_PROGRAMME__REPRESENTANT_PROMOTEUR: {
        order: 0,
        max: 1,
        group: 0
      },
      OPERATION__IMMOBILIER__VENTES_PROGRAMME__NOTAIRE_PROGRAMME: {
        order: 1,
        max: 1,
        group: 0
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'PROMOTEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'REPRESENTANT_PROMOTEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'GENERAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'COPROPRIETE'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 10
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'LOTS_IDEAL'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [
      {
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 999
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'VENTE_VEFA_IDEAL']
      }
    ],
    subOperations: 'Ventes',
    synthesis: {
      tables: [
        {
          id: 'LOTS',
          title: 'Grille de lots',
          titleWithArticle: 'un lot',
          linkSpecificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'LOTS_IDEAL'],
          type: 'PRIMARY',
          columns: [
            {
              label: 'N° de lot',
              property: 'numero_lot.value',
              bold: true,
              responsive: 'TITLE'
            },
            {
              label: 'N° de commercialisation',
              property: 'numero_commercialisation_lot.value',
              responsive: 'CONTENT'
            },
            {
              label: 'Type',
              property: 'nature_bien.value',
              format: 'SELECT',
              responsive: 'TITLE'
            },
            {
              label: 'Prix',
              property: 'programme_prix_vente.value',
              format: 'PRICE',
              responsive: 'CONTENT'
            },
            {
              label: 'Reservation',
              source: 'RESERVATION',
              property: 'status',
              responsive: 'CONTENT'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_IDEAL.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HABITATION']
            },
            {
              path: 'links.OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_IDEAL.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HORS_HABITATION']
            },
            {
              path: 'links.OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_IDEAL.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'INDIVIDUEL_HABITATION']
            }
          ]
        },
        {
          id: 'COPROS',
          title: 'Copropriétés',
          titleWithArticle: 'une copropriété',
          linkSpecificTypes: ['OPERATION', 'IMMOBILIER', 'VENTES_PROGRAMME', 'COPROPRIETE'],
          type: 'SECONDARY',
          columns: [
            {
              label: 'Dénomination',
              bold: true,
              property: 'denomination.value',
              responsive: 'TITLE'
            },
            {
              label: 'Adresse',
              property: 'adresse.value.formattedAddress',
              responsive: 'TITLE'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_IDEAL.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*.links.COMPOSITION__COPROPRIETE.\\d*.link.branches.CONTENU.\\d*.to.\\d*',
              specificType: ['STRUCTURE', 'COPROPRIETE']
            }
          ]
        }
      ]
    },
    contractModels: [
      {
        id: 'IDEAL_IMMOBILIER_VENTE_CONTRAT_RESERVATION',
        label: 'Contrat de réservation'
      }
    ],
    isParentOperation: true,
    documentsToExclude: [],
    documentsToInclude: ['diag_pollution', 'notaire_depot']
  },
  id: 'OPERATION__IDEAL__IMMOBILIER__PROGRAMME',
  label: 'IDEAL - Programme',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__VENTES_PROGRAMME',
  specificTypes: ['IDEAL', 'IMMOBILIER', 'PROGRAMME'],
  type: 'OPERATION'
};
