// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationUnisOrganisationInterne: LegalOperationTemplate = {
  config: {
    emailLabelPattern: '{{ nom_agence }}',
    labelPattern: '{{ nom_agence }}',
    tags: {
      nom_agence: {
        link: 'OPERATION__IMMOBILIER__VENTE__AGENTS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Dénomination Agence'
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'AGENTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'MANDATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_ANCIEN', 'FICHES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'ORGANISATION_INTERNE_PROTOCOLE_INTERNE_TRACFIN',
        label: 'Cadre Général - Protocole TRACFIN'
      },
      {
        id: 'ORGANISATION_INTERNE_DECLARATION_TRACFIN_MANDATAIRE',
        label: "Engagement sur l'honneur Négociateur - Protocole TRACFIN"
      },
      {
        id: 'UNIS_ORGANISATION_INTERNE_DESIGNATION_CORRESPONDANT_TRACFIN',
        label: 'Désignation du correspondant TRACFIN'
      },
      {
        id: 'UNIS_ORGANISATION_INTERNE_INFORMATIONS_RGPD',
        label: 'Informations RGPD'
      }
    ],
    documentsToExclude: [],
    documentsToInclude: []
  },
  id: 'OPERATION__UNIS__ORGANISATION_INTERNE',
  label: 'UNIS - Exercice de la profession',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__ORGANISATION_INTERNE',
  specificTypes: ['UNIS', 'ORGANISATION_INTERNE'],
  type: 'OPERATION'
};
