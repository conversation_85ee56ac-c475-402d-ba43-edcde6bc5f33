// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationPrelloImmobilierLocation: LegalOperationTemplate = {
  config: {
    labelPattern: '{{ adresse }} / {{ nom_bailleur }} / {{ nom_locataire }}',
    emailLabelPattern: '{{ adresse }}',
    tags: {
      adresse: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES',
        questionId: 'adresse',
        format: 'ADDRESS',
        max: 1,
        label: 'Adresse'
      },
      nom_bailleur: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom bailleur'
      },
      nom_locataire: {
        link: 'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom locataire'
      },
      biens_loues: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES',
        order: 0,
        max: 1
      },
      bailleurs: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
        order: 1,
        max: 1
      },
      locataires: {
        link: 'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
        order: 2,
        max: 1
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_VISITE', 'VISITEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'BAILLEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'PRELLO_BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'LOCATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'ACQUEREURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'REPRESENTANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'GARANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'BIENS_LOUES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'BIENS_VENDUS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'FICHES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'MANDANT_PRELLO'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'MANDATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'MANDATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'AGENCE_DELEGATAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'PRELLO_IMMOBILIER_LOCATION_OFFRE_PREALABLE',
        label: 'Offre préalable'
      },
      {
        id: 'PRELLO_IMMOBILIER_LOCATION_MANDAT_LOCATION_INVESTISSEUR',
        label: 'Mandat Exclusif de Recherche de Locataires et de Gestion - Investisseur'
      },
      {
        id: 'PRELLO_IMMOBILIER_LOCATION_MANDAT_LOCATION_OCCUPANT',
        label: 'Mandat Exclusif de Recherche de Locataires et de Gestion - Occupant'
      },
      {
        id: 'PRELLO_IMMOBILIER_LOCATION_MANDAT_GESTION',
        label: 'Mandat de Gestion'
      },
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_LOCATION_AVENANT',
        label: 'Mandat de location - Avenant'
      },
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_RECHERCHE',
        label: 'Mandat de recherche'
      },
      {
        id: 'LOCATION__MANDAT_DE_RECHERCHE_ANGLAIS',
        label: 'Mandat de Recherche - Anglais'
      },
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_RECHERCHE_AVENANT',
        label: 'Mandat de recherche - Avenant'
      },
      {
        id: 'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION',
        label: 'Délégation de Mandat de Location'
      },
      {
        id: 'LOCATION__DELEGATION_DE_MANDAT_DE_RECHERCHE',
        label: 'Délégation de Mandat de Recherche'
      },
      {
        id: 'IMMOBILIER_LOCATION_BON_VISITE_LOCATION',
        label: 'Bon de Visite'
      },
      {
        id: 'IMMOBILIER_LOCATION_ETAT_DES_LIEUX',
        label: 'Etat des lieux'
      },
      {
        id: 'IMMOBILIER_LOCATION_PROCATION_LOCATION_BAILLEUR',
        label: 'Procuration Bail - Bailleur'
      },
      {
        id: 'IMMOBILIER_LOCATION_PROCATION_LOCATION_LOCATAIRE',
        label: 'Procuration Bail - Locataire'
      },
      {
        id: 'IMMOBILIER_LOCATION_BAIL',
        label: "Bail d'habitation"
      },
      {
        id: 'LOCATION__BAIL_HABITATION_ANGLAIS',
        label: "Bail d'habitation - Anglais"
      },
      {
        id: 'IMMOBILIER_LOCATION_BAIL_AVENANT',
        label: "Bail d'habitation - Avenant"
      },
      {
        id: 'IMMOBILIER_LOCATION_CAUTIONNEMENT',
        label: 'Acte de Cautionnement'
      },
      {
        id: 'LOCATION__ACTE_DE_CAUTIONNEMENT_ANGLAIS',
        label: 'Acte de Cautionnement - Anglais'
      },
      {
        id: 'IMMOBILIER_LOCATION_BAIL_MOBILITE',
        label: 'Bail de mobilite'
      },
      {
        id: 'IMMOBILIER_LOCATION_BAIL_SAISONNIER',
        label: 'Bail location saisonnière'
      },
      {
        id: 'LOCATION__BAIL_LOCATION_SAISONNIERE_ANGLAIS',
        label: 'Bail location saisonnière - Anglais'
      },
      {
        id: 'IMMOBILIER_LOCATION_BAIL_COMMUN',
        label: 'Bail civil de droit commun (Parking, box, cave...)'
      },
      {
        id: 'IMMOBILIER_LOCATION_TRACFIN_LOCATION',
        label: 'Evaluation TRACFIN - Location'
      }
    ],
    recordExtensions: [],
    hiddenPages: {
      documents: true
    },
    documentsToExclude: [],
    documentsToInclude: [
      'mandat_retractation_prello',
      'prello_appel_charge',
      'prello_conciergerie',
      'prello_force_majeure',
      'prello_liste_entretien',
      'prello_photo'
    ]
  },
  id: 'OPERATION__PRELLO__IMMOBILIER__LOCATION',
  label: 'Prello ',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__LOCATION',
  specificTypes: ['PRELLO', 'IMMOBILIER', 'LOCATION'],
  type: 'OPERATION'
};
