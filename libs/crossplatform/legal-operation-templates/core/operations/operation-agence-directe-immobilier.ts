// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationAgenceDirecteImmobilier: LegalOperationTemplate = {
  config: {
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    labelPattern: '{{ numero_mandat }} / {{ adresse }} / {{ nom_vendeur }} / {{ nom_acquereur }}',
    emailLabelPattern: '{{ adresse }}',
    tags: {
      adresse: {
        link: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_AGENCE_DIRECTE',
        questionId: 'adresse',
        format: 'ADDRESS',
        max: 1,
        label: 'Adresse'
      },
      numero_mandat: {
        link: 'OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES',
        questionId: 'mandat_numero',
        max: 1,
        label: 'N° mandat',
        format: 'MANDAT'
      },
      nom_vendeur: {
        link: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom vendeur'
      },
      nom_acquereur: {
        link: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_AGENCE_DIRECTE',
        order: 0,
        max: 1
      },
      vendeurs: {
        link: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
        order: 1,
        max: 1
      },
      acquereurs: {
        link: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
        order: 2,
        max: 1
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VENDEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VISITEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'ACQUEREURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'OFFRANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'REPRESENTANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'BIENS_VENDUS_AGENCE_DIRECTE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_ANCIEN', 'FICHES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'AGENTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'MANDATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'NOTAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'REDACTEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'AGENCE_DIRECTE_IMMOBILIER_MANDAT_EXCLUSIF',
        label: 'Mandat Exclusif'
      },
      {
        id: 'AGENCE_DIRECTE_IMMOBILIER_MANDAT_EXCLUSIF_HORS_ETABLISSEMENT',
        label: 'Mandat Exclusif Hors Etablissement'
      },
      {
        id: 'AGENCE_DIRECTE_IMMOBILIER_MANDAT_SIMPLE',
        label: 'Mandat Sans Exclusivité'
      },
      {
        id: 'AGENCE_DIRECTE_IMMOBILIER_MANDAT_RECHERCHE',
        label: 'Mandat De Recherche'
      },
      {
        id: 'AGENCE_DIRECTE_IMMOBILIER_AVENANT_MANDAT',
        label: 'Avenant - Mandat de Vente'
      },
      {
        id: 'AGENCE_DIRECTE_IMMOBILIER_OFFRE_ACHAT',
        label: "Offre d'achat"
      },
      {
        id: 'AGENCE_DIRECTE_IMMOBILIER_ACCEPTATION_OFFRE',
        label: "Acceptation de l'offre par le Vendeur"
      },
      {
        id: 'AGENCE_DIRECTE_IMMOBILIER_PROCURATION_VENDRE',
        label: 'Procuration pour Vendre'
      },
      {
        id: 'AGENCE_DIRECTE_IMMOBILIER_PROCURATION_ACQUERIR',
        label: 'Procuration pour Acquérir'
      },
      {
        id: 'AGENCE_DIRECTE_IMMOBILIER_COMPROMIS_VENTE',
        label: 'Compromis de vente'
      },
      {
        id: 'AGENCE_DIRECTE_IMMOBILIER_AVENANT_COMPROMIS',
        label: 'Avenant au compromis de vente'
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'ACQUEREUR'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
          indirectLinks: ['SITUATION_MARITALE__MARIAGE', 'SITUATION_MARITALE__DIVORCE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_AGENCE_DIRECTE',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'NOTAIRE'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__NOTAIRES',
          directRecords: ['PERSONNE__MORALE__NOTAIRE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'BIEN'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_AGENCE_DIRECTE',
          directRecords: [
            'BIEN__INDIVIDUEL_HABITATION',
            'BIEN__INDIVIDUEL_HORS_HABITATION',
            'BIEN__TERRAIN_CONSTRUCTIBLE',
            'BIEN__LOT_HABITATION',
            'BIEN__LOT_HORS_HABITATION'
          ]
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'LOTISSEMENT'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_AGENCE_DIRECTE',
          indirectLinks: ['COMPOSITION__LOTISSEMENT__LOTISSEMENT'],
          indirectRecords: ['STRUCTURE__LOTISSEMENT']
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'VENDEUR'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
          directRecords: ['PERSONNE__PHYSIQUE'],
          indirectLinks: [
            'CAPACITE__TUTELLE',
            'CAPACITE__MINORITE',
            'CAPACITE__MANDAT_PROTECTION_FUTURE',
            'CAPACITE__HABILITATION_FAMILIALE'
          ]
        }
      }
    ],
    hiddenPages: {
      documents: true
    },
    documentsToExclude: [],
    documentsToInclude: [
      'ad_conge_locataire',
      'ad_permis_construire_construction',
      'ad_plan_lot_document',
      'agence_directe_dip_electronique',
      'agence_directe_dip_electronique_exclusif',
      'agence_directe_dip_papier',
      'agence_directe_dip_papier_exclusif',
      'agence_directe_retractation_electronique',
      'agence_directe_retractation_papier',
      'assainissement_non_collectif_controle_libre',
      'assurance_decennale_construction',
      'assurance_do_initial',
      'assurance_dommage_construction',
      'attestation_diagnostiqueur',
      'carnet_information_construction_document',
      'certificat_conformite_construction',
      'cheminee_facture_ramonage',
      'conformite_piscine',
      'construction_particulier_assurance_dommage',
      'contrat_attaches_list_contrat_attaches_document',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'declaration_achevement_construction',
      'declaration_prealable_construction',
      'diagnostic_amiante',
      'diagnostic_amiante_libre',
      'diagnostic_electrique',
      'diagnostic_electrique_libre',
      'diagnostic_gaz',
      'diagnostic_gaz_libre',
      'diagnostic_merule',
      'diagnostic_merule_libre',
      'diagnostic_plomb',
      'diagnostic_plomb_libre',
      'diagnostic_termites',
      'diagnostic_termites_libre',
      'document_pre_etat_date_scaprim',
      'dpe',
      'dpe_audit_document',
      'dpe_libre',
      'engagement_conjoint',
      'etude_geotechnique',
      'facture_ramonage',
      'factures_entreprises_construction',
      'location_bail_liste_contrat_bail',
      'location_bail_liste_etat_des_lieux_document',
      'location_bail_liste_quittance_loyer',
      'location_bail_liste_quittances_loyer',
      'mandat_bail',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'permis_construire_construction',
      'piscine_note_securite_document',
      'piscine_securite_attestation_document',
      'pluie_declaration_doc',
      'servitude_acte',
      'taxe_fonciere',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_autorisation_copropriete_travaux_global',
      'travaux_list_carnet_information_travaux_document',
      'travaux_list_certificat_conformite',
      'travaux_list_ctravaux_particulier_assurance_dommage',
      'travaux_list_declaration_achevement',
      'travaux_list_declaration_prealable',
      'travaux_list_dossier_urbanisme',
      'travaux_list_facture_entreprise_travaux',
      'travaux_list_permis_construire',
      'travaux_list_travaux_autorisation_pv_ag',
      'travaux_list_travaux_facture_doc'
    ]
  },
  id: 'OPERATION__AGENCE_DIRECTE__IMMOBILIER',
  label: 'Dossier de vente - Agence Directe',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
  specificTypes: ['AGENCE_DIRECTE', 'IMMOBILIER'],
  type: 'OPERATION'
};
