// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationCdcImmobilierVente: LegalOperationTemplate = {
  config: {
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_SOCIAL__VENTE'
    },
    labelPattern: '{{ nom_acquereur }} / {{ numero_lot }} / {{ programme_nom }}',
    emailLabelPattern: '{{ numero_lot }} / {{ programme_nom }} / {{ nom_acquereur }}',
    tags: {
      numero_lot: {
        link: 'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__LOTS',
        questionId: ['numero_commercialisation_lot'],
        max: 1,
        label: 'Numéro du lot'
      },
      programme_nom: {
        link: 'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME',
        questionId: 'programme_nom',
        max: 1,
        label: 'Nom du programme'
      },
      nom_acquereur: {
        link: 'OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__ACQUEREUR',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__LOTS',
        order: 0,
        max: 1
      },
      commercialisateurs: {
        link: 'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR',
        order: 3,
        max: 5,
        group: 0
      },
      acquereurs: {
        link: 'OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__ACQUEREUR',
        order: 2,
        max: 1
      }
    },
    hiddenPages: {
      documents: true
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'VENTE', 'ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'REPRESENTANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'VENTE', 'INTERVENANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'VENTE', 'FICHE_VENTE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'VENTE', 'VENDEUR_CDC'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'VENTE', 'NOTAIRE_ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'VENTE', 'COMMERCIALISATEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'VENTE', 'REPRESENTANT_COMMERCIALISATEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'BAILLEUR_SOCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'REPRESENTANT_BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'FICHE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'CLERC_NOTAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'GESTIONNAIRE_VENTE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'REFERENT_VENTE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    linkOverrides: [
      'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME',
      'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__BAILLEUR_SOCIAL',
      'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR',
      'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME',
      'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__CLERC_NOTAIRE',
      'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__GESTIONNAIRE_VENTE',
      'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__REFERENT_VENTE'
    ],
    operationLinks: [
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'VENTES']
      }
    ],
    contracts: [
      {
        id: 'CDC_IMMOBILIER_VENTE_PROMESSE_CDC_HABITAT_SOCIAL',
        label: ' PSV - CDCHS - Collectif'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_CDC_IMMOBILIER_VENTE_PROMESSE_CDC_HABITAT_SOCIAL_INDIVIDUEL',
        label: 'PSV - CDCHS - Individuel'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_PROMESSE_CDC_HABITAT_CONVENTIONNE',
        label: 'PSV - CDCH Conv - Collectif'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_CDC_IMMOBILIER_VENTE_PROMESSE_CDC_HABITAT_CONVENTIONNE_INDIVIDUEL',
        label: 'PSV - CDCH Conv - Individuel'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_PROMESSE_CDC_HABITAT_NON_CONVENTIONNE_COLLECTIF',
        label: 'PSV - CDCH Accord / Hors Accord 2005 - Collectif'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_PROMESSE_CDC_HABITAT_NON_CONVENTIONNE_PAVILLON',
        label: 'PSV - CDCH Hors Accord 2005 - Pavillon'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_INTEGRATION_CDC_HABITAT_SOCIAL',
        label: 'PSV - CDCHS. - Collectif'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_CDC_IMMOBILIER_VENTE_INTEGRATION_CDC_HABITAT_SOCIAL_INDIVIDUEL',
        label: 'PSV - CDCHS. - Individuel'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_INTEGRATION_CDC_HABITAT_CONVENTIONNE',
        label: 'PSV - CDCH. Conv - Collectif'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_CDC_IMMOBILIER_VENTE_INTEGRATION_CDC_HABITAT_CONVENTIONNE_INDIVIDUEL',
        label: 'PSV - CDCH. Conv - Individuel'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_INTEGRATION_CDC_HABITAT_NON_CONVENTIONNE_COLLECTIF',
        label: 'PSV - CDCH. Accord / Hors Accord 2005 - Collectif'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_INTEGRATION_CDC_HABITAT_NON_CONVENTIONNE_PAVILLON',
        label: 'PSV - CDCH. Hors Accord 2005 - Pavillon'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_PROMESSE_LIGHT',
        label: 'Promesse Light'
      },
      {
        id: 'CDC__IMMOBILIER__VENTE__PROCURATION',
        label: 'Procuration pour Acquérir'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_AVENANT_PROMESSE',
        label: 'Avenant - Promesse'
      },
      {
        id: 'CDC__IMMOBILIER__VENTE__ATTESTATION_DEBROUSSAILLEMENT',
        label: 'Attestation de Débroussaillement'
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'NOTAIRE'],
        match: {
          directLink: 'OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR',
          directRecords: ['PERSONNE__MORALE__NOTAIRE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__LOTS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      }
    ],
    isSubOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      'acquereur_social_information',
      'acquereur_social_proposition_achat',
      'acquereur_social_revenu',
      'appel_charge',
      'assainissement_non_collectif_controle_libre',
      'assurance_decennale_construction',
      'assurance_do_initial',
      'assurance_dommage_construction',
      'attestation_diagnostiqueur',
      'bailleur_pouvoir',
      'carnet_information_construction_document',
      'certificat_conformite_construction',
      'cheminee_facture_ramonage',
      'conformite_piscine',
      'construction_particulier_assurance_dommage',
      'contrat_bail',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'copro_alur_attestation',
      'copro_infos_financieres_alur',
      'declaration_achevement_construction',
      'declaration_prealable_construction',
      'diagnostic_amiante',
      'diagnostic_electrique',
      'diagnostic_gaz',
      'diagnostic_merule',
      'diagnostic_parties_communes_amiante',
      'diagnostic_parties_communes_diagnostic_dtg',
      'diagnostic_parties_communes_plomb',
      'diagnostic_plomb',
      'diagnostic_termites',
      'document_pre_etat_date',
      'dpe',
      'dpe_audit_document',
      'etude_geotechnique',
      'facture_ramonage',
      'factures_entreprises_construction',
      'gestion_lotissement_gestion_lotissement_autre_statuts',
      'gestion_lotissement_gestion_lotissement_statuts_aful',
      'gestion_lotissement_gestion_lotissement_statuts_asl',
      'gestion_volume_gestion_volume_autre_statuts',
      'gestion_volume_gestion_volume_statuts_aful',
      'gestion_volume_gestion_volume_statuts_asl',
      'location_bail_liste_contrat_bail',
      'mandat_bail',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'permis_construire_construction',
      'piscine_note_securite_document',
      'piscine_securite_attestation_document',
      'programme_numero_porte',
      'programme_escalier',
      'programme_cdc_jardin_surface',
      'programme_cdc_balcon_surface',
      'programme_cdc_terrasse_surface',
      'pv_ag',
      'servitude_acte',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_autorisation_copropriete_travaux_global',
      'travaux_list_carnet_information_travaux_document',
      'travaux_list_certificat_conformite',
      'travaux_list_ctravaux_particulier_assurance_dommage',
      'travaux_list_declaration_achevement',
      'travaux_list_declaration_prealable',
      'travaux_list_dossier_urbanisme',
      'travaux_list_facture_entreprise_travaux',
      'travaux_list_permis_construire',
      'travaux_list_travaux_autorisation_pv_ag',
      'travaux_list_travaux_facture_doc',
      'cdc_edd_rcp',
      'cdc_pv_ag',
      'cdc_fiche_synthetique',
      'cdc_carnet_entretien',
      'annexes_complementaires'
    ]
  },
  id: 'OPERATION__CDC__IMMOBILIER__VENTE',
  label: 'CDC Habitat Ancien - Vente',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__SOCIAL_VENTE',
  specificTypes: ['CDC', 'IMMOBILIER', 'VENTE'],
  type: 'OPERATION'
};
