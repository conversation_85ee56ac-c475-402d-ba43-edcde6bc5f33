// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationUnisLocationHabitation: LegalOperationTemplate = {
  config: {
    labelPattern: '{{ adresse }} / {{ nom_bailleur }} / {{ nom_locataire }}',
    emailLabelPattern: '{{ adresse }}',
    tags: {
      adresse: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES',
        questionId: 'adresse',
        format: 'ADDRESS',
        max: 1,
        label: 'Adresse'
      },
      nom_bailleur: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom bailleur'
      },
      nom_locataire: {
        link: 'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom locataire'
      },
      biens_loues: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES',
        order: 0,
        max: 1
      },
      bailleurs: {
        link: 'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
        order: 1,
        max: 1
      },
      locataires: {
        link: 'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
        order: 2,
        max: 1
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_VISITE', 'VISITEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'BAILLEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'LOCATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'NOUVEAU_LOCATAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'REPRESENTANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'BIENS_LOUES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'FICHES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'GARANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'AGENTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'MANDATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION', 'AGENCE_DELEGATAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_DIP',
        label: "Document d'Informations Précontractuelles"
      },
      {
        id: 'UNIS_LOCATION_HABITATION_MANDAT_LOCATION_UNIS',
        label: 'Mandat de Location Simple ou Exclusif - UNIS '
      },
      {
        id: 'UNIS_LOCATION_HABITATION_MANDAT_GESTION_UNIS',
        label: 'Mandat de Gestion - UNIS'
      },
      {
        id: 'UNIS_LOCATION_HABITATION_AVENANT_MANDAT_LOCATION',
        label: 'Avenant au Mandat de Location'
      },
      {
        id: 'UNIS_LOCATION_HABITATION_AVENANT_MANDAT_GESTION',
        label: 'Avenant au Mandat de Gestion'
      },
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_DROIT_COMMUN',
        label: 'Mandat de location - Parking, box, cave'
      },
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_RECHERCHE',
        label: 'Mandat de recherche'
      },
      {
        id: 'LOCATION__MANDAT_DE_RECHERCHE_ANGLAIS',
        label: 'Mandat de Recherche - Anglais'
      },
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_RECHERCHE_AVENANT',
        label: 'Mandat de recherche - Avenant'
      },
      {
        id: 'IMMOBILIER_LOCATION_MANDAT_SAISONNIER',
        label: 'Mandat de location Saisonnière'
      },
      {
        id: 'LOCATION__MANDAT_DE_LOCATION_SAISONNIERE_ANGLAIS',
        label: 'Mandat de location Saisonnière - Anglais'
      },
      {
        id: 'IMMOBILIER_LOCATION_DELEGATION_MANDAT_LOCATION',
        label: 'Délégation de Mandat de Location'
      },
      {
        id: 'LOCATION__DELEGATION_DE_MANDAT_DE_RECHERCHE',
        label: 'Délégation de Mandat de Recherche'
      },
      {
        id: 'IMMOBILIER_LOCATION_BON_VISITE_LOCATION',
        label: 'Bon de Visite'
      },
      {
        id: 'IMMOBILIER_LOCATION_ETAT_DES_LIEUX',
        label: 'Etat des lieux'
      },
      {
        id: 'IMMOBILIER_LOCATION_PROCATION_LOCATION_BAILLEUR',
        label: 'Procuration Bail - Bailleur'
      },
      {
        id: 'IMMOBILIER_LOCATION_PROCATION_LOCATION_LOCATAIRE',
        label: 'Procuration Bail - Locataire'
      },
      {
        id: 'UNIS_LOCATION_HABITATION_BAIL_HABITATION_UNIS',
        label: "Bail d'Habitation Résidence Principale - Nue ou Meublée - UNIS"
      },
      {
        id: 'IMMOBILIER_LOCATION_BAIL_AVENANT',
        label: "Bail d'habitation - Avenant"
      },
      {
        id: 'UNIS_LOCATION_HABITATION_AVENANT_CHANGEMENT_COLOCATAIRE',
        label: "Bail d'habitation - Avenant Changement de Colocataire"
      },
      {
        id: 'UNIS_LOCATION_HABITATION_CAUTIONNEMENT_UNIS',
        label: 'Engagement de Caution Solidaire'
      },
      {
        id: 'UNIS_LOCATION_HABITATION_BAIL_STATIONNEMENT',
        label: 'Bail - Stationnement'
      },
      {
        id: 'UNIS_LOCATION_HABITATION_BAIL_RESIDENCE_SECONDAIRE',
        label: 'Bail - Résidence secondaire'
      },
      {
        id: 'IMMOBILIER_LOCATION_BAIL_COMMUN',
        label: 'Bail civil de droit commun'
      },
      {
        id: 'UNIS_LOCATION_HABITATION_BAIL_MOBILITE_UNIS',
        label: 'Bail de mobilite - UNIS'
      },
      {
        id: 'UNIS_LOCATION_HABITATION_FACTURE_HONORAIRES',
        label: 'Facture Honoraires Locataire'
      },
      {
        id: 'UNIS_LOCATION_HABITATION_CONGE_LOCATION',
        label: 'Congé du propriétaire (vente, reprise, motif légitime et sérieux)'
      },
      {
        id: 'IMMOBILIER_LOCATION_BAIL_SAISONNIER',
        label: 'Bail location saisonnière'
      },
      {
        id: 'LOCATION__BAIL_LOCATION_SAISONNIERE_ANGLAIS',
        label: 'Bail location saisonnière - Anglais'
      },
      {
        id: 'IMMOBILIER_LOCATION_TRACFIN_LOCATION',
        label: 'Evaluation TRACFIN - Location'
      }
    ],
    recordExtensions: [],
    hiddenPages: {
      documents: true
    },
    documentsToExclude: ['mandat_bail', 'taxe_fonciere'],
    documentsToInclude: [
      'assainissement_non_collectif_controle_libre',
      'assurance_do_initial',
      'attestation_diagnostiqueur',
      'attestation_surface',
      'autorisation_location',
      'carnet_information_construction_document',
      'cheminee_facture_ramonage',
      'contrat_attaches_list_contrat_attaches_document',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'declaration_prealable_location',
      'diagnostic_amiante',
      'diagnostic_electrique',
      'diagnostic_gaz',
      'diagnostic_merule',
      'diagnostic_parties_communes_amiante',
      'diagnostic_parties_communes_plomb',
      'diagnostic_plomb',
      'diagnostic_plomb_libre',
      'diagnostic_termites',
      'dpe',
      'dpe_audit_document',
      'dpe_libre',
      'etude_geotechnique',
      'facture_ramonage',
      'location_bail_liste_contrat_bail',
      'location_bail_liste_etat_des_lieux_document',
      'location_bail_liste_quittance_loyer',
      'location_bail_liste_quittances_loyer',
      'mandat_bail',
      'mandat_retractation_unis',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'piscine_note_securite_document',
      'piscine_securite_attestation_document',
      'pv_ag',
      'servitude_acte',
      'taxe_fonciere',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_autorisation_copropriete_travaux_global',
      'travaux_list_carnet_information_travaux_document',
      'travaux_list_ctravaux_particulier_assurance_dommage',
      'travaux_list_dossier_urbanisme',
      'travaux_list_facture_entreprise_travaux',
      'travaux_list_travaux_autorisation_pv_ag',
      'travaux_list_travaux_facture_doc'
    ]
  },
  id: 'OPERATION__UNIS__LOCATION__HABITATION',
  label: 'UNIS - Gestion Locative - Habitation',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__LOCATION',
  specificTypes: ['UNIS', 'LOCATION', 'HABITATION'],
  type: 'OPERATION'
};
