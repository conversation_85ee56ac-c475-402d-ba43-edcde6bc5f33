// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationSyndicGeneral: LegalOperationTemplate = {
  config: {
    emailLabelPattern: '{{ syndicat }} / {{ copropriété }}',
    labelPattern: '{{ syndicat }} / {{ copropriété }}',
    tags: {
      syndicat: {
        link: 'OPERATION__SYNDIC__GENERAL__SYNDICAT',
        questionId: 'personne_morale_denomination',
        label: 'Nom syndicat',
        max: 1
      },
      copropriété: {
        link: 'OPERATION__SYNDIC__GENERAL__COPROPRIETE',
        questionId: 'adresse',
        format: 'ADDRESS',
        label: 'Adresse',
        max: 1
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'SYNDIC', 'GENERAL', 'SYNDIC<PERSON>'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'SYND<PERSON>', 'GENERAL', 'SYND<PERSON>'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'SYNDIC', 'GENERAL', 'COPROPRIETAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'SYNDIC', 'GENERAL', 'COPROPRIETE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'SYNDIC', 'GENERAL', 'FICHE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'AGENCE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'SYNDIC', 'GENERAL', 'ASL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'SYNDIC_GENERAL_CONTRAT_SYNDIC',
        label: 'Contrat type Syndic'
      },
      {
        id: 'SYNDIC_GENERAL_FICHE_INFORMATION_SYNDIC',
        label: 'Fiche prix et prestations du syndic - Obligatoire'
      },
      {
        id: 'UNIS_COPROPRIETE_RESOLUTION_AG',
        label: 'Résolution AG'
      },
      {
        id: 'UNIS_COPROPRIETE_PV_CARENCE',
        label: "PV de Carence de l'Assemblée"
      },
      {
        id: 'UNIS_COPROPRIETE_FORMULAIRE_VOTE_CORRESPONDANCE',
        label: 'Formulaire de vote par correspondance'
      },
      {
        id: 'UNIS_COPROPRIETE_MANDAT_GESTION_ASL',
        label: 'Mandat de Gestion - ASL'
      },
      {
        id: 'UNIS_COPROPRIETE_BULLETIN_VOTE_ASL',
        label: 'Bulletin vote par correspondance ASL'
      },
      {
        id: 'UNIS_COPROPRIETE_AFFICHAGE_AG',
        label: "Affichage tenue de l'AG"
      },
      {
        id: 'UNIS_COPROPRIETE_BORDEREAU_ARCHIVE',
        label: 'Bordereau Archives'
      },
      {
        id: 'UNIS_COPROPRIETE_COURRIER_TYPE',
        label: 'Courrier type - nomination Syndic'
      },
      {
        id: 'UNIS_COPROPRIETE_CARNET_ENTRETIEN',
        label: "Carnet d'Entretien"
      },
      {
        id: 'UNIS_COPROPRIETE_CARNET_ENTRETIEN_RESTREINT',
        label: "Carnet d'Entretien restreint"
      },
      {
        id: 'UNIS_COPROPRIETE_ACCORD_DEMATERIALISATION',
        label: 'Accord copropriétaire pour dématérialisation'
      },
      {
        id: 'UNIS_COPROPRIETE_INFORMATION_TRI_DECHET',
        label: 'Information tri des déchets'
      }
    ],
    documentsToExclude: [],
    documentsToInclude: []
  },
  id: 'OPERATION__SYNDIC__GENERAL',
  label: 'Opération Syndic',
  mynotaryTemplate: true,
  originTemplate: 'OPERATION__SYNDIC__GENERAL',
  specificTypes: ['SYNDIC', 'GENERAL'],
  type: 'OPERATION'
};
