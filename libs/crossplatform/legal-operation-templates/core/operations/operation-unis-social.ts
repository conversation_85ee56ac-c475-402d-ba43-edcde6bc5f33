// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationUnisSocial: LegalOperationTemplate = {
  config: {
    emailLabelPattern: '',
    contracts: [
      {
        id: 'UNIS_SOCIAL_NEGOCIATEUR_VRP',
        label: 'Négociateur VRP - CDI'
      },
      {
        id: 'UNIS_SOCIAL_NEGOCIATEUR_NON_VRP',
        label: 'Négociateur Non VRP - CDI'
      },
      {
        id: 'UNIS_SOCIAL_CDD_CATEGORIE_A',
        label: "Employé d'immeuble - CDD Catégorie A"
      },
      {
        id: 'UNIS_SOCIAL_CDD_CATEGORIE_B',
        label: 'Gardien Concierge - CDD Catégorie B'
      },
      {
        id: 'UNIS_SOCIAL_CDI_CATEGORIE_A',
        label: "Employé d'immeuble - CDI Catégorie A"
      },
      {
        id: 'UNIS_SOCIAL_CDI_CATEGORIE_B',
        label: '<PERSON>ardien Concierge - CDI Catégorie B'
      }
    ],
    operationLinks: [],
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'AGENCE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'AGENT_COMMERCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'GENERAL', 'SYNDICAT'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'GENERAL', 'EMPLOYE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'GENERAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      }
    ],
    documentsToExclude: [],
    documentsToInclude: []
  },
  id: 'OPERATION__UNIS__SOCIAL',
  label: 'UNIS - Social',
  mynotaryTemplate: true,
  originTemplate: 'OPERATION__UNIS__SOCIAL',
  specificTypes: ['UNIS', 'SOCIAL'],
  type: 'OPERATION'
};
