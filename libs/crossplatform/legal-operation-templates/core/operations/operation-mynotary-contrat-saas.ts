// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationMynotaryContratSaas: LegalOperationTemplate = {
  config: {
    emailLabelPattern: '{{ nom_entreprise }}',
    labelPattern: '{{ nom_entreprise }}',
    tags: {
      nom_entreprise: {
        link: 'OPERATION__MYNOTARY__CONTRAT_SAAS__CLIENT',
        questionId: 'personne_morale_denomination',
        max: 1,
        label: 'Nom Entreprise'
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'MYNOTARY', 'CONTRAT_SAAS', 'CONDITIONS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'MYNOTARY', 'CONTRAT_SAAS', 'CLIENT'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'MYNOTARY', 'CONTRAT_SAAS', 'COMMERCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'MYNOTARY_CONTRAT_SAAS_CONTRAT_SAAS_MYNOTARY',
        label: 'Contrat SAAS MyNotary'
      }
    ],
    documentsToExclude: [],
    documentsToInclude: []
  },
  id: 'OPERATION__MYNOTARY__CONTRAT_SAAS',
  label: 'Contrat SAAS MyNotary',
  mynotaryTemplate: true,
  originTemplate: 'OPERATION__MYNOTARY__CONTRAT_SAAS',
  specificTypes: ['MYNOTARY', 'CONTRAT_SAAS'],
  type: 'OPERATION'
};
