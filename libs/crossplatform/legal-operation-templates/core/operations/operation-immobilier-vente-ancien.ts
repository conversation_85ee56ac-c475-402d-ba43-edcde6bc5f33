// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationImmobilierVenteAncien: LegalOperationTemplate = {
  config: {
    labelPattern: '{{ numero_mandat }} / {{ adresse }} / {{ nom_vendeur }} / {{ nom_acquereur }}',
    emailLabelPattern: '{{ adresse }}',
    tags: {
      adresse: {
        link: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
        questionId: 'adresse',
        format: 'ADDRESS',
        max: 1,
        label: 'Adresse'
      },
      numero_mandat: {
        link: 'OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES',
        questionId: 'mandat_numero',
        max: 1,
        label: 'N° mandat',
        format: 'MANDAT'
      },
      nom_vendeur: {
        link: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom vendeur'
      },
      nom_acquereur: {
        link: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
        order: 0,
        max: 1
      },
      vendeurs: {
        link: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
        order: 1,
        max: 1
      },
      acquereurs: {
        link: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
        order: 2,
        max: 1
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VENDEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VISITEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VISITE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'ACQUEREURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'REPRESENTANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'OFFRANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'OFFRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'ACQUEREUR_CESSIONNAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'APPORTEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'BIENS_VENDUS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_ANCIEN', 'FICHES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'AGENTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'MANDATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'AGENCE_DELEGATAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'NOTAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_MANDAT_DIP',
        label: "Document d'Informations Précontractuelles"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_AVIS_DE_VALEUR',
        label: 'Avis de Valeur'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_MANDAT_SIMPLIFIE',
        label: 'Mandat Simplifié'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_MANDAT',
        label: 'Mandat de vente'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_MANDAT_ANGLAIS',
        label: 'Mandat de Vente - Anglais'
      },
      {
        id: 'TRANSACTION__MANDAT_DE_COMMERCIALISATION',
        label: 'Mandat de commercialisation'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
        label: 'Mandat de vente - Avenant'
      },
      {
        id: 'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
        label: 'Mandat de vente - Avenant Anglais'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT_ENCHERES',
        label: 'Avenant Mandat - Vente Interactive'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_MANDAT_RECHERCHE',
        label: 'Mandat de Recherche'
      },
      {
        id: 'TRANSACTION__MANDAT_DE_RECHERCHE_ANGLAIS',
        label: 'Mandat de Recherche - Anglais'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_MANDAT_RECHERCHE_AVENANT',
        label: 'Mandat de Recherche - Avenant'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_DELEGATION_MANDAT',
        label: 'Délégation de Mandat'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_CONVENTION_APPORTEUR_AFFAIRE',
        label: "Convention d'apporteur d'affaire"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
        label: 'Bon de visite'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
        label: 'Bon de Visite - Anglais'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
        label: "Offre d'achat sans Vendeur"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_AVEC_VENDEUR',
        label: "Offre d'achat avec Vendeur"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_ANGLAIS',
        label: "Offre d'Achat - Anglais"
      },
      {
        id: 'IMMOBILIER__VENTE_ANCIEN__OFFRE_DACHAT_ANGLAIS_AVEC_VENDEUR',
        label: "Offre d'Achat Avec Vendeur - Anglais"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
        label: "Acceptation de l'offre par le Vendeur"
      },
      {
        id: 'IMMOBILIER__VENTE_ANCIEN__CONTRE_OFFRE',
        label: 'Contre offre par le Vendeur'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
        label: 'Synthèse - Transmission notaire'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_PROCURATION_VENTE',
        label: 'Procuration pour vendre'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_PROCURATION_ACQUERIR',
        label: 'Procuration pour acquérir'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
        label: 'Compromis'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
        label: 'Promesse unilatérale de vente'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
        label: "Promesse unilatérale d'achat"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
        label: 'Compromis - Anglais'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_RECONNAISSANCE_HONORAIRES',
        label: "Reconnaissance d'honoraires"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_TRACFIN_SIMPLE_VENDEUR',
        label: "Fiche d'évaluation TRACFIN - Vendeur - Simplifiée"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_TRACFIN_SIMPLE_ACQUEREUR',
        label: "Fiche d'évaluation TRACFIN - Acquéreur - Simplifiée"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_TRACFIN_VENDEUR',
        label: "Fiche d'évaluation TRACFIN - Vendeur - Avec évaluation"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_TRACFIN_ACQUEREUR',
        label: "Fiche d'évaluation TRACFIN - Acquéreur - Avec évaluation"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_ATTESTATION_SIGNATURE',
        label: "Attestation de signature d'avant-contrat"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_AVENANT',
        label: 'Avenant au compromis'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_RESILIATION_COMPROMIS',
        label: 'Résiliation Compromis'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_SUBSTITUTION_COMPROMIS',
        label: 'Substitution Compromis'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_REMISE_ALUR',
        label: 'Remise des documents ALUR'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_ATTESTATION_CARREZ_VENDEUR',
        label: 'Attestation Carrez par le Vendeur'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_CONGE_VENTE',
        label: 'Congé pour vendre'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_ATTESTATION_SYNDIC_BENEVOLE',
        label: 'Attestation Syndic Bénévole'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_FICHE_SYNTHETIQUE',
        label: 'Todo list - Bêta'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_FICHE_CLOTURE',
        label: 'Fiche de Clôture'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_CONTROLE_TRACFIN',
        label: 'Tracfin - Evaluation globale'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_VERIFICATION_IDENTITE_RECOMMANDE',
        label: "Lettre pour vérification d'identité renforcée - Par Recommandé Electronique"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_VERIFICATION_IDENTITE_AVANCEE',
        label: "Lettre pour vérification d'identité renforcée - Par Signature avancée"
      }
    ],
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'ACQUEREUR'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
          indirectLinks: ['SITUATION_MARITALE__MARIAGE', 'SITUATION_MARITALE__DIVORCE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'NOTAIRE'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__NOTAIRES',
          directRecords: ['PERSONNE__MORALE__NOTAIRE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'BIEN'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
          directRecords: [
            'BIEN__INDIVIDUEL_HABITATION',
            'BIEN__INDIVIDUEL_HORS_HABITATION',
            'BIEN__TERRAIN_CONSTRUCTIBLE',
            'BIEN__TERRAIN_NON_CONSTRUCTIBLE',
            'BIEN__MONOPROPRIETE_HABITATION',
            'BIEN__MONOPROPRIETE_HORS_HABITATION',
            'BIEN__LOT_HABITATION',
            'BIEN__LOT_HORS_HABITATION'
          ]
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'LOTISSEMENT'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
          indirectLinks: ['COMPOSITION__LOTISSEMENT__LOTISSEMENT'],
          indirectRecords: ['STRUCTURE__LOTISSEMENT']
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'VENDEUR'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
          directRecords: ['PERSONNE__PHYSIQUE'],
          indirectLinks: [
            'CAPACITE__TUTELLE',
            'CAPACITE__MINORITE',
            'CAPACITE__MANDAT_PROTECTION_FUTURE',
            'CAPACITE__HABILITATION_FAMILIALE'
          ]
        }
      }
    ],
    hiddenPages: {
      documents: true
    },
    documentsToExclude: ['plan'],
    documentsToInclude: [
      'appel_charge',
      'assainissement_non_collectif_controle_libre',
      'assurance_decennale_construction',
      'assurance_do_initial',
      'assurance_dommage_construction',
      'attestation_diagnostiqueur',
      'carnet_information_construction_document',
      'certificat_conformite_construction',
      'cheminee_facture_ramonage',
      'conformite_piscine',
      'conge_locataire',
      'conge_vente',
      'construction_particulier_assurance_dommage',
      'contrat_attaches_list_contrat_attaches_document',
      'contrat_bail',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'copro_alur_attestation',
      'copro_infos_financieres_alur',
      'courrier_renonciation_preemption_locataire',
      'declaration_achevement_construction',
      'declaration_prealable_construction',
      'diagnostic_amiante',
      'diagnostic_electrique',
      'diagnostic_gaz',
      'diagnostic_merule',
      'diagnostic_parties_communes_amiante',
      'diagnostic_parties_communes_diagnostic_dtg',
      'diagnostic_parties_communes_plomb',
      'diagnostic_plomb',
      'diagnostic_termites',
      'dip_exclusif',
      'dip_libre_anglais',
      'dip_recherche',
      'dip_semi',
      'dip_simple',
      'document_pre_etat_date',
      'dpe',
      'dpe_audit_document',
      'engagement_conjoint',
      'etude_geotechnique',
      'facture_ramonage',
      'factures_entreprises_construction',
      'gestion_lotissement_gestion_lotissement_autre_statuts',
      'gestion_lotissement_gestion_lotissement_statuts_aful',
      'gestion_lotissement_gestion_lotissement_statuts_asl',
      'gestion_volume_gestion_volume_autre_statuts',
      'gestion_volume_gestion_volume_statuts_aful',
      'gestion_volume_gestion_volume_statuts_asl',
      'location_bail_liste_contrat_bail',
      'location_bail_liste_etat_des_lieux_document',
      'location_bail_liste_quittance_loyer',
      'location_bail_liste_quittances_loyer',
      'mandat_bail',
      'mandat_retractation_document',
      'offre_achat',
      'ordonnance_autorisation_document',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'permis_construire_construction',
      'piscine_note_securite_document',
      'piscine_securite_attestation_document',
      'pv_ag',
      'pv_ag_liste_pv_ag_doc',
      'servitude_acte',
      'taxe_fonciere',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_autorisation_copropriete_travaux_global',
      'travaux_list_carnet_information_travaux_document',
      'travaux_list_certificat_conformite',
      'travaux_list_ctravaux_particulier_assurance_dommage',
      'travaux_list_declaration_achevement',
      'travaux_list_declaration_prealable',
      'travaux_list_dossier_urbanisme',
      'travaux_list_facture_entreprise_travaux',
      'travaux_list_permis_construire',
      'travaux_list_travaux_autorisation_pv_ag',
      'travaux_list_travaux_facture_doc'
    ]
  },
  id: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
  label: "Vente dans l'ancien",
  mynotaryTemplate: true,
  originTemplate: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
  specificTypes: ['IMMOBILIER', 'VENTE_ANCIEN'],
  type: 'OPERATION'
};
