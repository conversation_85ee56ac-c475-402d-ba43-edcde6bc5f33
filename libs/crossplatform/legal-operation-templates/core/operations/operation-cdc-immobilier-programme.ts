// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationCdcImmobilierProgramme: LegalOperationTemplate = {
  config: {
    labelPattern: '',
    tags: {
      OPERATION__CDC_HABITAT__PROGRAMME__REPRESENTANT_PROMOTEUR: {
        order: 0,
        max: 1,
        group: 0
      },
      OPERATION__CDC_HABITAT__PROGRAMME__NOTAIRE_PROGRAMME: {
        order: 1,
        max: 1,
        group: 0
      }
    },
    hiddenPages: {
      documents: true
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'BAILLEUR_SOCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'REPRESENTANT_BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'FICHE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'FICHE_VENTE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'COPROPRIETE'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 10
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'CLERC_NOTAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'GESTIONNAIRE_VENTE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'REFERENT_VENTE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [
      {
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 999
        },
        specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'VENTES']
      }
    ],
    subOperations: 'Ventes',
    synthesis: {
      tables: [
        {
          id: 'LOTS',
          title: 'Grille de lots',
          titleWithArticle: 'un lot',
          linkSpecificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'LOTS'],
          type: 'PRIMARY',
          columns: [
            {
              label: 'N° de lot',
              property: 'numero_commercialisation_lot.value',
              bold: true,
              responsive: 'TITLE'
            },
            {
              label: 'N° EDD',
              property: 'numero_lot.value',
              responsive: 'CONTENT'
            },
            {
              label: 'Adresse',
              property: 'adresse.value.formattedAddress',
              responsive: 'CONTENT'
            },
            {
              label: 'Type',
              property: 'programme_typologie.value',
              responsive: 'CONTENT'
            },
            {
              label: 'Prix Occupé',
              property: 'programme_prix_vente_occupe.value',
              format: 'PRICE',
              responsive: 'CONTENT'
            },
            {
              label: 'Prix Vacant',
              property: 'programme_prix_vente_libre.value',
              format: 'PRICE',
              responsive: 'CONTENT'
            },
            {
              label: 'Reservation',
              source: 'RESERVATION',
              property: 'status',
              responsive: 'CONTENT'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'INDIVIDUEL_HABITATION']
            },
            {
              path: 'links.OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'INDIVIDUEL_HORS_HABITATION']
            },
            {
              path: 'links.OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HABITATION']
            },
            {
              path: 'links.OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HORS_HABITATION']
            }
          ]
        },
        {
          id: 'COPROS',
          title: 'Copropriétés',
          titleWithArticle: 'une copropriété',
          type: 'SECONDARY',
          linkSpecificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'PROGRAMME', 'COPROPRIETE'],
          columns: [
            {
              label: 'Dénomination',
              bold: true,
              property: 'denomination.value',
              responsive: 'TITLE'
            },
            {
              label: 'Adresse',
              property: 'adresse.value.formattedAddress',
              responsive: 'TITLE'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*.links.COMPOSITION__COPROPRIETE.\\d*.link.branches.CONTENU.\\d*.to.\\d*',
              specificType: ['STRUCTURE', 'COPROPRIETE']
            }
          ]
        }
      ]
    },
    contractModels: [
      {
        id: 'CDC_IMMOBILIER_VENTE_PROMESSE_CDC_HABITAT_SOCIAL',
        label: 'CDCHS - Collectif'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_CDC_IMMOBILIER_VENTE_PROMESSE_CDC_HABITAT_SOCIAL_INDIVIDUEL',
        label: 'CDCHS - Individuel'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_PROMESSE_CDC_HABITAT_CONVENTIONNE',
        label: 'CDCH Conv - Collectif'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_CDC_IMMOBILIER_VENTE_PROMESSE_CDC_HABITAT_CONVENTIONNE_INDIVIDUEL',
        label: 'CDCH Conv - Individuel'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_PROMESSE_CDC_HABITAT_NON_CONVENTIONNE_COLLECTIF',
        label: 'CDCH Accord / Hors Accord 2005 - Collectif'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_PROMESSE_CDC_HABITAT_NON_CONVENTIONNE_PAVILLON',
        label: 'CDCH Hors Accord 2005 - Pavillon'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_INTEGRATION_CDC_HABITAT_SOCIAL',
        label: 'CDCHS. - Collectif'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_CDC_IMMOBILIER_VENTE_INTEGRATION_CDC_HABITAT_SOCIAL_INDIVIDUEL',
        label: 'CDCHS. - Individuel'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_INTEGRATION_CDC_HABITAT_CONVENTIONNE',
        label: 'CDCH. Conv - Collectif'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_CDC_IMMOBILIER_VENTE_INTEGRATION_CDC_HABITAT_CONVENTIONNE_INDIVIDUEL',
        label: 'CDCH. Conv - Individuel'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_INTEGRATION_CDC_HABITAT_NON_CONVENTIONNE_COLLECTIF',
        label: 'CDCH. Accord / Hors Accord 2005 - Collectif'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_INTEGRATION_CDC_HABITAT_NON_CONVENTIONNE_PAVILLON',
        label: 'CDCH. Hors Accord 2005 - Pavillon'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_PROMESSE_LIGHT',
        label: 'Promesse Light'
      },
      {
        id: 'CDC__IMMOBILIER__VENTE__PROCURATION',
        label: 'Procuration pour Acquérir'
      },
      {
        id: 'CDC_IMMOBILIER_VENTE_AVENANT_PROMESSE',
        label: 'Avenant - Promesse'
      },
      {
        id: 'CDC__IMMOBILIER__VENTE__ATTESTATION_DEBROUSSAILLEMENT',
        label: 'Attestation de Débroussaillement'
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'NOTAIRE'],
        match: {
          directLink: 'OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR',
          directRecords: ['PERSONNE__MORALE__NOTAIRE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__LOTS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      }
    ],
    isParentOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      'acquereur_social_information',
      'acquereur_social_proposition_achat',
      'acquereur_social_revenu',
      'appel_charge',
      'assainissement_non_collectif_controle_libre',
      'assurance_decennale_construction',
      'assurance_do_initial',
      'assurance_dommage_construction',
      'attestation_diagnostiqueur',
      'bailleur_pouvoir',
      'carnet_information_construction_document',
      'certificat_conformite_construction',
      'cheminee_facture_ramonage',
      'construction_particulier_assurance_dommage',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'copro_alur_attestation',
      'copro_infos_financieres_alur',
      'declaration_achevement_construction',
      'declaration_prealable_construction',
      'diagnostic_amiante',
      'diagnostic_electrique',
      'diagnostic_gaz',
      'diagnostic_merule',
      'diagnostic_parties_communes_amiante',
      'diagnostic_parties_communes_diagnostic_dtg',
      'diagnostic_parties_communes_plomb',
      'diagnostic_plomb',
      'diagnostic_termites',
      'document_pre_etat_date',
      'dpe',
      'dpe_audit_document',
      'etude_geotechnique',
      'facture_ramonage',
      'factures_entreprises_construction',
      'gestion_lotissement_gestion_lotissement_autre_statuts',
      'gestion_lotissement_gestion_lotissement_statuts_aful',
      'gestion_lotissement_gestion_lotissement_statuts_asl',
      'gestion_volume_gestion_volume_autre_statuts',
      'gestion_volume_gestion_volume_statuts_aful',
      'gestion_volume_gestion_volume_statuts_asl',
      'location_bail_liste_contrat_bail',
      'mandat_bail',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'permis_construire_construction',
      'piscine_note_securite_document',
      'piscine_securite_attestation_document',
      'programme_numero_porte',
      'programme_escalier',
      'programme_cdc_jardin_surface',
      'programme_cdc_balcon_surface',
      'programme_cdc_terrasse_surface',
      'pv_ag',
      'servitude_acte',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_autorisation_copropriete_travaux_global',
      'travaux_list_carnet_information_travaux_document',
      'travaux_list_certificat_conformite',
      'travaux_list_ctravaux_particulier_assurance_dommage',
      'travaux_list_declaration_achevement',
      'travaux_list_declaration_prealable',
      'travaux_list_dossier_urbanisme',
      'travaux_list_facture_entreprise_travaux',
      'travaux_list_permis_construire',
      'travaux_list_travaux_autorisation_pv_ag',
      'travaux_list_travaux_facture_doc',
      'cdc_edd_rcp',
      'cdc_pv_ag',
      'cdc_fiche_synthetique',
      'cdc_carnet_entretien'
    ]
  },
  id: 'OPERATION__CDC__IMMOBILIER__PROGRAMME',
  label: 'CDC Habitat Ancien - Programme',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__SOCIAL_PROGRAMME',
  specificTypes: ['CDC', 'IMMOBILIER', 'PROGRAMME'],
  type: 'OPERATION'
};
