// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationClesenceImmobilierVente: LegalOperationTemplate = {
  config: {
    labelPattern: '{{ numero_lot }} / {{ programme_nom }} /  {{ nom_acquereur }}',
    emailLabelPattern: '{{ biens_vendus }}',
    tags: {
      numero_lot: {
        link: 'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__LOTS',
        questionId: ['numero_lot', 'numero_commercialisation_lot'],
        max: 1,
        label: 'Numéro du lot'
      },
      programme_nom: {
        link: 'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__FICHE_PSLA',
        questionId: 'programme_nom',
        max: 1,
        label: 'Nom du programme'
      },
      nom_acquereur: {
        link: 'OPERATION__CLESENCE__IMMOBILIER__VENTE__ACQUEREUR',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__CLESENCE__IMMOBILIER__VENTE__LOTS',
        order: 0,
        max: 1
      },
      vendeurs: {
        link: 'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR',
        order: 1,
        max: 1
      },
      acquereurs: {
        link: 'OPERATION__CLESENCE__IMMOBILIER__VENTE__ACQUEREUR',
        order: 2,
        max: 1
      }
    },
    hiddenPages: {
      documents: true
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'CLESENCE', 'IMMOBILIER', 'PROGRAMME', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CLESENCE', 'IMMOBILIER', 'VENTE', 'ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CLESENCE', 'IMMOBILIER', 'VENTE', 'FICHE_PSLA'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CLESENCE', 'IMMOBILIER', 'PROGRAMME', 'SUBVENTION'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 50
        }
      },
      {
        specificTypes: ['OPERATION', 'CLESENCE', 'IMMOBILIER', 'VENTE', 'NOTAIRE_ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CLESENCE', 'IMMOBILIER', 'PROGRAMME', 'BAILLEUR_SOCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'CLESENCE', 'IMMOBILIER', 'PROGRAMME', 'REPRESENTANT_BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CLESENCE', 'IMMOBILIER', 'PROGRAMME', 'FICHE_PSLA'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'CLESENCE', 'IMMOBILIER', 'PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    linkOverrides: [
      'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__FICHE_PSLA',
      'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__BAILLEUR_SOCIAL',
      'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR',
      'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME'
    ],
    operationLinks: [
      {
        specificTypes: ['OPERATION', 'CLESENCE', 'IMMOBILIER', 'PROGRAMME', 'VENTES']
      }
    ],
    contracts: [
      {
        id: 'CLESENCE_IMMOBILIER_VENTE_CONTRAT_PRELIMINAIRE_PSLA',
        label: 'Contrat préliminaire - PSLA'
      },
      {
        id: 'CLESENCE_IMMOBILIER_VENTE_VENTE_HLM_ACHEVE',
        label: 'Compromis - Vente HLM'
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'NOTAIRE'],
        match: {
          directLink: 'OPERATION__CLESENCE__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR',
          directRecords: ['PERSONNE__MORALE__NOTAIRE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME__LOTS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      }
    ],
    isSubOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      'acquereur_social_information',
      'acquereur_social_proposition_achat',
      'acquereur_social_revenu',
      'appel_charge',
      'assainissement_non_collectif_controle_libre',
      'assurance_decennale_construction',
      'assurance_do_initial',
      'assurance_dommage_construction',
      'attestation_diagnostiqueur',
      'base_environnement',
      'carnet_information_construction_document',
      'certificat_conformite_construction',
      'cheminee_facture_ramonage',
      'construction_particulier_assurance_dommage',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'copro_alur_attestation',
      'copro_infos_financieres_alur',
      'declaration_achevement_construction',
      'declaration_prealable_construction',
      'diagnostic_amiante',
      'diagnostic_electrique',
      'diagnostic_gaz',
      'diagnostic_merule',
      'diagnostic_parties_communes_amiante',
      'diagnostic_parties_communes_diagnostic_dtg',
      'diagnostic_parties_communes_plomb',
      'diagnostic_plomb',
      'diagnostic_termites',
      'document_arpentage',
      'document_asl',
      'document_pre_etat_date',
      'document_securisation_clesence',
      'documents_alur_global',
      'dpe',
      'erp',
      'etude_geotechnique',
      'facture_ramonage',
      'factures_entreprises_construction',
      'gestion_lotissement_gestion_lotissement_autre_statuts',
      'gestion_lotissement_gestion_lotissement_statuts_aful',
      'gestion_lotissement_gestion_lotissement_statuts_asl',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'permis_construire_construction',
      'piscine_note_securite_document',
      'piscine_securite_attestation_document',
      'plan_cadastral',
      'plan_lot',
      'pv_ag',
      'servitude_acte',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_certificat_conformite',
      'travaux_list_declaration_achevement',
      'travaux_list_declaration_prealable',
      'travaux_list_permis_construire'
    ]
  },
  id: 'OPERATION__CLESENCE__IMMOBILIER__VENTE',
  label: 'Clésence - Vente Ancien',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__PSLA_PRELIMINAIRE',
  specificTypes: ['CLESENCE', 'IMMOBILIER', 'VENTE'],
  type: 'OPERATION'
};
