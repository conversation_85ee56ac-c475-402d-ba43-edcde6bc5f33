// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationProprietesPriveesViager: LegalOperationTemplate = {
  config: {
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    labelPattern: '{{ adresse }} / {{ nom_vendeur }} / {{ nom_acquereur }}',
    emailLabelPattern: '{{ adresse }}',
    tags: {
      adresse: {
        link: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO',
        questionId: 'adresse',
        format: 'ADDRESS',
        max: 1,
        label: 'Adresse'
      },
      nom_vendeur: {
        link: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom vendeur'
      },
      nom_acquereur: {
        link: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO',
        order: 0,
        max: 1
      },
      vendeurs: {
        link: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
        order: 1,
        max: 1
      },
      acquereurs: {
        link: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
        order: 2,
        max: 1
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VENDEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VISITEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'ACQUEREURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'REPRESENTANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'OFFRANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'ACQUEREUR_CESSIONNAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'BIENS_VENDUS_SANS_MONO'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_ANCIEN', 'FICHES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'AGENTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'MANDATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'AGENCE_DELEGATAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'NOTAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [],
    contracts: [
      {
        id: 'PROPRIETES_PRIVEES_VIAGER_MANDAT_VENTE_TERME',
        label: 'Mandat de vente - Vente à terme'
      },
      {
        id: 'PROPRIETES_PRIVEES_VIAGER_MANDAT_VENTE_VIAGER',
        label: 'Mandat de vente - Viager'
      },
      {
        id: 'PROPRIETES_PRIVEES__TRANSACTION__MANDAT_DE_VENTE_NUE_PROPRIETE',
        label: 'Mandat de vente - Nue-Propriété'
      },
      {
        id: 'PROPRIETES_PRIVEES_VIAGER_AVENANT_MANDAT',
        label: 'Avenant au Mandat'
      },
      {
        id: 'PROPRIETES_PRIVEES_VIAGER_BON_VISITE',
        label: 'Bon de Visite'
      },
      {
        id: 'PROPRIETES_PRIVEES_VIAGER_LIA_TERME',
        label: "Lettre intention d'achat - Vente à Terme"
      },
      {
        id: 'PROPRIETES_PRIVEES_VIAGER_LIA_VIAGER',
        label: "Lettre intention d'achat - Vente Viager"
      },
      {
        id: 'PROPRIETES_PRIVEES_IMMOBILIER_FICHE_RENSEIGNEMENT_ACQUEREUR',
        label: 'Fiche Bénéficiaire Effectif'
      },
      {
        id: 'PROPRIETES_PRIVEES_IMMOBILIER_CONSTITUTION_DOSSIER',
        label: 'Fiche de synthèse'
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'ACQUEREUR'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
          indirectLinks: ['SITUATION_MARITALE__MARIAGE', 'SITUATION_MARITALE__DIVORCE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'NOTAIRE'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__NOTAIRES',
          directRecords: ['PERSONNE__MORALE__NOTAIRE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'BIEN'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO',
          directRecords: [
            'BIEN__INDIVIDUEL_HABITATION',
            'BIEN__INDIVIDUEL_HORS_HABITATION',
            'BIEN__TERRAIN_CONSTRUCTIBLE',
            'BIEN__TERRAIN_NON_CONSTRUCTIBLE',
            'BIEN__MONOPROPRIETE_HABITATION',
            'BIEN__MONOPROPRIETE_HORS_HABITATION',
            'BIEN__LOT_HABITATION',
            'BIEN__LOT_HORS_HABITATION'
          ]
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'LOTISSEMENT'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO',
          indirectLinks: ['COMPOSITION__LOTISSEMENT__LOTISSEMENT'],
          indirectRecords: ['STRUCTURE__LOTISSEMENT']
        }
      },
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'VENDEUR'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
          directRecords: ['PERSONNE__PHYSIQUE'],
          indirectLinks: [
            'CAPACITE__TUTELLE',
            'CAPACITE__MINORITE',
            'CAPACITE__MANDAT_PROTECTION_FUTURE',
            'CAPACITE__HABILITATION_FAMILIALE'
          ]
        }
      }
    ],
    hiddenPages: {
      documents: true
    },
    documentsToExclude: [],
    documentsToInclude: [
      'appel_charge',
      'assainissement_non_collectif_controle_libre',
      'assurance_decennale_construction',
      'assurance_dommage_construction',
      'attestation_diagnostiqueur',
      'bareme_pp_viager',
      'certificat_conformite_construction',
      'chaudiere_entretien_attestation',
      'cheminee_facture_ramonage',
      'conge_locataire',
      'conge_vente',
      'construction_particulier_assurance_dommage',
      'contrat_attaches_list_contrat_attaches_document',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'copro_alur_attestation',
      'copro_infos_financieres_alur',
      'courrier_renonciation_preemption_locataire',
      'cuve_fioul_contrat_document',
      'declaration_achevement_construction',
      'declaration_prealable_construction',
      'diagnostic_amiante',
      'diagnostic_amiante_libre',
      'diagnostic_electrique',
      'diagnostic_electrique_libre',
      'diagnostic_gaz',
      'diagnostic_gaz_libre',
      'diagnostic_merule',
      'diagnostic_merule_libre',
      'diagnostic_parties_communes_amiante',
      'diagnostic_parties_communes_diagnostic_dtg',
      'diagnostic_parties_communes_plomb',
      'diagnostic_plomb',
      'diagnostic_plomb_libre',
      'diagnostic_termites',
      'diagnostic_termites_libre',
      'dip_immoreseau',
      'dip_immoreseau_easy',
      'dip_pp',
      'dip_pp_happy',
      'dip_pp_ultra_vip',
      'dip_pp_vip',
      'dip_pp_vip_confiance',
      'dip_rezoximo',
      'dip_rezoximo_liberty',
      'dpe',
      'dpe_libre',
      'engagement_conjoint',
      'external_georisque',
      'factures_entreprises_construction',
      'formulaire_retractation_pp',
      'gestion_lotissement_gestion_lotissement_autre_statuts',
      'gestion_lotissement_gestion_lotissement_statuts_aful',
      'gestion_lotissement_gestion_lotissement_statuts_asl',
      'gestion_volume_gestion_volume_autre_statuts',
      'gestion_volume_gestion_volume_statuts_aful',
      'gestion_volume_gestion_volume_statuts_asl',
      'location_bail_liste_contrat_bail',
      'location_bail_liste_etat_des_lieux_document',
      'location_bail_liste_quittances_loyer',
      'mandat_bail',
      'mandat_retractation_document',
      'ordonnance_autorisation_document',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'panneau_voltaique_contrat',
      'permis_construire_construction',
      'pv_ag',
      'servitude_acte',
      'taxe_fonciere',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_autorisation_copropriete_travaux_global',
      'travaux_list_carnet_information_travaux_document',
      'travaux_list_certificat_conformite',
      'travaux_list_ctravaux_particulier_assurance_dommage',
      'travaux_list_declaration_achevement',
      'travaux_list_declaration_prealable',
      'travaux_list_dossier_urbanisme',
      'travaux_list_facture_entreprise_travaux',
      'travaux_list_permis_construire',
      'travaux_list_travaux_autorisation_pv_ag',
      'travaux_list_travaux_facture_doc'
    ]
  },
  id: 'OPERATION__PROPRIETES_PRIVEES__VIAGER',
  label: 'Dossier de Vente - Propriétés Privées Viager',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__VENTE_VIAGER',
  specificTypes: ['PROPRIETES_PRIVEES', 'VIAGER'],
  type: 'OPERATION'
};
