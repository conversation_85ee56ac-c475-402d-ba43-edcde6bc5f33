// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationSdAccessImmobilierVente: LegalOperationTemplate = {
  config: {
    labelPattern: '{{ numero_lot }} / {{ programme_nom }} /  {{ nom_acquereur }}',
    emailLabelPattern: '{{ biens_vendus }}',
    tags: {
      numero_lot: {
        link: 'OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__LOTS',
        questionId: ['numero_lot', 'numero_commercialisation_lot'],
        max: 1,
        label: 'Numéro du lot'
      },
      programme_nom: {
        link: 'OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__FICHE_BRS',
        questionId: 'programme_nom',
        max: 1,
        label: 'Nom du programme'
      },
      nom_acquereur: {
        link: 'OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__LOTS',
        order: 0,
        max: 1
      },
      acquereurs: {
        link: 'OPERATION__SD_ACCESS__IMMOBILIER__VENTE__ACQUEREUR',
        order: 2,
        max: 1
      }
    },
    hiddenPages: {
      documents: true
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'SD_ACCESS', 'IMMOBILIER', 'PROGRAMME', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'SD_ACCESS', 'IMMOBILIER', 'VENTE', 'ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'SD_ACCESS', 'IMMOBILIER', 'VENTE', 'FICHE_BRS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'SD_ACCESS', 'IMMOBILIER', 'VENTE', 'NOTAIRE_ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'SD_ACCESS', 'IMMOBILIER', 'PROGRAMME', 'FICHE_BRS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'SD_ACCESS', 'IMMOBILIER', 'PROGRAMME', 'BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'SD_ACCESS', 'IMMOBILIER', 'PROGRAMME', 'REPRESENTANT_BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'SD_ACCESS', 'IMMOBILIER', 'PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    linkOverrides: [
      'OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__FICHE_BRS',
      'OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__BAILLEUR',
      'OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR',
      'OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME'
    ],
    operationLinks: [
      {
        specificTypes: ['OPERATION', 'SD_ACCESS', 'IMMOBILIER', 'PROGRAMME', 'VENTES']
      }
    ],
    contracts: [
      {
        id: 'SD_ACCESS_IMMOBILIER_VENTE_CONTRAT_PRELIMINAIRE_BRS',
        label: 'Contrat Préliminaire BRS'
      }
    ],
    isSubOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      'acquereur_social_information',
      'acquereur_social_proposition_achat',
      'acquereur_social_revenu',
      'bailleur_agrement_pref',
      'garantie_revente',
      'plan_masse',
      'plan_stationnement',
      'programme_brs_bail_projet',
      'programme_brs_garantie_complementaire',
      'programme_brs_notice_provisoire',
      'renonciation_financement_sdaccess_pas_pret',
      'renonciation_financement_sdaccess_pret',
      'programme_brs_note_condition_eligibilite'
    ]
  },
  id: 'OPERATION__SD_ACCESS__IMMOBILIER__VENTE',
  label: 'SD Access - Vente',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__VENTE_NEUF',
  specificTypes: ['SD_ACCESS', 'IMMOBILIER', 'VENTE'],
  type: 'OPERATION'
};
