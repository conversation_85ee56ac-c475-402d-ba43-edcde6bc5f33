// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationWhitebirdGeneralLocation: LegalOperationTemplate = {
  config: {
    emailLabelPattern: '{{ numero_lot }} / {{ nom_locataire }}',
    hiddenPages: {
      documents: true
    },
    labelPattern: '{{ numero_lot }} / {{ nom_locataire }}',
    tags: {
      numero_lot: {
        link: 'OPERATION__IMMOBILIER__LOCATION_PROGRAMME__BIENS_LOUES',
        questionId: ['numero_lot', 'numero_commercialisation_lot'],
        max: 1,
        label: 'Numéro du lot'
      },
      nom_locataire: {
        link: 'OPERATION__IMMOBILIER__LOCATION_CONTRAT__LOCATAIRES',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom Locataire'
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_PROGRAMME', 'BIENS_LOUES'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_CONTRAT', 'LOCATAIRES'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_CONTRAT', 'GARANTS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_CONTRAT', 'CONDITION_LOCATION'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 10
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_PROGRAMME', 'BAILLEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_PROGRAMME', 'REPRESENTANT_BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_PROGRAMME', 'ADMINISTRATEUR_BIEN'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_PROGRAMME', 'MANDATAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_PROGRAMME', 'FICHE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_PROGRAMME', 'COPROPRIETE'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 10
        }
      }
    ],
    linkOverrides: [
      'OPERATION__IMMOBILIER__LOCATION_PROGRAMME__BAILLEURS',
      'OPERATION__IMMOBILIER__LOCATION_PROGRAMME__REPRESENTANT_BAILLEUR',
      'OPERATION__IMMOBILIER__LOCATION_PROGRAMME__ADMINISTRATEUR_BIEN',
      'OPERATION__IMMOBILIER__LOCATION_PROGRAMME__MANDATAIRE',
      'OPERATION__IMMOBILIER__LOCATION_PROGRAMME__FICHE_PROGRAMME',
      'OPERATION__IMMOBILIER__LOCATION_PROGRAMME__COPROPRIETE'
    ],
    operationLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_PROGRAMME', 'LOCATION_WHITEBIRD']
      }
    ],
    contracts: [
      {
        id: 'WHITEBIRD_GENERAL_LOCATION_BAIL_HABITATION',
        label: 'Bail Habitation'
      }
    ],
    recordExtensions: [],
    isSubOperation: true,
    documentsToExclude: [],
    documentsToInclude: ['dpe', 'dpe_audit_document', 'dpe_libre']
  },
  id: 'OPERATION__WHITEBIRD__GENERAL_LOCATION',
  label: 'Dossier de Location Programme - Whitebird',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__VENTE_NEUF',
  specificTypes: ['WHITEBIRD', 'GENERAL_LOCATION'],
  type: 'OPERATION'
};
