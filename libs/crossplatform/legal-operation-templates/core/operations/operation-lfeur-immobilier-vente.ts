// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationLfeurImmobilierVente: LegalOperationTemplate = {
  config: {
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    labelPattern: '{{ numero_lot }} / {{ programme_nom }} /  {{ nom_acquereur }}',
    emailLabelPattern: '{{ biens_vendus }}',
    tags: {
      numero_lot: {
        link: 'OPERATION__LFEUR__IMMOBILIER__PROGRAMME__LOTS',
        questionId: ['numero_lot', 'numero_commercialisation_lot'],
        max: 1,
        label: 'Numéro du lot'
      },
      programme_nom: {
        link: 'OPERATION__LFEUR__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME',
        questionId: 'programme_nom',
        max: 1,
        label: 'Nom du programme'
      },
      nom_acquereur: {
        link: 'OPERATION__LFEUR__IMMOBILIER__VENTE__ACQUEREUR',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__LFEUR__IMMOBILIER__VENTE__LOTS',
        order: 0,
        max: 1
      },
      commercialisateurs: {
        link: 'OPERATION__LFEUR__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR',
        order: 3,
        max: 5,
        group: 0
      },
      acquereurs: {
        link: 'OPERATION__LFEUR__IMMOBILIER__VENTE__ACQUEREUR',
        order: 2,
        max: 1
      }
    },
    hiddenPages: {
      documents: true
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'LFEUR', 'IMMOBILIER', 'PROGRAMME', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'LFEUR', 'IMMOBILIER', 'VENTE', 'ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'LFEUR', 'IMMOBILIER', 'VENTE', 'FICHE_VENTE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'LFEUR', 'IMMOBILIER', 'VENTE', 'NOTAIRE_ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'LFEUR', 'IMMOBILIER', 'PROGRAMME', 'BAILLEUR_SOCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'LFEUR', 'IMMOBILIER', 'PROGRAMME', 'REPRESENTANT_BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'LFEUR', 'IMMOBILIER', 'PROGRAMME', 'FICHE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'LFEUR', 'IMMOBILIER', 'PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    linkOverrides: [
      'OPERATION__LFEUR__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME',
      'OPERATION__LFEUR__IMMOBILIER__PROGRAMME__BAILLEUR_SOCIAL',
      'OPERATION__LFEUR__IMMOBILIER__PROGRAMME__REPRESENTANT_BAILLEUR',
      'OPERATION__LFEUR__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME'
    ],
    operationLinks: [
      {
        specificTypes: ['OPERATION', 'LFEUR', 'IMMOBILIER', 'PROGRAMME', 'VENTES']
      }
    ],
    contracts: [
      {
        id: 'LFEUR_IMMOBILIER_VENTE_COMPROMIS_ACHEVE_LFEUR',
        label: 'Compromis de vente'
      },
      {
        id: 'LFEUR__IMMOBILIER__VENTE__AVENANT',
        label: 'Avenant au compromis'
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'NOTAIRE'],
        match: {
          directLink: 'OPERATION__LFEUR__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR',
          directRecords: ['PERSONNE__MORALE__NOTAIRE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__LFEUR__IMMOBILIER__PROGRAMME__LOTS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      }
    ],
    isSubOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      'acquereur_social_information',
      'acquereur_social_proposition_achat',
      'acquereur_social_revenu',
      'appel_charge',
      'assainissement_non_collectif_controle_libre',
      'assurance_decennale_construction',
      'assurance_do_initial',
      'assurance_dommage_construction',
      'assurance_revente',
      'attestation_diagnostiqueur',
      'attestation_vacance',
      'bail_conge_ancien_locataire',
      'carnet_information_construction_document',
      'certificat_conformite_construction',
      'cheminee_facture_ramonage',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'copro_alur_attestation',
      'copro_infos_financieres_alur',
      'declaration_prealable_construction',
      'diagnostic_amiante',
      'diagnostic_electrique',
      'diagnostic_gaz',
      'diagnostic_merule',
      'diagnostic_parties_communes_amiante',
      'diagnostic_parties_communes_diagnostic_dtg',
      'diagnostic_parties_communes_plomb',
      'diagnostic_plomb',
      'diagnostic_termites',
      'document_pre_etat_date',
      'dpe',
      'dpe_audit_document',
      'etude_geotechnique',
      'facture_ramonage',
      'gestion_lotissement_gestion_lotissement_autre_statuts',
      'gestion_lotissement_gestion_lotissement_statuts_aful',
      'gestion_lotissement_gestion_lotissement_statuts_asl',
      'gestion_volume_gestion_volume_autre_statuts',
      'gestion_volume_gestion_volume_statuts_aful',
      'gestion_volume_gestion_volume_statuts_asl',
      'lfeur_accord_ddtm_doc',
      'lfeur_attestation_habitabilite',
      'lfeur_demande_alienation_doc',
      'lfeur_echelle_doc',
      'lfeur_programme_cavite',
      'lfeur_programme_commune_accord',
      'lfeur_programme_deliberation_ca',
      'lfeur_programme_radon',
      'lfeur_programme_sociale_autorisation_prefet',
      'lfeur_programme_sociale_demande_prefet',
      'lfeur_rgpd',
      'location_bail_liste_contrat_bail',
      'mandat_bail',
      'occupation_social_contrat_bail_quittance',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'permis_construire_construction',
      'piscine_note_securite_document',
      'piscine_securite_attestation_document',
      'pv_ag',
      'renonciation_financement_reservation_lfeur',
      'servitude_acte',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_autorisation_copropriete_travaux_global',
      'travaux_list_carnet_information_travaux_document',
      'travaux_list_certificat_conformite',
      'travaux_list_ctravaux_particulier_assurance_dommage',
      'travaux_list_declaration_achevement',
      'travaux_list_declaration_prealable',
      'travaux_list_dossier_urbanisme',
      'travaux_list_facture_entreprise_travaux',
      'travaux_list_permis_construire',
      'travaux_list_travaux_autorisation_pv_ag',
      'travaux_list_travaux_facture_doc'
    ]
  },
  id: 'OPERATION__LFEUR__IMMOBILIER__VENTE',
  label: 'Logement Familial de l Eure - Vente',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__SOCIAL_VENTE',
  specificTypes: ['LFEUR', 'IMMOBILIER', 'VENTE'],
  type: 'OPERATION'
};
