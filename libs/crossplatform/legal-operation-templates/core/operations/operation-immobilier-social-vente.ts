// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationImmobilierSocialVente: LegalOperationTemplate = {
  config: {
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    labelPattern: '{{ numero_lot }} / {{ programme_nom }} /  {{ nom_acquereur }}',
    emailLabelPattern: '{{ biens_vendus }}',
    tags: {
      numero_lot: {
        link: 'OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__LOTS',
        questionId: ['numero_lot', 'numero_commercialisation_lot'],
        max: 1,
        label: 'Numéro du lot'
      },
      programme_nom: {
        link: 'OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__FICHE_PROGRAMME',
        questionId: 'programme_nom',
        max: 1,
        label: 'Nom du programme'
      },
      nom_acquereur: {
        link: 'OPERATION__IMMOBILIER__VENTE_SOCIAL__ACQUEREUR',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__LOTS',
        order: 0,
        max: 1
      },
      commercialisateurs: {
        link: 'OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__REPRESENTANT_PROMOTEUR',
        order: 3,
        max: 5,
        group: 0
      },
      acquereurs: {
        link: 'OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__ACQUEREUR',
        order: 2,
        max: 1
      }
    },
    hiddenPages: {
      documents: true
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'PROGRAMME_SOCIAL', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_SOCIAL', 'ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_SOCIAL', 'FICHE_CONTRAT'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_SOCIAL', 'NOTAIRE_ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'PROGRAMME_SOCIAL', 'PROMOTEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'PROGRAMME_SOCIAL', 'REPRESENTANT_PROMOTEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'COOPERATIVE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'PROGRAMME_SOCIAL', 'FICHE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'PROGRAMME_SOCIAL', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    linkOverrides: [
      'OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__FICHE_PROGRAMME',
      'OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__PROMOTEUR',
      'OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__REPRESENTANT_PROMOTEUR',
      'OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__NOTAIRE_PROGRAMME',
      'OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__COPROPRIETE',
      'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__COOPERATIVE'
    ],
    operationLinks: [
      {
        specificTypes: ['OPERATION', 'IMMOBILIER', 'PROGRAMME_SOCIAL', 'VENTES']
      }
    ],
    contracts: [
      {
        id: 'IMMOBILIER_SOCIAL_VENTE_VENTE_SOCIAL_ACHEVE',
        label: 'Vente sociale achevé'
      },
      {
        id: 'IMMOBILIER_SOCIAL_VENTE_AVENANT_COMPROMIS',
        label: 'Avenant au compromis'
      },
      {
        id: 'IMMOBILIER_SOCIAL_VENTE_BON_SOUSCRIPTION',
        label: 'Bulletin de souscription'
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'NOTAIRE'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE_SOCIAL__NOTAIRE_ACQUEREUR',
          directRecords: ['PERSONNE__MORALE__NOTAIRE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__LOTS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      }
    ],
    isSubOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      'acquereur_social_information',
      'acquereur_social_proposition_achat',
      'acquereur_social_revenu',
      'appel_charge',
      'assainissement_non_collectif_controle_libre',
      'assurance_decennale_construction',
      'assurance_do_initial',
      'assurance_dommage_construction',
      'attestation_diagnostiqueur',
      'bailleur_pouvoir',
      'carnet_information_construction_document',
      'certificat_conformite_construction',
      'cheminee_facture_ramonage',
      'conformite_piscine',
      'construction_particulier_assurance_dommage',
      'contrat_bail',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'copro_alur_attestation',
      'copro_infos_financieres_alur',
      'declaration_achevement_construction',
      'declaration_prealable_construction',
      'diagnostic_amiante',
      'diagnostic_electrique',
      'diagnostic_gaz',
      'diagnostic_merule',
      'diagnostic_parties_communes_amiante',
      'diagnostic_parties_communes_diagnostic_dtg',
      'diagnostic_parties_communes_plomb',
      'diagnostic_plomb',
      'diagnostic_termites',
      'document_pre_etat_date',
      'dpe',
      'dpe_audit_document',
      'etude_geotechnique',
      'facture_ramonage',
      'factures_entreprises_construction',
      'gestion_lotissement_gestion_lotissement_autre_statuts',
      'gestion_lotissement_gestion_lotissement_statuts_aful',
      'gestion_lotissement_gestion_lotissement_statuts_asl',
      'gestion_volume_gestion_volume_autre_statuts',
      'gestion_volume_gestion_volume_statuts_aful',
      'gestion_volume_gestion_volume_statuts_asl',
      'location_bail_liste_contrat_bail',
      'mandat_bail',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'permis_construire_construction',
      'piscine_note_securite_document',
      'piscine_securite_attestation_document',
      'pv_ag',
      'servitude_acte',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_autorisation_copropriete_travaux_global',
      'travaux_list_carnet_information_travaux_document',
      'travaux_list_certificat_conformite',
      'travaux_list_ctravaux_particulier_assurance_dommage',
      'travaux_list_declaration_achevement',
      'travaux_list_declaration_prealable',
      'travaux_list_dossier_urbanisme',
      'travaux_list_facture_entreprise_travaux',
      'travaux_list_permis_construire',
      'travaux_list_travaux_autorisation_pv_ag',
      'travaux_list_travaux_facture_doc'
    ]
  },
  id: 'OPERATION__IMMOBILIER__SOCIAL_VENTE',
  label: 'Vente Social Achevé',
  mynotaryTemplate: true,
  originTemplate: 'OPERATION__IMMOBILIER__SOCIAL_VENTE',
  specificTypes: ['IMMOBILIER', 'SOCIAL_VENTE'],
  type: 'OPERATION'
};
