// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationAquitanisReservationBrs: LegalOperationTemplate = {
  config: {
    hiddenPages: {
      documents: true
    },
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    labelPattern: '{{ numero_commercialisation_lot }} / {{ programme_nom }} /  {{ nom_acquereur }}',
    emailLabelPattern: '{{ biens_vendus }}',
    tags: {
      numero_lot: {
        link: 'OPERATION__AQUITANIS__PROGRAMME__LOTS',
        questionId: ['numero_lot', 'numero_commercialisation_lot'],
        max: 1,
        label: 'Numéro du lot'
      },
      programme_nom: {
        link: 'OPERATION__AQUITANIS__PROGRAMME__FICHE_PROGRAMME',
        questionId: 'programme_nom',
        max: 1,
        label: 'Nom du programme'
      },
      nom_acquereur: {
        link: 'OPERATION__AQUITANIS__VENTE__ACQUEREUR',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__AQUITANIS__PROGRAMME__LOTS',
        order: 0,
        max: 1
      },
      bailleurs: {
        link: 'OPERATION__AQUITANIS__PROGRAMME__BAILLEUR_SOCIAL',
        order: 1,
        max: 1
      },
      acquereurs: {
        link: 'OPERATION__AQUITANIS__VENTE__ACQUEREUR',
        order: 2,
        max: 1
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'VENTE', 'ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'VENTE', 'FICHE_VENTE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'VENTE', 'NOTAIRE_ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'BAILLEUR_SOCIAL'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'REPRESENTANT_BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'FICHE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    linkOverrides: [
      'OPERATION__AQUITANIS__PROGRAMME__FICHE_PROGRAMME',
      'OPERATION__AQUITANIS__PROGRAMME__BAILLEUR_SOCIAL',
      'OPERATION__AQUITANIS__PROGRAMME__REPRESENTANT_BAILLEUR',
      'OPERATION__AQUITANIS__PROGRAMME__NOTAIRE_PROGRAMME'
    ],
    operationLinks: [
      {
        specificTypes: ['OPERATION', 'AQUITANIS', 'PROGRAMME', 'VENTES_BRS']
      }
    ],
    contracts: [
      {
        id: 'AQUITANIS__BRS__RESERVATION_BRS',
        label: 'Contrat de Réservation - Bail Réel Solidaire'
      },
      {
        id: 'AQUITANIS__TRANSACTION__ATTESTATION_ALUR',
        label: 'Attestation ALUR'
      },
      {
        id: 'AQUITANIS__BRS__CONVENTION_HONORAIRES',
        label: "Convention d'honoraires"
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'ANCIEN', 'NOTAIRE'],
        match: {
          directLink: 'OPERATION__AQUITANIS__VENTE__NOTAIRE_ACQUEREUR',
          directRecords: ['PERSONNE__MORALE__NOTAIRE']
        }
      },
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__AQUITANIS__PROGRAMME__LOTS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      }
    ],
    isSubOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      'fiscal_n_2',
      'notice_descriptive',
      'plan_exposition_bruit_libre',
      'programme_plan',
      'programme_aquitanis_demande_agrement_modele',
      'programme_aquitanis_pieces_agrement_modele',
      'detecteur_fumee_attestation',
      'servitude_acte',
      'document_pre_etat_date'
    ]
  },
  id: 'OPERATION__AQUITANIS__RESERVATION_BRS',
  label: 'Aquitanis - BRS Réservation',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__BRS_VENTE_HYBRIDE',
  specificTypes: ['AQUITANIS', 'RESERVATION_BRS'],
  type: 'OPERATION'
};
