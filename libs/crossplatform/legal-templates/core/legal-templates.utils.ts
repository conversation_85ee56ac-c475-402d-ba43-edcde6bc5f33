import { LegalTemplateId } from './legal-templates';
import { LegalRecordTemplateId } from './legal-record-template-id';
import { LegalLinkTemplateId } from './legal-link-template-id';
import { startsWith } from 'lodash';
import { LegalOperationTemplateId } from './legal-operation-template-id';

export const generateLegalTemplateId = (args: { specificTypes: string[]; type: string }): LegalTemplateId => {
  return `${args.type}__${args.specificTypes.join('__')}` as LegalTemplateId;
};

export const generateLegalRecordTemplateId = (specificTypes: string[]): LegalRecordTemplateId => {
  return generateLegalTemplateId({ specificTypes, type: 'RECORD' }) as LegalRecordTemplateId;
};

export const generateLegalLinkTemplateId = (specificTypes: string[]): LegalLinkTemplateId => {
  return generateLegalTemplateId({ specificTypes, type: 'LINK' }) as LegalLinkTemplateId;
};

export const generateLegalOperationTemplateId = (specificTypes: string[]): LegalOperationTemplateId => {
  return generateLegalTemplateId({ specificTypes, type: 'OPERATION' }) as LegalOperationTemplateId;
};

export const generateSpecificTypes = (id: LegalTemplateId | string): string[] => {
  return id.split('__').slice(1);
};

export const isLegalRecordTemplateId = (id?: LegalTemplateId | string): id is LegalRecordTemplateId => {
  return startsWith(id, 'RECORD__');
};
