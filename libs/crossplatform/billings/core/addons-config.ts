import { PlanType, QuantityType, PriceType, AddonCategory, <PERSON>Level } from './plans';
import { keys, reduce } from 'lodash';
import { getPlanConfig } from './plans-config';

export enum AddonType {
  BIBLIOTHEQUE_CONTRAT = 'BIBLIOTHEQUE_CONTRAT',
  CLUB_MYNOTARY = 'CLUB_MYNOTARY',
  COMPLETION_INFO_AUTOMATIQUE_VIA_API = 'COMPLETION_INFO_AUTOMATIQUE_VIA_API',
  CONFIGURATION_ROLES = 'CONFIGURATION_ROLES',
  CONFIGURATION_VALIDATION_CONTRAT = 'CONFIGURATION_VALIDATION_CONTRAT',
  CONFIGURATION_VUES_SUIVIS = 'CONFIGURATION_VUES_SUIVIS',
  CONSOMMABLE_ERP = 'CONSOMMABLE_ERP',
  CONSOMMABLE_PRE_ETAT_DATE = 'CONSOMMABLE_PRE_ETAT_DATE',
  CONSOMMABLE_RECOMMANDE_ELECTRONIQUE = 'CONSOMMABLE_RECOMMANDE_ELECTRONIQUE',
  CONSOMMABLE_SIGNATURE_ELECTRONIQUE = 'CONSOMMABLE_SIGNATURE_ELECTRONIQUE',
  CONSOMMABLE_SIGNATURE_ELECTRONIQUE_AVANCEE = 'CONSOMMABLE_SIGNATURE_ELECTRONIQUE_AVANCEE',
  CONSOMMABLE_SIGNATURE_ELECTRONIQUE_FREEMIUM = 'CONSOMMABLE_SIGNATURE_ELECTRONIQUE_FREEMIUM',
  ESPACE_ACQUEREUR = 'ESPACE_ACQUEREUR',
  FACTURE_HONORAIRES = 'FACTURE_HONORAIRES',
  FORMATION_ALUR = 'FORMATION_ALUR',
  FORMATION_PERSONNALISEE = 'FORMATION_PERSONNALISEE',
  IMPORT_FICHES_CSV = 'IMPORT_FICHES_CSV',
  INTERCONNECTION = 'INTERCONNECTION',
  INTERCONNECTION_PERSONNALISEE = 'INTERCONNECTION_PERSONNALISEE',
  INVITATION_DOSSIER = 'INVITATION_DOSSIER',
  PACK_ERP = 'PACK_ERP',
  PACK_PRE_ETAT_DATE = 'PACK_PRE_ETAT_DATE',
  PACK_RECOMMANDE_ELECTRONIQUE = 'PACK_RECOMMANDE_ELECTRONIQUE',
  PACK_SIGNATURE_AVANCEE = 'PACK_SIGNATURE_AVANCEE',
  PACK_SIGNATURE_SIMPLE = 'PACK_SIGNATURE_SIMPLE',
  PARTAGE_DOSSIER = 'PARTAGE_DOSSIER',
  PARTAGE_FICHE = 'PARTAGE_FICHE',
  PERSONNALISATION_APPLICATION = 'PERSONNALISATION_APPLICATION',
  PERSONNALISATION_CONTRAT = 'PERSONNALISATION_CONTRAT',
  PERSONNALISATION_EMAIL = 'PERSONNALISATION_EMAIL',
  REGISTRE_GESTION = 'REGISTRE_GESTION',
  REGISTRE_REPERTOIRE = 'REGISTRE_REPERTOIRE',
  REGISTRE_TRANSACTION = 'REGISTRE_TRANSACTION',
  REGISTRE_TRANSACTION_SMS = 'REGISTRE_TRANSACTION_SMS',
  SUPPORT_TCHAT = 'SUPPORT_TCHAT',
  TABLEAU_DE_BORD = 'TABLEAU_DE_BORD',
  TELECHARGMENT_DES_CONTRATS_ET_DOCS = 'TELECHARGMENT_DES_CONTRATS_ET_DOCS',
  UTILISATEUR_ADDITIONNEL_BUSINESS = 'UTILISATEUR_ADDITIONNEL_BUSINESS',
  UTILISATEUR_ADDITIONNEL_CCMI = 'UTILISATEUR_ADDITIONNEL_CCMI',
  UTILISATEUR_ADDITIONNEL_FILIALE = 'UTILISATEUR_ADDITIONNEL_FILIALE',
  UTILISATEUR_ADDITIONNEL_FREEMIUM = 'UTILISATEUR_ADDITIONNEL_FREEMIUM',
  UTILISATEUR_ADDITIONNEL_NOTAIRE = 'UTILISATEUR_ADDITIONNEL_NOTAIRE',
  UTILISATEUR_ADDITIONNEL_PREMIUM = 'UTILISATEUR_ADDITIONNEL_PREMIUM',
  WEBINAIRE_FORMATION = 'WEBINAIRE_FORMATION'
}

export type AddonPrice =
  | { id: AddonType; price: number; priceType: PriceType.UNIT }
  | { id: AddonType; priceLevels: PriceLevel[]; priceType: PriceType.LEVELS }
  | { id: AddonType; priceLevels: PriceLevel[]; priceType: PriceType.RANGE };

type AddonConfig = {
  category?: AddonCategory;
  description?: string;
  id: AddonType;
  label: string;
  price?: AddonPrice;
  quantityType: QuantityType;
};

const addonsConfig: Record<AddonType, AddonConfig> = {
  [AddonType.BIBLIOTHEQUE_CONTRAT]: {
    category: AddonCategory.JURIDIQUE,
    description:
      'Accès aux 350 trames pour tous les types de dossiers (transaction, location, gestion, vente dans le neuf, PSLA, CCMI, viager, opération de syndic, recrutement d’agent etc …)',
    id: AddonType.BIBLIOTHEQUE_CONTRAT,
    label: 'Bibliothèque complète de contrats',
    quantityType: QuantityType.USER
  },
  [AddonType.CLUB_MYNOTARY]: {
    category: AddonCategory.ADMINISTRATION,
    description: 'Accès à une communauté intéractive de gérants d’agences immobilières et à du contenu de qualité',
    id: AddonType.CLUB_MYNOTARY,
    label: 'Club MyNotary',
    quantityType: QuantityType.USER
  },
  [AddonType.COMPLETION_INFO_AUTOMATIQUE_VIA_API]: {
    category: AddonCategory.AUTOMATISATION,
    description:
      'Références cadastrales saisies automatiquement grâce à l’adresse du bien, informations d’une personne morale récupérées grâce au SIREN',
    id: AddonType.COMPLETION_INFO_AUTOMATIQUE_VIA_API,
    label: 'Récupération automatique des informations (SIREN, références cadastrales ...)',
    quantityType: QuantityType.USER
  },
  [AddonType.CONFIGURATION_VUES_SUIVIS]: {
    category: AddonCategory.ADMINISTRATION,
    description: 'Vues filtrées pour simplifier la navigation sur l’outil',
    id: AddonType.CONFIGURATION_VUES_SUIVIS,
    label: 'Paramétrage des vues par défaut',
    quantityType: QuantityType.USER
  },
  [AddonType.ESPACE_ACQUEREUR]: {
    category: AddonCategory.COLLABORATION,
    description:
      "Espace en ligne dédié au partage d’informations avec le potentiel acquéreur. Invitez le sur un espace en ligne pour qu'il bénéficie d'informations facilitant sa prise de décision.",
    id: AddonType.ESPACE_ACQUEREUR,
    label: 'Espace acquéreurs',
    quantityType: QuantityType.USER
  },
  [AddonType.TELECHARGMENT_DES_CONTRATS_ET_DOCS]: {
    category: AddonCategory.ADMINISTRATION,
    description: 'Possibilité de télécharger tous les contrats et documents',
    id: AddonType.TELECHARGMENT_DES_CONTRATS_ET_DOCS,
    label: 'Export des données',
    quantityType: QuantityType.USER
  },
  [AddonType.FORMATION_ALUR]: {
    category: AddonCategory.FORMATION,
    description: 'Formations ALUR illimitées (e-learning)',
    id: AddonType.FORMATION_ALUR,
    label: 'Accès illimité à MyNotary Académie (formations ALUR en e-learning)',
    quantityType: QuantityType.USER
  },
  [AddonType.FORMATION_PERSONNALISEE]: {
    category: AddonCategory.FORMATION,
    description: 'Conseiller client dédié pour vous accompagner au quotidien',
    id: AddonType.FORMATION_PERSONNALISEE,
    label: 'Formation personnalisée à l’outil MyNotary (visioconférence)',
    quantityType: QuantityType.USER
  },
  [AddonType.IMPORT_FICHES_CSV]: {
    category: AddonCategory.ADMINISTRATION,
    description: 'Importez votre fichier de contacts et de biens pour éviter de les saisir de nouveau sur MyNotary',
    id: AddonType.IMPORT_FICHES_CSV,
    label: 'Import de CSV (annuaire personnes & biens)',
    quantityType: QuantityType.USER
  },
  [AddonType.INTERCONNECTION_PERSONNALISEE]: {
    category: AddonCategory.INTEGRATION,
    description:
      'Votre outil métier ne fait pas partie des outils connectés à MyNotary ? Nous créons une interconnexion sur mesure',
    id: AddonType.INTERCONNECTION_PERSONNALISEE,
    label: 'Interconnexion sur-mesure via API (coût pour la mise en place)',
    quantityType: QuantityType.USER
  },
  [AddonType.SUPPORT_TCHAT]: {
    category: AddonCategory.SUPPORT,
    description:
      'Assistance technique opérée par nos conseillers clients, en moins de 5 min du lundi au samedi compris',
    id: AddonType.SUPPORT_TCHAT,
    label: 'Support en direct',
    price: { id: AddonType.SUPPORT_TCHAT, price: 5, priceType: PriceType.UNIT },
    quantityType: QuantityType.USER
  },
  [AddonType.TABLEAU_DE_BORD]: {
    category: AddonCategory.ADMINISTRATION,
    description:
      'Analysez les performances de votre agence et étudiez les données sur vos clients grâce à de nombreux graphiques (honoraires, démographie, prix de vente...)',
    id: AddonType.TABLEAU_DE_BORD,
    label: 'Tableau de bord',
    price: {
      id: AddonType.TABLEAU_DE_BORD,
      priceLevels: [
        {
          end: 50,
          price: 0.9,
          start: 1
        },
        {
          end: 100,
          price: 0.5,
          start: 51
        },
        {
          end: 500,
          price: 0.25,
          start: 101
        },
        {
          end: 501,
          price: 0.1,
          start: 10000
        }
      ],
      priceType: PriceType.LEVELS
    },
    quantityType: QuantityType.USER
  },
  [AddonType.WEBINAIRE_FORMATION]: {
    category: AddonCategory.FORMATION,
    description: 'Visioconférence intéractive hebdomadaire avec une session de questions/réponses',
    id: AddonType.WEBINAIRE_FORMATION,
    label: 'Webinaire de formation à l’outil MyNotary',
    quantityType: QuantityType.USER
  },
  [AddonType.CONFIGURATION_ROLES]: {
    category: AddonCategory.ADMINISTRATION,
    description: 'Configurez les droits et accès de tous vos collaborateurs, par type d’actions, dossiers et contrats.',
    id: AddonType.CONFIGURATION_ROLES,
    label: 'Paramétrage des rôles',
    price: { id: AddonType.CONFIGURATION_ROLES, price: 2, priceType: PriceType.UNIT },
    quantityType: QuantityType.USER
  },
  [AddonType.CONFIGURATION_VALIDATION_CONTRAT]: {
    category: AddonCategory.ADMINISTRATION,
    description: 'Configurez par défaut les personnes qui pourront valider les contrats de vos collaborateurs.',
    id: AddonType.CONFIGURATION_VALIDATION_CONTRAT,
    label: 'Optimisation validation compromis',
    quantityType: QuantityType.USER
  },
  [AddonType.CONSOMMABLE_RECOMMANDE_ELECTRONIQUE]: {
    id: AddonType.CONSOMMABLE_RECOMMANDE_ELECTRONIQUE,
    label: 'Crédit recommandé électronique',
    quantityType: QuantityType.USER
  },
  [AddonType.CONSOMMABLE_PRE_ETAT_DATE]: {
    id: AddonType.CONSOMMABLE_PRE_ETAT_DATE,
    label: 'Crédit Pré-état daté',
    quantityType: QuantityType.USER
  },
  [AddonType.CONSOMMABLE_ERP]: {
    id: AddonType.CONSOMMABLE_ERP,
    label: 'Crédit ERP',
    quantityType: QuantityType.USER
  },
  [AddonType.CONSOMMABLE_SIGNATURE_ELECTRONIQUE]: {
    id: AddonType.CONSOMMABLE_SIGNATURE_ELECTRONIQUE,
    label: 'Crédit signature électronique',
    quantityType: QuantityType.USER
  },
  [AddonType.CONSOMMABLE_SIGNATURE_ELECTRONIQUE_AVANCEE]: {
    id: AddonType.CONSOMMABLE_SIGNATURE_ELECTRONIQUE_AVANCEE,
    label: 'Crédit signature électronique avancée',
    quantityType: QuantityType.USER
  },
  [AddonType.CONSOMMABLE_SIGNATURE_ELECTRONIQUE_FREEMIUM]: {
    id: AddonType.CONSOMMABLE_SIGNATURE_ELECTRONIQUE_FREEMIUM,
    label: 'Crédit signature électronique',
    quantityType: QuantityType.USER
  },
  [AddonType.INTERCONNECTION]: {
    category: AddonCategory.INTEGRATION,
    description: 'Connexion avec votre outil métier pour éviter la double saisie',
    id: AddonType.INTERCONNECTION,
    label: 'Interconnexion native',
    price: { id: AddonType.INTERCONNECTION, price: 5, priceType: PriceType.UNIT },
    quantityType: QuantityType.USER
  },
  [AddonType.INVITATION_DOSSIER]: {
    category: AddonCategory.COLLABORATION,
    description: 'Faites intervenir vos collaborateurs ou le notaire en charge du dossier pour accélérer la procédure.',
    id: AddonType.INVITATION_DOSSIER,
    label: 'Ajouter des intervenants externes',
    price: { id: AddonType.INVITATION_DOSSIER, price: 1, priceType: PriceType.UNIT },
    quantityType: QuantityType.USER
  },
  [AddonType.PACK_PRE_ETAT_DATE]: {
    id: AddonType.PACK_PRE_ETAT_DATE,
    label: 'Pack de pré-états datés',
    quantityType: QuantityType.USER
  },
  [AddonType.PACK_RECOMMANDE_ELECTRONIQUE]: {
    id: AddonType.PACK_RECOMMANDE_ELECTRONIQUE,
    label: 'Pack de recommandés électroniques',
    quantityType: QuantityType.USER
  },
  [AddonType.PACK_SIGNATURE_AVANCEE]: {
    id: AddonType.PACK_SIGNATURE_AVANCEE,
    label: 'Pack de signatures électroniques avancées',
    quantityType: QuantityType.USER
  },
  [AddonType.PACK_SIGNATURE_SIMPLE]: {
    id: AddonType.PACK_SIGNATURE_SIMPLE,
    label: 'Pack de signatures électroniques simples',
    quantityType: QuantityType.USER
  },
  [AddonType.PACK_ERP]: {
    id: AddonType.PACK_ERP,
    label: 'Pack de ERP',
    quantityType: QuantityType.USER
  },
  [AddonType.PARTAGE_DOSSIER]: {
    category: AddonCategory.COLLABORATION,
    description:
      'Partagez dossier et documents sans limite de taille et recevez une notification lors du téléchargement.',
    id: AddonType.PARTAGE_DOSSIER,
    label: 'Partage des dossiers et des documents',
    quantityType: QuantityType.USER
  },
  [AddonType.PARTAGE_FICHE]: {
    category: AddonCategory.COLLABORATION,
    description:
      'Faites compléter les informations par les personnes concernées pour éviter les allers-retours inutiles.',
    id: AddonType.PARTAGE_FICHE,
    label: 'Partage des fiches',
    price: { id: AddonType.PARTAGE_FICHE, price: 1, priceType: PriceType.UNIT },
    quantityType: QuantityType.USER
  },
  [AddonType.PERSONNALISATION_APPLICATION]: {
    category: AddonCategory.ADMINISTRATION,
    description: 'La plateforme MyNotary à votre image (logo et couleurs)',
    id: AddonType.PERSONNALISATION_APPLICATION,
    label: 'Personnalisation de la plateforme',
    price: { id: AddonType.PERSONNALISATION_APPLICATION, price: 5, priceType: PriceType.UNIT },
    quantityType: QuantityType.USER
  },
  [AddonType.PERSONNALISATION_CONTRAT]: {
    category: AddonCategory.ADMINISTRATION,
    description: 'Tous les contrats rédigés depuis MyNotary respectent votre charte graphique (logo et couleurs).',
    id: AddonType.PERSONNALISATION_CONTRAT,
    label: 'Personnalisation des contrats',
    price: { id: AddonType.PERSONNALISATION_CONTRAT, price: 5, priceType: PriceType.UNIT },
    quantityType: QuantityType.USER
  },
  [AddonType.PERSONNALISATION_EMAIL]: {
    category: AddonCategory.ADMINISTRATION,
    description:
      'Tous les emails envoyés depuis MyNotary à vos interlocuteurs respectent votre charte graphique (logo et couleurs)',
    id: AddonType.PERSONNALISATION_EMAIL,
    label: 'Personnalisation des emails',
    price: { id: AddonType.PERSONNALISATION_EMAIL, price: 2, priceType: PriceType.UNIT },
    quantityType: QuantityType.USER
  },
  [AddonType.REGISTRE_GESTION]: {
    category: AddonCategory.JURIDIQUE,
    description:
      'Votre registre des mandats de gestion est tenu automatiquement sur MyNotary. Prenez votre numéro en un clic pendant la rédaction de votre mandat.',
    id: AddonType.REGISTRE_GESTION,
    label: 'Registre des mandats - Gestion',
    price: {
      id: AddonType.REGISTRE_GESTION,
      priceLevels: [
        {
          end: 50,
          price: 1,
          start: 1
        },
        {
          end: 5000,
          price: 0.5,
          start: 51
        }
      ],
      priceType: PriceType.RANGE
    },
    quantityType: QuantityType.USER
  },
  [AddonType.FACTURE_HONORAIRES]: {
    category: AddonCategory.ADMINISTRATION,
    description: 'Générez, centralisez et envoyez en quelques clics toutes vos factures',
    id: AddonType.FACTURE_HONORAIRES,
    label: 'Factures d’honoraires',
    price: {
      id: AddonType.FACTURE_HONORAIRES,
      priceLevels: [
        {
          end: 20,
          price: 1,
          start: 1
        },
        {
          end: 40,
          price: 0.5,
          start: 21
        },
        {
          end: 10000,
          price: 0.25,
          start: 41
        }
      ],
      priceType: PriceType.LEVELS
    },
    quantityType: QuantityType.USER
  },
  [AddonType.REGISTRE_REPERTOIRE]: {
    category: AddonCategory.JURIDIQUE,
    description: 'Vous avez un compte séquestre ? Votre registre répertoire se tient automatiquement sur MyNotary.',
    id: AddonType.REGISTRE_REPERTOIRE,
    label: 'Registre répertoire',
    price: {
      id: AddonType.REGISTRE_REPERTOIRE,
      priceLevels: [
        {
          end: 50,
          price: 1,
          start: 1
        },
        {
          end: 5000,
          price: 0.5,
          start: 51
        }
      ],
      priceType: PriceType.RANGE
    },
    quantityType: QuantityType.USER
  },
  [AddonType.REGISTRE_TRANSACTION]: {
    category: AddonCategory.JURIDIQUE,
    description:
      'Votre registre des mandats de transaction est tenu automatiquement sur MyNotary. Prenez votre numéro en un clic pendant la rédaction de votre mandat.',
    id: AddonType.REGISTRE_TRANSACTION,
    label: 'Registre des mandats - Transaction',
    price: {
      id: AddonType.REGISTRE_TRANSACTION,
      priceLevels: [
        {
          end: 50,
          price: 1,
          start: 1
        },
        {
          end: 5000,
          price: 0.5,
          start: 51
        }
      ],
      priceType: PriceType.RANGE
    },
    quantityType: QuantityType.USER
  },
  [AddonType.REGISTRE_TRANSACTION_SMS]: {
    category: AddonCategory.JURIDIQUE,
    description:
      'Prenez vos numéros de mandat en envoyant un sms. Très pratique si vous rédigez vos mandats en papier.',
    id: AddonType.REGISTRE_TRANSACTION_SMS,
    label: 'Registre des mandats - Option prise de numéro par SMS',
    price: {
      id: AddonType.REGISTRE_TRANSACTION_SMS,
      priceLevels: [
        {
          end: 50,
          price: 0.5,
          start: 1
        },
        {
          end: 5000,
          price: 0.25,
          start: 51
        }
      ],
      priceType: PriceType.RANGE
    },
    quantityType: QuantityType.USER
  },
  [AddonType.UTILISATEUR_ADDITIONNEL_BUSINESS]: {
    id: AddonType.UTILISATEUR_ADDITIONNEL_BUSINESS,
    label: 'BUSINESS - Utilisateur(s) Supplémentaire(s)',
    price: {
      id: AddonType.UTILISATEUR_ADDITIONNEL_BUSINESS,
      priceLevels: [
        {
          end: 16,
          price: 12,
          start: 1
        },
        {
          end: 36,
          price: 8,
          start: 17
        },
        {
          end: 5000,
          price: 4,
          start: 37
        }
      ],
      priceType: PriceType.LEVELS
    },
    quantityType: QuantityType.LICENCE
  },
  [AddonType.UTILISATEUR_ADDITIONNEL_CCMI]: {
    id: AddonType.UTILISATEUR_ADDITIONNEL_CCMI,
    label: 'CCMI - Utilisateur(s) Supplémentaire(s)',
    price: { id: AddonType.UTILISATEUR_ADDITIONNEL_CCMI, price: 10, priceType: PriceType.UNIT },
    quantityType: QuantityType.LICENCE
  },
  [AddonType.UTILISATEUR_ADDITIONNEL_FILIALE]: {
    id: AddonType.UTILISATEUR_ADDITIONNEL_FILIALE,
    label: 'FILIALE - Utilisateur(s) Supplémentaire(s)',
    price: {
      id: AddonType.UTILISATEUR_ADDITIONNEL_FILIALE,
      price: 0,
      priceType: PriceType.UNIT
    },
    quantityType: QuantityType.LICENCE
  },
  [AddonType.UTILISATEUR_ADDITIONNEL_FREEMIUM]: {
    id: AddonType.UTILISATEUR_ADDITIONNEL_FREEMIUM,
    label: 'FREEMIUM - Utilisateur(s) Supplémentaire(s)',
    price: {
      id: AddonType.UTILISATEUR_ADDITIONNEL_FREEMIUM,
      price: 0,
      priceType: PriceType.UNIT
    },
    quantityType: QuantityType.LICENCE
  },
  [AddonType.UTILISATEUR_ADDITIONNEL_NOTAIRE]: {
    id: AddonType.UTILISATEUR_ADDITIONNEL_NOTAIRE,
    label: 'NOTAIRE - Utilisateur(s) Supplémentaire(s)',
    price: {
      id: AddonType.UTILISATEUR_ADDITIONNEL_NOTAIRE,
      price: 0,
      priceType: PriceType.UNIT
    },
    quantityType: QuantityType.LICENCE
  },
  [AddonType.UTILISATEUR_ADDITIONNEL_PREMIUM]: {
    id: AddonType.UTILISATEUR_ADDITIONNEL_PREMIUM,
    label: 'PREMIUM - Utilisateur(s) supplémentaire(s)',
    price: {
      id: AddonType.UTILISATEUR_ADDITIONNEL_PREMIUM,
      priceLevels: [
        {
          end: 16,
          price: 16,
          start: 1
        },
        {
          end: 36,
          price: 12,
          start: 17
        },
        {
          end: 5000,
          price: 8,
          start: 37
        }
      ],
      priceType: PriceType.LEVELS
    },
    quantityType: QuantityType.LICENCE
  }
};

export const getAddonQuantity = ({
  licenceCount,
  planType,
  type
}: {
  licenceCount: number;
  planType: PlanType;
  type: AddonType;
}) => {
  const defaultLicences = getPlanConfig(planType).defaultLicenceCount;
  switch (getAddonConfig(type).quantityType) {
    case QuantityType.USER:
      return Math.max(licenceCount, defaultLicences);
    case QuantityType.LICENCE:
      return licenceCount - defaultLicences;
  }
};

export function getAddonConfig(addonType: AddonType) {
  return addonsConfig[addonType];
}

export function getAddonTypeList(): AddonType[] {
  return keys(addonsConfig) as AddonType[];
}

export const getAddonCost = ({
  addonPlanInfo,
  licenceCount,
  planType
}: {
  addonPlanInfo?: AddonPrice;
  licenceCount: number;
  planType: PlanType;
}): number => {
  if (addonPlanInfo == null) {
    return 0;
  }
  const addonQuantity = getAddonQuantity({
    licenceCount,
    planType,
    type: addonPlanInfo.id
  });

  if (addonPlanInfo.priceType === PriceType.UNIT) {
    return addonPlanInfo.price * addonQuantity;
  }

  if (addonPlanInfo.priceType === PriceType.LEVELS) {
    return reduce(
      addonPlanInfo.priceLevels,
      (total, level) => {
        if (addonQuantity > level.end) {
          total += (level.end - (level.start - 1)) * level.price;
        } else if (addonQuantity >= level.start) {
          total += (addonQuantity - (level.start - 1)) * level.price;
        }
        return total;
      },
      0
    );
  }

  if (addonPlanInfo.priceType === PriceType.RANGE) {
    const price =
      addonPlanInfo.priceLevels.find((priceLevel) => priceLevel.start <= licenceCount && priceLevel.end >= licenceCount)
        ?.price || 0;

    return price * licenceCount;
  }

  return 0;
};
