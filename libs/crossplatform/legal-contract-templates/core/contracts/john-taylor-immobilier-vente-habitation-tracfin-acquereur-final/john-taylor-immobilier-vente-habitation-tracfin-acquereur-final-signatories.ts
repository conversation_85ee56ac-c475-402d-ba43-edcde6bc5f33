import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const mandatairesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES'
  });
  const manager_agencesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANAGER_AGENCE'
  });
  const declarant_tracfinsSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__DECLARANT_TRACFIN'
  });
  const correspondant_tracfinsSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__CORRESPONDANT_TRACFIN'
  });

  return [
    mandatairesSignatories,
    manager_agencesSignatories,
    declarant_tracfinsSignatories,
    correspondant_tracfinsSignatories
  ].flat();
}

export const JohnTaylorImmobilierVenteHabitationTracfinAcquereurFinalSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
