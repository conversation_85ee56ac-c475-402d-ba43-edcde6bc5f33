// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const GiboireImmobilierVenteMandatGiboire: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES: {
        branches: {
          CONDITIONS_GENERALES: {
            constraints: {
              min: 1
            }
          },
          MANDAT: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__VENDEURS: {
        branches: {
          VENDEUR: {
            constraints: {
              min: 1
            },
            recordLinks: ['SITUATION_MARITALE', 'CAPACITE', 'PROCURATION', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO: {
        branches: {
          BIEN_VENDU: {
            constraints: {
              min: 1
            },
            recordLinks: [
              'COMPOSITION__COPROPRIETE',
              'COMPOSITION__LOTISSEMENT',
              'COMPOSITION__ENSEMBLE_IMMOBILIER',
              'COMPOSITION__LOT_ANNEXE'
            ]
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__AGENTS: {
        branches: {
          AGENT_IMMOBILIER: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
        branches: {
          MANDATAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: [
        {
          recordPath: ['OPERATION__IMMOBILIER__VENTE__VENDEURS', 'BRANCHES', 'VENDEUR', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__VENDEURS',
            'BRANCHES',
            'VENDEUR',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__VENDEURS',
            'BRANCHES',
            'VENDEUR',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__VENDEURS',
            'BRANCHES',
            'VENDEUR',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__VENDEURS',
            'BRANCHES',
            'VENDEUR',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__VENDEURS',
            'BRANCHES',
            'VENDEUR',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        }
      ]
    },
    register: {
      TRANSACTION: {
        mandants_registre: [
          {
            path: [
              'OPERATION__IMMOBILIER__VENTE__VENDEURS',
              'BRANCHES',
              'VENDEUR',
              'TYPES',
              'PERSONNE__PHYSIQUE',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'sexe'
              },
              {
                type: 'CONSTANT',
                value: ' '
              },
              {
                type: 'QUESTION_ID',
                value: 'nom'
              },
              {
                type: 'CONSTANT',
                value: ' '
              },
              {
                type: 'QUESTION_ID',
                value: 'prenoms'
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__VENTE__VENDEURS',
              'BRANCHES',
              'VENDEUR',
              'TYPES',
              'PERSONNE__MORALE',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'personne_morale_forme_sociale'
              },
              {
                type: 'CONSTANT',
                value: ', '
              },
              {
                type: 'QUESTION_ID',
                value: 'personne_morale_denomination'
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'personne_morale_adresse'
              }
            ]
          }
        ],
        biens_registre: [
          {
            path: [
              'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO',
              'BRANCHES',
              'BIEN_VENDU',
              'TYPES',
              'BIEN__INDIVIDUEL_HABITATION',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'nature_bien',
                withPrefix: 'Typologie : '
              },
              {
                type: 'QUESTION_ID',
                value: 'nature_bien_autre',
                withPrefix: ' : '
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO',
              'BRANCHES',
              'BIEN_VENDU',
              'TYPES',
              'BIEN__INDIVIDUEL_HORS_HABITATION',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'nature_bien',
                withPrefix: 'Typologie : '
              },
              {
                type: 'QUESTION_ID',
                value: 'nature_bien_autre',
                withPrefix: ' : '
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO',
              'BRANCHES',
              'BIEN_VENDU',
              'TYPES',
              'BIEN__LOT_HABITATION',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'nature_bien',
                withPrefix: 'Typologie : '
              },
              {
                type: 'QUESTION_ID',
                value: 'nature_bien_autre',
                withPrefix: ' : '
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO',
              'BRANCHES',
              'BIEN_VENDU',
              'TYPES',
              'BIEN__LOT_HORS_HABITATION',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'nature_bien',
                withPrefix: 'Typologie : '
              },
              {
                type: 'QUESTION_ID',
                value: 'nature_bien_autre',
                withPrefix: ' : '
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO',
              'BRANCHES',
              'BIEN_VENDU',
              'TYPES',
              'BIEN__MONOPROPRIETE_HABITATION',
              'RECORDS'
            ],
            items: [
              {
                type: 'CONSTANT',
                value: 'Lot n°'
              },
              {
                type: 'QUESTION_ID',
                value: 'numero_logement'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO',
              'BRANCHES',
              'BIEN_VENDU',
              'TYPES',
              'BIEN__MONOPROPRIETE_HORS_HABITATION',
              'RECORDS'
            ],
            items: [
              {
                type: 'CONSTANT',
                value: 'Lot n°'
              },
              {
                type: 'QUESTION_ID',
                value: 'numero_logement'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO',
              'BRANCHES',
              'BIEN_VENDU',
              'TYPES',
              'BIEN__TERRAIN_CONSTRUCTIBLE',
              'RECORDS'
            ],
            items: [
              {
                type: 'CONSTANT',
                value: 'Terrain - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO',
              'BRANCHES',
              'BIEN_VENDU',
              'TYPES',
              'BIEN__TERRAIN_NON_CONSTRUCTIBLE',
              'RECORDS'
            ],
            items: [
              {
                type: 'CONSTANT',
                value: 'Terrain non constructible - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO',
              'BRANCHES',
              'BIEN_VENDU',
              'LINKS',
              'COMPOSITION_LOT_ANNEXE_LOT_ANNEXE',
              'BRANCHES',
              'LOT_ANNEXE',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'nature_bien',
                withPrefix: 'Typologie : '
              },
              {
                type: 'QUESTION_ID',
                value: 'nature_bien_autre',
                withPrefix: ' : '
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          }
        ],
        objet_registre: [
          {
            path: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'BRANCHES', 'MANDAT', 'RECORDS'],
            items: [
              {
                type: 'CONSTANT',
                value: 'Mandat de vente '
              },
              {
                type: 'QUESTION_ID',
                value: 'mandat_type'
              }
            ]
          }
        ]
      }
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__IMMOBILIER__VENTE__VENDEURS: {
          branches: {
            VENDEUR: true
          }
        },
        OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
          branches: {
            MANDATAIRE: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'GIBOIRE_IMMOBILIER_VENTE_MANDAT_GIBOIRE',
  jeffersonPath: 'mynotary/giboire/immobilier/vente/mandatGiboire.json',
  label: 'Mandat Giboire',
  mainContract: false,
  originTemplate: null
};
