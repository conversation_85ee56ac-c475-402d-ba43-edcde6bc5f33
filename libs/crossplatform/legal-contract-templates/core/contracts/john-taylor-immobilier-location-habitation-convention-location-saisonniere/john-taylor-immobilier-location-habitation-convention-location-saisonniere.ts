// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const JohnTaylorImmobilierLocationHabitationConventionLocationSaisonniere: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__LOCATION__FICHES: {
        branches: {
          LOCATION: {
            constraints: {
              min: 1,
              max: 1
            }
          },
          MANDAT: {
            constraints: {
              min: 1,
              max: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__LOCATAIRES: {
        branches: {
          LOCATAIRE: {
            constraints: {
              min: 1
            },
            recordLinks: ['SITUATION_MARITALE', 'CAPACITE', 'PROCURATION', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES: {
        branches: {
          BIEN_LOUE: {
            constraints: {
              min: 1
            },
            recordLinks: ['COMPOSITION__COPROPRIETE', 'COMPOSITION__ENSEMBLE_IMMOBILIER']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__AGENTS: {
        branches: {
          AGENT_IMMOBILIER: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANAGER_AGENCE: {
        branches: {
          MANAGER: {
            constraints: {
              min: 1
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: [
        {
          recordPath: ['OPERATION__IMMOBILIER__LOCATION__BAILLEURS', 'BRANCHES', 'BAILLEUR', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_statuts', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'SITUATION_MARITALE_DIVORCE',
            'RECORD'
          ],
          documentIds: ['jugement_divorce', 'convention_de_divorce']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'SITUATION_MARITALE_MARIAGE',
            'RECORD'
          ],
          documentIds: ['livret_famille', 'contrat_mariage_upload', 'acte_changement_regime']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
            'BRANCHES',
            'BAILLEUR',
            'LINKS',
            'SITUATION_MARITALE_PACS',
            'RECORD'
          ],
          documentIds: ['contrat_pacs']
        },
        {
          recordPath: ['OPERATION__IMMOBILIER__LOCATION__LOCATAIRES', 'BRANCHES', 'LOCATAIRE', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_statuts', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_DIVORCE',
            'RECORD'
          ],
          documentIds: ['jugement_divorce', 'convention_de_divorce']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_MARIAGE',
            'RECORD'
          ],
          documentIds: ['livret_famille', 'contrat_mariage_upload', 'acte_changement_regime']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_PACS',
            'RECORD'
          ],
          documentIds: ['contrat_pacs']
        }
      ]
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__IMMOBILIER__VENTE__MANAGER_AGENCE: {
          branches: {
            MANAGER: true
          }
        },
        OPERATION__IMMOBILIER__LOCATION__LOCATAIRES: {
          branches: {
            LOCATAIRE: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_CONVENTION_LOCATION_SAISONNIERE',
  jeffersonPath: 'mynotary/johnTaylor/immobilier/locationHabitation/conventionLocationSaisonniere.json',
  label: 'Convention de location meublée saisonnière',
  mainContract: false,
  originTemplate: null
};
