import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const manager_agencesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANAGER_AGENCE'
  });
  const locatairesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION__LOCATAIRES'
  });

  return [manager_agencesSignatories, locatairesSignatories].flat();
}

export const JohnTaylorImmobilierLocationHabitationConventionLocationSaisonniereSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
