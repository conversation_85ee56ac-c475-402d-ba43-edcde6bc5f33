// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const KellerWilliamsLocationCommercialMandatCessionBailKeller: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BIENS_LOUES_COMMERCIAL: {
        branches: {
          BIEN_LOUE: {
            constraints: {
              min: 1
            },
            recordLinks: ['COMPOSITION__COPROPRIETE', 'COMPOSITION__ENSEMBLE_IMMOBILIER']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BAIL_COMMERCIAL: {
        branches: {
          MANDAT: {
            constraints: {
              min: 1,
              max: 1
            }
          },
          BAIL: {
            constraints: {
              min: 1,
              max: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__LOCATAIRES: {
        branches: {
          LOCATAIRE: {
            constraints: {
              min: 1
            },
            recordLinks: ['SITUATION_MARITALE', 'CAPACITE', 'PROCURATION', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__AGENTS: {
        branches: {
          AGENT_IMMOBILIER: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
        branches: {
          MANDATAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: [
        {
          recordPath: ['OPERATION__IMMOBILIER__LOCATION__LOCATAIRES', 'BRANCHES', 'LOCATAIRE', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_statuts', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_DIVORCE',
            'RECORD'
          ],
          documentIds: ['jugement_divorce', 'convention_de_divorce']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_MARIAGE',
            'RECORD'
          ],
          documentIds: ['livret_famille', 'contrat_mariage_upload', 'acte_changement_regime']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_PACS',
            'RECORD'
          ],
          documentIds: ['contrat_pacs']
        },
        {
          recordPath: ['OPERATION__IMMOBILIER__LOCATION__LOCATAIRES', 'BRANCHES', 'LOCATAIRE', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_statuts', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_DIVORCE',
            'RECORD'
          ],
          documentIds: ['jugement_divorce', 'convention_de_divorce']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_MARIAGE',
            'RECORD'
          ],
          documentIds: ['livret_famille', 'contrat_mariage_upload', 'acte_changement_regime']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_PACS',
            'RECORD'
          ],
          documentIds: ['contrat_pacs']
        }
      ]
    },
    register: {
      TRANSACTION: {
        mandants_registre: [
          {
            path: [
              'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
              'BRANCHES',
              'BAILLEUR',
              'TYPES',
              'PERSONNE__PHYSIQUE',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'sexe'
              },
              {
                type: 'CONSTANT',
                value: ' '
              },
              {
                type: 'QUESTION_ID',
                value: 'nom'
              },
              {
                type: 'CONSTANT',
                value: ' '
              },
              {
                type: 'QUESTION_ID',
                value: 'prenoms'
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__LOCATION__BAILLEURS',
              'BRANCHES',
              'BAILLEUR',
              'TYPES',
              'PERSONNE__MORALE',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'personne_morale_forme_sociale'
              },
              {
                type: 'CONSTANT',
                value: ', '
              },
              {
                type: 'QUESTION_ID',
                value: 'personne_morale_denomination'
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'personne_morale_adresse'
              }
            ]
          }
        ],
        biens_registre: [
          {
            path: [
              'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BIENS_LOUES_COMMERCIAL',
              'BRANCHES',
              'BIEN_LOUE',
              'TYPES',
              'BIEN__INDIVIDUEL_HABITATION',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'nature_bien',
                withPrefix: 'Typologie : '
              },
              {
                type: 'QUESTION_ID',
                value: 'nature_bien_autre',
                withPrefix: ' : '
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BIENS_LOUES_COMMERCIAL',
              'BRANCHES',
              'BIEN_LOUE',
              'TYPES',
              'BIEN__INDIVIDUEL_HORS_HABITATION',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'nature_bien',
                withPrefix: 'Typologie : '
              },
              {
                type: 'QUESTION_ID',
                value: 'nature_bien_autre',
                withPrefix: ' : '
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BIENS_LOUES_COMMERCIAL',
              'BRANCHES',
              'BIEN_LOUE',
              'TYPES',
              'BIEN__LOT_HABITATION',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'nature_bien',
                withPrefix: 'Typologie : '
              },
              {
                type: 'QUESTION_ID',
                value: 'nature_bien_autre',
                withPrefix: ' : '
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BIENS_LOUES_COMMERCIAL',
              'BRANCHES',
              'BIEN_LOUE',
              'TYPES',
              'BIEN__LOT_HORS_HABITATION',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'nature_bien',
                withPrefix: 'Typologie : '
              },
              {
                type: 'QUESTION_ID',
                value: 'nature_bien_autre',
                withPrefix: ' : '
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BIENS_LOUES_COMMERCIAL',
              'BRANCHES',
              'BIEN_LOUE',
              'TYPES',
              'BIEN__MONOPROPRIETE_HABITATION',
              'RECORDS'
            ],
            items: [
              {
                type: 'CONSTANT',
                value: 'Lot n°'
              },
              {
                type: 'QUESTION_ID',
                value: 'numero_logement'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BIENS_LOUES_COMMERCIAL',
              'BRANCHES',
              'BIEN_LOUE',
              'TYPES',
              'BIEN__MONOPROPRIETE_HORS_HABITATION',
              'RECORDS'
            ],
            items: [
              {
                type: 'CONSTANT',
                value: 'Lot n°'
              },
              {
                type: 'QUESTION_ID',
                value: 'numero_logement'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BIENS_LOUES_COMMERCIAL',
              'BRANCHES',
              'BIEN_LOUE',
              'TYPES',
              'BIEN__TERRAIN_CONSTRUCTIBLE',
              'RECORDS'
            ],
            items: [
              {
                type: 'CONSTANT',
                value: 'Terrain - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BIENS_LOUES_COMMERCIAL',
              'BRANCHES',
              'BIEN_LOUE',
              'TYPES',
              'BIEN__TERRAIN_NON_CONSTRUCTIBLE',
              'RECORDS'
            ],
            items: [
              {
                type: 'CONSTANT',
                value: 'Terrain non constructible - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BIENS_LOUES_COMMERCIAL',
              'BRANCHES',
              'BIEN_LOUE',
              'LINKS',
              'COMPOSITION_LOT_ANNEXE_LOT_ANNEXE',
              'BRANCHES',
              'LOT_ANNEXE',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'nature_bien',
                withPrefix: 'Typologie : '
              },
              {
                type: 'QUESTION_ID',
                value: 'nature_bien_autre',
                withPrefix: ' : '
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          }
        ],
        objet_registre: [
          {
            path: ['OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BAIL_COMMERCIAL', 'BRANCHES', 'MANDAT', 'RECORDS'],
            items: [
              {
                type: 'CONSTANT',
                value: 'Mandat de cession de Bail '
              },
              {
                type: 'QUESTION_ID',
                value: 'mandat_type'
              }
            ]
          }
        ]
      }
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__IMMOBILIER__LOCATION__LOCATAIRES: {
          branches: {
            LOCATAIRE: true
          }
        },
        OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
          branches: {
            MANDATAIRE: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'KELLER_WILLIAMS_LOCATION_COMMERCIAL_MANDAT_CESSION_BAIL_KELLER',
  jeffersonPath: 'mynotary/kellerWilliams/locationCommercial/mandatCessionBailKeller.json',
  label: 'Mandat - Cession de bail',
  mainContract: false,
  originTemplate: 'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_MANDAT_CESSION_BAIL'
};
