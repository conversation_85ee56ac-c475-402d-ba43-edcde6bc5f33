// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RasquainImmobilierLocationBailHabitation: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__LOCATION__FICHES: {
        branches: {
          LOCATION: {
            constraints: {
              min: 1,
              max: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__BAILLEURS: {
        branches: {
          BAILLEUR: {
            constraints: {
              min: 1
            },
            recordLinks: ['REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__LOCATAIRES: {
        branches: {
          LOCATAIRE: {
            constraints: {
              min: 1
            },
            recordLinks: ['REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__GARANTS: {
        condition: {
          id: 'GARANTIE_CAUTIONNEMENT',
          legalRecordTemplate: 'RECORD__OPERATION__IMMOBILIER__LOCATION__LOCATION',
          questionId: 'garantie_cautionnement',
          value: 'oui'
        },
        branches: {
          GARANT: {
            constraints: {
              min: 1
            },
            recordLinks: ['REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES: {
        branches: {
          BIEN_LOUE: {
            constraints: {
              min: 1
            },
            recordLinks: ['COMPOSITION__COPROPRIETE', 'COMPOSITION__ENSEMBLE_IMMOBILIER']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__AGENTS: {
        branches: {
          AGENT_IMMOBILIER: {
            constraints: {
              min: 1
            }
          }
        }
      }
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__IMMOBILIER__LOCATION__BAILLEURS: {
          branches: {
            BAILLEUR: true
          }
        },
        OPERATION__IMMOBILIER__LOCATION__LOCATAIRES: {
          branches: {
            LOCATAIRE: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'RASQUAIN_IMMOBILIER_LOCATION_BAIL_HABITATION',
  jeffersonPath: 'mynotary/rasquain/immobilier/location/bailHabitation.json',
  label: "Bail d'habitation",
  mainContract: false,
  originTemplate: null
};
