import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const agence_delegatairesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION__AGENCE_DELEGATAIRE'
  });
  const mandatairesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES'
  });

  return [agence_delegatairesSignatories, mandatairesSignatories].flat();
}

export const EfficityImmobilierLocationHabitationMandatLocationDelegationEfficitySignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
