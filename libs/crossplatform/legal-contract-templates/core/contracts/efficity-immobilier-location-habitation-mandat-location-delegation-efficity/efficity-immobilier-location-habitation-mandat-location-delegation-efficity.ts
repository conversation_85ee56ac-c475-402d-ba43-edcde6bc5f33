// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const EfficityImmobilierLocationHabitationMandatLocationDelegationEfficity: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__LOCATION__FICHES: {
        branches: {
          LOCATION: {
            constraints: {
              min: 1,
              max: 1
            }
          },
          MANDAT: {
            constraints: {
              min: 1,
              max: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES: {
        branches: {
          BIEN_LOUE: {
            constraints: {
              min: 1
            },
            recordLinks: ['COMPOSITION__ENSEMBLE_IMMOBILIER', 'COMPOSITION__LOT_ANNEXE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
        branches: {
          MANDATAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__AGENTS: {
        branches: {
          AGENT_IMMOBILIER: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__AGENCE_DELEGATAIRE: {
        branches: {
          AGENCE_DELEGATAIRE: {
            constraints: {
              min: 1
            },
            recordLinks: ['REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__BAILLEURS: {
        branches: {
          BAILLEUR: {
            constraints: {
              min: 1
            },
            recordLinks: ['SITUATION_MARITALE', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      }
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__IMMOBILIER__LOCATION__AGENCE_DELEGATAIRE: {
          branches: {
            AGENCE_DELEGATAIRE: true
          }
        },
        OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
          branches: {
            MANDATAIRE: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'EFFICITY_IMMOBILIER_LOCATION_HABITATION_MANDAT_LOCATION_DELEGATION_EFFICITY',
  jeffersonPath: 'mynotary/efficity/immobilier/location/habitation/mandatLocationDelegationEfficity.json',
  label: 'Délégation - Mandat de Location',
  mainContract: false,
  originTemplate: null
};
