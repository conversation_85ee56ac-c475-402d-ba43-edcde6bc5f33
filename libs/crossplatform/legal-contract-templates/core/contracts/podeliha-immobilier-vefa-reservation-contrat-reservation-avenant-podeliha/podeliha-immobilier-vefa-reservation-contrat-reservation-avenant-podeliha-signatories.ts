import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const reservatairesSignatories = createDefaultSignatoriesFromRecords({
    config: { defaultGroup: 1 },
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE_NEUF__RESERVATAIRES'
  });
  const representant_promoteursSignatories = createDefaultSignatoriesFromRecords({
    config: { defaultGroup: 2 },
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__REPRESENTANT_PROMOTEUR'
  });

  return [reservatairesSignatories, representant_promoteursSignatories].flat();
}

export const PodelihaImmobilierVefaReservationContratReservationAvenantPodelihaSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
