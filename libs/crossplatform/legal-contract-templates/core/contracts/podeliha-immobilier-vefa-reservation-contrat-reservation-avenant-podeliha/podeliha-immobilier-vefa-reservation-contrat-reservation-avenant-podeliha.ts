// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const PodelihaImmobilierVefaReservationContratReservationAvenantPodeliha: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__VENTE_NEUF__RESERVATAIRES: {
        branches: {
          RESERVATAIRE: {
            constraints: {
              min: 1
            },
            recordLinks: ['SITUATION_MARITALE', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE_NEUF__FICHES: {
        branches: {
          CONDITIONS_GENERALES: {
            constraints: {
              min: 1
            }
          },
          FINANCEMENT_ACQUEREUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTES_PROGRAMME__PROMOTEURS: {
        branches: {
          PROMOTEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTES_PROGRAMME__REPRESENTANT_PROMOTEUR: {
        branches: {
          REPRESENTANT_PROMOTEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__COPROPRIETE: {
        branches: {
          COPROPRIETE: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS: {
        constraints: {
          subOperationOnly: true
        },
        branches: {
          BIEN_VENDU: {
            constraints: {
              min: 1
            },
            recordLinks: ['COMPOSITION__ENSEMBLE_IMMOBILIER', 'COMPOSITION__COPROPRIETE']
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: [
        {
          recordPath: ['OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR', 'BRANCHES', 'ACQUEREUR', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_statuts', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'SITUATION_MARITALE_DIVORCE',
            'RECORD'
          ],
          documentIds: ['jugement_divorce', 'convention_de_divorce']
        },
        {
          recordPath: [
            'OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'SITUATION_MARITALE_MARIAGE',
            'RECORD'
          ],
          documentIds: ['livret_famille', 'contrat_mariage_upload', 'acte_changement_regime']
        },
        {
          recordPath: [
            'OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'SITUATION_MARITALE_PACS',
            'RECORD'
          ],
          documentIds: ['contrat_pacs']
        }
      ]
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__IMMOBILIER__VENTE_NEUF__RESERVATAIRES: {
          branches: {
            RESERVATAIRE: true
          },
          defaultGroup: 1
        },
        OPERATION__IMMOBILIER__VENTES_PROGRAMME__REPRESENTANT_PROMOTEUR: {
          branches: {
            REPRESENTANT_PROMOTEUR: true
          },
          defaultGroup: 2
        }
      }
    },
    registeredLetter: {
      letterTemplateId: 'REGISTERED_LETTER_VEFA',
      letterSubstitutions: {
        senderAddress: {
          items: [
            {
              type: 'VARIABLE',
              path: ['sender', 'address', 'formattedAddress']
            }
          ]
        },
        senderName: {
          items: [
            {
              type: 'VARIABLE',
              path: ['sender', 'firstname']
            },
            {
              type: 'CONSTANT',
              constant: ' '
            },
            {
              type: 'VARIABLE',
              path: ['sender', 'lastname']
            }
          ],
          aggregate: {
            type: 'CONCAT'
          }
        },
        signatureDate: {
          items: [
            {
              type: 'VARIABLE',
              path: ['configuration', 'signatureTime'],
              transformers: [
                {
                  type: 'DATE'
                }
              ]
            }
          ]
        }
      },
      defaultReceivers: {
        OPERATION__IMMOBILIER__VENTE_NEUF__RESERVATAIRES: {
          branches: {
            RESERVATAIRE: true
          }
        }
      }
    }
  },
  id: 'PODELIHA_IMMOBILIER_VEFA_RESERVATION_CONTRAT_RESERVATION_AVENANT_PODELIHA',
  jeffersonPath: 'mynotary/podeliha/immobilier/vefaReservation/contratReservationAvenantPodeliha.json',
  label: 'CONTRAT_RESERVATION_AVENANT_PODELIHA',
  mainContract: false,
  originTemplate: 'PODELIHA_IMMOBILIER_VEFA_RESERVATION_CONTRAT_RESERVATION_AVENANT_PODELIHA'
};
