import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const locatairesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION__LOCATAIRES'
  });
  const mandatairesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES'
  });

  return [locatairesSignatories, mandatairesSignatories].flat();
}

export const ImmobilierVenteBienProfessionnelMandatCessionBailAvenantSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
