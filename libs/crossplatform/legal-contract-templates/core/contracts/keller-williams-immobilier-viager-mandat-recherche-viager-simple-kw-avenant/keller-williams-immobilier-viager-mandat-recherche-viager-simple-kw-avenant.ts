// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const Keller<PERSON>illiamsImmobilierViagerMandatRechercheViagerSimpleKwAvenant: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES: {
        branches: {
          MANDAT: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__ACQUEREURS: {
        branches: {
          ACQUEREUR: {
            constraints: {
              min: 1
            },
            recordLinks: ['SITUATION_MARITALE', 'CAPACITE', 'PROCURATION', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__AGENTS: {
        branches: {
          AGENT_IMMOBILIER: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
        branches: {
          MANDATAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: [
        {
          recordPath: ['OPERATION__IMMOBILIER__VENTE__ACQUEREURS', 'BRANCHES', 'ACQUEREUR', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        }
      ]
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__IMMOBILIER__VENTE__ACQUEREURS: {
          branches: {
            ACQUEREUR: true
          }
        },
        OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
          branches: {
            MANDATAIRE: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_RECHERCHE_VIAGER_SIMPLE_KW_AVENANT',
  jeffersonPath: 'mynotary/kellerWilliams/immobilierViager/mandatRechercheViagerSimpleKwAvenant.json',
  label: 'Mandat de recherche - Viager - Avenant',
  mainContract: false,
  originTemplate: 'IMMOBILIER_VENTE_VIAGER_MANDAT_RECHERCHE_VIAGER_AVENANT'
};
