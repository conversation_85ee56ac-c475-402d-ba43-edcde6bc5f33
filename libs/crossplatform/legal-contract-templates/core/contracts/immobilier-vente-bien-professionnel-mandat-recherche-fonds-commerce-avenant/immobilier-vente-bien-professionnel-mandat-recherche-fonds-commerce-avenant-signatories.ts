import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const acquereursSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__ACQUEREURS'
  });
  const mandatairesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES'
  });

  return [acquereursSignatories, mandatairesSignatories].flat();
}

export const ImmobilierVenteBienProfessionnelMandatRechercheFondsCommerceAvenantSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
