// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const ErigereVefaContratDeReservation: LegalContractTemplate = {
  config: {
    defaultSubscribers: ['OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__NOTAIRE_PROGRAMME'],
    operationRecords: {
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__LOTS: {
        constraints: {
          subOperationOnly: true
        },
        branches: {
          BIEN_VENDU: {
            constraints: {
              min: 1
            },
            recordLinks: ['COMPOSITION__COPROPRIETE']
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__PROMOTEURS: {
        branches: {
          PROMOTEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__REPRESENTANT_PROMOTEURS: {
        branches: {
          REPRESENTANT_PROMOTEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__FICHE_PROGRAMME: {
        branches: {
          CONDITIONS_GENERALES: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__NOTAIRE_PROGRAMME: {
        branches: {
          NOTAIRE_PROGRAMME: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__COPROPRIETE: {
        branches: {
          COPROPRIETE: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__CONTRAT_RESERVATION: {
        branches: {
          CONTRAT_RESA: {
            constraints: {
              min: 1
            }
          },
          FINANCEMENT: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES: {
        branches: {
          RESERVATAIRE: {
            constraints: {
              min: 1
            },
            recordLinks: ['SITUATION_MARITALE', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__NOTAIRE_RESERVATAIRE: {
        branches: {
          NOTAIRE_RESERVATAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__COMMERCIALISATEUR: {
        branches: {
          COMMERCIALISATEUR: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__REPRESENTANT_COMMERCIALISATEUR: {
        branches: {
          REPRESENTANT_COMMERCIALISATEUR: {
            constraints: {
              min: 0
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: [
        {
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__CONTRAT_RESERVATION',
            'BRANCHES',
            'FINANCEMENT',
            'RECORDS'
          ],
          documentIds: ['emprunt_simulation']
        },
        {
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'RECORDS'
          ],
          documentIds: [
            'carte_identite',
            'titre_sejour',
            'personne_morale_statuts',
            'personne_morale_KBIS',
            '3f_bulletin_paie',
            '3f_justificatif',
            'fiscal_n_2'
          ]
        },
        {
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'SITUATION_MARITALE_DIVORCE',
            'RECORD'
          ],
          documentIds: ['jugement_divorce', 'convention_de_divorce']
        },
        {
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'SITUATION_MARITALE_MARIAGE',
            'RECORD'
          ],
          documentIds: ['livret_famille', 'contrat_mariage_upload', 'acte_changement_regime']
        },
        {
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'SITUATION_MARITALE_PACS',
            'RECORD'
          ],
          documentIds: ['contrat_pacs']
        }
      ]
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES: {
          branches: {
            RESERVATAIRE: true
          }
        },
        OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__REPRESENTANT_PROMOTEURS: {
          branches: {
            REPRESENTANT_PROMOTEUR: true
          }
        }
      }
    },
    registeredLetter: {
      letterTemplateId: 'REGISTERED_LETTER_VEFA',
      letterSubstitutions: {
        senderAddress: {
          items: [
            {
              type: 'VARIABLE',
              path: ['sender', 'address', 'formattedAddress']
            }
          ]
        },
        senderName: {
          items: [
            {
              type: 'VARIABLE',
              path: ['sender', 'firstname']
            },
            {
              type: 'CONSTANT',
              constant: ' '
            },
            {
              type: 'VARIABLE',
              path: ['sender', 'lastname']
            }
          ],
          aggregate: {
            type: 'CONCAT'
          }
        },
        signatureDate: {
          items: [
            {
              type: 'VARIABLE',
              path: ['configuration', 'signatureTime'],
              transformers: [
                {
                  type: 'DATE'
                }
              ]
            }
          ]
        }
      },
      defaultReceivers: {
        OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES: {
          branches: {
            RESERVATAIRE: true
          }
        }
      }
    }
  },
  id: 'ERIGERE__VEFA__CONTRAT_DE_RESERVATION',
  jeffersonPath: 'mynotary/erigere/vefa/contratDeReservation.json',
  label: 'Contrat de Réservation',
  mainContract: false,
  originTemplate: null
};
