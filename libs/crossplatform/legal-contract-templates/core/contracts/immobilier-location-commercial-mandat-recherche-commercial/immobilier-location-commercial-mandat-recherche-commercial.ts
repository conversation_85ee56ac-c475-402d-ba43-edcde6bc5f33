// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const ImmobilierLocationCommercialMandatRechercheCommercial: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BAIL_COMMERCIAL: {
        branches: {
          MANDAT: {
            constraints: {
              min: 1,
              max: 1
            }
          },
          BAIL: {
            constraints: {
              min: 1,
              max: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__LOCATAIRES: {
        branches: {
          LOCATAIRE: {
            constraints: {
              min: 1
            },
            recordLinks: ['SITUATION_MARITALE', 'CAPACITE', 'PROCURATION', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__IMMOBILIER__LOCATION__AGENTS: {
        branches: {
          AGENT_IMMOBILIER: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
        branches: {
          MANDATAIRE: {
            constraints: {
              min: 0
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: [
        {
          recordPath: ['OPERATION__IMMOBILIER__LOCATION__LOCATAIRES', 'BRANCHES', 'LOCATAIRE', 'RECORDS'],
          documentIds: ['carte_identite', 'titre_sejour', 'personne_morale_statuts', 'personne_morale_KBIS']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ],
          documentIds: ['carte_identite']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_DIVORCE',
            'RECORD'
          ],
          documentIds: ['jugement_divorce', 'convention_de_divorce']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_MARIAGE',
            'RECORD'
          ],
          documentIds: ['livret_famille', 'contrat_mariage_upload', 'acte_changement_regime']
        },
        {
          recordPath: [
            'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
            'BRANCHES',
            'LOCATAIRE',
            'LINKS',
            'SITUATION_MARITALE_PACS',
            'RECORD'
          ],
          documentIds: ['contrat_pacs']
        }
      ]
    },
    register: {
      TRANSACTION: {
        mandants_registre: [
          {
            path: [
              'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
              'BRANCHES',
              'LOCATAIRE',
              'TYPES',
              'PERSONNE__PHYSIQUE',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'sexe'
              },
              {
                type: 'CONSTANT',
                value: ' '
              },
              {
                type: 'QUESTION_ID',
                value: 'nom'
              },
              {
                type: 'CONSTANT',
                value: ' '
              },
              {
                type: 'QUESTION_ID',
                value: 'prenoms'
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'adresse'
              }
            ]
          },
          {
            path: [
              'OPERATION__IMMOBILIER__LOCATION__LOCATAIRES',
              'BRANCHES',
              'LOCATAIRE',
              'TYPES',
              'PERSONNE__MORALE',
              'RECORDS'
            ],
            items: [
              {
                type: 'QUESTION_ID',
                value: 'personne_morale_forme_sociale'
              },
              {
                type: 'CONSTANT',
                value: ', '
              },
              {
                type: 'QUESTION_ID',
                value: 'personne_morale_denomination'
              },
              {
                type: 'CONSTANT',
                value: ' - '
              },
              {
                type: 'QUESTION_ID',
                value: 'personne_morale_adresse'
              }
            ]
          }
        ],
        objet_registre: [
          {
            path: ['OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BAIL_COMMERCIAL', 'BRANCHES', 'MANDAT', 'RECORDS'],
            items: [
              {
                type: 'CONSTANT',
                value: 'Mandat de recherche de location commerciale '
              },
              {
                type: 'QUESTION_ID',
                value: 'mandat_recherche_type'
              }
            ]
          }
        ],
        biens_registre: [
          {
            path: ['OPERATION__IMMOBILIER__LOCATION__BIENS_LOUES', 'BRANCHES', 'BIEN_LOUE', 'RECORDS'],
            items: [
              {
                type: 'CONSTANT',
                value: '/'
              }
            ]
          }
        ]
      }
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
          branches: {
            MANDATAIRE: true
          }
        },
        OPERATION__IMMOBILIER__LOCATION__LOCATAIRES: {
          branches: {
            LOCATAIRE: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'IMMOBILIER_LOCATION_COMMERCIAL_MANDAT_RECHERCHE_COMMERCIAL',
  jeffersonPath: 'mynotary/immobilier/locationCommercial/mandatRechercheCommercial.json',
  label: 'Mandat de recherche - Local Commercial',
  mainContract: false,
  originTemplate: 'IMMOBILIER_LOCATION_COMMERCIAL_MANDAT_RECHERCHE_COMMERCIAL'
};
