// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const JohnTaylorImmobilierLocationHabitationNoteInterneTracfin: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
        branches: {
          MANDATAIRE: {
            constraints: {
              min: 1,
              contractSpecific: true
            }
          }
        }
      }
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__IMMOBILIER__VENTE__MANDATAIRES: {
          config: {
            mention: {
              text: 'Je soussigné(e) {{ MANDATAIRE }} reconnais avoir pris connaissance de la présente note de procédure, en avoir bien compris la teneur et avoir reçu toutes les informations complémentaires que j’ai jugées utiles.',
              variables: {
                MANDATAIRE: {
                  type: 'CONCAT',
                  path: ['OPERATION__IMMOBILIER__VENTE__MANDATAIRES', 'BRANCHES', 'MANDATAIRE', 'RECORDS'],
                  items: [
                    {
                      type: 'QUESTION_ID',
                      value: 'prenoms'
                    },
                    {
                      type: 'QUESTION_ID',
                      value: 'nom',
                      withPrefix: ' '
                    }
                  ]
                }
              }
            }
          },
          branches: {
            MANDATAIRE: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'JOHN_TAYLOR_IMMOBILIER_LOCATION_HABITATION_NOTE_INTERNE_TRACFIN',
  jeffersonPath: 'mynotary/johnTaylor/immobilier/locationHabitation/noteInterneTracfin.json',
  label: 'Note Interne TRACFIN',
  mainContract: false,
  originTemplate: null
};
