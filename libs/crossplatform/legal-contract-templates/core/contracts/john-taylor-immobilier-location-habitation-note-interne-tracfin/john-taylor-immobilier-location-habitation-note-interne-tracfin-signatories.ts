import {
  GetDefaultSignatoriesArgs,
  getMention,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const mandatairesSignatories = createDefaultSignatoriesFromRecords({
    config: {
      mention: getMention({
        ctx,
        mentions: {
          text: 'Je soussigné(e) {{ MANDATAIRE }} reconnais avoir pris connaissance de la présente note de procédure, en avoir bien compris la teneur et avoir reçu toutes les informations complémentaires que j’ai jugées utiles.',
          variables: {
            MANDATAIRE: {
              items: [
                { type: 'QUESTION_ID', value: 'prenoms' },
                { type: 'QUESTION_ID', value: 'nom', withPrefix: ' ' }
              ],
              path: ['OPERATION__IMMOBILIER__VENTE__MANDATAIRES', 'BRANCHES', 'MANDATAIRE', 'RECORDS'],
              type: 'CONCAT'
            }
          }
        }
      })
    },
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES'
  });

  return [mandatairesSignatories].flat();
}

export const JohnTaylorImmobilierLocationHabitationNoteInterneTracfinSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
