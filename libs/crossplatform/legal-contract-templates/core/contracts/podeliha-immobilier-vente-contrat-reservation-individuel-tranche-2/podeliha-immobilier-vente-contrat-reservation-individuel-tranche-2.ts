// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const PodelihaImmobilierVenteContratReservationIndividuelTranche2: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__LOTS: {
        constraints: {
          subOperationOnly: true
        },
        branches: {
          BIEN_VENDU: {
            constraints: {
              min: 1,
              max: 1
            },
            recordLinks: ['COMPOSITION__ENSEMBLE_IMMOBILIER']
          }
        }
      },
      OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR: {
        branches: {
          ACQUEREUR: {
            constraints: {
              min: 1
            },
            recordLinks: ['SITUATION_MARITALE', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__PODELIHA__IMMOBILIER__VENTE__FICHE_VEFA: {
        branches: {
          CONDITIONS_GENERALES: {
            constraints: {
              min: 1
            }
          },
          FINANCEMENT_ACQUEREUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__PODELIHA__IMMOBILIER__VENTE__NOTAIRE_ACQUEREUR: {
        branches: {
          NOTAIRE_ACQUEREUR: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__PROMOTEUR: {
        branches: {
          PROMOTEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__REPRESENTANT_PROMOTEUR: {
        branches: {
          REPRESENTANT_PROMOTEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__FICHE_VEFA: {
        branches: {
          CONDITIONS_GENERALES: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__NOTAIRE_PROGRAMME: {
        branches: {
          NOTAIRE_PROGRAMME: {
            constraints: {
              min: 1
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: []
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__PODELIHA__IMMOBILIER__PROGRAMME__REPRESENTANT_PROMOTEUR: {
          branches: {
            REPRESENTANT_PROMOTEUR: true
          }
        },
        OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR: {
          branches: {
            ACQUEREUR: true
          }
        }
      }
    },
    registeredLetter: {
      letterTemplateId: 'REGISTERED_LETTER_VEFA',
      letterSubstitutions: {
        senderAddress: {
          items: [
            {
              type: 'VARIABLE',
              path: ['sender', 'address', 'formattedAddress']
            }
          ]
        },
        senderName: {
          items: [
            {
              type: 'VARIABLE',
              path: ['sender', 'firstname']
            },
            {
              type: 'CONSTANT',
              constant: ' '
            },
            {
              type: 'VARIABLE',
              path: ['sender', 'lastname']
            }
          ],
          aggregate: {
            type: 'CONCAT'
          }
        },
        signatureDate: {
          items: [
            {
              type: 'VARIABLE',
              path: ['configuration', 'signatureTime'],
              transformers: [
                {
                  type: 'DATE'
                }
              ]
            }
          ]
        }
      },
      defaultReceivers: {
        OPERATION__PODELIHA__IMMOBILIER__VENTE__ACQUEREUR: {
          branches: {
            ACQUEREUR: true
          }
        }
      }
    }
  },
  id: 'PODELIHA_IMMOBILIER_VENTE_CONTRAT_RESERVATION_INDIVIDUEL_TRANCHE_2',
  jeffersonPath: 'mynotary/podeliha/immobilier/vente/contratReservationIndividuelTranche2.json',
  label: 'CONTRAT_RESERVATION_INDIVIDUEL_TRANCHE_2',
  mainContract: false,
  originTemplate: null
};
