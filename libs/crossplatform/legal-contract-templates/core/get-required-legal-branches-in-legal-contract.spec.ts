import { getRequiredLegalBranchesInLegalContract } from './get-required-legal-branches-in-legal-contract';

describe(getRequiredLegalBranchesInLegalContract.name, () => {
  it('should retrieve the required legal branches list', async () => {
    const requiredLegalBranches = getRequiredLegalBranchesInLegalContract('IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT');

    expect(requiredLegalBranches.length).toEqual(1);

    expect(requiredLegalBranches).toEqual([
      {
        legalBranchType: 'FICHE_OFFRE',
        legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRE',
        legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__OFFRE_ACHAT'
      }
    ]);
  });
});
