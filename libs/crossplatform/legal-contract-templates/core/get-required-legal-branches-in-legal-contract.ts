import {
  generateLegalRecordTemplateId,
  isLegalBranchWithTo,
  LegalContractTemplateId,
  LegalLinkTemplateId,
  LegalRecordTemplateId
} from '@mynotary/crossplatform/legal-templates/api';
import { find, forEach } from 'lodash';
import { getLegalLinkTemplate } from '@mynotary/crossplatform/legal-link-templates/api';
import { getLegalContractTemplate } from './legal-contract-template-map';

/**
 * Returns a list of required legal branches that are specific to a contract.
 *
 * This function parses a contract's template to identify records that must be automatically
 * created and are specific to each contract instance. A record is considered required and
 * contract-specific when both `autoCreate` and `contractSpecific` constraints are set to true.
 *
 * For example, in a "bon de visite" contract, the record storing "Date de visite" will be
 * different for each contract instance since the date depends on the visitor's availability.
 *
 * The function:
 * 1. Retrieves the contract template configuration
 * 2. Iterates through operation records and their branches
 * 3. Identifies branches marked as auto-create and contract-specific
 * 4. Generates corresponding legal record template IDs
 * 5. Returns a list of required branches with their template IDs
 *
 * @param legalContractTemplateId - The ID of the contract template to analyze
 * @returns An array of RequiredLegalBranch objects containing:
 *          - legalBranchType: The type of the branch
 *          - legalLinkTemplateId: The ID of the link template
 *          - legalRecordTemplateId: The ID of the record template
 */
export function getRequiredLegalBranchesInLegalContract(
  legalContractTemplateId: LegalContractTemplateId
): RequiredLegalBranch[] {
  const legalContractTemplate = getLegalContractTemplate(legalContractTemplateId);

  const legalBranches: RequiredLegalBranch[] = [];

  forEach(legalContractTemplate.config.operationRecords, (operationRecord, key) => {
    const legalLinkTemplateId = `LINK__${key}` as LegalLinkTemplateId;

    const linkTemplateConfig = getLegalLinkTemplate(legalLinkTemplateId);

    forEach(operationRecord.branches, (branch, branchType) => {
      if (branch.constraints.autoCreate && branch.constraints.contractSpecific) {
        const legalBranchConfig = find(
          linkTemplateConfig.config.branches,
          (legalBranchConfig) => legalBranchConfig.type === branchType
        );

        if (isLegalBranchWithTo(legalBranchConfig)) {
          const legalRecordTemplateId = generateLegalRecordTemplateId(legalBranchConfig.to.specificTypes[0]);

          legalBranches.push({
            legalBranchType: branchType,
            legalLinkTemplateId,
            legalRecordTemplateId: legalRecordTemplateId
          });
        }
      }
    });
  });

  return legalBranches;
}

export interface RequiredLegalBranch {
  legalBranchType: string;
  legalLinkTemplateId: LegalLinkTemplateId;
  legalRecordTemplateId: LegalRecordTemplateId;
}
