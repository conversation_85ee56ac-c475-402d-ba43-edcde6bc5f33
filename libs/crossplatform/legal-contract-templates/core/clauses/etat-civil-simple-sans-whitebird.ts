// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
export const EtatCivilSimpleSansWhitebird = {
  children: [
    {
      id: 'si-personne-personne-physique-simple-sans',
      condition: 'PERSONNE.PERSONNE_PHYSIQUE_SIMPLE_SANS',
      children: [
        {
          id: 'si-personne-personne-physique-simple-sans-texte',
          content:
            '**{{ PERSONNE.PHYSIQUE.CIVILITE }}** **{{ PERSONNE.PHYSIQUE.PRENOMS }}** **{{ PERSONNE.PHYSIQUE.NOM }}** demeurant **{{ PERSONNE.PHYSIQUE.ADRESSE }}**.\n{{ CIVILITE_NE }} à **{{ PERSONNE.PHYSIQUE.VILLE.NAISSANCE }}** le **{{ PERSONNE.PHYSIQUE.DATE.NAISSANCE }}**\nDe nationalité **{{ PERSONNE__PHYSIQUE__NATIONALITE }}**.\n',
          prerequisites: {
            variables: {
              'PERSONNE.PHYSIQUE.CIVILITE': true,
              'PERSONNE.PHYSIQUE.PRENOMS': true,
              'PERSONNE.PHYSIQUE.NOM': true,
              'PERSONNE.PHYSIQUE.ADRESSE': true,
              CIVILITE_NE: true,
              'PERSONNE.PHYSIQUE.VILLE.NAISSANCE': true,
              'PERSONNE.PHYSIQUE.DATE.NAISSANCE': true,
              PERSONNE__PHYSIQUE__NATIONALITE: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-simple-sans-si-personne-physique-celibataire',
          condition: 'PERSONNE.PHYSIQUE.CELIBATAIRE',
          content: 'Célibataire\n',
          prerequisites: {
            conditions: {
              'PERSONNE.PHYSIQUE.CELIBATAIRE': true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-simple-sans-si-personne-physique-marie',
          condition: 'PERSONNE.PHYSIQUE.MARIE',
          content:
            '{{ CIVILITE_MARIE_CAPS }} à **{{ PERSONNE.PHYSIQUE.MARIAGE.EPOUX.CIVILITE }} {{ PERSONNE.PHYSIQUE.MARIAGE.EPOUX.PRENOMS }} {{ PERSONNE.PHYSIQUE.MARIAGE.EPOUX.NOM }}**\n',
          prerequisites: {
            variables: {
              CIVILITE_MARIE_CAPS: true,
              'PERSONNE.PHYSIQUE.MARIAGE.EPOUX.CIVILITE': true,
              'PERSONNE.PHYSIQUE.MARIAGE.EPOUX.PRENOMS': true,
              'PERSONNE.PHYSIQUE.MARIAGE.EPOUX.NOM': true
            },
            conditions: {
              'PERSONNE.PHYSIQUE.MARIE': true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-simple-sans-si-personne-physique-divorce-termine',
          condition: 'PERSONNE__PHYSIQUE__DIVORCE__TERMINE',
          content:
            '{{ CIVILITE_DIVORCE }} de **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__CIVILITE }}** **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__PRENOMS }}** **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__NOM }}**.\n',
          prerequisites: {
            variables: {
              CIVILITE_DIVORCE: true,
              PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__CIVILITE: true,
              PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__PRENOMS: true,
              PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__NOM: true
            },
            conditions: {
              PERSONNE__PHYSIQUE__DIVORCE__TERMINE: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-simple-sans-si-personne-physique-divorce-en-cours',
          condition: 'PERSONNE__PHYSIQUE__DIVORCE__EN_COURS',
          content:
            'En instance de divorce avec **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__CIVILITE }}** **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__PRENOMS }}** **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__NOM }}**.\n',
          prerequisites: {
            variables: {
              PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__CIVILITE: true,
              PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__PRENOMS: true,
              PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__NOM: true
            },
            conditions: {
              PERSONNE__PHYSIQUE__DIVORCE__EN_COURS: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-simple-sans-si-personne-physique-pacse',
          condition: 'PERSONNE.PHYSIQUE.PACSE',
          content:
            '{{ CIVILITE_PACSE }} à **{{ PERSONNE.PHYSIQUE.PACS.PARTENAIRE.CIVILITE }} {{ PERSONNE.PHYSIQUE.PACS.PARTENAIRE.PRENOMS }} {{ PERSONNE.PHYSIQUE.PACS.PARTENAIRE.NOM }}**\n',
          prerequisites: {
            variables: {
              CIVILITE_PACSE: true,
              'PERSONNE.PHYSIQUE.PACS.PARTENAIRE.CIVILITE': true,
              'PERSONNE.PHYSIQUE.PACS.PARTENAIRE.PRENOMS': true,
              'PERSONNE.PHYSIQUE.PACS.PARTENAIRE.NOM': true
            },
            conditions: {
              'PERSONNE.PHYSIQUE.PACSE': true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-simple-sans-si-personne-physique-veuf',
          condition: 'PERSONNE.PHYSIQUE.VEUF',
          content:
            '{{ CIVILITE_VEUF }} de **{{ PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.CIVILITE }} {{ PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.PRENOMS }} {{ PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.NOM }}**\n',
          prerequisites: {
            variables: {
              CIVILITE_VEUF: true,
              'PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.CIVILITE': true,
              'PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.PRENOMS': true,
              'PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.NOM': true
            },
            conditions: {
              'PERSONNE.PHYSIQUE.VEUF': true
            }
          }
        }
      ],
      prerequisites: {
        conditions: {
          'PERSONNE.PERSONNE_PHYSIQUE_SIMPLE_SANS': true
        }
      }
    },
    {
      id: 'si-personne-personne-morale-simple-sans',
      condition: 'PERSONNE.PERSONNE_MORALE_SIMPLE_SANS',
      children: [
        {
          id: 'si-personne-personne-morale-simple-sans-si-personne-morale-societe',
          condition: 'PERSONNE_MORALE_SOCIETE',
          content:
            'La société dénommée **{{ PERSONNE.SOCIETE.DENOMINATION }}** **{{ PERSONNE.SOCIETE.FORME_SOCIALE }}** au capital de **{{ PERSONNE.SOCIETE.CAPITAL }}**, dont le siège est à **{{ PERSONNE.SOCIETE.ADRESSE }}**, identifiée au SIREN sous le numéro **{{ PERSONNE.SOCIETE.SIREN }}** et immatriculée au Registre du Commerce et des sociétés de **{{ PERSONNE.SOCIETE.VILLE_RCS }}**.\n',
          prerequisites: {
            variables: {
              'PERSONNE.SOCIETE.DENOMINATION': true,
              'PERSONNE.SOCIETE.FORME_SOCIALE': true,
              'PERSONNE.SOCIETE.CAPITAL': true,
              'PERSONNE.SOCIETE.ADRESSE': true,
              'PERSONNE.SOCIETE.SIREN': true,
              'PERSONNE.SOCIETE.VILLE_RCS': true
            },
            conditions: {
              PERSONNE_MORALE_SOCIETE: true
            }
          }
        },
        {
          id: 'si-personne-personne-morale-simple-sans-si-personne-morale-association',
          condition: 'PERSONNE_MORALE_ASSOCIATION',
          content:
            "L'association dénommée **{{ PERSONNE.SOCIETE.DENOMINATION }}** , dont le siège est à **{{ PERSONNE.SOCIETE.ADRESSE }}**, identifiée au RNA sous le numéro **{{ PERSONNE.SOCIETE.RNA }}** \n",
          prerequisites: {
            variables: {
              'PERSONNE.SOCIETE.DENOMINATION': true,
              'PERSONNE.SOCIETE.ADRESSE': true,
              'PERSONNE.SOCIETE.RNA': true
            },
            conditions: {
              PERSONNE_MORALE_ASSOCIATION: true
            }
          }
        },
        {
          id: 'si-personne-personne-morale-simple-sans-si-personne-morale-commune',
          condition: 'PERSONNE_MORALE_COMMUNE',
          content:
            'La commune de **{{ PERSONNE.SOCIETE.DENOMINATION }}**, dont le siège est à **{{ PERSONNE.SOCIETE.ADRESSE }}**, identifiée au SIREN sous le numéro **{{ PERSONNE.SOCIETE.SIREN }}**.\n',
          prerequisites: {
            variables: {
              'PERSONNE.SOCIETE.DENOMINATION': true,
              'PERSONNE.SOCIETE.ADRESSE': true,
              'PERSONNE.SOCIETE.SIREN': true
            },
            conditions: {
              PERSONNE_MORALE_COMMUNE: true
            }
          }
        },
        {
          id: 'si-personne-personne-morale-simple-sans-liste-representants',
          repetition: {
            source: 'REPRESENTANTS',
            item: 'PERSONNE_MORALE_REPRESENTANT'
          },
          content:
            'Dont le représentant légal est **{{ PERSONNE_MORALE_REPRESENTANT.CIVILITE }}** **{{ PERSONNE_MORALE_REPRESENTANT.PRENOMS }}** **{{ PERSONNE_MORALE_REPRESENTANT.NOM }}**, demeurant **{{ PERSONNE_MORALE_REPRESENTANT.ADRESSE }}**.\n',
          prerequisites: {
            repeats: {
              REPRESENTANTS: {
                variables: {
                  'PERSONNE_MORALE_REPRESENTANT.CIVILITE': true,
                  'PERSONNE_MORALE_REPRESENTANT.PRENOMS': true,
                  'PERSONNE_MORALE_REPRESENTANT.NOM': true,
                  'PERSONNE_MORALE_REPRESENTANT.ADRESSE': true
                },
                conditions: {},
                repeats: {},
                raws: {}
              }
            }
          }
        }
      ],
      prerequisites: {
        conditions: {
          'PERSONNE.PERSONNE_MORALE_SIMPLE_SANS': true
        }
      }
    }
  ],
  mapping: {
    conditions: {
      'PERSONNE.PERSONNE_PHYSIQUE_SIMPLE_SANS': {
        value: "_.isTemplateOfType(PERSONNE.template.id, ['PERSONNE', 'PHYSIQUE'])",
        dependencies: []
      },
      'PERSONNE.PHYSIQUE.CELIBATAIRE': {
        value:
          '!RAWS.PERSONNE.PHYSIQUE.MARIAGE.INFOS && !RAWS.PERSONNE.PHYSIQUE.DIVORCE.INFOS && !RAWS.PERSONNE.PHYSIQUE.PACS.INFOS && !RAWS.PERSONNE.PHYSIQUE.VEUVAGE.INFOS',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.MARIAGE.INFOS'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.DIVORCE.INFOS'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.PACS.INFOS'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.VEUVAGE.INFOS'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.MARIE': {
        value: '!!RAWS.PERSONNE.PHYSIQUE.MARIAGE.INFOS',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.MARIAGE.INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__TERMINE: {
        value: "answers[RAWS.PERSONNE.PHYSIQUE.DIVORCE.INFOS.id].statut.value === 'prononce'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE.PHYSIQUE.DIVORCE.INFOS.id',
            questionId: 'statut'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.DIVORCE.INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__EN_COURS: {
        value: "answers[RAWS.PERSONNE.PHYSIQUE.DIVORCE.INFOS.id].statut.value === 'en_cours'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE.PHYSIQUE.DIVORCE.INFOS.id',
            questionId: 'statut'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.DIVORCE.INFOS'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.PACSE': {
        value: '!!RAWS.PERSONNE.PHYSIQUE.PACS.INFOS',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.PACS.INFOS'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.VEUF': {
        value: '!!RAWS.PERSONNE.PHYSIQUE.VEUVAGE.INFOS',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.VEUVAGE.INFOS'
          }
        ]
      },
      'PERSONNE.PERSONNE_MORALE_SIMPLE_SANS': {
        value: "_.isTemplateOfType(PERSONNE.template.id, ['PERSONNE', 'MORALE'])",
        dependencies: []
      },
      PERSONNE_MORALE_SOCIETE: {
        value:
          "answers[PERSONNE.id].personne_morale_forme_sociale.value !== 'commune' && answers[PERSONNE.id].personne_morale_forme_sociale.value !== 'association'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_forme_sociale'
          }
        ]
      },
      PERSONNE_MORALE_ASSOCIATION: {
        value: "answers[PERSONNE.id].personne_morale_forme_sociale.value === 'association'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_forme_sociale'
          }
        ]
      },
      PERSONNE_MORALE_COMMUNE: {
        value: "answers[PERSONNE.id].personne_morale_forme_sociale.value === 'commune'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_forme_sociale'
          }
        ]
      }
    },
    variables: {
      'PERSONNE.PHYSIQUE.CIVILITE': {
        value: '_.formatCivility(answers[PERSONNE.id].sexe.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'sexe'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PROCURATION__INFOS'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PROCURATION__INFOS.id',
            questionId: 'procuration'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'telephone'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'email'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.PRENOMS': {
        value: 'answers[PERSONNE.id].prenoms.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'prenoms'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.NOM': {
        value: 'answers[PERSONNE.id].nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'nom'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.ADRESSE': {
        value: 'answers[PERSONNE.id].adresse.value.formattedAddress',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'adresse'
          }
        ]
      },
      CIVILITE_NE: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'Né', 'Née')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.VILLE.NAISSANCE': {
        value: 'answers[PERSONNE.id].informations_personnelles_ville_naissance.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'informations_personnelles_ville_naissance'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.DATE.NAISSANCE': {
        value: '_.formatDate(answers[PERSONNE.id].informations_personnelles_date_naissance.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'informations_personnelles_date_naissance'
          }
        ]
      },
      PERSONNE__PHYSIQUE__NATIONALITE: {
        value:
          'staticMetadata.pays[answers[PERSONNE.id].informations_personnelles_nationalite.value].nationalite.femme',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'informations_personnelles_nationalite'
          }
        ]
      },
      CIVILITE_MARIE_CAPS: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'Marié', 'Mariée')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.MARIAGE.EPOUX.CIVILITE': {
        value: '_.formatCivility(answers[RAWS.PERSONNE.PHYSIQUE.MARIAGE.EPOUX.id].sexe.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE.PHYSIQUE.MARIAGE.EPOUX.id',
            questionId: 'sexe'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.MARIAGE.EPOUX'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.MARIAGE.EPOUX.PRENOMS': {
        value: 'answers[RAWS.PERSONNE.PHYSIQUE.MARIAGE.EPOUX.id].prenoms.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE.PHYSIQUE.MARIAGE.EPOUX.id',
            questionId: 'prenoms'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.MARIAGE.EPOUX'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.MARIAGE.EPOUX.NOM': {
        value: 'answers[RAWS.PERSONNE.PHYSIQUE.MARIAGE.EPOUX.id].nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE.PHYSIQUE.MARIAGE.EPOUX.id',
            questionId: 'nom'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.MARIAGE.EPOUX'
          }
        ]
      },
      CIVILITE_DIVORCE: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'Divorcé', 'Divorcée')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__CIVILITE: {
        value: '_.formatCivility(answers[RAWS.PERSONNE.PHYSIQUE.DIVORCE.EX_EPOUX.id].sexe.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE.PHYSIQUE.DIVORCE.EX_EPOUX.id',
            questionId: 'sexe'
          },
          {
            type: 'RAW',
            rawId: 'RAWS.PERSONNE.PHYSIQUE.DIVORCE.EX_EPOUX.id'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__PRENOMS: {
        value: 'answers[RAWS.PERSONNE.PHYSIQUE.DIVORCE.EX_EPOUX.id].prenoms.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE.PHYSIQUE.DIVORCE.EX_EPOUX.id',
            questionId: 'prenoms'
          },
          {
            type: 'RAW',
            rawId: 'RAWS.PERSONNE.PHYSIQUE.DIVORCE.EX_EPOUX.id'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__NOM: {
        value: 'answers[RAWS.PERSONNE.PHYSIQUE.DIVORCE.EX_EPOUX.id].nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE.PHYSIQUE.DIVORCE.EX_EPOUX.id',
            questionId: 'nom'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.DIVORCE.EX_EPOUX'
          }
        ]
      },
      CIVILITE_PACSE: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'Pacsé', 'Pacsée')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.PACS.PARTENAIRE.CIVILITE': {
        value: '_.formatCivility(answers[RAWS.PERSONNE.PHYSIQUE.PACS.PARTENAIRE.id].sexe.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE.PHYSIQUE.PACS.PARTENAIRE.id',
            questionId: 'sexe'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.PACS.PARTENAIRE'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.PACS.PARTENAIRE.PRENOMS': {
        value: 'answers[RAWS.PERSONNE.PHYSIQUE.PACS.PARTENAIRE.id].prenoms.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE.PHYSIQUE.PACS.PARTENAIRE.id',
            questionId: 'prenoms'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.PACS.PARTENAIRE'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.PACS.PARTENAIRE.NOM': {
        value: 'answers[RAWS.PERSONNE.PHYSIQUE.PACS.PARTENAIRE.id].nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE.PHYSIQUE.PACS.PARTENAIRE.id',
            questionId: 'nom'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.PACS.PARTENAIRE'
          }
        ]
      },
      CIVILITE_VEUF: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'Veuf', 'Veuve')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.CIVILITE': {
        value: '_.formatCivility(answers[RAWS.PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.id].sexe.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.id',
            questionId: 'sexe'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.PRENOMS': {
        value: 'answers[RAWS.PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.id].prenoms.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.id',
            questionId: 'prenoms'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT'
          }
        ]
      },
      'PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.NOM': {
        value: 'answers[RAWS.PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.id].nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT.id',
            questionId: 'nom'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT'
          }
        ]
      },
      'PERSONNE.SOCIETE.DENOMINATION': {
        value: 'answers[PERSONNE.id].personne_morale_denomination.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_denomination'
          }
        ]
      },
      'PERSONNE.SOCIETE.FORME_SOCIALE': {
        value:
          "answers[PERSONNE.id].personne_morale_forme_sociale.value !== 'autre' ? {societe_sci: 'Société Civile Immobilière',societe_sarl: 'Société à Responsabilité Limitée',societe_sasu: 'Société par Actions Simplifiée Unipersonnelle',societe_sa: 'Société par Actions',societe_sas: 'Société par Actions Simplifiée',association: 'Association'}[answers[PERSONNE.id].personne_morale_forme_sociale.value] : answers[PERSONNE.id].personne_morale_forme_sociale_autre.value",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_forme_sociale'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_forme_sociale_autre'
          }
        ]
      },
      'PERSONNE.SOCIETE.CAPITAL': {
        value: '_.formatPrice(answers[PERSONNE.id].personne_morale_capital.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_capital'
          }
        ]
      },
      'PERSONNE.SOCIETE.ADRESSE': {
        value: 'answers[PERSONNE.id].adresse.value.formattedAddress',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'adresse'
          }
        ]
      },
      'PERSONNE.SOCIETE.SIREN': {
        value: 'answers[PERSONNE.id].siren.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'siren'
          }
        ]
      },
      'PERSONNE.SOCIETE.VILLE_RCS': {
        value: 'answers[PERSONNE.id].personne_morale_ville_rcs.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_ville_rcs'
          }
        ]
      },
      'PERSONNE.SOCIETE.RNA': {
        value: 'answers[PERSONNE.id].personne_morale_rna.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_rna'
          }
        ]
      }
    },
    repeats: {
      REPRESENTANTS: {
        value: 'RAWS.PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT',
        item: 'REPRESENTANTS',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT'
          }
        ],
        mapping: {
          conditions: {},
          variables: {
            KEY: {
              value: 'REPRESENTANTS.id',
              dependencies: []
            },
            NAME: {
              value: '',
              dependencies: []
            },
            ORDER: {
              value: '',
              dependencies: []
            },
            CIVILITE: {
              value: '_.formatCivility(answers[REPRESENTANTS.id].sexe.value)',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'REPRESENTANTS.id',
                  questionId: 'sexe'
                }
              ]
            },
            PRENOMS: {
              value: 'answers[REPRESENTANTS.id].prenoms.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'REPRESENTANTS.id',
                  questionId: 'prenoms'
                }
              ]
            },
            NOM: {
              value: 'answers[REPRESENTANTS.id].nom.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'REPRESENTANTS.id',
                  questionId: 'nom'
                }
              ]
            },
            ADRESSE: {
              value: 'answers[REPRESENTANTS.id].adresse.value.formattedAddress',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'REPRESENTANTS.id',
                  questionId: 'adresse'
                }
              ]
            }
          },
          repeats: {},
          raws: [
            {
              id: 'INFOS_REPRESENTANT',
              value: 'rawLinks[PERSONNE.id].REPRESENTANT',
              dependencies: []
            }
          ]
        }
      }
    },
    raws: [
      {
        id: 'PERSONNE.PHYSIQUE.TELEPHONE',
        value: 'answers[PERSONNE.id].telephone.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'telephone'
          }
        ]
      },
      {
        id: 'PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT',
        value: 'branches[PERSONNE.id].REPRESENTANT',
        dependencies: []
      },
      {
        id: 'PERSONNE.SOCIETE.REPRESENTATION.REPRESENTANT',
        value: 'branches[PERSONNE.id].EPOUX[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE.SOCIETE.REPRESENTATION.REPRESENTANT_TELEPHONE',
        value: 'answers[RAWS.PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT.id].telephone.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT.id',
            questionId: 'telephone'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT'
          }
        ]
      },
      {
        id: 'PERSONNE.SOCIETE.REPRESENTATION.REPRESENTANT_EMAIL',
        value: 'answers[RAWS.PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT.id].email.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT.id',
            questionId: 'email'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT'
          }
        ]
      },
      {
        id: 'PERSONNE.PHYSIQUE.MARIAGE.EPOUX',
        value: 'branches[PERSONNE.id].EPOUX[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE.PHYSIQUE.MARIAGE.INFOS',
        value: 'links[PERSONNE.id].EPOUX[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE.PHYSIQUE.DIVORCE.EX_EPOUX',
        value: 'branches[PERSONNE.id].EX_EPOUX[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE.PHYSIQUE.DIVORCE.INFOS',
        value: 'links[PERSONNE.id].EX_EPOUX[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE.PHYSIQUE.PACS.PARTENAIRE',
        value: 'branches[PERSONNE.id].PARTENAIRE[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE.PHYSIQUE.PACS.INFOS',
        value: 'links[PERSONNE.id].PARTENAIRE[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE.PHYSIQUE.VEUVAGE.DEFUNT',
        value: 'branches[PERSONNE.id].DECEDE[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE.PHYSIQUE.VEUVAGE.INFOS',
        value: 'links[PERSONNE.id].DECEDE[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE__PROCURATION__REPRESENTANT',
        value: 'branches[PERSONNE.id].REPRESENTANT_PROCURATION[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE__PROCURATION__INFOS',
        value: 'links[PERSONNE.id].REPRESENTANT_PROCURATION[0]',
        dependencies: []
      }
    ]
  },
  metadata: {}
};
