// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
export const EtatCivilPolynesie = {
  children: [
    {
      id: 'si-personne-personne-physique',
      condition: 'PERSONNE__PERSONNE_PHYSIQUE',
      children: [
        {
          id: 'si-personne-personne-physique-texte',
          content:
            '**{{ PERSONNE__PHYSIQUE__CIVILITE }}** **{{ PERSONNE__PHYSIQUE__PRENOMS }}** **{{ PERSONNE__PHYSIQUE__NOM }}**, **{{ PERSONNE__PHYSIQUE__PROFESSION }}**, demeurant **{{ PERSONNE__PHYSIQUE__ADRESSE }}**.\n{{ CIVILITE_NE }} à **{{ PERSONNE__PHYSIQUE__VILLE_NAISSANCE }}** le **{{ PERSONNE__PHYSIQUE__DATE_NAISSANC<PERSON> }}**.\n',
          prerequisites: {
            variables: {
              PERSONNE__PHYSIQUE__CIVILITE: true,
              PERSONNE__PHYSIQUE__PRENOMS: true,
              PERSONNE__PHYSIQUE__NOM: true,
              PERSONNE__PHYSIQUE__PROFESSION: true,
              PERSONNE__PHYSIQUE__ADRESSE: true,
              CIVILITE_NE: true,
              PERSONNE__PHYSIQUE__VILLE_NAISSANCE: true,
              PERSONNE__PHYSIQUE__DATE_NAISSANCE: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-si-personne-physique-celibataire',
          condition: 'PERSONNE__PHYSIQUE__CELIBATAIRE',
          content: 'Célibataire.\nNon {{ CIVILITE_LIE }} par un pacte civil de solidarité (PACS).\n',
          prerequisites: {
            variables: {
              CIVILITE_LIE: true
            },
            conditions: {
              PERSONNE__PHYSIQUE__CELIBATAIRE: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-si-personne-physique-divorce-termine',
          condition: 'PERSONNE__PHYSIQUE__DIVORCE__TERMINE',
          children: [
            {
              id: 'si-personne-personne-physique-si-personne-physique-divorce-termine-si-personne-physique-divorce-devant-tribunal',
              condition: 'PERSONNE__PHYSIQUE__DIVORCE__DEVANT_TRIBUNAL',
              content:
                '{{ CIVILITE_DIVORCE }} de **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__CIVILITE }}** **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__PRENOMS }}** **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__NOM }}** suivant jugement rendu au tribunal de **{{ PERSONNE__PHYSIQUE__DIVORCE__VILLE }}** le **{{ PERSONNE__PHYSIQUE__DIVORCE__DATE_TRIBUNAL }}** et non {{ CIVILITE_MARIE }} et non {{ CIVILITE_LIE }} par un pacte civil de solidarité (PACS).\n',
              prerequisites: {
                variables: {
                  CIVILITE_DIVORCE: true,
                  PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__CIVILITE: true,
                  PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__PRENOMS: true,
                  PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__NOM: true,
                  PERSONNE__PHYSIQUE__DIVORCE__VILLE: true,
                  PERSONNE__PHYSIQUE__DIVORCE__DATE_TRIBUNAL: true,
                  CIVILITE_MARIE: true,
                  CIVILITE_LIE: true
                },
                conditions: {
                  PERSONNE__PHYSIQUE__DIVORCE__DEVANT_TRIBUNAL: true
                }
              }
            },
            {
              id: 'si-personne-personne-physique-si-personne-physique-divorce-termine-si-personne-physique-divorce-devant-notaire',
              condition: 'PERSONNE__PHYSIQUE__DIVORCE__DEVANT_NOTAIRE',
              content:
                '{{ CIVILITE_DIVORCE }} de **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__CIVILITE }}** **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__PRENOMS }}** **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__NOM }}** aux termes d’un acte de dépôt de convention de divorce, reçu par Maître **{{ PERSONNE__PHYSIQUE__DIVORCE__NOM_NOTAIRE }}**, notaire à **{{ PERSONNE__PHYSIQUE__DIVORCE__VILLE_NOTAIRE }}** le **{{ PERSONNE__PHYSIQUE__DIVORCE__DATE_NOTAIRE }}** et non {{ CIVILITE_MARIE }} et non {{ CIVILITE_LIE }} par un pacte civil de solidarité (PACS).\n',
              prerequisites: {
                variables: {
                  CIVILITE_DIVORCE: true,
                  PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__CIVILITE: true,
                  PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__PRENOMS: true,
                  PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__NOM: true,
                  PERSONNE__PHYSIQUE__DIVORCE__NOM_NOTAIRE: true,
                  PERSONNE__PHYSIQUE__DIVORCE__VILLE_NOTAIRE: true,
                  PERSONNE__PHYSIQUE__DIVORCE__DATE_NOTAIRE: true,
                  CIVILITE_MARIE: true,
                  CIVILITE_LIE: true
                },
                conditions: {
                  PERSONNE__PHYSIQUE__DIVORCE__DEVANT_NOTAIRE: true
                }
              }
            }
          ],
          prerequisites: {
            conditions: {
              PERSONNE__PHYSIQUE__DIVORCE__TERMINE: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-si-personne-physique-divorce-en-cours',
          condition: 'PERSONNE__PHYSIQUE__DIVORCE__EN_COURS',
          content:
            'En instance de divorce avec **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__CIVILITE }}** **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__PRENOMS }}** **{{ PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__NOM }}**.\n',
          prerequisites: {
            variables: {
              PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__CIVILITE: true,
              PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__PRENOMS: true,
              PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__NOM: true
            },
            conditions: {
              PERSONNE__PHYSIQUE__DIVORCE__EN_COURS: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-si-personne-physique-veuf',
          condition: 'PERSONNE__PHYSIQUE__VEUF',
          content:
            '{{ CIVILITE_VEUF }} de **{{ PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT__CIVILITE }}** **{{ PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT__PRENOMS }}** **{{ PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT__NOM }}** et non {{ CIVILITE_MARIE }} et non {{ CIVILITE_LIE }} par un pacte civil de solidarité (PACS).\n',
          prerequisites: {
            variables: {
              CIVILITE_VEUF: true,
              PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT__CIVILITE: true,
              PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT__PRENOMS: true,
              PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT__NOM: true,
              CIVILITE_MARIE: true,
              CIVILITE_LIE: true
            },
            conditions: {
              PERSONNE__PHYSIQUE__VEUF: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-si-personne-physique-pacs-au-tribunal',
          condition: 'PERSONNE__PHYSIQUE__PACS__AU_TRIBUNAL',
          content:
            '{{ CIVILITE_LIE_CAPS }} par un pacte civil de solidarité avec **{{ PERSONNE__PHYSIQUE__PACS__PARTENAIRE__CIVILITE }}** **{{ PERSONNE__PHYSIQUE__PACS__PARTENAIRE__PRENOMS }}** **{{ PERSONNE__PHYSIQUE__PACS__PARTENAIRE__NOM }}**, déclaré au Greffe du tribunal **{{ PERSONNE__PHYSIQUE__PACS__VILLE }}**, et inscrit sur le registre tenu à cet effet par le tribunal susvisé à la date du **{{ PERSONNE__PHYSIQUE__PACS__DATE }}**.\n',
          prerequisites: {
            variables: {
              CIVILITE_LIE_CAPS: true,
              PERSONNE__PHYSIQUE__PACS__PARTENAIRE__CIVILITE: true,
              PERSONNE__PHYSIQUE__PACS__PARTENAIRE__PRENOMS: true,
              PERSONNE__PHYSIQUE__PACS__PARTENAIRE__NOM: true,
              PERSONNE__PHYSIQUE__PACS__VILLE: true,
              PERSONNE__PHYSIQUE__PACS__DATE: true
            },
            conditions: {
              PERSONNE__PHYSIQUE__PACS__AU_TRIBUNAL: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-si-personne-physique-pacs-en-mairie',
          condition: 'PERSONNE__PHYSIQUE__PACS__EN_MAIRIE',
          content:
            '{{ CIVILITE_LIE_CAPS }} par un pacte civil de solidarité avec **{{ PERSONNE__PHYSIQUE__PACS__PARTENAIRE__CIVILITE }}** **{{ PERSONNE__PHYSIQUE__PACS__PARTENAIRE__PRENOMS }}** **{{ PERSONNE__PHYSIQUE__PACS__PARTENAIRE__NOM }}**, enregistré à la mairie de **{{ PERSONNE__PHYSIQUE__PACS__MAIRIE }}**, et inscrit sur le registre tenu à cet effet à la date du **{{ PERSONNE__PHYSIQUE__PACS__DATE }}**.\n',
          prerequisites: {
            variables: {
              CIVILITE_LIE_CAPS: true,
              PERSONNE__PHYSIQUE__PACS__PARTENAIRE__CIVILITE: true,
              PERSONNE__PHYSIQUE__PACS__PARTENAIRE__PRENOMS: true,
              PERSONNE__PHYSIQUE__PACS__PARTENAIRE__NOM: true,
              PERSONNE__PHYSIQUE__PACS__MAIRIE: true,
              PERSONNE__PHYSIQUE__PACS__DATE: true
            },
            conditions: {
              PERSONNE__PHYSIQUE__PACS__EN_MAIRIE: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-si-personne-physique-pacs-chez-notaire',
          condition: 'PERSONNE__PHYSIQUE__PACS__CHEZ_NOTAIRE',
          content:
            "{{ CIVILITE_LIE_CAPS }} par un pacte civil de solidarité avec **{{ PERSONNE__PHYSIQUE__PACS__PARTENAIRE__CIVILITE }}** **{{ PERSONNE__PHYSIQUE__PACS__PARTENAIRE__PRENOMS }}** **{{ PERSONNE__PHYSIQUE__PACS__PARTENAIRE__NOM }}**, déclaré au terme d'un contrat de PACS reçu par Maître **{{ PERSONNE__PHYSIQUE__PACS__NOTAIRE__NOM }}**, notaire à **{{ PERSONNE__PHYSIQUE__PACS__NOTAIRE__VILLE }}** le **{{ PERSONNE__PHYSIQUE__PACS__DATE }}**.\n",
          prerequisites: {
            variables: {
              CIVILITE_LIE_CAPS: true,
              PERSONNE__PHYSIQUE__PACS__PARTENAIRE__CIVILITE: true,
              PERSONNE__PHYSIQUE__PACS__PARTENAIRE__PRENOMS: true,
              PERSONNE__PHYSIQUE__PACS__PARTENAIRE__NOM: true,
              PERSONNE__PHYSIQUE__PACS__NOTAIRE__NOM: true,
              PERSONNE__PHYSIQUE__PACS__NOTAIRE__VILLE: true,
              PERSONNE__PHYSIQUE__PACS__DATE: true
            },
            conditions: {
              PERSONNE__PHYSIQUE__PACS__CHEZ_NOTAIRE: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-si-personne-physique-marie',
          condition: 'PERSONNE__PHYSIQUE__MARIE',
          children: [
            {
              id: 'si-personne-personne-physique-si-personne-physique-marie-si-personne-physique-mariage-sans-contrat-initial-sans-changement-regime',
              condition: 'PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL_SANS_CHANGEMENT_REGIME',
              content:
                "{{ CIVILITE_MARIE_CAPS }} avec **{{ PERSONNE__PHYSIQUE__MARIAGE__EPOUX__CIVILITE }}** **{{ PERSONNE__PHYSIQUE__MARIAGE__EPOUX__PRENOMS }}** **{{ PERSONNE__PHYSIQUE__MARIAGE__EPOUX__NOM }}** à la mairie de **{{ PERSONNE__PHYSIQUE__MARIAGE__VILLE_MARIAGE }}**, **{{ PERSONNE__PHYSIQUE__MARIAGE__PAYS_MARIAGE }}**, le **{{ PERSONNE__PHYSIQUE__MARIAGE__DATE_MARIAGE }}**, sans contrat de mariage, sous le régime de la communauté légale **{{ PERSONNE__PHYSIQUE__MARIAGE__REGIME_COMMUNAUTE }}**, ce régime n'ayant subi aucune modification depuis.\n",
              prerequisites: {
                variables: {
                  CIVILITE_MARIE_CAPS: true,
                  PERSONNE__PHYSIQUE__MARIAGE__EPOUX__CIVILITE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__EPOUX__PRENOMS: true,
                  PERSONNE__PHYSIQUE__MARIAGE__EPOUX__NOM: true,
                  PERSONNE__PHYSIQUE__MARIAGE__VILLE_MARIAGE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__PAYS_MARIAGE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__DATE_MARIAGE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__REGIME_COMMUNAUTE: true
                },
                conditions: {
                  PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL_SANS_CHANGEMENT_REGIME: true
                }
              }
            },
            {
              id: 'si-personne-personne-physique-si-personne-physique-marie-si-personne-physique-mariage-sans-contrat-initial-avec-changement-regime',
              condition: 'PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL_AVEC_CHANGEMENT_REGIME',
              content:
                '{{ CIVILITE_MARIE_CAPS }} avec **{{ PERSONNE__PHYSIQUE__MARIAGE__EPOUX__CIVILITE }}** **{{ PERSONNE__PHYSIQUE__MARIAGE__EPOUX__PRENOMS }}** **{{ PERSONNE__PHYSIQUE__MARIAGE__EPOUX__NOM }}** à la mairie de **{{ PERSONNE__PHYSIQUE__MARIAGE__VILLE_MARIAGE }}**, **{{ PERSONNE__PHYSIQUE__MARIAGE__PAYS_MARIAGE }}**, le **{{ PERSONNE__PHYSIQUE__MARIAGE__DATE_MARIAGE }}**, initialement sans contrat de mariage sous le régime de la communauté légale **{{ PERSONNE__PHYSIQUE__MARIAGE__REGIME_COMMUNAUTE }}**, et actuellement soumis au régime de **{{ PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__REGIME }}** aux termes du contrat de mariage reçu par Maître **{{ PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__NOTAIRE__NOM }}** notaire à **{{ PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__NOTAIRE__VILLE }}** le **{{ PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__DATE }}**.\n',
              prerequisites: {
                variables: {
                  CIVILITE_MARIE_CAPS: true,
                  PERSONNE__PHYSIQUE__MARIAGE__EPOUX__CIVILITE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__EPOUX__PRENOMS: true,
                  PERSONNE__PHYSIQUE__MARIAGE__EPOUX__NOM: true,
                  PERSONNE__PHYSIQUE__MARIAGE__VILLE_MARIAGE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__PAYS_MARIAGE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__DATE_MARIAGE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__REGIME_COMMUNAUTE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__REGIME: true,
                  PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__NOTAIRE__NOM: true,
                  PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__NOTAIRE__VILLE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__DATE: true
                },
                conditions: {
                  PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL_AVEC_CHANGEMENT_REGIME: true
                }
              }
            },
            {
              id: 'si-personne-personne-physique-si-personne-physique-marie-si-personne-physique-mariage-avec-contrat-initial-sans-changement-regime',
              condition: 'PERSONNE__PHYSIQUE__MARIAGE__AVEC_CONTRAT_INITIAL_SANS_CHANGEMENT_REGIME',
              content:
                '{{ CIVILITE_MARIE_CAPS }} avec **{{ PERSONNE__PHYSIQUE__MARIAGE__EPOUX__CIVILITE }}** **{{ PERSONNE__PHYSIQUE__MARIAGE__EPOUX__PRENOMS }}** **{{ PERSONNE__PHYSIQUE__MARIAGE__EPOUX__NOM }}** à la mairie de **{{ PERSONNE__PHYSIQUE__MARIAGE__VILLE_MARIAGE }}**, **{{ PERSONNE__PHYSIQUE__MARIAGE__PAYS_MARIAGE }}**, le **{{ PERSONNE__PHYSIQUE__MARIAGE__DATE_MARIAGE }}**, sous le régime de **{{ PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__REGIME }}** aux termes du contrat de mariage reçu par Maître **{{ PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__NOTAIRE__NOM }}** notaire à **{{ PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__NOTAIRE__VILLE }}** le **{{ PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__DATE }}**.\n',
              prerequisites: {
                variables: {
                  CIVILITE_MARIE_CAPS: true,
                  PERSONNE__PHYSIQUE__MARIAGE__EPOUX__CIVILITE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__EPOUX__PRENOMS: true,
                  PERSONNE__PHYSIQUE__MARIAGE__EPOUX__NOM: true,
                  PERSONNE__PHYSIQUE__MARIAGE__VILLE_MARIAGE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__PAYS_MARIAGE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__DATE_MARIAGE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__REGIME: true,
                  PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__NOTAIRE__NOM: true,
                  PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__NOTAIRE__VILLE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__DATE: true
                },
                conditions: {
                  PERSONNE__PHYSIQUE__MARIAGE__AVEC_CONTRAT_INITIAL_SANS_CHANGEMENT_REGIME: true
                }
              }
            },
            {
              id: 'si-personne-personne-physique-si-personne-physique-marie-si-personne-physique-mariage-avec-contrat-initial-avec-changement-regime',
              condition: 'PERSONNE__PHYSIQUE__MARIAGE__AVEC_CONTRAT_INITIAL_AVEC_CHANGEMENT_REGIME',
              content:
                '{{ CIVILITE_MARIE_CAPS }} avec **{{ PERSONNE__PHYSIQUE__MARIAGE__EPOUX__CIVILITE }}** **{{ PERSONNE__PHYSIQUE__MARIAGE__EPOUX__PRENOMS }}** **{{ PERSONNE__PHYSIQUE__MARIAGE__EPOUX__NOM }}** à la mairie de **{{ PERSONNE__PHYSIQUE__MARIAGE__VILLE_MARIAGE }}**, **{{ PERSONNE__PHYSIQUE__MARIAGE__PAYS_MARIAGE }}**, le **{{ PERSONNE__PHYSIQUE__MARIAGE__DATE_MARIAGE }}**, initialement sous le régime de **{{ PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__REGIME }}** aux termes du contrat de mariage reçu par Maître **{{ PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__NOTAIRE__NOM }}** notaire à **{{ PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__NOTAIRE__VILLE }}** le **{{ PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__DATE }}** et actuellement soumis au régime de **{{ PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__REGIME }}** aux termes du contrat de mariage reçu par Maître **{{ PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__NOTAIRE__NOM }}** notaire à **{{ PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__NOTAIRE__VILLE }}** le **{{ PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__DATE }}**.\n',
              prerequisites: {
                variables: {
                  CIVILITE_MARIE_CAPS: true,
                  PERSONNE__PHYSIQUE__MARIAGE__EPOUX__CIVILITE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__EPOUX__PRENOMS: true,
                  PERSONNE__PHYSIQUE__MARIAGE__EPOUX__NOM: true,
                  PERSONNE__PHYSIQUE__MARIAGE__VILLE_MARIAGE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__PAYS_MARIAGE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__DATE_MARIAGE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__REGIME: true,
                  PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__NOTAIRE__NOM: true,
                  PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__NOTAIRE__VILLE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__DATE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__REGIME: true,
                  PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__NOTAIRE__NOM: true,
                  PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__NOTAIRE__VILLE: true,
                  PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__DATE: true
                },
                conditions: {
                  PERSONNE__PHYSIQUE__MARIAGE__AVEC_CONTRAT_INITIAL_AVEC_CHANGEMENT_REGIME: true
                }
              }
            }
          ],
          prerequisites: {
            conditions: {
              PERSONNE__PHYSIQUE__MARIE: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-texte-1',
          content: 'De nationalité **{{ PERSONNE__PHYSIQUE__NATIONALITE }}**.\n',
          prerequisites: {
            variables: {
              PERSONNE__PHYSIQUE__NATIONALITE: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-si-personne-physique-resident-fiscal',
          condition: 'PERSONNE__PHYSIQUE__RESIDENT_FISCAL',
          content: '{{ PERSONNE_PHYSIQUE_RESIDENT_SEXE_CAPS }} au sens de la réglementation fiscale.\n',
          prerequisites: {
            variables: {
              PERSONNE_PHYSIQUE_RESIDENT_SEXE_CAPS: true
            },
            conditions: {
              PERSONNE__PHYSIQUE__RESIDENT_FISCAL: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-si-non-personne-physique-resident-fiscal',
          condition: '!PERSONNE__PHYSIQUE__RESIDENT_FISCAL',
          content: '**Non {{ PERSONNE_PHYSIQUE_RESIDENT_SEXE }} au sens de la réglementation fiscale.**\n',
          prerequisites: {
            variables: {
              PERSONNE_PHYSIQUE_RESIDENT_SEXE: true
            },
            conditions: {
              PERSONNE__PHYSIQUE__RESIDENT_FISCAL: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-texte-2',
          content:
            'Téléphone : **{{ PERSONNE__PHYSIQUE__TELEPHONE }}**\nEmail : **{{ PERSONNE__PHYSIQUE__EMAIL }}**\n\n',
          prerequisites: {
            variables: {
              PERSONNE__PHYSIQUE__TELEPHONE: true,
              PERSONNE__PHYSIQUE__EMAIL: true
            }
          }
        },
        {
          id: 'si-personne-personne-physique-si-procuration',
          condition: 'PROCURATION',
          prerequisites: {
            conditions: {
              PROCURATION: true
            }
          }
        }
      ],
      prerequisites: {
        conditions: {
          PERSONNE__PERSONNE_PHYSIQUE: true
        }
      }
    },
    {
      id: 'si-personne-personne-morale',
      condition: 'PERSONNE__PERSONNE_MORALE',
      children: [
        {
          id: 'si-personne-personne-morale-si-personne-morale-societe',
          condition: 'PERSONNE_MORALE_SOCIETE',
          content:
            'La société dénommée **{{ PERSONNE__SOCIETE__DENOMINATION }}** **{{ PERSONNE__SOCIETE__FORME_SOCIALE }}** au capital de **{{ PERSONNE__SOCIETE__CAPITAL }}**, dont le siège est à **{{ PERSONNE__SOCIETE__ADRESSE }}**, identifiée au RCS sous le numéro **{{ PERSONNE__SOCIETE__SIREN }}** et immatriculée au Registre du Commerce et des sociétés de **{{ PERSONNE__SOCIETE__VILLE_RCS }}**.\n',
          prerequisites: {
            variables: {
              PERSONNE__SOCIETE__DENOMINATION: true,
              PERSONNE__SOCIETE__FORME_SOCIALE: true,
              PERSONNE__SOCIETE__CAPITAL: true,
              PERSONNE__SOCIETE__ADRESSE: true,
              PERSONNE__SOCIETE__SIREN: true,
              PERSONNE__SOCIETE__VILLE_RCS: true
            },
            conditions: {
              PERSONNE_MORALE_SOCIETE: true
            }
          }
        },
        {
          id: 'si-personne-personne-morale-si-personne-morale-association',
          condition: 'PERSONNE_MORALE_ASSOCIATION',
          content:
            "L'Association dénommée **{{ PERSONNE__SOCIETE__DENOMINATION }}**, dont le siège est à **{{ PERSONNE__SOCIETE__ADRESSE }}**, identifiée au Registre national des association sous le numéro **{{ PERSONNE__SOCIETE__RNA }}** \n",
          prerequisites: {
            variables: {
              PERSONNE__SOCIETE__DENOMINATION: true,
              PERSONNE__SOCIETE__ADRESSE: true,
              PERSONNE__SOCIETE__RNA: true
            },
            conditions: {
              PERSONNE_MORALE_ASSOCIATION: true
            }
          }
        },
        {
          id: 'si-personne-personne-morale-si-personne-morale-commune',
          condition: 'PERSONNE_MORALE_COMMUNE',
          content:
            'La commune de **{{ PERSONNE__SOCIETE__DENOMINATION }}**, dont le siège est à **{{ PERSONNE__SOCIETE__ADRESSE }}**, identifiée au SIREN sous le numéro **{{ PERSONNE__SOCIETE__SIREN }}**.\n',
          prerequisites: {
            variables: {
              PERSONNE__SOCIETE__DENOMINATION: true,
              PERSONNE__SOCIETE__ADRESSE: true,
              PERSONNE__SOCIETE__SIREN: true
            },
            conditions: {
              PERSONNE_MORALE_COMMUNE: true
            }
          }
        },
        {
          id: 'si-personne-personne-morale-liste-representants',
          repetition: {
            source: 'REPRESENTANTS',
            item: 'PERSONNE_MORALE_REPRESENTANT'
          },
          content:
            'Dont le représentant est **{{ PERSONNE_MORALE_REPRESENTANT.CIVILITE }}** **{{ PERSONNE_MORALE_REPRESENTANT.PRENOMS }}** **{{ PERSONNE_MORALE_REPRESENTANT.NOM }}**, demeurant **{{ PERSONNE_MORALE_REPRESENTANT.ADRESSE }}**.\nTéléphone : **{{ PERSONNE_MORALE_REPRESENTANT.TELEPHONE }}**\nEmail : **{{ PERSONNE_MORALE_REPRESENTANT.EMAIL }}**\n',
          prerequisites: {
            repeats: {
              REPRESENTANTS: {
                variables: {
                  'PERSONNE_MORALE_REPRESENTANT.CIVILITE': true,
                  'PERSONNE_MORALE_REPRESENTANT.PRENOMS': true,
                  'PERSONNE_MORALE_REPRESENTANT.NOM': true,
                  'PERSONNE_MORALE_REPRESENTANT.ADRESSE': true,
                  'PERSONNE_MORALE_REPRESENTANT.TELEPHONE': true,
                  'PERSONNE_MORALE_REPRESENTANT.EMAIL': true
                },
                conditions: {},
                repeats: {},
                raws: {}
              }
            }
          }
        },
        {
          id: 'si-personne-personne-morale-si-procuration',
          condition: 'PROCURATION',
          prerequisites: {
            conditions: {
              PROCURATION: true
            }
          }
        }
      ],
      prerequisites: {
        conditions: {
          PERSONNE__PERSONNE_MORALE: true
        }
      }
    }
  ],
  mapping: {
    conditions: {
      PERSONNE__PERSONNE_PHYSIQUE: {
        value: "_.isTemplateOfType(PERSONNE.template.id, ['PERSONNE', 'PHYSIQUE'])",
        dependencies: []
      },
      PERSONNE__PHYSIQUE__CELIBATAIRE: {
        value:
          '!RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS && !RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS && !RAWS.PERSONNE__PHYSIQUE__PACS__INFOS && !RAWS.PERSONNE__PHYSIQUE__VEUVAGE__INFOS',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__INFOS'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__INFOS'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__VEUVAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__TERMINE: {
        value: "answers[RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id].statut.value === 'prononce'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX.id',
            questionId: 'nom'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX.id',
            questionId: 'sexe'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX.id',
            questionId: 'prenoms'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id',
            questionId: 'statut'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__DEVANT_TRIBUNAL: {
        value: "answers[RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id].procedure.value === 'tribunal'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id',
            questionId: 'procedure'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__DEVANT_NOTAIRE: {
        value: "answers[RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id].procedure.value === 'notaire'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id',
            questionId: 'procedure'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__EN_COURS: {
        value: "answers[RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id].statut.value === 'en_cours'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX.id',
            questionId: 'nom'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX.id',
            questionId: 'sexe'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX.id',
            questionId: 'prenoms'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id',
            questionId: 'statut'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__VEUF: {
        value: '!!RAWS.PERSONNE__PHYSIQUE__VEUVAGE__INFOS',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__VEUVAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__PACS__AU_TRIBUNAL: {
        value:
          "!!RAWS.PERSONNE__PHYSIQUE__PACS__INFOS && !RAWS.PERSONNE__PHYSIQUE__PACS__CHEZ_NOTAIRE && !_.isDateAfter(answers[RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id].date_pacs.value, new Date('2017-11-01T00:00:00'))",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id',
            questionId: 'nom'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id',
            questionId: 'prenoms'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id',
            questionId: 'sexe'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__PARTENAIRE'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id',
            questionId: 'date_pacs'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__INFOS'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__CHEZ_NOTAIRE'
          }
        ]
      },
      PERSONNE__PHYSIQUE__PACS__EN_MAIRIE: {
        value:
          "!!RAWS.PERSONNE__PHYSIQUE__PACS__INFOS && !RAWS.PERSONNE__PHYSIQUE__PACS__CHEZ_NOTAIRE && _.isDateAfter(answers[RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id].date_pacs.value, new Date('2017-11-01T00:00:00'))",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id',
            questionId: 'nom'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id',
            questionId: 'prenoms'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id',
            questionId: 'sexe'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__PARTENAIRE'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id',
            questionId: 'date_pacs'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__INFOS'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__CHEZ_NOTAIRE'
          }
        ]
      },
      PERSONNE__PHYSIQUE__PACS__CHEZ_NOTAIRE: {
        value: '!!RAWS.PERSONNE__PHYSIQUE__PACS__INFOS && RAWS.PERSONNE__PHYSIQUE__PACS__CHEZ_NOTAIRE',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id',
            questionId: 'nom'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id',
            questionId: 'prenoms'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id',
            questionId: 'sexe'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__PARTENAIRE'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__INFOS'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__CHEZ_NOTAIRE'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIE: {
        value: '!!RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL_SANS_CHANGEMENT_REGIME: {
        value:
          'RAWS.PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL && RAWS.PERSONNE__PHYSIQUE__MARIAGE__SANS_CHANGEMENT_REGIME',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__SANS_CHANGEMENT_REGIME'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL_AVEC_CHANGEMENT_REGIME: {
        value:
          'RAWS.PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL && !RAWS.PERSONNE__PHYSIQUE__MARIAGE__SANS_CHANGEMENT_REGIME',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__SANS_CHANGEMENT_REGIME'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__AVEC_CONTRAT_INITIAL_SANS_CHANGEMENT_REGIME: {
        value:
          '!RAWS.PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL && RAWS.PERSONNE__PHYSIQUE__MARIAGE__SANS_CHANGEMENT_REGIME',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__SANS_CHANGEMENT_REGIME'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__AVEC_CONTRAT_INITIAL_AVEC_CHANGEMENT_REGIME: {
        value:
          '!RAWS.PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL && !RAWS.PERSONNE__PHYSIQUE__MARIAGE__SANS_CHANGEMENT_REGIME',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__SANS_CHANGEMENT_REGIME'
          }
        ]
      },
      PERSONNE__PHYSIQUE__RESIDENT_FISCAL: {
        value: "answers[PERSONNE.id].informations_personnelles_resident_fiscal.value === 'oui'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'informations_personnelles_resident_fiscal'
          }
        ]
      },
      PROCURATION: {
        value: '!!RAWS.PERSONNE__PROCURATION__INFOS',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE__PROCURATION__INFOS'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PROCURATION__INFOS.id',
            questionId: 'procuration'
          }
        ]
      },
      PERSONNE__PERSONNE_MORALE: {
        value: "_.isTemplateOfType(PERSONNE.template.id, ['PERSONNE', 'MORALE'])",
        dependencies: []
      },
      PERSONNE_MORALE_SOCIETE: {
        value:
          "answers[PERSONNE.id].personne_morale_forme_sociale.value !== 'commune' && answers[PERSONNE.id].personne_morale_forme_sociale.value !== 'association'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_forme_sociale'
          }
        ]
      },
      PERSONNE_MORALE_ASSOCIATION: {
        value: "answers[PERSONNE.id].personne_morale_forme_sociale.value === 'association'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_forme_sociale'
          }
        ]
      },
      PERSONNE_MORALE_COMMUNE: {
        value: "answers[PERSONNE.id].personne_morale_forme_sociale.value === 'commune'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_forme_sociale'
          }
        ]
      }
    },
    variables: {
      PERSONNE__PHYSIQUE__CIVILITE: {
        value: '_.formatCivility(answers[PERSONNE.id].sexe.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'sexe'
          }
        ]
      },
      PERSONNE__PHYSIQUE__PRENOMS: {
        value: 'answers[PERSONNE.id].prenoms.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'prenoms'
          }
        ]
      },
      PERSONNE__PHYSIQUE__NOM: {
        value: 'answers[PERSONNE.id].nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'nom'
          }
        ]
      },
      PERSONNE__PHYSIQUE__PROFESSION: {
        value: 'answers[PERSONNE.id].informations_personnelles_profession.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'informations_personnelles_profession'
          }
        ]
      },
      PERSONNE__PHYSIQUE__ADRESSE: {
        value: 'answers[PERSONNE.id].adresse.value.formattedAddress',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'adresse'
          }
        ]
      },
      CIVILITE_NE: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'Né', 'Née')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      PERSONNE__PHYSIQUE__VILLE_NAISSANCE: {
        value: 'answers[PERSONNE.id].informations_personnelles_ville_naissance.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'informations_personnelles_ville_naissance'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DATE_NAISSANCE: {
        value: '_.formatDate(answers[PERSONNE.id].informations_personnelles_date_naissance.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'informations_personnelles_date_naissance'
          }
        ]
      },
      CIVILITE_LIE: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'lié', 'liée')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      CIVILITE_DIVORCE: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'Divorcé', 'Divorcée')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__CIVILITE: {
        value: '_.formatCivility(answers[RAWS.PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX.id].sexe.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX.id',
            questionId: 'sexe'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__PRENOMS: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX.id].prenoms.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX.id',
            questionId: 'prenoms'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX__NOM: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX.id].nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX.id',
            questionId: 'nom'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__VILLE: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id].tribunal_ville.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id',
            questionId: 'tribunal_ville'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__DATE_TRIBUNAL: {
        value: '_.formatDate(answers[RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id].tribunal_date.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id',
            questionId: 'tribunal_date'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__INFOS'
          }
        ]
      },
      CIVILITE_MARIE: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'marié', 'mariée')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__NOM_NOTAIRE: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id].notaire_nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id',
            questionId: 'notaire_nom'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__VILLE_NOTAIRE: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id].notaire_ville.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id',
            questionId: 'notaire_ville'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__DIVORCE__DATE_NOTAIRE: {
        value: '_.formatDate(answers[RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id].notaire_date.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__DIVORCE__INFOS.id',
            questionId: 'notaire_date'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__DIVORCE__INFOS'
          }
        ]
      },
      CIVILITE_VEUF: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'Veuf', 'Veuve')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT__CIVILITE: {
        value: '_.formatCivility(answers[RAWS.PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT.id].sexe.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT.id',
            questionId: 'sexe'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT'
          }
        ]
      },
      PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT__PRENOMS: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT.id].prenoms.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT.id',
            questionId: 'prenoms'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT'
          }
        ]
      },
      PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT__NOM: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT.id].nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT.id',
            questionId: 'nom'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT'
          }
        ]
      },
      CIVILITE_LIE_CAPS: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'Lié', 'Liée')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      PERSONNE__PHYSIQUE__PACS__PARTENAIRE__CIVILITE: {
        value: '_.formatCivility(answers[RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id].sexe.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id',
            questionId: 'sexe'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__PARTENAIRE'
          }
        ]
      },
      PERSONNE__PHYSIQUE__PACS__PARTENAIRE__PRENOMS: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id].prenoms.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id',
            questionId: 'prenoms'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__PARTENAIRE'
          }
        ]
      },
      PERSONNE__PHYSIQUE__PACS__PARTENAIRE__NOM: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id].nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__PARTENAIRE.id',
            questionId: 'nom'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__PARTENAIRE'
          }
        ]
      },
      PERSONNE__PHYSIQUE__PACS__VILLE: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id].tribunal.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id',
            questionId: 'tribunal'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__PACS__DATE: {
        value: '_.formatDate(answers[RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id].date_pacs.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id',
            questionId: 'date_pacs'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__PACS__MAIRIE: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id].mairie.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id',
            questionId: 'mairie'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__PACS__NOTAIRE__NOM: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id].nom_notaire.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id',
            questionId: 'nom_notaire'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__PACS__NOTAIRE__VILLE: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id].ville_notaire.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id',
            questionId: 'ville_notaire'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__INFOS'
          }
        ]
      },
      CIVILITE_MARIE_CAPS: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'Marié', 'Mariée')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__EPOUX__CIVILITE: {
        value: '_.formatCivility(answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__EPOUX.id].sexe.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__EPOUX.id',
            questionId: 'sexe'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__EPOUX'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__EPOUX__PRENOMS: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__EPOUX.id].prenoms.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__EPOUX.id',
            questionId: 'prenoms'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__EPOUX'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__EPOUX__NOM: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__EPOUX.id].nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__EPOUX.id',
            questionId: 'nom'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__EPOUX'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__VILLE_MARIAGE: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].ville_mariage.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'ville_mariage'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__PAYS_MARIAGE: {
        value: 'staticMetadata.pays[answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].pays_mariage.value].nom',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'pays_mariage'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__DATE_MARIAGE: {
        value: '_.formatDate(answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].date_mariage.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'date_mariage'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__REGIME_COMMUNAUTE: {
        value:
          "_.isDateAfter(answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].date_mariage.value, new Date('1966-02-01T00:00:00')) ? 'réduite aux acquêts' : 'de meubles et acquêts'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'date_mariage'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__REGIME: {
        value:
          "answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].changement_regime_matrimonial.value === 'autre_regime' ? answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].changement_regime_matrimonial_autre.value : staticMetadata.regimeMatrimonial[answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].changement_regime_matrimonial.value].label",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'changement_regime_matrimonial'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'changement_regime_matrimonial_autre'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__NOTAIRE__NOM: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].changement_regime_nom_notaire.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'changement_regime_nom_notaire'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__NOTAIRE__VILLE: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].changement_regime_ville_notaire.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'changement_regime_ville_notaire'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__NOUVEAU_REGIME__DATE: {
        value: '_.formatDate(answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].date_changement_regime.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'date_changement_regime'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__REGIME: {
        value:
          "answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].contrat_regime_matrimonial.value === 'autre_regime' ? answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].contrat_regime_matrimonial_autre.value : staticMetadata.regimeMatrimonial[answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].contrat_regime_matrimonial.value].label",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'contrat_regime_matrimonial'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'contrat_regime_matrimonial_autre'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__NOTAIRE__NOM: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].contrat_mariage_nom_notaire.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'contrat_mariage_nom_notaire'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__NOTAIRE__VILLE: {
        value: 'answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].contrat_mariage_ville_notaire.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'contrat_mariage_ville_notaire'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__MARIAGE__CONTRAT_INITIAL__DATE: {
        value: '_.formatDate(answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].date_contrat_mariage.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'date_contrat_mariage'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      PERSONNE__PHYSIQUE__NATIONALITE: {
        value:
          'staticMetadata.pays[answers[PERSONNE.id].informations_personnelles_nationalite.value].nationalite.femme',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'informations_personnelles_nationalite'
          }
        ]
      },
      PERSONNE_PHYSIQUE_RESIDENT_SEXE_CAPS: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'Résident', 'Résidente')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      PERSONNE_PHYSIQUE_RESIDENT_SEXE: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'résident', 'résidente')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      PERSONNE__PHYSIQUE__TELEPHONE: {
        value: '_.formatPhone(RAWS.PERSONNE__PHYSIQUE__TELEPHONE)',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__TELEPHONE'
          }
        ]
      },
      PERSONNE__PHYSIQUE__EMAIL: {
        value: 'answers[PERSONNE.id].email.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'email'
          }
        ]
      },
      PERSONNE__SOCIETE__DENOMINATION: {
        value: 'answers[PERSONNE.id].personne_morale_denomination.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_denomination'
          }
        ]
      },
      PERSONNE__SOCIETE__FORME_SOCIALE: {
        value:
          "answers[PERSONNE.id].personne_morale_forme_sociale.value !== 'autre' ? {societe_sci: 'Société Civile Immobilière',societe_sarl: 'Société à Responsabilité Limitée',societe_sasu: 'Société par Actions Simplifiée Unipersonnelle',societe_sa: 'Société par Actions',societe_sas: 'Société par Actions Simplifiée',association: 'Association'}[answers[PERSONNE.id].personne_morale_forme_sociale.value] : answers[PERSONNE.id].personne_morale_forme_sociale_autre.value",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_forme_sociale'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_forme_sociale_autre'
          }
        ]
      },
      PERSONNE__SOCIETE__CAPITAL: {
        value: '_.formatPriceCfp(answers[PERSONNE.id].personne_morale_capital_cfp.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_capital_cfp'
          }
        ]
      },
      PERSONNE__SOCIETE__ADRESSE: {
        value: 'answers[PERSONNE.id].adresse.value.formattedAddress',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'adresse'
          }
        ]
      },
      PERSONNE__SOCIETE__SIREN: {
        value: 'answers[PERSONNE.id].personne_morale_immatriculation.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_immatriculation'
          }
        ]
      },
      PERSONNE__SOCIETE__VILLE_RCS: {
        value: 'answers[PERSONNE.id].personne_morale_ville_rcs.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_ville_rcs'
          }
        ]
      },
      PERSONNE__SOCIETE__RNA: {
        value: 'answers[PERSONNE.id].personne_morale_rna.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_rna'
          }
        ]
      }
    },
    repeats: {
      REPRESENTANTS: {
        value: 'RAWS.PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT',
        item: 'REPRESENTANTS',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT'
          }
        ],
        mapping: {
          conditions: {},
          variables: {
            KEY: {
              value: 'REPRESENTANTS.id',
              dependencies: []
            },
            NAME: {
              value: '',
              dependencies: []
            },
            ORDER: {
              value: '',
              dependencies: []
            },
            CIVILITE: {
              value: '_.formatCivility(answers[REPRESENTANTS.id].sexe.value)',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'REPRESENTANTS.id',
                  questionId: 'sexe'
                }
              ]
            },
            PRENOMS: {
              value: 'answers[REPRESENTANTS.id].prenoms.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'REPRESENTANTS.id',
                  questionId: 'prenoms'
                }
              ]
            },
            NOM: {
              value: 'answers[REPRESENTANTS.id].nom.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'REPRESENTANTS.id',
                  questionId: 'nom'
                }
              ]
            },
            ADRESSE: {
              value: 'answers[REPRESENTANTS.id].adresse.value.formattedAddress',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'REPRESENTANTS.id',
                  questionId: 'adresse'
                }
              ]
            },
            TELEPHONE: {
              value: '_.formatPhone(answers[REPRESENTANTS.id].telephone.value)',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'REPRESENTANTS.id',
                  questionId: 'telephone'
                }
              ]
            },
            EMAIL: {
              value: 'answers[REPRESENTANTS.id].email.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'REPRESENTANTS.id',
                  questionId: 'email'
                }
              ]
            }
          },
          repeats: {},
          raws: [
            {
              id: 'INFOS_REPRESENTANT',
              value: 'rawLinks[PERSONNE.id].REPRESENTANT',
              dependencies: []
            }
          ]
        }
      }
    },
    raws: [
      {
        id: 'PERSONNE__PHYSIQUE__TELEPHONE',
        value: 'answers[PERSONNE.id].telephone.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'telephone'
          }
        ]
      },
      {
        id: 'PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT',
        value: 'branches[PERSONNE.id].REPRESENTANT',
        dependencies: []
      },
      {
        id: 'PERSONNE__SOCIETE__REPRESENTATION__INFOS',
        value: 'links[PERSONNE.id].REPRESENTANT[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT_TELEPHONE',
        value: 'answers[RAWS.PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT.id].telephone.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT.id',
            questionId: 'telephone'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT'
          }
        ]
      },
      {
        id: 'PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT_EMAIL',
        value: 'answers[RAWS.PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT.id].email.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT.id',
            questionId: 'email'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__SOCIETE__REPRESENTATION__REPRESENTANT'
          }
        ]
      },
      {
        id: 'PERSONNE__PHYSIQUE__MARIAGE__EPOUX',
        value: 'branches[PERSONNE.id].EPOUX[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS',
        value: 'links[PERSONNE.id].EPOUX[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE__PHYSIQUE__MARIAGE__SANS_CONTRAT_INITIAL',
        value: "answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].contrat_mariage.value === 'non'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'contrat_mariage'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      {
        id: 'PERSONNE__PHYSIQUE__MARIAGE__SANS_CHANGEMENT_REGIME',
        value: "answers[RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id].changement_regime.value === 'non'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__MARIAGE__INFOS.id',
            questionId: 'changement_regime'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__MARIAGE__INFOS'
          }
        ]
      },
      {
        id: 'PERSONNE__PHYSIQUE__DIVORCE__EX_EPOUX',
        value: 'branches[PERSONNE.id].EX_EPOUX[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE__PHYSIQUE__DIVORCE__INFOS',
        value: 'links[PERSONNE.id].EX_EPOUX[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE__PHYSIQUE__PACS__PARTENAIRE',
        value: 'branches[PERSONNE.id].PARTENAIRE[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE__PHYSIQUE__PACS__INFOS',
        value: 'links[PERSONNE.id].PARTENAIRE[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE__PHYSIQUE__PACS__CHEZ_NOTAIRE',
        value: "answers[RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id].notarie.value === 'oui'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'RAWS.PERSONNE__PHYSIQUE__PACS__INFOS.id',
            questionId: 'notarie'
          },
          {
            type: 'RAW',
            rawId: 'PERSONNE__PHYSIQUE__PACS__INFOS'
          }
        ]
      },
      {
        id: 'PERSONNE__PHYSIQUE__VEUVAGE__DEFUNT',
        value: 'branches[PERSONNE.id].DECEDE[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE__PHYSIQUE__VEUVAGE__INFOS',
        value: 'links[PERSONNE.id].DECEDE[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE__PROCURATION__REPRESENTANT',
        value: 'branches[PERSONNE.id].REPRESENTANT_PROCURATION[0]',
        dependencies: []
      },
      {
        id: 'PERSONNE__PROCURATION__INFOS',
        value: 'links[PERSONNE.id].REPRESENTANT_PROCURATION[0]',
        dependencies: []
      },
      {
        id: '',
        value: '',
        dependencies: []
      }
    ]
  },
  metadata: {}
};
