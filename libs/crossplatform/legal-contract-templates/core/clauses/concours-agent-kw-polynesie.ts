// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
export const ConcoursAgentKwPolynesie = {
  children: [
    {
      id: 'si-agent-personne-physique',
      condition: 'AGENT.PERSONNE_PHYSIQUE',
      prerequisites: {
        conditions: {
          'AGENT.PERSONNE_PHYSIQUE': true
        }
      }
    },
    {
      id: 'si-agent-personne-morale',
      condition: 'AGENT.PERSONNE_MORALE',
      content:
        "La société **{{ AGENT.SOCIETE_DENOMINATION }}** au capital de **{{ AGENT.SOCIETE_CAPITAL }}**, RCS de **{{ AGENT.SOCIETE_VILLE_RCS }}** N° **{{ AGENT.SOCIETE_SIREN }}**, exerçant sous l'enseigne **{{ AGENT.SOCIETE_DENOMINATION_COMMERCIALE }}** l’activité d’intermédiaire immobilier, dont le siège social est situé **{{ AGENT.SOCIETE_ADRESSE_SIEGE }}**.\n\nDont le représentant légal est : **{{ AGENCE_REPRESENTANT_CIVILITE }}** **{{ AGENCE_REPRESENTANT_PRENOM }}** **{{ AGENCE_REPRESENTANT_NOM }}**.\n\nTitulaire de la carte professionnelle numéro **{{ AGENCE_CARTE_NUMERO }}**, délivrée le **{{ AGENCE_CARTE_DATE }}** par la Direction Générale des Affaires Économiques de Polynésie Française.\n\nGarantie à hauteur de **{{ AGENCE_ASSURANCE_RCP_MONTANT }}** par sinistre pour sa responsabilité civile Professionnelle par **{{ AGENCE_ASSURANCE_RCP_NOM }}**, sis **{{ AGENCE_ASSURANCE_RCP_ADRESSE }}**, sur le territoire national sous le numéro **{{ AGENCE_ASSURANCE_RCP_POLICE }}**.\n**Le Mandataire n'est pas habilité à détenir des fonds.**\n\n",
      prerequisites: {
        variables: {
          'AGENT.SOCIETE_DENOMINATION': true,
          'AGENT.SOCIETE_CAPITAL': true,
          'AGENT.SOCIETE_VILLE_RCS': true,
          'AGENT.SOCIETE_SIREN': true,
          'AGENT.SOCIETE_DENOMINATION_COMMERCIALE': true,
          'AGENT.SOCIETE_ADRESSE_SIEGE': true,
          'AGENCE_REPRESENTANT_CIVILITE': true,
          'AGENCE_REPRESENTANT_PRENOM': true,
          'AGENCE_REPRESENTANT_NOM': true,
          'AGENCE_CARTE_NUMERO': true,
          'AGENCE_CARTE_DATE': true,
          'AGENCE_ASSURANCE_RCP_MONTANT': true,
          'AGENCE_ASSURANCE_RCP_NOM': true,
          'AGENCE_ASSURANCE_RCP_ADRESSE': true,
          'AGENCE_ASSURANCE_RCP_POLICE': true
        },
        conditions: {
          'AGENT.PERSONNE_MORALE': true
        }
      }
    }
  ],
  mapping: {
    conditions: {
      'AGENT.PERSONNE_PHYSIQUE': {
        value: "_.isTemplateOfType(AGENT.template.id, ['PERSONNE', 'PHYSIQUE', 'AGENT_IMMOBILIER'])",
        dependencies: []
      },
      'AGENT.PERSONNE_MORALE': {
        value: "_.isTemplateOfType(AGENT.template.id, ['PERSONNE', 'MORALE', 'AGENT_IMMOBILIER'])",
        dependencies: []
      }
    },
    variables: {
      'AGENT.SOCIETE_DENOMINATION': {
        value: 'answers[AGENT.id].personne_morale_denomination.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'personne_morale_denomination'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'bareme_honoraires'
          }
        ]
      },
      'AGENT.SOCIETE_CAPITAL': {
        value: '_.formatPriceCfp(answers[AGENT.id].capital_cfp.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'capital_cfp'
          }
        ]
      },
      'AGENT.SOCIETE_VILLE_RCS': {
        value: 'answers[AGENT.id].ville_immatriculation.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'ville_immatriculation'
          }
        ]
      },
      'AGENT.SOCIETE_SIREN': {
        value: 'answers[AGENT.id].immatriculation_numero.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'immatriculation_numero'
          }
        ]
      },
      'AGENT.SOCIETE_DENOMINATION_COMMERCIALE': {
        value: 'answers[AGENT.id].personne_morale_denomination_commerciale.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'personne_morale_denomination_commerciale'
          }
        ]
      },
      'AGENT.SOCIETE_ADRESSE_SIEGE': {
        value: 'answers[AGENT.id].adresse.value.formattedAddress',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'adresse'
          }
        ]
      },
      'AGENCE_REPRESENTANT_CIVILITE': {
        value: '_.formatCivility(answers[AGENT.id].representant_civilite.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'representant_civilite'
          }
        ]
      },
      'AGENCE_REPRESENTANT_PRENOM': {
        value: 'answers[AGENT.id].representant_prenom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'representant_prenom'
          }
        ]
      },
      'AGENCE_REPRESENTANT_NOM': {
        value: 'answers[AGENT.id].representant_nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'representant_nom'
          }
        ]
      },
      'AGENCE_CARTE_NUMERO': {
        value: 'answers[AGENT.id].agence_carte_numero.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'agence_carte_numero'
          }
        ]
      },
      'AGENCE_CARTE_DATE': {
        value: '_.formatDate(answers[AGENT.id].agence_carte_date.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'agence_carte_date'
          }
        ]
      },
      'AGENCE_ASSURANCE_RCP_MONTANT': {
        value: '_.formatPriceCfp(answers[AGENT.id].agence_assurance_rcp_montant_cfp.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'agence_assurance_rcp_montant_cfp'
          }
        ]
      },
      'AGENCE_ASSURANCE_RCP_NOM': {
        value: 'answers[AGENT.id].agence_assurance_rcp_nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'agence_assurance_rcp_nom'
          }
        ]
      },
      'AGENCE_ASSURANCE_RCP_ADRESSE': {
        value: 'answers[AGENT.id].agence_assurance_rcp_adresse.value.formattedAddress',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'agence_assurance_rcp_adresse'
          }
        ]
      },
      'AGENCE_ASSURANCE_RCP_POLICE': {
        value: 'answers[AGENT.id].agence_assurance_rcp_police.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'AGENT.id',
            questionId: 'agence_assurance_rcp_police'
          }
        ]
      }
    },
    repeats: {},
    raws: [
      {
        id: 'REPRESENTANT',
        value: 'branches[AGENT.id].REPRESENTANT[0]',
        dependencies: []
      }
    ]
  },
  metadata: {}
};
