// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
export const ConcoursMandataire = {
  children: [
    {
      id: 'si-representant',
      condition: 'REPRESENTANT',
      content:
        "La représentation de l'agence est assurée par **{{ AGENT.CIVILITE }}** **{{ AGENT.PRENOM }}** **{{ AGENT.NOM }}**, son représentant légal.\n",
      prerequisites: {
        variables: {
          'AGENT.CIVILITE': true,
          'AGENT.PRENOM': true,
          'AGENT.NOM': true
        },
        conditions: {
          REPRESENTANT: true
        }
      }
    },
    {
      id: 'si-agent-co-salarie',
      condition: 'AGENT_CO_SALARIE',
      content:
        "La représentation de l'agence est assurée par **{{ AGENT.CIVILITE }}** **{{ AGENT.PRENOM }}** **{{ AGENT.NOM }}**, agent salari<PERSON>, ayant tout pouvoir à cet effet.\n",
      prerequisites: {
        variables: {
          'AGENT.CIVILITE': true,
          'AGENT.PRENOM': true,
          'AGENT.NOM': true
        },
        conditions: {
          AGENT_CO_SALARIE: true
        }
      }
    },
    {
      id: 'si-agent-co-independant',
      condition: 'AGENT_CO_INDEPENDANT',
      content:
        "L'agence est assistée par **{{ AGENT.CIVILITE }}** **{{ AGENT.PRENOM }}** **{{ AGENT.NOM }}** - EI, agent commercial indépendant, inscrit au Registre Spécial des Agents Commerciaux sous le numéro **{{ AGENT.RSAC }}**, ayant tout pouvoir à cet effet.\n",
      prerequisites: {
        variables: {
          'AGENT.CIVILITE': true,
          'AGENT.PRENOM': true,
          'AGENT.NOM': true,
          'AGENT.RSAC': true
        },
        conditions: {
          AGENT_CO_INDEPENDANT: true
        }
      }
    },
    {
      id: 'si-agent-co-portage',
      condition: 'AGENT_CO_PORTAGE',
      content:
        "L'agence est assistée par **{{ AGENT.CIVILITE }}** **{{ AGENT.PRENOM }}** **{{ AGENT.NOM }}** - sous portage salarial, ayant tout pouvoir à cet effet.\n",
      prerequisites: {
        variables: {
          'AGENT.CIVILITE': true,
          'AGENT.PRENOM': true,
          'AGENT.NOM': true
        },
        conditions: {
          AGENT_CO_PORTAGE: true
        }
      }
    },
    {
      id: 'si-agent-co-structure',
      condition: 'AGENT_CO_STRUCTURE',
      content:
        "L'agence est assistée par **{{ AGENT.CIVILITE }}** **{{ AGENT.PRENOM }}** **{{ AGENT.NOM }}** - Représentant de la société **{{ MANDATAIRE.NOM_SOCIETE }}**, dont le siège social se situe **{{ MANDATAIRE.SIEGE_SOCIAL }}**, titulaire de la carte professionnelle CPI **{{ MANDATAIRE.CARTE_PRO_NUMERO }}**, délivrée par la CCI de **{{ MANDATAIRE.CARTE_PRO_VILLE }}** - inscrit au Registre Spécial des Agents Commerciaux sous le numéro **{{ AGENT.RSAC }}**, ayant tout pouvoir à cet effet.\n",
      prerequisites: {
        variables: {
          'AGENT.CIVILITE': true,
          'AGENT.PRENOM': true,
          'AGENT.NOM': true,
          'MANDATAIRE.NOM_SOCIETE': true,
          'MANDATAIRE.SIEGE_SOCIAL': true,
          'MANDATAIRE.CARTE_PRO_NUMERO': true,
          'MANDATAIRE.CARTE_PRO_VILLE': true,
          'AGENT.RSAC': true
        },
        conditions: {
          AGENT_CO_STRUCTURE: true
        }
      }
    }
  ],
  mapping: {
    conditions: {
      REPRESENTANT: {
        value: "answers[MANDATAIRE.id].intermediaire_statut.value === 'representant'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'MANDATAIRE.id',
            questionId: 'intermediaire_statut'
          }
        ]
      },
      AGENT_CO_SALARIE: {
        value: "answers[MANDATAIRE.id].intermediaire_statut.value === 'agent_co_salarie'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'MANDATAIRE.id',
            questionId: 'intermediaire_statut'
          }
        ]
      },
      AGENT_CO_INDEPENDANT: {
        value: "answers[MANDATAIRE.id].intermediaire_statut.value === 'agent_co_independannt'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'MANDATAIRE.id',
            questionId: 'intermediaire_statut'
          }
        ]
      },
      AGENT_CO_PORTAGE: {
        value: "answers[MANDATAIRE.id].intermediaire_statut.value === 'agent_co_portage'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'MANDATAIRE.id',
            questionId: 'intermediaire_statut'
          }
        ]
      },
      AGENT_CO_STRUCTURE: {
        value: "answers[MANDATAIRE.id].intermediaire_statut.value === 'agent_co_structure'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'MANDATAIRE.id',
            questionId: 'intermediaire_statut'
          }
        ]
      }
    },
    variables: {
      'AGENT.CIVILITE': {
        value: '_.formatCivility(answers[MANDATAIRE.id].sexe.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'MANDATAIRE.id',
            questionId: 'sexe'
          }
        ]
      },
      'AGENT.PRENOM': {
        value: 'answers[MANDATAIRE.id].prenoms.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'MANDATAIRE.id',
            questionId: 'prenoms'
          }
        ]
      },
      'AGENT.NOM': {
        value: 'answers[MANDATAIRE.id].nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'MANDATAIRE.id',
            questionId: 'nom'
          }
        ]
      },
      'AGENT.RSAC': {
        value: 'answers[MANDATAIRE.id].numero_rsac.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'MANDATAIRE.id',
            questionId: 'numero_rsac'
          }
        ]
      },
      'MANDATAIRE.NOM_SOCIETE': {
        value: 'answers[MANDATAIRE.id].mandataire_societe_denomination.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'MANDATAIRE.id',
            questionId: 'mandataire_societe_denomination'
          }
        ]
      },
      'MANDATAIRE.SIEGE_SOCIAL': {
        value: 'answers[MANDATAIRE.id].mandataire_societe_siege.value.formattedAddress',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'MANDATAIRE.id',
            questionId: 'mandataire_societe_siege'
          }
        ]
      },
      'MANDATAIRE.CARTE_PRO_NUMERO': {
        value: 'answers[MANDATAIRE.id].kw_mega_societe_carte.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'MANDATAIRE.id',
            questionId: 'kw_mega_societe_carte'
          }
        ]
      },
      'MANDATAIRE.CARTE_PRO_VILLE': {
        value: 'answers[MANDATAIRE.id].kw_mega_societe_cci.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'MANDATAIRE.id',
            questionId: 'kw_mega_societe_cci'
          }
        ]
      }
    },
    repeats: {},
    raws: []
  },
  metadata: {}
};
