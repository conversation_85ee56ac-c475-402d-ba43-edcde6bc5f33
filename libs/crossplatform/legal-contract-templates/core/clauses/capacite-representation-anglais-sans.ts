// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
export const CapaciteRepresentationAnglaisSans = {
  children: [
    {
      id: 'si-personne-physique',
      condition: 'PERSONNE_PHYSIQUE',
      children: [
        {
          id: 'si-personne-physique-si-non-incapable',
          condition: '!INCAPABLE',
          children: [
            {
              id: 'si-personne-physique-si-non-incapable-si-non-represente',
              condition: '!REPRESENTE',
              content:
                "**{{ CIVILITE }}** **{{ PRENOM }}** **{{ NOM }}** est {{ CIVILITE_PRESENT }} à l'acte.\n_{{ CIVILITE }} {{ PRENOM }} {{ NOM }} is signing by {{ CIVILITE_HIMSELF }}._\n",
              prerequisites: {
                variables: {
                  CIVILITE: true,
                  PRENOM: true,
                  NOM: true,
                  CIVILITE_PRESENT: true,
                  CIVILITE_HIMSELF: true
                },
                conditions: {
                  REPRESENTE: true
                }
              }
            }
          ],
          prerequisites: {
            conditions: {
              INCAPABLE: true
            }
          }
        },
        {
          id: 'si-personne-physique-si-incapable',
          condition: 'INCAPABLE',
          children: [
            {
              id: 'si-personne-physique-si-incapable-liste-capacites',
              repetition: {
                source: 'CAPACITES',
                item: 'CAPACITE'
              },
              children: [
                {
                  id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-sauvegarde-justice',
                  condition: 'CAPACITE.SAUVEGARDE_JUSTICE',
                  children: [
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-sauvegarde-justice-texte',
                      content:
                        '**{{ CAPACITE.CIVILITE }}** **{{ CAPACITE.PRENOM }}** **{{ CAPACITE.NOM }}** fait actuellement l’objet d’une procédure de Sauvegarde de Justice, en vertu d’un jugement rendu par le Juge du contentieux de la protection de **{{ CAPACITE.SAUVEGARDE_JUSTICE_TRIBUNAL }}** en date du **{{ CAPACITE.SAUVEGARDE_JUSTICE_DATE }}**, qui demeure annexé.\n_{{ CAPACITE.PRENOM }} {{ CAPACITE.NOM }} under a judicial safeguard measure_\n',
                      prerequisites: {
                        variables: {
                          'CAPACITE.CIVILITE': true,
                          'CAPACITE.PRENOM': true,
                          'CAPACITE.NOM': true,
                          'CAPACITE.SAUVEGARDE_JUSTICE_TRIBUNAL': true,
                          'CAPACITE.SAUVEGARDE_JUSTICE_DATE': true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-sauvegarde-justice-si-non-represente',
                      condition: '!REPRESENTE',
                      content:
                        "**{{ CAPACITE.CIVILITE }}** **{{ CAPACITE.PRENOM }}** **{{ CAPACITE.NOM }}** est par ailleurs {{ CIVILITE_PRESENT }} à l'acte.\n_{{ CIVILITE }} {{ PRENOM }} {{ NOM }} is signing by {{ CIVILITE_HIMSELF }}._\n",
                      prerequisites: {
                        variables: {
                          'CAPACITE.CIVILITE': true,
                          'CAPACITE.PRENOM': true,
                          'CAPACITE.NOM': true,
                          CIVILITE_PRESENT: true,
                          CIVILITE: true,
                          PRENOM: true,
                          NOM: true,
                          CIVILITE_HIMSELF: true
                        },
                        conditions: {
                          REPRESENTE: true
                        }
                      }
                    }
                  ],
                  prerequisites: {
                    conditions: {
                      'CAPACITE.SAUVEGARDE_JUSTICE': true
                    }
                  }
                },
                {
                  id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-curatelle',
                  condition: 'CAPACITE.CURATELLE',
                  children: [
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-curatelle-texte',
                      content:
                        '**{{ CAPACITE.CIVILITE }}** **{{ CAPACITE.PRENOM }}** **{{ CAPACITE.NOM }}** fait actuellement l’objet d’une procédure de Curatelle, en vertu d’un jugement rendu par le Juge du contentieux de la protection de **{{ CAPACITE.CURATELLE_TRIBUNAL }}** en date du **{{ CAPACITE.CURATELLE_DATE }}**, qui demeure annexé.\n_{{ CAPACITE.PRENOM }} {{ CAPACITE.NOM }} under a curatorship measure_\n\n',
                      prerequisites: {
                        variables: {
                          'CAPACITE.CIVILITE': true,
                          'CAPACITE.PRENOM': true,
                          'CAPACITE.NOM': true,
                          'CAPACITE.CURATELLE_TRIBUNAL': true,
                          'CAPACITE.CURATELLE_DATE': true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-curatelle-liste-capacite-representant',
                      repetition: {
                        source: 'CAPACITE.REPRESENTANT',
                        item: 'REPRESENTANT'
                      },
                      content:
                        'Son assistance est assurée par **{{ REPRESENTANT.CIVILITE }}** **{{ REPRESENTANT.PRENOM }}** **{{ REPRESENTANT.NOM }}**.\n',
                      prerequisites: {
                        repeats: {
                          'CAPACITE.REPRESENTANT': {
                            variables: {
                              'REPRESENTANT.CIVILITE': true,
                              'REPRESENTANT.PRENOM': true,
                              'REPRESENTANT.NOM': true
                            },
                            conditions: {},
                            repeats: {},
                            raws: {}
                          }
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-curatelle-si-non-represente',
                      condition: '!REPRESENTE',
                      content:
                        "  **{{ CAPACITE.CIVILITE }}** **{{ CAPACITE.PRENOM }}** **{{ CAPACITE.NOM }}** est par ailleurs {{ CIVILITE_PRESENT }} à l'acte.\n  _{{ CIVILITE }} {{ PRENOM }} {{ NOM }} is signing by {{ CIVILITE_HIMSELF }}._\n\n  \n",
                      prerequisites: {
                        variables: {
                          'CAPACITE.CIVILITE': true,
                          'CAPACITE.PRENOM': true,
                          'CAPACITE.NOM': true,
                          CIVILITE_PRESENT: true,
                          CIVILITE: true,
                          PRENOM: true,
                          NOM: true,
                          CIVILITE_HIMSELF: true
                        },
                        conditions: {
                          REPRESENTE: true
                        }
                      }
                    }
                  ],
                  prerequisites: {
                    conditions: {
                      'CAPACITE.CURATELLE': true
                    }
                  }
                },
                {
                  id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-emancipation',
                  condition: 'CAPACITE.EMANCIPATION',
                  content:
                    '\n  Il est par ailleurs précisé que **{{ CAPACITE.CIVILITE }}** **{{ CAPACITE.PRENOM }}** **{{ CAPACITE.NOM }}** est {{ CIVILITE_EMANCIPE }}.\n  ',
                  prerequisites: {
                    variables: {
                      'CAPACITE.CIVILITE': true,
                      'CAPACITE.PRENOM': true,
                      'CAPACITE.NOM': true,
                      CIVILITE_EMANCIPE: true
                    },
                    conditions: {
                      'CAPACITE.EMANCIPATION': true
                    }
                  }
                },
                {
                  id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-tutelle',
                  condition: 'CAPACITE.TUTELLE',
                  children: [
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-tutelle-texte',
                      content:
                        '\n  **{{ CAPACITE.CIVILITE }}** **{{ CAPACITE.PRENOM }}** **{{ CAPACITE.NOM }}**, actuellement {{ CIVILITE_SOUMIS }} à un régime de tutelle.\n  _{{ CAPACITE.PRENOM }} {{ CAPACITE.NOM }} under a guardianship measure_\n\n  Sa représentation est assurée par :\n  ',
                      prerequisites: {
                        variables: {
                          'CAPACITE.CIVILITE': true,
                          'CAPACITE.PRENOM': true,
                          'CAPACITE.NOM': true,
                          CIVILITE_SOUMIS: true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-tutelle-liste-capacite-representant',
                      repetition: {
                        source: 'CAPACITE.REPRESENTANT',
                        item: 'REPRESENTANT'
                      },
                      content:
                        '\n  **{{ REPRESENTANT.CIVILITE }}** **{{ REPRESENTANT.PRENOM }}** **{{ REPRESENTANT.NOM }}**.\n  ',
                      prerequisites: {
                        repeats: {
                          'CAPACITE.REPRESENTANT': {
                            variables: {
                              'REPRESENTANT.CIVILITE': true,
                              'REPRESENTANT.PRENOM': true,
                              'REPRESENTANT.NOM': true
                            },
                            conditions: {},
                            repeats: {},
                            raws: {}
                          }
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-tutelle-texte-1',
                      content:
                        '\n  Intervenant aux présentes en qualité de Tuteur, ayant tout pouvoir à cet effet, en vertu d’un jugement rendu par le Juge du contentieux de la protection de **{{ CAPACITE.TUTELLE_TRIBUNAL }}** en date du **{{ CAPACITE.TUTELLE_DATE_JUGEMENT }}**.\n  ',
                      prerequisites: {
                        variables: {
                          'CAPACITE.TUTELLE_TRIBUNAL': true,
                          'CAPACITE.TUTELLE_DATE_JUGEMENT': true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-tutelle-si-capacite-vendeur-tutelle-vente',
                      condition: 'CAPACITE.VENDEUR_TUTELLE_VENTE',
                      content:
                        '\n  **Il est ici précisé que le jugement de placement sous tutelle autorise expressément la vente du Bien désigné ci-dessus.**\n  _the guardianship measure auhtorises the sale_\n  ',
                      prerequisites: {
                        conditions: {
                          'CAPACITE.VENDEUR_TUTELLE_VENTE': true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-tutelle-si-non-capacite-vendeur-tutelle-vente',
                      condition: '!CAPACITE.VENDEUR_TUTELLE_VENTE',
                      children: [
                        {
                          id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-tutelle-si-non-capacite-vendeur-tutelle-vente-si-capacite-vendeur-autorisation-juge',
                          condition: 'CAPACITE.VENDEUR_AUTORISATION_JUGE',
                          content:
                            "\n  **Une ordonnance a été rendue par le juge, autorisant spécialement la vente du Bien désigné ci-dessus, en date du {{ CAPACITE.VENDEUR_DATE_ORDONNANCE }}**.\n  _a special judge's order auhtorises the sale_\n\n  ",
                          prerequisites: {
                            variables: {
                              'CAPACITE.VENDEUR_DATE_ORDONNANCE': true
                            },
                            conditions: {
                              'CAPACITE.VENDEUR_AUTORISATION_JUGE': true
                            }
                          }
                        }
                      ],
                      prerequisites: {
                        conditions: {
                          'CAPACITE.VENDEUR_TUTELLE_VENTE': true
                        }
                      }
                    }
                  ],
                  prerequisites: {
                    conditions: {
                      'CAPACITE.TUTELLE': true
                    }
                  }
                },
                {
                  id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-mandat-protection-future',
                  condition: 'CAPACITE.MANDAT_PROTECTION_FUTURE',
                  children: [
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-mandat-protection-future-texte',
                      content:
                        '\n  **{{ CAPACITE.CIVILITE }}** **{{ CAPACITE.PRENOM }}** **{{ CAPACITE.NOM }}** est {{ CIVILITE_SOUMIS }} à un Mandat de protection future.\n  Sa représentation est assurée par :\n  ',
                      prerequisites: {
                        variables: {
                          'CAPACITE.CIVILITE': true,
                          'CAPACITE.PRENOM': true,
                          'CAPACITE.NOM': true,
                          CIVILITE_SOUMIS: true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-mandat-protection-future-liste-capacite-representant',
                      repetition: {
                        source: 'CAPACITE.REPRESENTANT',
                        item: 'REPRESENTANT'
                      },
                      content:
                        '\n  **{{ REPRESENTANT.CIVILITE }}** **{{ REPRESENTANT.PRENOM }}** **{{ REPRESENTANT.NOM }}**.\n',
                      prerequisites: {
                        repeats: {
                          'CAPACITE.REPRESENTANT': {
                            variables: {
                              'REPRESENTANT.CIVILITE': true,
                              'REPRESENTANT.PRENOM': true,
                              'REPRESENTANT.NOM': true
                            },
                            conditions: {},
                            repeats: {},
                            raws: {}
                          }
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-mandat-protection-future-si-capacite-mandat-protection-future-notaire',
                      condition: 'CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE',
                      content:
                        '\n  Intervenant aux présentes en qualité de représentant, aux termes d’un mandat de protection future reçu par **Maître {{ CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE_NOM }}**, notaire à {{ CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE_VILLE }}, en date du **{{ CAPACITE.MANDAT_PROTECTION_FUTURE_DATE_MANDAT }}**, qui demeure annexé.\n  ',
                      prerequisites: {
                        variables: {
                          'CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE_NOM': true,
                          'CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE_VILLE': true,
                          'CAPACITE.MANDAT_PROTECTION_FUTURE_DATE_MANDAT': true
                        },
                        conditions: {
                          'CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE': true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-mandat-protection-future-si-non-capacite-mandat-protection-future-notaire',
                      condition: '!CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE',
                      content:
                        '\n  Intervenant aux présentes en qualité de représentant, aux termes d’un mandat de protection future conclu sous seing privé en date du **{{ CAPACITE.MANDAT_PROTECTION_FUTURE_DATE_MANDAT }}**, qui demeure annexé.\n  ',
                      prerequisites: {
                        variables: {
                          'CAPACITE.MANDAT_PROTECTION_FUTURE_DATE_MANDAT': true
                        },
                        conditions: {
                          'CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE': true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-mandat-protection-future-si-capacite-mandat-protection-future-notaire-1',
                      condition: 'CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE',
                      children: [
                        {
                          id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-mandat-protection-future-si-capacite-mandat-protection-future-notaire-1-si-capacite-mandat-protection-future-notaire-vente',
                          condition: 'CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE_VENTE',
                          content:
                            '\n  **Il est ici précisé que le mandat de protection future autorise expressément la vente du Bien désigné ci-dessus.**\n  ',
                          prerequisites: {
                            conditions: {
                              'CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE_VENTE': true
                            }
                          }
                        },
                        {
                          id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-mandat-protection-future-si-capacite-mandat-protection-future-notaire-1-si-non-capacite-mandat-protection-future-notaire-vente',
                          condition: '!CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE_VENTE',
                          children: [
                            {
                              id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-mandat-protection-future-si-capacite-mandat-protection-future-notaire-1-si-non-capacite-mandat-protection-future-notaire-vente-si-capacite-vendeur-autorisation-juge',
                              condition: 'CAPACITE.VENDEUR_AUTORISATION_JUGE',
                              content:
                                '\n  **Une ordonnance a été rendue par le juge, autorisant spécialement la vente du Bien désigné ci-dessus, en date du {{ CAPACITE.VENDEUR_DATE_ORDONNANCE }}**.\n  ',
                              prerequisites: {
                                variables: {
                                  'CAPACITE.VENDEUR_DATE_ORDONNANCE': true
                                },
                                conditions: {
                                  'CAPACITE.VENDEUR_AUTORISATION_JUGE': true
                                }
                              }
                            }
                          ],
                          prerequisites: {
                            conditions: {
                              'CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE_VENTE': true
                            }
                          }
                        }
                      ],
                      prerequisites: {
                        conditions: {
                          'CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE': true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-mandat-protection-future-si-non-capacite-mandat-protection-future-notaire-1',
                      condition: '!CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE',
                      children: [
                        {
                          id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-mandat-protection-future-si-non-capacite-mandat-protection-future-notaire-1-si-capacite-vendeur-autorisation-juge',
                          condition: 'CAPACITE.VENDEUR_AUTORISATION_JUGE',
                          content:
                            '**Une ordonnance a été rendue par le juge, autorisant spécialement la vente du Bien désigné ci-dessus, en date du {{ CAPACITE.VENDEUR_DATE_ORDONNANCE }}**.\n',
                          prerequisites: {
                            variables: {
                              'CAPACITE.VENDEUR_DATE_ORDONNANCE': true
                            },
                            conditions: {
                              'CAPACITE.VENDEUR_AUTORISATION_JUGE': true
                            }
                          }
                        }
                      ],
                      prerequisites: {
                        conditions: {
                          'CAPACITE.MANDAT_PROTECTION_FUTURE_NOTAIRE': true
                        }
                      }
                    }
                  ],
                  prerequisites: {
                    conditions: {
                      'CAPACITE.MANDAT_PROTECTION_FUTURE': true
                    }
                  }
                },
                {
                  id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-habilitation-familiale',
                  condition: 'CAPACITE.HABILITATION_FAMILIALE',
                  children: [
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-habilitation-familiale-texte',
                      content:
                        "**{{ CAPACITE.CIVILITE }}** **{{ CAPACITE.PRENOM }}** **{{ CAPACITE.NOM }}** est {{ CIVILITE_SOUMIS }} à une procédure d'Habilitation Familiale.\nSa représentation est assurée par :\n",
                      prerequisites: {
                        variables: {
                          'CAPACITE.CIVILITE': true,
                          'CAPACITE.PRENOM': true,
                          'CAPACITE.NOM': true,
                          CIVILITE_SOUMIS: true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-habilitation-familiale-liste-capacite-representant',
                      repetition: {
                        source: 'CAPACITE.REPRESENTANT',
                        item: 'REPRESENTANT'
                      },
                      content:
                        '**{{ REPRESENTANT.CIVILITE }}** **{{ REPRESENTANT.PRENOM }}** **{{ REPRESENTANT.NOM }}**.\n  \n',
                      prerequisites: {
                        repeats: {
                          'CAPACITE.REPRESENTANT': {
                            variables: {
                              'REPRESENTANT.CIVILITE': true,
                              'REPRESENTANT.PRENOM': true,
                              'REPRESENTANT.NOM': true
                            },
                            conditions: {},
                            repeats: {},
                            raws: {}
                          }
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-habilitation-familiale-texte-1',
                      content:
                        '  Intervenant aux présentes en qualité de représentant et ayant tout pouvoir à cet effet, en vertu d’un jugement rendu par le Juge du contentieux de la protection de **{{ CAPACITE.HABILITATION_FAMILIALE.TRIBUNAL }}**, en date du **{{ CAPACITE.HABILITATION_FAMILIALE.DATE_JUGEMENT }}**, qui demeure annexé.\n  ',
                      prerequisites: {
                        variables: {
                          'CAPACITE.HABILITATION_FAMILIALE.TRIBUNAL': true,
                          'CAPACITE.HABILITATION_FAMILIALE.DATE_JUGEMENT': true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-habilitation-familiale-si-capacite-vendeur-habilitation-vente',
                      condition: 'CAPACITE.VENDEUR_HABILITATION_VENTE',
                      content:
                        '\n  **Il est ici précisé que le jugement de placement sous habilitation familiale autorise expressément la vente du Bien désigné ci-dessus.**\n  ',
                      prerequisites: {
                        conditions: {
                          'CAPACITE.VENDEUR_HABILITATION_VENTE': true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-habilitation-familiale-si-non-capacite-vendeur-habilitation-vente',
                      condition: '!CAPACITE.VENDEUR_HABILITATION_VENTE',
                      children: [
                        {
                          id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-habilitation-familiale-si-non-capacite-vendeur-habilitation-vente-si-capacite-vendeur-autorisation-juge',
                          condition: 'CAPACITE.VENDEUR_AUTORISATION_JUGE',
                          content:
                            '\n  **Une ordonnance a été rendue par le juge, autorisant spécialement la vente du Bien désigné ci-dessus, en date du {{ CAPACITE.VENDEUR_DATE_ORDONNANCE }}**.\n  ',
                          prerequisites: {
                            variables: {
                              'CAPACITE.VENDEUR_DATE_ORDONNANCE': true
                            },
                            conditions: {
                              'CAPACITE.VENDEUR_AUTORISATION_JUGE': true
                            }
                          }
                        }
                      ],
                      prerequisites: {
                        conditions: {
                          'CAPACITE.VENDEUR_HABILITATION_VENTE': true
                        }
                      }
                    }
                  ],
                  prerequisites: {
                    conditions: {
                      'CAPACITE.HABILITATION_FAMILIALE': true
                    }
                  }
                },
                {
                  id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-minorite',
                  condition: 'CAPACITE.MINORITE',
                  children: [
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-minorite-texte',
                      content:
                        '\n  **{{ CAPACITE.CIVILITE }}** **{{ CAPACITE.PRENOM }}** **{{ CAPACITE.NOM }}**, {{ CIVILITE_MINEUR }}, est {{ CIVILITE_REPRESENTE }} par :\n  ',
                      prerequisites: {
                        variables: {
                          'CAPACITE.CIVILITE': true,
                          'CAPACITE.PRENOM': true,
                          'CAPACITE.NOM': true,
                          CIVILITE_MINEUR: true,
                          CIVILITE_REPRESENTE: true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-minorite-si-capacite-minorite-legal',
                      condition: 'CAPACITE.MINORITE_LEGAL',
                      children: [
                        {
                          id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-minorite-si-capacite-minorite-legal-liste-capacite-representant',
                          repetition: {
                            source: 'CAPACITE.REPRESENTANT',
                            item: 'REPRESENTANT'
                          },
                          content:
                            '\n  **{{ REPRESENTANT.CIVILITE }}** **{{ REPRESENTANT.PRENOM }}** **{{ REPRESENTANT.NOM }}**.\n',
                          prerequisites: {
                            repeats: {
                              'CAPACITE.REPRESENTANT': {
                                variables: {
                                  'REPRESENTANT.CIVILITE': true,
                                  'REPRESENTANT.PRENOM': true,
                                  'REPRESENTANT.NOM': true
                                },
                                conditions: {},
                                repeats: {},
                                raws: {}
                              }
                            }
                          }
                        },
                        {
                          id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-minorite-si-capacite-minorite-legal-texte-1',
                          content:
                            '  Intervenants aux présentes en leur qualité de parents et représentants légaux.\n  '
                        }
                      ],
                      prerequisites: {
                        conditions: {
                          'CAPACITE.MINORITE_LEGAL': true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-minorite-si-capacite-minorite-controlee',
                      condition: 'CAPACITE.MINORITE_CONTROLEE',
                      children: [
                        {
                          id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-minorite-si-capacite-minorite-controlee-liste-capacite-representant',
                          repetition: {
                            source: 'CAPACITE.REPRESENTANT',
                            item: 'REPRESENTANT'
                          },
                          content:
                            '\n  **{{ REPRESENTANT.CIVILITE }}** **{{ REPRESENTANT.PRENOM }}** **{{ REPRESENTANT.NOM }}**.\n  ',
                          prerequisites: {
                            repeats: {
                              'CAPACITE.REPRESENTANT': {
                                variables: {
                                  'REPRESENTANT.CIVILITE': true,
                                  'REPRESENTANT.PRENOM': true,
                                  'REPRESENTANT.NOM': true
                                },
                                conditions: {},
                                repeats: {},
                                raws: {}
                              }
                            }
                          }
                        },
                        {
                          id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-minorite-si-capacite-minorite-controlee-texte-1',
                          content: '\n  Intervenant aux présentes en sa qualité de parent et représentant légal.\n  '
                        }
                      ],
                      prerequisites: {
                        conditions: {
                          'CAPACITE.MINORITE_CONTROLEE': true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-minorite-si-capacite-minorite-tutelle',
                      condition: 'CAPACITE.MINORITE_TUTELLE',
                      children: [
                        {
                          id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-minorite-si-capacite-minorite-tutelle-liste-capacite-representant',
                          repetition: {
                            source: 'CAPACITE.REPRESENTANT',
                            item: 'REPRESENTANT'
                          },
                          content:
                            '\n  **{{ REPRESENTANT.CIVILITE }}** **{{ REPRESENTANT.PRENOM }}** **{{ REPRESENTANT.NOM }}**.\n\n  ',
                          prerequisites: {
                            repeats: {
                              'CAPACITE.REPRESENTANT': {
                                variables: {
                                  'REPRESENTANT.CIVILITE': true,
                                  'REPRESENTANT.PRENOM': true,
                                  'REPRESENTANT.NOM': true
                                },
                                conditions: {},
                                repeats: {},
                                raws: {}
                              }
                            }
                          }
                        },
                        {
                          id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-minorite-si-capacite-minorite-tutelle-texte-1',
                          content:
                            '\n  Intervenant aux présentes en sa qualité de tuteur, spécialement désigné à cet effet en vertu d’un jugement rendu par le Juge du contentieux de la protection de **{{ CAPACITE.MINORITE_TUTELLE_TRIBUNAL }}** en date du **{{ CAPACITE.MINORITE_TUTELLE_DATE }}**, qui demeure annexé.\n  ',
                          prerequisites: {
                            variables: {
                              'CAPACITE.MINORITE_TUTELLE_TRIBUNAL': true,
                              'CAPACITE.MINORITE_TUTELLE_DATE': true
                            }
                          }
                        }
                      ],
                      prerequisites: {
                        conditions: {
                          'CAPACITE.MINORITE_TUTELLE': true
                        }
                      }
                    },
                    {
                      id: 'si-personne-physique-si-incapable-liste-capacites-si-capacite-minorite-si-capacite-vendeur-autorisation-juge',
                      condition: 'CAPACITE.VENDEUR_AUTORISATION_JUGE',
                      content:
                        '\n  **Une ordonnance a été rendue par le juge, autorisant spécialement la vente du Bien désigné ci-dessus, en date du {{ CAPACITE.VENDEUR_DATE_ORDONNANCE }}**.\n  ',
                      prerequisites: {
                        variables: {
                          'CAPACITE.VENDEUR_DATE_ORDONNANCE': true
                        },
                        conditions: {
                          'CAPACITE.VENDEUR_AUTORISATION_JUGE': true
                        }
                      }
                    }
                  ],
                  prerequisites: {
                    conditions: {
                      'CAPACITE.MINORITE': true
                    }
                  }
                }
              ],
              prerequisites: {
                repeats: {
                  CAPACITES: {
                    variables: {},
                    conditions: {},
                    repeats: {},
                    raws: {}
                  }
                }
              }
            }
          ],
          prerequisites: {
            conditions: {
              INCAPABLE: true
            }
          }
        },
        {
          id: 'si-personne-physique-si-represente',
          condition: 'REPRESENTE',
          children: [
            {
              id: 'si-personne-physique-si-represente-liste-representations',
              repetition: {
                source: 'REPRESENTATIONS',
                item: 'REPRESENTATION'
              },
              children: [
                {
                  id: 'si-personne-physique-si-represente-liste-representations-texte',
                  content:
                    "\n  **{{ REPRESENTATION.CIVILITE }}** **{{ REPRESENTATION.PRENOM }}** **{{ REPRESENTATION.NOM }}** n'est pas {{ REPRESENTATION.CIVILITE_PRESENT }} à l'acte.\n  _{{ REPRESENTATION.CIVILITE }} {{ REPRESENTATION.PRENOM }} {{ REPRESENTATION.NOM }} is not signing by {{ CIVILITE_HIMSELF }}._\n  ",
                  prerequisites: {
                    variables: {
                      'REPRESENTATION.CIVILITE': true,
                      'REPRESENTATION.PRENOM': true,
                      'REPRESENTATION.NOM': true,
                      'REPRESENTATION.CIVILITE_PRESENT': true,
                      CIVILITE_HIMSELF: true
                    }
                  }
                },
                {
                  id: 'si-personne-physique-si-represente-liste-representations-liste-representation-representant',
                  repetition: {
                    source: 'REPRESENTATION.REPRESENTANT',
                    item: 'REPRESENTANT'
                  },
                  content:
                    '\n  Sa représentation est assurée par **{{ REPRESENTANT.CIVILITE }}** **{{ REPRESENTANT.PRENOM }}** **{{ REPRESENTANT.NOM }}**.\n  _Power of attorney is given to {{ REPRESENTANT.CIVILITE }} {{ REPRESENTANT.PRENOM }} {{ REPRESENTANT.NOM }}._\n\n',
                  prerequisites: {
                    repeats: {
                      'REPRESENTATION.REPRESENTANT': {
                        variables: {
                          'REPRESENTANT.CIVILITE': true,
                          'REPRESENTANT.PRENOM': true,
                          'REPRESENTANT.NOM': true
                        },
                        conditions: {},
                        repeats: {},
                        raws: {}
                      }
                    }
                  }
                }
              ],
              prerequisites: {
                repeats: {
                  REPRESENTATIONS: {
                    variables: {},
                    conditions: {},
                    repeats: {},
                    raws: {}
                  }
                }
              }
            }
          ],
          prerequisites: {
            conditions: {
              REPRESENTE: true
            }
          }
        }
      ],
      prerequisites: {
        conditions: {
          PERSONNE_PHYSIQUE: true
        }
      }
    },
    {
      id: 'si-personne-morale',
      condition: 'PERSONNE_MORALE',
      children: [
        {
          id: 'si-personne-morale-si-non-represente',
          condition: '!REPRESENTE',
          content:
            "\n{{ SOCIETE.TYPE_ARTICLE }} **{{ SOCIETE.DENOMINATION }}** est représentée à l'acte comme exposé ci-dessus.\n_The company is represented by its legal representative_\n  ",
          prerequisites: {
            variables: {
              'SOCIETE.TYPE_ARTICLE': true,
              'SOCIETE.DENOMINATION': true
            },
            conditions: {
              REPRESENTE: true
            }
          }
        },
        {
          id: 'si-personne-morale-si-represente',
          condition: 'REPRESENTE',
          children: [
            {
              id: 'si-personne-morale-si-represente-liste-representations',
              repetition: {
                source: 'REPRESENTATIONS',
                item: 'REPRESENTATION'
              },
              children: [
                {
                  id: 'si-personne-morale-si-represente-liste-representations-liste-representation-representant',
                  repetition: {
                    source: 'REPRESENTATION.REPRESENTANT',
                    item: 'REPRESENTANT'
                  },
                  content:
                    "\n{{ SOCIETE.TYPE_ARTICLE }} **{{ SOCIETE.DENOMINATION }}** est représentée par -_The company is represented by_- **{{ REPRESENTANT.CIVILITE }}** **{{ REPRESENTANT.PRENOM }}** **{{ REPRESENTANT.NOM }}** en vertu d'un pouvoir qui est annexé au contrat.\n\n  ",
                  prerequisites: {
                    repeats: {
                      'REPRESENTATION.REPRESENTANT': {
                        variables: {
                          'SOCIETE.TYPE_ARTICLE': true,
                          'SOCIETE.DENOMINATION': true,
                          'REPRESENTANT.CIVILITE': true,
                          'REPRESENTANT.PRENOM': true,
                          'REPRESENTANT.NOM': true
                        },
                        conditions: {},
                        repeats: {},
                        raws: {}
                      }
                    }
                  }
                }
              ],
              prerequisites: {
                repeats: {
                  REPRESENTATIONS: {
                    variables: {},
                    conditions: {},
                    repeats: {},
                    raws: {}
                  }
                }
              }
            }
          ],
          prerequisites: {
            conditions: {
              REPRESENTE: true
            }
          }
        }
      ],
      prerequisites: {
        conditions: {
          PERSONNE_MORALE: true
        }
      }
    }
  ],
  mapping: {
    conditions: {
      PERSONNE_PHYSIQUE: {
        value: "_.isTemplateOfType(PERSONNE.template.id, ['PERSONNE', 'PHYSIQUE'])",
        dependencies: []
      },
      INCAPABLE: {
        value: '_.size(RAWS.CAPACITE_TOTAL) > 0',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'CAPACITE_TOTAL'
          }
        ]
      },
      REPRESENTE: {
        value: '_.size(RAWS.REPRESENTATION) > 0',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'REPRESENTATION'
          }
        ]
      },
      PERSONNE_MORALE: {
        value: "_.isTemplateOfType(PERSONNE.template.id, ['PERSONNE', 'MORALE'])",
        dependencies: []
      }
    },
    variables: {
      CIVILITE: {
        value: '_.formatCivility(answers[PERSONNE.id].sexe.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'sexe'
          }
        ]
      },
      PRENOM: {
        value: 'answers[PERSONNE.id].prenoms.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'prenoms'
          }
        ]
      },
      NOM: {
        value: 'answers[PERSONNE.id].nom.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'nom'
          }
        ]
      },
      CIVILITE_PRESENT: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'présent', 'présente')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      CIVILITE_HIMSELF: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'himself', 'herself')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      CIVILITE_EMANCIPE: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'émancipé', 'émancipée')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      CIVILITE_SOUMIS: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'soumis', 'soumise')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      CIVILITE_MINEUR: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'mineur', 'mineur')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      CIVILITE_REPRESENTE: {
        value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'représenté', 'représentée')",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id'
          }
        ]
      },
      'SOCIETE.TYPE_ARTICLE': {
        value:
          '({societe_sci:"La société",societe_sarl:"Société à Responsabilité Limitée",societe_sa:"La société",societe_sas:"La société",societe_sasu:"La société",commune:"La commune de",association:"L\'association",autre:"La société"})[answers[PERSONNE.id].personne_morale_forme_sociale.value]',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_forme_sociale'
          }
        ]
      },
      'SOCIETE.DENOMINATION': {
        value: 'answers[PERSONNE.id].personne_morale_denomination.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'PERSONNE.id',
            questionId: 'personne_morale_denomination'
          }
        ]
      }
    },
    repeats: {
      CAPACITES: {
        value: 'RAWS.CAPACITE_TOTAL',
        item: 'CAPACITE',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'CAPACITE_TOTAL'
          }
        ],
        mapping: {
          conditions: {
            SAUVEGARDE_JUSTICE: {
              value: "_.isTemplateOfType(CAPACITE.template.id, ['CAPACITE', 'SAUVEGARDE_JUSTICE'])",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  questionId: 'PERSONNE.id'
                },
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'jugement_sauvegarde'
                }
              ]
            },
            CURATELLE: {
              value: "_.isTemplateOfType(CAPACITE.template.id, ['CAPACITE', 'CURATELLE'])",
              dependencies: []
            },
            EMANCIPATION: {
              value: "_.isTemplateOfType(CAPACITE.template.id, ['CAPACITE', 'EMANCIPATION'])",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'jugement_emancipation'
                }
              ]
            },
            TUTELLE: {
              value: "_.isTemplateOfType(CAPACITE.template.id, ['CAPACITE', 'TUTELLE'])",
              dependencies: []
            },
            VENDEUR_TUTELLE_VENTE: {
              value: "answers[extensions[CAPACITE.record.id].id].tutelle_vente.value === 'oui'",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'extensions[CAPACITE.record.id].id',
                  questionId: 'tutelle_vente'
                }
              ]
            },
            VENDEUR_AUTORISATION_JUGE: {
              value: "answers[extensions[CAPACITE.record.id].id].ordonnance_autorisation.value === 'oui'",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'extensions[CAPACITE.record.id].id',
                  questionId: 'ordonnance_autorisation'
                },
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'extensions[CAPACITE.record.id].id',
                  questionId: 'ordonnance_autorisation_document'
                }
              ]
            },
            MANDAT_PROTECTION_FUTURE: {
              value: "_.isTemplateOfType(CAPACITE.template.id, ['CAPACITE', 'MANDAT_PROTECTION_FUTURE'])",
              dependencies: []
            },
            MANDAT_PROTECTION_FUTURE_NOTAIRE: {
              value: "answers[CAPACITE.record.id].notaire_statut.value === 'oui'",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'notaire_statut'
                }
              ]
            },
            MANDAT_PROTECTION_FUTURE_NOTAIRE_VENTE: {
              value: "answers[extensions[CAPACITE.record.id].id].mandat_vente.value === 'oui'",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'extensions[CAPACITE.record.id].id',
                  questionId: 'mandat_vente'
                }
              ]
            },
            HABILITATION_FAMILIALE: {
              value: "_.isTemplateOfType(CAPACITE.template.id, ['CAPACITE', 'HABILITATION_FAMILIALE'])",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'jugement_habilitation'
                }
              ]
            },
            VENDEUR_HABILITATION_VENTE: {
              value: "answers[extensions[CAPACITE.record.id].id].habilitation_vente.value === 'oui'",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'extensions[CAPACITE.record.id].id',
                  questionId: 'habilitation_vente'
                }
              ]
            },
            MINORITE: {
              value: "_.isTemplateOfType(CAPACITE.template.id, ['CAPACITE', 'MINORITE'])",
              dependencies: []
            },
            MINORITE_LEGAL: {
              value: "answers[CAPACITE.record.id].statut.value.includes('administration_legale')",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'statut'
                }
              ]
            },
            MINORITE_CONTROLEE: {
              value: "answers[CAPACITE.record.id].statut.value.includes('administration_controlee')",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'statut'
                }
              ]
            },
            MINORITE_TUTELLE: {
              value: "answers[CAPACITE.record.id].statut.value.includes('administration_tutelle')",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'statut'
                },
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'jugement_minorite_tuteur'
                }
              ]
            }
          },
          variables: {
            KEY: {
              value: 'CAPACITE.id',
              dependencies: []
            },
            NAME: {
              value: '',
              dependencies: []
            },
            ORDER: {
              value: '',
              dependencies: []
            },
            CIVILITE: {
              value: '_.formatCivility(answers[PERSONNE.id].sexe.value)',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'PERSONNE.id',
                  questionId: 'sexe'
                }
              ]
            },
            PRENOM: {
              value: 'answers[PERSONNE.id].prenoms.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'PERSONNE.id',
                  questionId: 'prenoms'
                }
              ]
            },
            NOM: {
              value: 'answers[PERSONNE.id].nom.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'PERSONNE.id',
                  questionId: 'nom'
                }
              ]
            },
            SAUVEGARDE_JUSTICE_TRIBUNAL: {
              value: 'answers[CAPACITE.record.id].tribunal.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'tribunal'
                }
              ]
            },
            SAUVEGARDE_JUSTICE_DATE: {
              value: '_.formatDate(answers[CAPACITE.record.id].date.value)',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'date'
                },
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'jugement_sauvegarde'
                }
              ]
            },
            CURATELLE_TRIBUNAL: {
              value: 'answers[CAPACITE.record.id].tribunal.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'tribunal'
                },
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'jugement_curatelle'
                }
              ]
            },
            CURATELLE_DATE: {
              value: '_.formatDate(answers[CAPACITE.record.id].date.value)',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'date'
                }
              ]
            },
            TUTELLE_TRIBUNAL: {
              value: 'answers[CAPACITE.record.id].tribunal.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'tribunal'
                },
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'jugement_tutelle'
                }
              ]
            },
            TUTELLE_DATE_JUGEMENT: {
              value: '_.formatDate(answers[CAPACITE.record.id].date.value)',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'date'
                }
              ]
            },
            VENDEUR_DATE_ORDONNANCE: {
              value: '_.formatDate(answers[extensions[CAPACITE.record.id].id].ordonnance_autorisation_juge.value)',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'extensions[CAPACITE.record.id].id',
                  questionId: 'ordonnance_autorisation_juge'
                }
              ]
            },
            MANDAT_PROTECTION_FUTURE_NOTAIRE_NOM: {
              value: 'answers[CAPACITE.record.id].notaire_nom.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'notaire_nom'
                }
              ]
            },
            MANDAT_PROTECTION_FUTURE_NOTAIRE_VILLE: {
              value: 'answers[CAPACITE.record.id].notaire_ville.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'notaire_ville'
                }
              ]
            },
            MANDAT_PROTECTION_FUTURE_DATE_MANDAT: {
              value: '_.formatDate(answers[CAPACITE.record.id].date.value)',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'date'
                },
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'mandat_de_protection_future'
                }
              ]
            },
            'HABILITATION_FAMILIALE.TRIBUNAL': {
              value: 'answers[CAPACITE.record.id].tribunal.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'tribunal'
                }
              ]
            },
            'HABILITATION_FAMILIALE.DATE_JUGEMENT': {
              value: '_.formatDate(answers[CAPACITE.record.id].date.value)',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'date'
                }
              ]
            },
            MINORITE_TUTELLE_TRIBUNAL: {
              value: 'answers[CAPACITE.record.id].tribunal.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'tribunal'
                }
              ]
            },
            MINORITE_TUTELLE_DATE: {
              value: '_.formatDate(answers[CAPACITE.record.id].date.value)',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'CAPACITE.record.id',
                  questionId: 'date'
                }
              ]
            }
          },
          repeats: {
            REPRESENTANT: {
              value: "_.getLinkBranchesTo(CAPACITE.id, 'TUTEUR')",
              item: 'REPRESENTANT',
              dependencies: [],
              mapping: {
                conditions: {},
                variables: {
                  KEY: {
                    value: 'REPRESENTANT.id',
                    dependencies: []
                  },
                  NAME: {
                    value: '',
                    dependencies: []
                  },
                  ORDER: {
                    value: '',
                    dependencies: []
                  },
                  CIVILITE: {
                    value: '_.formatCivility(answers[REPRESENTANT.id].sexe.value)',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'REPRESENTANT.id',
                        questionId: 'sexe'
                      }
                    ]
                  },
                  PRENOM: {
                    value: 'answers[REPRESENTANT.id].prenoms.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'REPRESENTANT.id',
                        questionId: 'prenoms'
                      }
                    ]
                  },
                  NOM: {
                    value: 'answers[REPRESENTANT.id].nom.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'REPRESENTANT.id',
                        questionId: 'nom'
                      }
                    ]
                  }
                },
                repeats: {},
                raws: []
              }
            }
          },
          raws: []
        }
      },
      REPRESENTATIONS: {
        value: 'RAWS.REPRESENTATION',
        item: 'REPRESENTATION',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'REPRESENTATION'
          }
        ],
        mapping: {
          conditions: {},
          variables: {
            KEY: {
              value: 'REPRESENTATION.id',
              dependencies: []
            },
            NAME: {
              value: '',
              dependencies: []
            },
            ORDER: {
              value: '',
              dependencies: []
            },
            CIVILITE: {
              value: '_.formatCivility(answers[PERSONNE.id].sexe.value)',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'PERSONNE.id',
                  questionId: 'sexe'
                }
              ]
            },
            PRENOM: {
              value: 'answers[PERSONNE.id].prenoms.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'PERSONNE.id',
                  questionId: 'prenoms'
                }
              ]
            },
            NOM: {
              value: 'answers[PERSONNE.id].nom.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'PERSONNE.id',
                  questionId: 'nom'
                }
              ]
            },
            CIVILITE_PRESENT: {
              value: "_.getCivility(answers[PERSONNE.id].sexe.value, 'présent', 'présente')",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'PERSONNE.id'
                }
              ]
            }
          },
          repeats: {
            REPRESENTANT: {
              value: "_.getLinkBranchesTo(REPRESENTATION.id, 'REPRESENTANT_PROCURATION')",
              item: 'REPRESENTANT',
              dependencies: [],
              mapping: {
                conditions: {},
                variables: {
                  KEY: {
                    value: 'REPRESENTANT.id',
                    dependencies: []
                  },
                  NAME: {
                    value: '',
                    dependencies: []
                  },
                  ORDER: {
                    value: '',
                    dependencies: []
                  },
                  CIVILITE: {
                    value: '_.formatCivility(answers[REPRESENTANT.id].sexe.value)',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'REPRESENTANT.id',
                        questionId: 'sexe'
                      }
                    ]
                  },
                  PRENOM: {
                    value: 'answers[REPRESENTANT.id].prenoms.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'REPRESENTANT.id',
                        questionId: 'prenoms'
                      }
                    ]
                  },
                  NOM: {
                    value: 'answers[REPRESENTANT.id].nom.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'REPRESENTANT.id',
                        questionId: 'nom'
                      }
                    ]
                  }
                },
                repeats: {},
                raws: []
              }
            }
          },
          raws: []
        }
      }
    },
    raws: [
      {
        id: 'CAPACITE_REGIME',
        value: 'rawLinks[PERSONNE.id].SOUS_TUTELLE',
        dependencies: []
      },
      {
        id: 'CAPACITE_CURATEUR',
        value: 'rawLinks[PERSONNE.id].TUTEUR',
        dependencies: []
      },
      {
        id: 'CAPACITE_TOTAL',
        value: '_.concat(RAWS.CAPACITE_REGIME,RAWS.CAPACITE_CURATEUR)',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'CAPACITE_REGIME'
          },
          {
            type: 'RAW',
            rawId: 'CAPACITE_CURATEUR'
          }
        ]
      },
      {
        id: 'REPRESENTATION',
        value: 'rawLinks[PERSONNE.id].REPRESENTANT_PROCURATION',
        dependencies: []
      }
    ]
  },
  metadata: {}
};
