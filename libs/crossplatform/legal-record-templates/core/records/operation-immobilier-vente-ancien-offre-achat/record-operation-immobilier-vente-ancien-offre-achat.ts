// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordOperationImmobilierVenteAncienOffreAchat: LegalRecordTemplate = {
  config: {},
  form: [
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'acceptation_developpee',
          label: 'Acceptation développée',
          type: 'SELECT-BINARY'
        },
        {
          id: 'date_emission_offre',
          label: "Date d'emission de l'offre",
          type: 'DATE'
        },
        {
          id: 'acceptation_nom_notaire',
          label: 'Nom du notaire du Vendeur',
          type: 'TEXT'
        },
        {
          id: 'acceptation_adresse_notaire',
          label: 'Adresse du notaire du Vendeur',
          type: 'ADDRESS'
        }
      ],
      id: '03816d57_65a1_44fc_b1aa_430456380b10',
      label: "Acceptation de l'offre",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'offre_developpee',
          label: 'Offre développée',
          type: 'SELECT-BINARY'
        },
        {
          id: 'offre_achat_initiale_date',
          label: "Date de l'offre d'achat initiale",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            "Si 'oui' permet de remplir informatiquement certaines parties de l'offre avant le rendez-vous et de compléter le reste à la main lors de la signature",
          id: 'tapuscrit',
          label: 'L\'offre d\'achat est-elle complétée en tout ou partie en mode "papier"?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'presence_vendeur',
          label: "Ajouter le Vendeur à l'offre",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'libre_prix',
              label: 'Le prix de vente'
            },
            {
              id: 'libre_honoraires',
              label: "Les honoraires de l'Agence"
            },
            {
              id: 'libre_financement',
              label: "Le financement de l'acquéreur"
            },
            {
              id: 'libre_depot',
              label: 'Les modalités du dépôt de garantie'
            },
            {
              id: 'aucune_clause',
              label: 'Aucune clause'
            }
          ],
          description: 'Les autres éléments seront complétés à la main lors de la signature',
          id: 'tapuscrit_libre_liste',
          label: 'Quelles clauses sont complétées informatiquement avant le rendez-vous ?',
          multiple: true,
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            "Les coordonnées pourront toujours être renseignées dans la fiche mais n'apparaîtront pas dans le contrat.",
          id: 'coordonnees',
          label: 'Les coordonnées des parties doivent-elles être masquées ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'offre_designation_bien_autre',
          label: "Désignation du bien objet de l'offre",
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'offre_cadastre',
          label: "Les références cadastrales du bien sont elles reprises dans l'offre ?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'offre_notaire_acquereur',
          label: "Notaire de l'acquéreur",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'presence_faillite_accord',
          label: "Le proposant prend le bien en connaissance d'une procédure collective en cours",
          type: 'SELECT-BINARY'
        },
        {
          description: "En cas d'honoraires charge Acquéreur indiquer le prix hors frais d'agence",
          id: 'offre_prix',
          label: 'Prix proposé',
          type: 'PRICE'
        },
        {
          id: 'offre_prix_efficity',
          label: 'Prix proposé',
          type: 'PRICE'
        },
        {
          description: "En cas d'honoraires charge Acquéreur indiquer le prix hors frais d'agence",
          id: 'offre_prix_cfp',
          label: 'Prix proposé',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'ttc',
              label: 'Toutes Taxes Comprises'
            },
            {
              id: 'ht',
              label: 'Hors Taxes'
            }
          ],
          id: 'prix_vente_tva',
          label: 'Ce prix est exprimé',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'fai',
              label: 'Frais agence inclus'
            },
            {
              id: 'nv',
              label: 'Net vendeur'
            }
          ],
          id: 'prix_fai_nv',
          label: 'Ce prix est exprimé',
          type: 'SELECT-BINARY'
        },
        {
          id: 'loyer_annuel_offre',
          label: 'Montant du loyer annuel HT et HC',
          type: 'PRICE'
        },
        {
          id: 'stock_montant',
          label: 'Montant du stock',
          type: 'PRICE'
        },
        {
          id: 'stock_mensualites',
          label: 'Nombre de mensualités pour le paiement du stock',
          type: 'NUMBER'
        },
        {
          id: 'stock_tabac',
          label: 'Montant du stock tabac et FDJ',
          type: 'PRICE'
        },
        {
          id: 'accompagnement_jours',
          label: "Nombre de jours d'accompagnement du Vendeur",
          suffix: 'jours',
          type: 'NUMBER'
        },
        {
          id: 'clause_non_concurrence_annees',
          label: "Nombre d'années de non-concurrence",
          suffix: 'années',
          type: 'NUMBER'
        },
        {
          id: 'clause_non_concurrence_distance',
          label: 'Rayon de distance pour la clause de non-concurrence',
          suffix: 'km',
          type: 'NUMBER'
        },
        {
          id: 'date_prise_possession',
          label: 'Date de prise de possession du bien',
          type: 'DATE'
        },
        {
          id: 'offre_bouquet',
          label: "Montant du bouquet / capital proposé par l'Acquéreur :",
          type: 'PRICE'
        },
        {
          id: 'offre_rente',
          label: "Montant de la rente / mensualité proposée par l'Acquéreur :",
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'urbanisme',
              label: "Obtention d'une autorisation administrative"
            },
            {
              id: 'pret',
              label: "Obtention d'un prêt"
            },
            {
              id: 'substitution',
              label: 'Clause de substitution'
            },
            {
              id: 'autre',
              label: 'Autre Condition'
            },
            {
              id: 'aucune_clause',
              label: 'Aucune clause'
            }
          ],
          id: 'offre_achat_kw_cs',
          label: "Conditions suspensives de l'offre",
          multiple: true,
          type: 'SELECT'
        },
        {
          id: 'offre_achat_autre_cs',
          label: 'Autre condition suspensive',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'offre_emprunt',
          label: 'Financement par emprunt',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'offre_emprunt',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'offre_emprunt_total',
          label: "Montant total de l'emprunt",
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'offre_emprunt',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          description: "En cas d'honoraires charge Acquéreur indiquer le prix hors frais d'agence",
          id: 'offre_emprunt_total_cfp',
          label: "Montant total de l'emprunt",
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'offre_emprunt',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'offre_emprunt_taux',
          label: "Taux d'intérêt maximum",
          suffix: '%',
          type: 'NUMBER'
        },
        {
          conditions: [
            [
              {
                id: 'offre_emprunt',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'offre_emprunt_duree',
          label: "Durée de maximale de l'emprunt",
          suffix: 'ans',
          type: 'NUMBER'
        },
        {
          conditions: [
            [
              {
                id: 'offre_emprunt',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'simulation_emprunt',
          label: "Simulation d'emprunt",
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__EFFICITY__IMMOBILIER']
                  }
                ]
              ],
              title: 'Apport personnel'
            }
          ],
          id: 'offre_apport',
          label: 'Financement par apport personnel',
          type: 'SELECT-BINARY'
        },
        {
          id: 'offre_apport_total',
          label: "Montant total de l'apport",
          type: 'PRICE'
        },
        {
          description: "En cas d'honoraires charge Acquéreur indiquer le prix hors frais d'agence",
          id: 'offre_apport_total_cfp',
          label: "Montant total de l'apport",
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'offre_montant_loyer_maximal',
          label: 'Montant du loyer maximal',
          type: 'PRICE'
        },
        {
          id: 'offre_acceptation_activites',
          label: "Autorisation d'exercer les activités suivantes",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'stipulation_bail_activite_autre',
          label: "Ajouter une autre stipulation concernant le bail et l'excercice de l'activité nouvelle",
          type: 'SELECT-BINARY'
        },
        {
          id: 'stipulation_bail_activite_autre_liste',
          label: 'Stipulation à ajouter',
          type: 'TEXT'
        },
        {
          id: 'offre_date_transfert_propriete',
          label: 'Date extrême du transfert de propriété',
          type: 'DATE'
        },
        {
          conditions: [
            [
              {
                id: 'offre_apport',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'attestation_apport',
          label: 'Attestation bancaire - Apport',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cession',
          label: "Financement par vente d'un bien immobilier",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'cession_compromis_signe_doc_libre',
              label: 'Compromis de vente signé',
              type: 'UPLOAD'
            },
            {
              id: 'cession_type',
              label: 'Type de bien vendu',
              type: 'TEXTAREA'
            },
            {
              id: 'cession_adresse',
              label: 'Adresse du bien vendu',
              type: 'ADDRESS'
            },
            {
              id: 'cession_prix_vente',
              label: 'Montant du prix de vente réutilisé',
              type: 'PRICE'
            },
            {
              description: "En cas d'honoraires charge Acquéreur indiquer le prix hors frais d'agence",
              id: 'cession_prix_vente_cfp',
              label: 'Montant du prix de vente réutilisé',
              suffix: 'CFP',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cession_compromis_signe',
              label: 'Compromis de vente signé',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  before: {
                    type: 'N_DAYS_FROM_NOW',
                    value: '1'
                  },
                  id: 'cession_date_compromis',
                  label: 'Date de signature du compromis',
                  type: 'DATE'
                },
                {
                  after: {
                    type: 'N_DAYS_FROM_NOW',
                    value: '1'
                  },
                  id: 'cession_date_signature',
                  label: "Date envisagée pour la signature de l'acte de vente",
                  type: 'DATE'
                },
                {
                  id: 'cession_nom_notaire',
                  label: "Nom du notaire en charge de la rédaction de l'acte de vente",
                  type: 'TEXT'
                },
                {
                  id: 'cession_ville_notaire',
                  label: "Ville du notaire en charge de la rédaction de l'acte de vente",
                  type: 'TEXT'
                },
                {
                  id: 'cession_compromis_signe_doc',
                  label: 'Compromis de vente signé',
                  type: 'UPLOAD'
                }
              ],
              id: '2854ac41_0e70_489b_ac6f_860479761450',
              label: 'CONDITION_BLOCK_Compromis signé',
              type: 'CONDITION_BLOCK'
            }
          ],
          conditions: [
            [
              {
                id: 'cession',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'de167b2a_b5fd_4150_9270_bf148ddd18d6',
          label: 'Cession',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'emprunt_travaux',
              label: 'Travaux à inclure dans le financement',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'emprunt_travaux',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'emprunt_travaux_total',
              label: 'Montant total des travaux',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'frais_supplementaire_ajout',
              label: 'Ajouter des frais supplémentaires dans le financement',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'frais_supplementaire_liste_frais_supplementaire_type',
                  label: 'Type de frais',
                  type: 'TEXT'
                },
                {
                  id: 'frais_supplementaire_liste_frais_supplementaire_montant',
                  label: 'Montant',
                  type: 'PRICE'
                }
              ],
              conditions: [
                [
                  {
                    id: 'frais_supplementaire_ajout',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'frais_supplementaire_liste',
              label: 'Frais supplémentaires',
              repetition: {
                label: [
                  {
                    type: 'VARIABLE',
                    value: 'frais_supplementaire_liste_frais_supplementaire_type'
                  },
                  {
                    type: 'TEXT',
                    value: ' '
                  },
                  {
                    type: 'VARIABLE',
                    value: 'frais_supplementaire_liste_frais_supplementaire_montant'
                  },
                  {
                    type: 'TEXT',
                    value: '€'
                  }
                ],
                max: 1000,
                min: 1
              },
              type: 'REPEAT'
            }
          ],
          id: 'de16mn3a_ld2q_013s_937e_bf148dddpol4',
          label: 'Frais supplémentaires',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'offre_acquereur_primo_accedant',
          label: 'Offrant primo-accédant',
          type: 'SELECT-BINARY'
        }
      ],
      id: '6bf911b3_d277_4511_ba0c_c2b0147846fe',
      label: "Offre d'achat",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'offre_cs',
          label: "L'offre comporte une condition suspensive supplémentaire",
          type: 'SELECT-BINARY'
        },
        {
          id: 'offre_cs_liste_simple',
          label: 'Décrire la condition supplémentaire',
          type: 'TEXT'
        },
        {
          children: [
            {
              id: 'offre_cs_liste_titre_cs',
              label: 'Titre de la condition suspensive',
              type: 'TEXT'
            },
            {
              id: 'offre_cs_liste_description_cs',
              label: 'Description de la condition suspensive',
              type: 'TEXTAREA'
            }
          ],
          id: 'offre_cs_liste',
          label: "Ajout d'une condition suspensive supplémentaire",
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'offre_cs_liste_titre_cs'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          id: 'offre_delai_validite',
          label: "Délai de validité de l'offre",
          suffix: 'jours',
          type: 'NUMBER'
        },
        {
          after: {
            type: 'N_DAYS_FROM_NOW',
            value: '1'
          },
          id: 'offre_date_validite',
          label: "Date de validité de l'offre d'achat",
          type: 'DATE'
        },
        {
          after: {
            type: 'N_DAYS_FROM_NOW',
            value: '1'
          },
          id: 'offre_date_extreme_signature',
          label: "Date de signature extrême de l'avant contrat",
          type: 'DATE'
        },
        {
          description: 'indiquer en texte libre',
          id: 'offre_date_extreme_jouissance',
          label: "Date extrême d'entrée en jouissance",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'kw_abondance_acceptation_1161',
          label: 'Le Vendeur renonce à se prévaloir du bénéfice des articles 1161 et 1596',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description: 'A défaut il faudra indiquer un notaire en charge de la rédaction du contrat',
          id: 'offre_mn',
          label: 'Le compromis de vente sera-t-il rédigé par MyNotary et le service juridique de PropriétésPrivées ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'offre_business_redacteur',
          label: 'Les parties ont-elles déjà choisi un cabinet conseil rédacteur des actes ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'offre_business_redacteur_nom',
          label: 'Nom du cabinet conseil rédacteur des actes',
          type: 'TEXT'
        },
        {
          after: {
            type: 'N_DAYS_FROM_NOW',
            value: '1'
          },
          id: 'offre_date_comrpomis',
          label: "Date maximale de signature du compromis en cas d'acceptation de l'offre",
          type: 'DATE'
        },
        {
          description:
            'Le délai usuel pour la signature d’un compromis de vente à compter de la signature d’une lettre d’intention d’achat est d’un mois',
          id: 'offre_date_compromis_pp',
          label: "Date maximale de signature du compromis en cas d'acceptation de l'offre",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'acceptation_pp_defaut',
          label: "Le délai de validité de l'offre est de 7 jours par défaut. Conserver ce délai ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'quinze',
              label: '15 jours'
            },
            {
              id: 'autre',
              label: 'Autre délai'
            }
          ],
          id: 'acceptation_pp_liste',
          label: "Délai d'acceptation par le Vendeur",
          type: 'SELECT'
        },
        {
          id: 'offre_date_vendeur_jours',
          label: "Délai maximum d'acceptation de l'offre par le vendeur",
          suffix: 'jours',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'compromis_pp_defaut',
          label: "Le délai pour la signature du compromis est d'un mois par défaut. Conserver ce délai ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'deux',
              label: '2 mois'
            },
            {
              id: 'autre',
              label: 'autre délai'
            }
          ],
          id: 'compromis_pp_liste',
          label: 'Délai de signature du compromis',
          type: 'SELECT'
        },
        {
          id: 'offre_date_compromis_jours',
          label: "Délai maximum de signature du compromis en cas d'acceptation :",
          suffix: 'jours',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'remise_documents',
          label: 'Des documents sont remis au visiteur',
          type: 'SELECT-BINARY'
        },
        {
          id: 'remise_documents_liste',
          label: 'Liste des documents remis :',
          type: 'TEXTAREA'
        },
        {
          children: [
            {
              id: 'offre_redacteur_redacteur_nom',
              label: 'Nom du rédacteur',
              type: 'TEXT'
            },
            {
              id: 'offre_redacteur_redacteur_adresse',
              label: 'Adresse du rédacteur',
              type: 'ADDRESS'
            },
            {
              choices: [
                {
                  id: 'avocat',
                  label: 'Avocat'
                },
                {
                  id: 'notaire',
                  label: 'Notaire'
                }
              ],
              id: 'offre_redacteur_redacteur_type',
              label: 'Type de rédacteur',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'acquereur',
                  label: 'Acquéreur'
                },
                {
                  id: 'vendeur',
                  label: 'Vendeur'
                },
                {
                  id: 'double',
                  label: 'les Deux parties'
                }
              ],
              id: 'offre_redacteur_redacteur_assistance',
              label: 'Le rédacteur assiste',
              type: 'SELECT'
            },
            {
              id: 'offre_redacteur_redacteur_test',
              label: 'Test',
              type: 'TEXT'
            }
          ],
          id: 'offre_redacteur',
          label: 'Ajouter un Rédacteur de la promesse',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: 'Maître '
              },
              {
                type: 'VARIABLE',
                value: 'offre_redacteur_redacteur_nom'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'enregistrement',
                  label: 'Enregistrement'
                },
                {
                  id: 'honoraires_negociation',
                  label: 'Honoraires de Négociation'
                },
                {
                  id: 'pret',
                  label: 'Frais de prêt'
                },
                {
                  id: 'honoraires_redaction',
                  label: 'Honoraires de Rédaction'
                },
                {
                  id: 'societe',
                  label: 'Constitution de Société'
                },
                {
                  id: 'droit_bail',
                  label: 'Cession de droit au Bail'
                },
                {
                  id: 'formation',
                  label: 'Formations obligatoires'
                },
                {
                  id: 'depot',
                  label: 'Dépôt de garantie + 1er Loyer'
                },
                {
                  id: 'debours',
                  label: 'Débours'
                },
                {
                  id: 'travaux',
                  label: 'Travaux'
                },
                {
                  id: 'stock',
                  label: 'Stock'
                },
                {
                  id: 'autre',
                  label: 'Autre'
                }
              ],
              id: 'offre_frais_acquisition_frais_type',
              label: 'Type de frais',
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'offre_frais_acquisition_frais_type',
                    value: 'autre',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'offre_frais_acquisition_frais_type_autre',
              label: 'Autre type',
              type: 'TEXT'
            },
            {
              id: 'offre_frais_acquisition_frais_montant',
              label: 'Montant (hors taxe si TVA)',
              type: 'PRICE'
            },
            {
              conditions: [
                [
                  {
                    id: 'offre_frais_acquisition_frais_type',
                    value: 'autre',
                    type: 'DIFFERENT'
                  },
                  {
                    id: 'offre_frais_acquisition_frais_type',
                    value: 'debours',
                    type: 'DIFFERENT'
                  },
                  {
                    id: 'offre_frais_acquisition_frais_type',
                    value: 'depot',
                    type: 'DIFFERENT'
                  },
                  {
                    id: 'offre_frais_acquisition_frais_type',
                    value: 'enregistrement',
                    type: 'DIFFERENT'
                  },
                  {
                    id: 'offre_frais_acquisition_frais_type',
                    value: 'stock',
                    type: 'DIFFERENT'
                  }
                ]
              ],
              id: 'offre_frais_acquisition_frais_tva_montant',
              label: 'Montant de la TVA',
              type: 'PRICE'
            }
          ],
          id: 'offre_frais_acquisition',
          label: "Compléter les frais d'acquisition",
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'offre_frais_acquisition_frais_type'
              },
              {
                type: 'TEXT',
                value: ' '
              },
              {
                type: 'VARIABLE',
                value: 'offre_frais_acquisition_frais_montant'
              },
              {
                type: 'TEXT',
                value: '€'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'offre_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'offre_electronique_pp',
          label: "L'offre est signée de manière électronique. Conserver ce mode de signature?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'offre_papernest',
          label: "L'offrant autorise la transmission de son numéro à PAPERNEST",
          type: 'SELECT-BINARY'
        }
      ],
      id: '386ee724_cf69_4b52_9faa_a07657c4d7d5',
      label: "Offre d'achat",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'clause_particuliere',
          label: 'Ajouter une clause particulière',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'clause_particuliere_liste_clause_particuliere_liste_titre',
              label: 'Titre de la clause',
              type: 'TEXT'
            },
            {
              id: 'clause_particuliere_liste_clause_particuliere_liste_contenu',
              label: 'Contenu de la clause',
              type: 'TEXTAREA'
            }
          ],
          conditions: [
            [
              {
                id: 'clause_particuliere',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER']
              }
            ],
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER']
              }
            ]
          ],
          id: 'clause_particuliere_liste',
          label: "Ajout d'une clause particulière",
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'clause_particuliere_liste_clause_particuliere_liste_titre'
              }
            ],
            max: 1000,
            min: 0
          },
          type: 'REPEAT'
        }
      ],
      id: '697758dd_50b8_49b8_8181_dd393faef0e9',
      label: 'Clause particulière',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'acceptation_offre_date',
          label: "Date de transmission de l'offre de l'Acquéreur",
          type: 'DATE'
        },
        {
          id: 'acceptation_offre_heure',
          label: "Heure de transmission de l'offre",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'acceptation',
              label: "Acceptation de l'offre"
            },
            {
              id: 'conditions',
              label: "Acceptation de l'offre sous conditions"
            },
            {
              id: 'refus',
              label: "Refus de l'offre"
            }
          ],
          id: 'acceptation_decision_vendeur',
          label: 'Décision du vendeur',
          type: 'SELECT'
        },
        {
          id: 'acceptation_decision_vendeur_conditions',
          label: 'Conditions du vendeur',
          type: 'TEXT'
        },
        {
          id: 'acceptation_prix_offre',
          label: 'Prix proposé',
          type: 'PRICE'
        },
        {
          id: 'acceptation_date_signature_compromis',
          label: "Date extrême de signature de l'avant contrat",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'presence_acquereur',
          label: "Ajouter l'Acquéreur à l'acceptation",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'acceptation_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        },
        {
          id: 'offre_achat',
          label: "Offre d'achat",
          type: 'UPLOAD'
        }
      ],
      id: '711d0379_2e03_48ee_a63c_c1c3b1885562',
      label: "Acceptation de l'offre",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'offre_cpa_date_visite',
          label: 'Date de visite du bien',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'offre_cpa_signature_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        },
        {
          description: 'Indiquer le montant hors frais agence',
          id: 'offre_cpa_montant',
          label: "Montant de l'offre",
          type: 'PRICE'
        },
        {
          id: 'offre_cpa_montant_composition',
          label: 'Composition du prix',
          type: 'TEXT'
        },
        {
          id: 'offre_cpa_depot_garantie_montant',
          label: 'Montant maximum du dépôt de garantie',
          type: 'PRICE'
        },
        {
          id: 'offre_cpa_depot_garantie_sequestre',
          label: 'Nom du séquestre',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'offre_cpa_pret',
          label: 'Recours à un prêt',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'offre_cpa_pret_montant_total',
              label: "Montant total de l'emprunt",
              type: 'PRICE'
            },
            {
              id: 'offre_cpa_pret_duree',
              label: "Durée de l'emprunt",
              suffix: 'ans',
              type: 'NUMBER'
            },
            {
              id: 'offre_cpa_pret_taux',
              label: "Taux maximum de l'emprunt",
              suffix: '%',
              type: 'NUMBER'
            }
          ],
          conditions: [
            [
              {
                id: 'offre_cpa_pret',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: '74e502dc_890a_44f5_9353_2d4dfa7d6ec0',
          label: 'CONDITION_BLOCK_Pret',
          type: 'CONDITION_BLOCK'
        },
        {
          id: 'offre_cpa_honoraires_pourcentage',
          label: 'Pourcentage des honoraires',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          id: 'offre_cpa_honoraires_fixe',
          label: 'Montant des honoraires',
          type: 'PRICE'
        },
        {
          id: 'offre_cpa_date_validite',
          label: "Date limite de validité de l'offre",
          type: 'DATE'
        }
      ],
      id: 'dc0a39c7_7c88_4138_8494_e7bf999a6ed6',
      label: "Contrat préliminaire d'achat",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'offre_cpa_acceptation_vendeur',
          label: "Ajouter l'acceptation du vendeur",
          type: 'SELECT-BINARY'
        },
        {
          id: 'offre_cpa_acceptation_vendeur_date_transmission',
          label: "Date de transmission de l'offre",
          type: 'DATE'
        },
        {
          id: 'offre_cpa_acceptation_vendeur_heure_transmission',
          label: "Heure de transmission de l'offre",
          suffix: 'heures',
          type: 'NUMBER'
        },
        {
          id: 'offre_cpa_acceptation_vendeur_date_cpv',
          label: 'Date de signature extrême du compromis',
          type: 'DATE'
        }
      ],
      id: 'ebf255ba_bbee_400b_8f43_91702acf56dd',
      label: 'Acceptation du Vendeur',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'contre_offre_date_initiale',
          label: "Date de l'offre initiale",
          type: 'DATE'
        },
        {
          id: 'contre_offre_montant',
          label: 'Montant de la contre-offre',
          type: 'PRICE'
        },
        {
          id: 'contre_offre_date_signature',
          label: 'Date de signature extrême du compromis',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'contre_offre_signature_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        },
        {
          id: 'offre_achat_initiale',
          label: "Offre d'achat initiale",
          type: 'UPLOAD'
        }
      ],
      id: 'ebf255ba_bbee_32zs_65tg_91702acf56yt',
      label: 'Contre Offre',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__OFFRE_ACHAT',
  label: 'Offre Achat',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__OFFRE_ACHAT',
  specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_ANCIEN', 'OFFRE_ACHAT'],
  type: 'RECORD'
};
