// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordOperationImmobilierLocationCommercialBailCommercial: LegalRecordTemplate = {
  config: {},
  form: [
    {
      children: [
        {
          choices: [
            {
              id: 'commercial',
              label: 'Commercial'
            },
            {
              id: 'professionnel',
              label: 'Professionnel'
            }
          ],
          id: 'bail_pro_commercial',
          label: 'Bail de type',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_gestion_conditions',
          label: 'Conditions de la location eprises au mandat',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            "Les coordonnées pourront toujours être renseignées dans la fiche mais n'apparaîtront pas dans le contrat.",
          id: 'coordonnees',
          label: 'coordonnées des parties masquées au sein du contrat',
          type: 'SELECT-BINARY'
        },
        {
          id: 'bail_precaire_motif',
          label: "Motif justifiant l'occupation précaire",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'commercial',
              label: 'de Commerce'
            },
            {
              id: 'bureau',
              label: 'de Bureaux'
            },
            {
              id: 'entrepot',
              label: "d'Entrepot ou de Stockage"
            },
            {
              id: 'usage_autre',
              label: 'Autre usage'
            }
          ],
          id: 'destination',
          label: 'Le bien est loué pour un usage',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'bureau',
              label: 'Bureau'
            },
            {
              id: 'commercial',
              label: 'Local Commercial'
            },
            {
              id: 'professionnel',
              label: 'Local Professionnel'
            },
            {
              id: 'autre',
              label: 'Autre'
            }
          ],
          id: 'destination_pp',
          label: 'Le bien est loué pour un usage',
          type: 'SELECT'
        },
        {
          id: 'usage_professionnel',
          label: 'Le bien est loué pour un usage',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PRECAIRE']
                  }
                ]
              ],
              title: "La convention autorise l'exploitation de tous types de commerce dans les locaux ?"
            }
          ],
          conditions: [
            [
              {
                id: 'destination',
                value: 'commercial',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'destination_tout_commerce',
          label: "Autorisation d'exploitation de tous types de commerce dans les locaux",
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'destination',
                value: 'usage_autre',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'destination_pp',
                value: 'autre',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'destination_autre',
          label: 'Usage du bien',
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'destination',
                value: 'entrepot',
                type: 'EQUALS'
              }
            ]
          ],
          description: "Attention le stockage de boissons alcoolisées impose la rédaction d'un acte authentique",
          id: 'destination_stockage_marchandises',
          label: 'Nature des marchandises stockées',
          type: 'TEXT'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PRECAIRE']
                  }
                ]
              ],
              title: 'Liste des activités autorisées'
            }
          ],
          id: 'destination_commercial_activites',
          label: 'Liste des activités autorisées au bail',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'destination',
                value: 'commercial',
                type: 'EQUALS'
              }
            ]
          ],
          description: 'Par exemple hôpital - théatre - cinéma...',
          id: 'destination_monovalent',
          label: "Local loué construit en vue d'une seule utilisation",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'destination_conforme',
          label: "Local adapté à l'activité envisagée par le Preneur",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'destination_professionnelle_volontaire',
          label: 'Le local loué est-il un bien professionnel, soumis volontairement au statut des baux commerciaux ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'etat_des_lieux',
          label: 'Etat des lieux déjà effectué',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'etat_des_lieux',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'etat_des_lieux_date',
          label: "Date de l'état des lieux",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'bail_type_commercial',
              label: 'Bail commercial classique'
            },
            {
              id: 'bail_type_derogatoire',
              label: 'Bail commercial dérogatoire'
            },
            {
              id: 'bail_type_autre',
              label: 'Autre type de bail'
            }
          ],
          id: 'bail_type',
          label: 'Type de bail',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'professionnel',
              label: 'Professionnel'
            },
            {
              id: 'commercial',
              label: 'Commercial'
            },
            {
              id: 'derogatoire',
              label: 'Dérogatoire'
            }
          ],
          id: 'bail_type_pp',
          label: 'Type de bail',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'bail_type',
                value: 'bail_type_autre',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'bail_type_autre',
          label: 'Autre type de bail',
          type: 'TEXT'
        },
        {
          id: 'bail_prise_effet',
          label: "Date de prise d'effet du bail",
          type: 'DATE'
        },
        {
          id: 'bail_date_disponibilite',
          label: 'Date de disponibilité du bien',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'bail_bien_asl',
          label: "Présence d'une ASL / AFUL",
          type: 'SELECT-BINARY'
        },
        {
          id: 'regime_juridique_location',
          label: 'Régime juridique de la location',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'bail_assurance_renonciation',
          label: "Renonciation à recours et prise en cahrge par le preneur du remboursement de la prime d'assurance",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'bail_restaurant_entreprise',
          label: "Présence d'un restaurant d'entreprise",
          type: 'SELECT-BINARY'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'inventaire_impots_charges',
          label: 'Inventaire des impôts et charges',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'signature_electronique_bail_immobilier_entreprise',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'signature_electronique_bail_bureau',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '999df3ed_5f2c_420b_9b4a_a267a41a3dba',
      label: 'Informations générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PRECAIRE']
                  }
                ]
              ],
              title: "Durée de la convention d'occupation"
            }
          ],
          id: 'bail_duree_libre',
          label: 'Durée du bail',
          type: 'TEXT'
        },
        {
          description:
            'La durée minimale du bail doit être de 6 ans. A partir de 12 ans le bail doit être obligatoirement reçu par un notaire',
          id: 'bail_professionnel_duree',
          label: 'Le bail professionnel est conclu pour une durée de',
          suffix: 'ans',
          type: 'NUMBER'
        },
        {
          conditions: [
            [
              {
                id: 'bail_type',
                value: 'bail_type_commercial',
                type: 'EQUALS'
              }
            ]
          ],
          description:
            'La durée minimale du bail doit être de 9 ans. A partir de 12 ans le bail doit être obligatoirement reçu par un notaire',
          id: 'bail_commercial_duree',
          label: 'Le bail est conclu pour une durée de',
          suffix: 'ans',
          type: 'NUMBER'
        },
        {
          conditions: [
            [
              {
                id: 'bail_type',
                value: 'bail_type_derogatoire',
                type: 'EQUALS'
              }
            ]
          ],
          description: 'La durée maximale du bail dérogatoire doit être au total de 72 mois renouvellement inclus',
          id: 'bail_derogatoire_duree',
          label: 'Le bail dérogatoire est conclu pour une durée de',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PRECAIRE']
                  }
                ]
              ],
              title: "Durée de la convention d'occupation"
            }
          ],
          id: 'bail_autre_duree',
          label: 'Durée du bail',
          type: 'TEXT'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PRECAIRE']
                  }
                ]
              ],
              title: 'Date de début de la convention'
            }
          ],
          id: 'bail_date_debut',
          label: 'Date de début du bail',
          type: 'DATE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PRECAIRE']
                  }
                ]
              ],
              title: 'Date de fin de la convention'
            }
          ],
          id: 'bail_date_fin',
          label: 'Date de fin du bail',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'bail_duree_faculte_triennale',
          label: 'Bail autorise la faculté de résiliation triénnale',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'bail_duree_faculte_triennale_renonciation',
          label: 'Le Preneur renonce-t-il purement et simplement à sa faculté de résiliation ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'bail_duree_faculte_triennale_renonciation',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'bail_duree_faculte_triennale_renonciation_amenagement',
          label: "Modalités d'aménagement du congé délivré par le Preneur",
          type: 'TEXT'
        },
        {
          id: 'fin_conge_preneur',
          label: 'Date du congé par le Preneur',
          type: 'DATE'
        }
      ],
      conditionalTitles: [
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PRECAIRE']
              }
            ]
          ],
          title: 'Durée de la convention'
        }
      ],
      id: '78c90a24_bba2_4346_a993_43637cd90dbb',
      label: 'Durée du bail',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'loyer_clause_recette',
          label: "Loyer indexé sur le chiffre d'affaire du Locataire",
          type: 'SELECT-BINARY'
        },
        {
          id: 'precaire_indemnite',
          label: "Montant de l'indemnité pénale journalière en cas de retard",
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'frequence_mensuelle',
              label: 'Mensuellement'
            },
            {
              id: 'frequence_trimestrielle',
              label: 'Trimestriellement'
            },
            {
              id: 'frequence_semestrielle',
              label: 'Semestriellement'
            },
            {
              id: 'frequence_annuelle',
              label: 'Annuellement'
            },
            {
              id: 'frequence_autre',
              label: 'Autre fréquence'
            }
          ],
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PRECAIRE']
                  }
                ]
              ],
              title: 'La redevance est payable'
            }
          ],
          id: 'loyer_modalite_paiement_frequence',
          label: 'Le loyer est payable',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'loyer_modalite_paiement_frequence',
                value: 'frequence_mensuelle',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'loyer_modalite_paiement_date_mensualite',
          label: 'Jour de paiement dans le mois',
          type: 'NUMBER'
        },
        {
          conditions: [
            [
              {
                id: 'loyer_modalite_paiement_frequence',
                value: 'frequence_autre',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'loyer_modalite_paiement_autre',
          label: 'Indiquez la fréquence de paiement',
          type: 'TEXT'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PRECAIRE']
                  }
                ]
              ],
              title: 'Montant de la redevance'
            }
          ],
          description: 'Le montant sera automatiquement recalculé dans le contrat',
          id: 'loyer_initial',
          label: 'Montant du loyer (sans les charges)',
          type: 'PRICE'
        },
        {
          id: 'loyer_initial_annuel',
          label: 'Montant du loyer annuel (hors taxes)',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'mensuel',
              label: 'Mensuellement'
            },
            {
              id: 'trimestriel',
              label: 'Trimestriellement'
            }
          ],
          id: 'loyer_periode_paiement',
          label: 'Loyer payable',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'avance',
              label: "Payable d'avance"
            },
            {
              id: 'echu',
              label: 'Payable à terme échu'
            }
          ],
          id: 'loyer_mode_paiement',
          label: 'Mode de paiement du loyer',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PRECAIRE']
                  }
                ]
              ],
              title: 'Redevance soumise à TVA'
            }
          ],
          id: 'loyer_tva',
          label: 'Loyer soumis à la TVA',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'loyer_crl',
          label: 'Loyer soumis à la Contribution sur les Revenus Locatifs',
          type: 'SELECT-BINARY'
        },
        {
          id: 'loyer_date_paiement_premier',
          label: 'Date du paiement du premier loyer',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            "Le pas-de-porte (également appelé droit d'entrée ou droit au bail) est un montant versé en une fois par le locataire au bailleur lors de la conclusion du bail",
          id: 'loyer_pas_porte',
          label: 'Pas-de-porte prévu lors de la conclusion du bail',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'loyer_pas_porte',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'loyer_pas_porte_montatn',
          label: 'Montant du pas-de-porte à verser',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'trimestre_avance',
              label: "Par trimestre, d'avance"
            },
            {
              id: 'trimestre_echu',
              label: 'Par trimestre, à terme échu'
            },
            {
              id: 'mois_avance',
              label: "Par mois, d'avance"
            },
            {
              id: 'mois_echu',
              label: 'Par mois, à terme échu'
            }
          ],
          id: 'mode_paiement_pp',
          label: 'Mode de paiement',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'prelevement',
              label: 'Prélèvement Bancaire'
            },
            {
              id: 'cheque',
              label: 'Chèque ou virement'
            }
          ],
          id: 'mode_paiement_loyer',
          label: 'Mode de paiement du loyer',
          multiple: true,
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'echu',
              label: 'Terme échu'
            },
            {
              id: 'echoir',
              label: 'Terme à échoir'
            }
          ],
          id: 'paiement_terme',
          label: 'Paiement à terme',
          type: 'SELECT'
        },
        {
          id: 'loyer_frequence',
          label: 'Fréquence de paiement du loyer',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'loyer_condition_particuliere',
          label: 'Ajouter une condition particulière aux conditions de location',
          type: 'SELECT-BINARY'
        },
        {
          id: 'loyer_condition_particuliere_texte',
          label: 'Texte de la condition particulière',
          type: 'TEXTAREA'
        },
        {
          id: 'taxe_fonciere',
          label: 'Montant de la taxe foncière',
          type: 'PRICE'
        }
      ],
      conditionalTitles: [
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PRECAIRE']
              }
            ]
          ],
          title: 'Redevance'
        }
      ],
      id: '7d572c9c_1240_4924_b064_a78cbfee72b8',
      label: 'Loyer',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'provision',
              label: 'Provision simple avec régularisation postérieure'
            },
            {
              id: 'provision_ordures',
              label: 'Provision simple avec régularisation postérieure et taxe foncière payée sur justificatif'
            },
            {
              id: 'forfait',
              label: 'Forfaitairement'
            },
            {
              id: 'autre',
              label: 'Autres modalités'
            },
            {
              id: 'aucune',
              label: 'Pas de charges'
            }
          ],
          id: 'charges_modalite',
          label: 'Les charges récupérables sont payées selon les modalités suivantes',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'charges_modalite',
                type: 'EQUALS',
                value: 'autre'
              }
            ]
          ],
          id: 'charges_modalite_autre',
          label: 'Précisez les modalités',
          type: 'TEXTAREA'
        },
        {
          description:
            'Si le loyer est payé annuellement par exemple le montant des charges à indiquer est le montant annuel',
          id: 'charges_montant',
          label: 'Montant des charges (montant des charges à payer en même temps que le loyer)',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'charges_taxes_foncieres_paiement',
          label: "Taxe foncière payée sur présentation de l'avis",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'charges_modalite_supplementaire',
          label: 'Ajouter des charges supplémentaires',
          type: 'SELECT-BINARY'
        },
        {
          id: 'charges_modalite_supplementaire_liste',
          label: 'Liste des charges supplémentaires',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'charges_modalites_precision',
          label: 'Ajouter des précision sur les charges',
          type: 'SELECT-BINARY'
        },
        {
          id: 'charges_modalites_precision_texte',
          label: 'Précisions sur les charges',
          type: 'TEXTAREA'
        }
      ],
      id: '06613719_614c_4c22_aeaf_a83590b5fcc7',
      label: 'Charges récupérables',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'precaire_revision',
          label: 'La redevance sera revisable',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'revision_annuelle',
          label: 'Loyer révisé annuellement',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'indice_ilc',
              label: 'Indice des loyers commerciaux'
            },
            {
              id: 'indice_ilat',
              label: 'Indice des loyers des activités tertiaires '
            },
            {
              id: 'indice_icc',
              label: 'Indice du coût de la construction'
            },
            {
              id: 'indice_autre',
              label: 'Autre indice'
            }
          ],
          id: 'loyer_revision_indice',
          label: "L'indice de révision du loyer utilisé est",
          type: 'SELECT'
        },
        {
          id: 'loyer_revision_indice_autre',
          label: 'Indice de révision',
          type: 'TEXT'
        },
        {
          id: 'loyer_revision_indice_trimestre',
          label: 'Numéro de trimestre du dernier indice publié',
          type: 'TEXT'
        },
        {
          id: 'loyer_revision_indice_annee',
          label: 'Année du trimestre du dernier indice publié',
          type: 'YEAR'
        },
        {
          id: 'loyer_revision_indice_montant',
          label: 'Montant du dernier indice publié',
          type: 'TEXT'
        },
        {
          id: 'loyer_revision_frequence',
          label: 'Fréquence de la révision du loyer',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'loyer_revision_deplafonnement',
          label: 'Loyer révisé déplafonné',
          type: 'SELECT-BINARY'
        }
      ],
      conditionalTitles: [
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['IMMOBILIER_LOCATION_COMMERCIAL_BAIL_PRECAIRE']
              }
            ]
          ],
          title: 'Révision'
        }
      ],
      id: '47970002_a5d9_4ae4_afda_b8c9c9b19371',
      label: 'Révision du loyer',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'depot_garantie',
          label: 'Dépôt de garantie',
          type: 'SELECT-BINARY'
        },
        {
          id: 'depot_garantie_montant_libre',
          label: 'Montant du depôt de garantie',
          type: 'PRICE'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'depot_garantie_chiffre',
                  label: 'Par un montant chiffré'
                },
                {
                  id: 'depot_garantie_lettre',
                  label: 'Par une autre méthode'
                }
              ],
              id: 'depot_garantie_statut',
              label: 'Le dépôt de garantie est indiqué',
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'depot_garantie',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'depot_garantie_statut',
                    value: 'depot_garantie_chiffre',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'depot_garantie_montant',
              label: 'Montant du depôt de garantie',
              type: 'PRICE'
            },
            {
              conditions: [
                [
                  {
                    id: 'depot_garantie',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'depot_garantie_statut',
                    value: 'depot_garantie_lettre',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'depot_garantie_montant_texte',
              label: 'Montant du depôt de garantie',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'depot_proprietaire',
                  label: 'Directement au propriétaire'
                },
                {
                  id: 'depot_agence',
                  label: "A l'agence"
                },
                {
                  id: 'depot_agence_gestion',
                  label: "A l'agence de gestion"
                },
                {
                  id: 'depot_autre',
                  label: 'Autre'
                }
              ],
              id: 'depot_garantie_depositaire',
              label: 'Le dépôt de garantie est versé',
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'depot_garantie',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'depot_garantie_depositaire',
                    type: 'EQUALS',
                    value: 'autre'
                  }
                ]
              ],
              id: 'depot_garantie_depositaire_autre_nom',
              label: 'Précisez le dépositaire',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'depot_garantie_versement',
              label: 'Le dépôt de garantie doit-il être versé le jour de la signature du bail ?',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'depot_garantie',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'depot_garantie_versement',
                    type: 'EQUALS',
                    value: 'non'
                  }
                ]
              ],
              id: 'depot_garantie_versement_date',
              label: 'Date de versement du dépôt de garantie',
              type: 'DATE'
            }
          ],
          conditions: [
            [
              {
                id: 'depot_garantie',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'aff1a8c0_608f_46be_a043_d8ef5fe90a33',
          label: 'Informations sur le dépôt de garantie',
          type: 'CATEGORY'
        }
      ],
      id: 'bf8e3e71_142a_41a9_bae1_6e09a909f13f',
      label: 'Dépôt de garantie',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'garantie_cuation_bancaire',
          label: 'Caution bancaire',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'garantie_cautionnement',
          label: 'Cautionnement',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'garantie_cautionnement',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'garantie_cautionnement_annexe',
          label: 'Cautionnement pris par acte séparé',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'garantie_cautionnement',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'signature_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'cautionnement_duree_bail_initial',
              label: 'Cautionnement pour la durée du bail initial seulement'
            },
            {
              id: 'cautionnement_duree_bail_prolongation',
              label: 'Cautionnement pour la durée du bail initial et de sa prolongation éventuelle'
            },
            {
              id: 'cautionnement_duree_bail_renouvellement',
              label: 'Cautionnement pour la durée du bail initial et de ses renouvellements éventuels'
            },
            {
              id: 'cautionnement_duree_bail_autre',
              label: 'Autre durée'
            }
          ],
          id: 'cautionnement_duree',
          label: 'Durée du Cautionnement',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'cautionnement_duree',
                value: 'cautionnement_duree_bail_prolongation',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'cautionnement_renouvellement_limite',
          label: 'Limite de durée du Cautionnement après la fin du Bail initial',
          suffix: 'ans',
          type: 'NUMBER'
        },
        {
          conditions: [
            [
              {
                id: 'cautionnement_duree',
                value: 'cautionnement_duree_bail_renouvellement',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'cautionnement_renouvellement',
          label: 'Nombre de renouvellement compris dans le Cautionnement',
          type: 'NUMBER'
        },
        {
          conditions: [
            [
              {
                id: 'cautionnement_duree',
                value: 'cautionnement_duree_bail_autre',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'cautionnement_duree_autre',
          label: 'Durée du Cautionnement',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cautionnement_locataire_changement',
          label: 'Cautionnement maintenu en cas de changement dans la personne du Locataire',
          type: 'SELECT-BINARY'
        }
      ],
      id: '97bd681c_59a6_4558_a180_cc4d5ca7137e',
      label: 'Garantie des loyers',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'location_gerance',
          label: 'Autorisation de location-gérance',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'sous_location',
          label: 'Autorisation de sous-location',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'partiel',
              label: 'Sous location partielle'
            },
            {
              id: 'preneur',
              label: 'Sous location à une filiale du preneur'
            },
            {
              id: 'autre',
              label: 'Autre'
            }
          ],
          id: 'sous_location_liste',
          label: 'Sous-location autorisée :',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'sous_location_restriction',
          label: 'Restreindre la sous-location à une partie des locaux',
          type: 'SELECT-BINARY'
        },
        {
          id: 'sous_location_restriction_texte',
          label: 'Clause de restriction',
          type: 'TEXTAREA'
        },
        {
          id: 'sous_location_liste_texte',
          label: 'Clause de sous-location',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'sous_location_intervention_bailleur',
          label: 'Intervention du Bailleur à la sous-location',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cession_bail',
          label: 'Autorisation de cession de bail',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cession_bail_veto',
          label: "Le bailleur conserve la faculté de s'opposer à la cession du bail",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'travaux_anterieurs',
          label: 'Travaux effectués sur le local depuis les trois dernières années',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'travaux_anterieurs_liste_travaux_anterieurs_date',
              label: 'Date des travaux',
              type: 'DATE'
            },
            {
              id: 'travaux_anterieurs_liste_travaux_anterieurs_description',
              label: 'Description des travaux',
              type: 'TEXT'
            }
          ],
          id: 'travaux_anterieurs_liste',
          label: 'Liste des travaux antérieurs',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'travaux_anterieurs_liste_travaux_anterieurs_description'
              }
            ],
            max: 1000,
            min: 0
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'travaux_posterieurs',
          label: 'Travaux  prévus par le Bailleur pour les trois prochaines années',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'travaux_posterieurs_liste_travaux_posterieurs_date',
              label: 'Date des travaux',
              type: 'DATE'
            },
            {
              id: 'travaux_posterieurs_liste_travaux_posterieurs_description',
              label: 'Description des travaux',
              type: 'TEXT'
            }
          ],
          id: 'travaux_posterieurs_liste',
          label: 'Liste des travaux à venir',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'travaux_posterieurs_liste_travaux_posterieurs_description'
              }
            ],
            max: 1000,
            min: 0
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'non_concurrence',
          label: 'Clause de non concurrence par le Bailleur',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'droit_preference',
          label: 'Droit de préférence du preneur en cas de vente du local',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'locataire_recommande_electronique',
          label: 'Notifications par voie électronique',
          type: 'SELECT-BINARY'
        },
        {
          id: 'date_signature_bail',
          label: 'Date de signature du contrat de bail initial',
          type: 'DATE'
        }
      ],
      id: 'fd3a82b9_f0a9_4aeb_8fed_07abc023be4f',
      label: 'Informations générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'loue_actuellement',
          label: 'Bien actuellement occupé',
          type: 'SELECT-BINARY'
        },
        {
          id: 'date_liberation',
          label: 'Date de libération effective du bien',
          type: 'DATE'
        }
      ],
      id: '08aea69b_9033_4e4b_888e_077854191c12',
      label: 'Occupation du bien',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'bail_icpe',
          label: 'Activité de preneur soumise à autorisation (ICPE)',
          type: 'SELECT-BINARY'
        },
        {
          id: 'bail_icpe_date',
          label: "Date d'autorisation d'exploitation de l'ICPE",
          type: 'DATE'
        },
        {
          conditions: [[]],
          id: 'bail_icpe_autorisation',
          label: 'Autorisation exploitation ICPE',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'bail_dechets',
          label: 'Activité du Preneur génère des déchets',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostic_dossier_annexe',
          label: 'Dossier de diagnostic technique annexé au contrat',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostic_resultats_statut',
          label: 'Résultats des diagnostics repris dans le contrat',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'norme_accessibilite',
          label: "Local concerné par les normes d'accessibilité (Etablissement recevant du public)",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'norme_accessibilite',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'norme_accessibilite_statut',
          label: "Local conforme aux normes d'accessibilité",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'accessibilite_travaux_agenda',
              label: "Un Agenda D'Accessibilité Programmée a été déposé"
            },
            {
              id: 'accessibilite_travaux_derogation_obtenue',
              label: 'Une dérogation a été obtenue'
            },
            {
              id: 'accessibilite_travaux_derogation_demandee',
              label: 'Une dérogation a été demandée'
            },
            {
              id: 'accessibilite_travaux_bailleur',
              label: 'Le Bailleur se charge des travaux'
            },
            {
              id: 'accessibilite_travaux_lcoataire',
              label: 'Le Locataire se charge des travaux'
            },
            {
              id: 'accessibilite_travaux_undefined',
              label: "Aucune démarche n'a été effectuée"
            }
          ],
          conditions: [
            [
              {
                id: 'norme_accessibilite',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_statut',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'norme_accessibilite_travaux',
          label: 'Concernant les travaux de mise en conformité',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'norme_accessibilite',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_statut',
                value: 'non',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_travaux',
                value: 'accessibilite_travaux_agenda',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'travaux_adap_date',
          label: "Date de l'Agenda d'Accessibilité Programmé",
          type: 'DATE'
        },
        {
          conditions: [
            [
              {
                id: 'norme_accessibilite',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_statut',
                value: 'non',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_travaux',
                value: 'accessibilite_travaux_derogation_obtenue',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'travaux_derogation_obtention',
          label: "Date de l'obtention de la dérogation",
          type: 'DATE'
        },
        {
          conditions: [
            [
              {
                id: 'norme_accessibilite',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_statut',
                value: 'non',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_travaux',
                value: 'accessibilite_travaux_derogation_demandee',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'travaux_derogation_depot',
          label: 'Date de demande de la dérogation',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'partie_bailleur',
              label: 'Le Bailleur'
            },
            {
              id: 'partie_locataire',
              label: 'Le Locataire'
            }
          ],
          conditions: [
            [
              {
                id: 'norme_accessibilite',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_statut',
                value: 'non',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_travaux',
                value: 'accessibilite_travaux_agenda',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'norme_accessibilite_travaux_agenda_charge',
          label: "Suite à l'Agenda d'Accessibilité Programmé, les travaux seront réalisés par",
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'derogation_pendante',
              label: "Le délai de réponse de l'administration n'a pas encore expiré"
            },
            {
              id: 'derogation_tacite_positif',
              label:
                "Le délai de réponse est expiré et l'absence de réponse vaut autorisation tacite (ERP de catégorie 3 à 5)"
            },
            {
              id: 'derogation_tacite_negatif',
              label: "Le délai de réponse est expiré et l'absence de réponse vaut refus tacite (ERP de catégorie 1 à 2)"
            }
          ],
          conditions: [
            [
              {
                id: 'norme_accessibilite',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_statut',
                value: 'non',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_travaux',
                value: 'accessibilite_travaux_derogation_demandee',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'norme_accessibilite_travaux_derogation',
          label: 'Concernant la dérogation demandée',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'norme_accessibilite',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_statut',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'norme_accessibilite_travaux_derogation_convention',
          label: 'Les parties conviennent de la dérogation suivante',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'ajout_decret_tertiaire',
          label: 'Ajouter le décret tertiaire',
          type: 'SELECT-BINARY'
        }
      ],
      id: '41a4dcbc_4374_43bb_a0a5_7d13ae25486f',
      label: 'Règlementations spéciales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'clause_particuliere',
          label: 'Clause particulière supplémentaire',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'clause_particuliere_liste_clause_particuliere_liste_titre',
              label: 'Titre de la clause',
              type: 'TEXT'
            },
            {
              id: 'clause_particuliere_liste_clause_particuliere_liste_contenu',
              label: 'Contenu de la clause',
              type: 'TEXTAREA'
            }
          ],
          conditions: [
            [
              {
                id: 'clause_particuliere',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'clause_particuliere_liste',
          label: 'Clause particulière',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'clause_particuliere_liste_clause_particuliere_liste_titre'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        }
      ],
      id: '697758dd_50b8_49b8_8181_dd393faef0e9',
      label: 'Clause particulière',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'clause_particuliere_engagement',
          label: 'Clause particulière supplémentaire',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'clause_particuliere_liste_engagement_clause_particuliere_liste_titre_engagement',
              label: 'Titre de la clause',
              type: 'TEXT'
            },
            {
              id: 'clause_particuliere_liste_engagement_clause_particuliere_liste_contenu_engagement',
              label: 'Contenu de la clause',
              type: 'TEXTAREA'
            }
          ],
          id: 'clause_particuliere_liste_engagement',
          label: 'Clause particulière',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'clause_particuliere_liste_engagement_clause_particuliere_liste_titre'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        }
      ],
      id: 'bb761d2b_a9a5_4a2f_934c_fd48e94d2b67',
      label: 'Clause particulière',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'avenant_texte',
          label: "Modification opérée par l'avenant",
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avenant_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '3f5fbdc9_0a14_49d1_80d4_f0381ebd491c',
      label: 'Avenant',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'engagement_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: 'd21499bd_497a_46b6_9452_568c5386e22e',
      label: 'Engagement de prise à bail',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          conditions: [
            [
              {
                id: 'etat_des_lieux',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'etat_des_lieux_doc',
          label: 'Etat des lieux',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'norme_accessibilite',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'attestation_accessibilite',
          label: "Attestation d'accessibilité",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'norme_accessibilite',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_statut',
                value: 'non',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_travaux',
                value: 'accessibilite_travaux_agenda',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'adap',
          label: "Agenda d'Accessibilité Programmé",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'norme_accessibilite',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_statut',
                value: 'non',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_travaux',
                value: 'accessibilite_travaux_derogation_obtenue',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'autorisation_derogation',
          label: "Autorisation de dérogation aux normes d'accessibilité",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'norme_accessibilite',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_statut',
                value: 'non',
                type: 'EQUALS'
              },
              {
                id: 'norme_accessibilite_travaux',
                value: 'accessibilite_travaux_derogation_demandee',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'demande_derogation',
          label: "Demande de dérogation aux normes d'accessibilité",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'garantie_cautionnement',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'garantie_cautionnement_annexe',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'cautionnement',
          label: 'Acte de Cautionnement',
          type: 'UPLOAD'
        }
      ],
      id: 'c5f24a3c_309d_4136_b385_bca502140ea1',
      label: 'Documents',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'conge_vente_conditions',
          label: 'Conditions de la vente',
          type: 'TEXT'
        }
      ],
      id: '79af9bc0_74ef_442d_bd26_34a59b6f2c34',
      label: 'Congé',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BAIL_COMMERCIAL',
  label: 'Bail Commercial',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BAIL_COMMERCIAL',
  specificTypes: ['OPERATION', 'IMMOBILIER', 'LOCATION_COMMERCIAL', 'BAIL_COMMERCIAL'],
  type: 'RECORD'
};
