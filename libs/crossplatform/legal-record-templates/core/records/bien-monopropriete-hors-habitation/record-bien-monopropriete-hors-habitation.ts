// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordBienMonoproprieteHorsHabitation: LegalRecordTemplate = {
  config: {
    tags: {
      programme_superficie: {
        questionId: 'programme_superficie',
        format: 'AREA',
        order: 0
      }
    },
    recordLinks: [
      {
        id: 'ENSEMBLE_IMMOBILIER',
        specificTypes: ['COMPOSITION', 'ENSEMBLE_IMMOBILIER'],
        label: 'Ensemble immobilier',
        constraints: {
          min: 1,
          max: 1
        },
        creation: {
          autoCreate: false
        }
      }
    ],
    search: ['numero_logement'],
    duplicate: ['numero_logement']
  },
  form: [
    {
      children: [
        {
          filters: {
            qualificationQuestions: true
          },
          id: 'adresse',
          label: 'Adresse du Bien',
          type: 'ADDRESS'
        },
        {
          id: 'nombre_piece',
          label: 'Nombre de pièces',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'caracteristique_neuf',
              label: 'Neuf'
            },
            {
              id: 'caracteristique_ancien',
              label: 'Ancien'
            },
            {
              id: 'caracteristique_vefa',
              label: "En état futur d'achèvement"
            },
            {
              id: 'caracteristique_renover',
              label: 'A rénover'
            }
          ],
          id: 'caracteristiques',
          label: 'Caractéristiques et état du bien',
          type: 'SELECT'
        },
        {
          id: 'programme_superficie',
          label: 'Superficie du Bien',
          suffix: 'm2',
          type: 'NUMBER'
        },
        {
          id: 'programme_niveau',
          label: 'Niveau',
          type: 'TEXT'
        },
        {
          id: 'programme_affectation',
          label: 'Affectation',
          suffix: 'm2',
          type: 'NUMBER'
        },
        {
          id: 'programme_lineaire_vitrine',
          label: 'Linéaire Vitrine',
          suffix: 'm2',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'mandat_superficie_mandant',
              label: 'Le Mandant'
            },
            {
              id: 'mandat_superficie_mandataire',
              label: 'Le Mandataire'
            }
          ],
          id: 'mandat_superficie_fourniture',
          label: 'La superficie est établie par',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'moins_10_ans',
              label: 'Il y a moins de 10 ans',
              icon: 'calendar.svg'
            },
            {
              id: 'apres_1997',
              label: 'Après le 1er juillet 1997 (date du permis)',
              icon: 'calendar.svg'
            },
            {
              id: '1949_1997',
              label: 'Après le 1er janvier 1949',
              icon: 'calendar.svg'
            },
            {
              id: 'avant_1949',
              label: 'Avant 1949',
              icon: 'calendar.svg'
            }
          ],
          id: 'construction',
          label: "L'immeuble a été construit",
          type: 'SELECT-PICTURES'
        },
        {
          filters: {
            qualificationQuestions: true
          },
          id: 'numero_logement',
          label: 'Numéro du local',
          type: 'TEXT'
        },
        {
          id: 'usage',
          label: 'Le local a comme usage',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'habitation',
              label: 'Habitation'
            },
            {
              id: 'mixte',
              label: 'Mixte'
            },
            {
              id: 'commercial',
              label: 'Commercial'
            },
            {
              id: 'professionnel',
              label: 'Professionnel'
            }
          ],
          id: 'usage_gestion',
          label: 'Usage du bien',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'meublee_statut',
          label: 'Bien meublé',
          type: 'SELECT-BINARY'
        },
        {
          id: 'destination_libre',
          label: 'Destination du bien',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'exclusion_bail',
          label: 'Exclusion de certaines parties réservées par le propriétaire',
          type: 'SELECT-BINARY'
        },
        {
          id: 'exclusion_bail_liste',
          label: 'Désignation des parties réservées',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'autres_caracteristiques',
          label: "Le bien possède-t-il d'autres caractéristiques ?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'autres_caracteristiques_liste',
          label: 'Autres caractéristiques',
          type: 'TEXTAREA'
        },
        {
          id: 'programme_typologie',
          label: 'Typologie du Bien',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'enregistrement_meuble',
          label: 'Enregistrement du Meublé obligatoire',
          type: 'SELECT-BINARY'
        },
        {
          id: 'enregistrement_meuble_numero',
          label: "Numéro d'enregistrement du Meublé",
          type: 'TEXT'
        }
      ],
      id: 'a245d301_81df_45d6_b417_5145e55e1083',
      label: 'Informations générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          filters: {
            recordOnly: true
          },
          id: 'designation',
          label: 'Désignation du bien',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'designation_modifiee',
          label: 'Désignation du bien actuelle différente de celle du titre de propriété',
          type: 'SELECT-BINARY'
        },
        {
          id: 'designation_ancienne',
          label: 'Ancienne désignation du bien, telle que figurant dans le titre de propriété',
          type: 'TEXTAREA'
        },
        {
          id: 'designation_ancienne_travaux',
          label: 'Liste des travaux effectués depuis la désignation du titre',
          type: 'TEXTAREA'
        },
        {
          children: [
            {
              id: 'designation_ancienne_factures',
              label: 'Factures',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_assurance_decennale',
              label: 'Assurance décennale',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_assurance_do',
              label: 'Assurance dommage-ouvrage',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_permis',
              label: 'Permis de construire',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_declaration_travaux',
              label: 'Déclaration de travaux',
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_declaration_travaux_achevement',
              label: "Déclaration d'achèvement des travaux",
              type: 'UPLOAD'
            },
            {
              id: 'designation_ancienne_certificat_conformite',
              label: 'Certificat de conformité',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'designation_modifiee',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: '1325fg20_p36d_o4e2_lk4e_0d5f1380452e',
          label: 'CONDITION_BLOCK_Document_travaux',
          type: 'CONDITION_BLOCK'
        },
        {
          id: 'designation_avenant',
          label: 'Désignation modifiée du bien',
          type: 'TEXTAREA'
        },
        {
          id: 'numero_immatriculation',
          label: "Numéro d'immatriculation du bien",
          type: 'TEXT'
        },
        {
          id: 'capacite_personne',
          label: 'Capacité du bien (personnes maximum)',
          type: 'NUMBER'
        }
      ],
      id: '66c56625_a1b4_4d70_b464_d1159b41d8a0',
      label: "Description de l'immeuble",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'dernier_decompte_charges',
          label: 'Dernier décompte de charges',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'occupation_mandat_libre',
              label: 'Libre de toute occupation'
            },
            {
              id: 'occupation_mandat_liberation',
              label: 'Libre à la date'
            },
            {
              id: 'occupation_mandat_loue',
              label: 'Loué selon état locatif figurant en annexe'
            }
          ],
          id: 'occupation_mandat',
          label: 'Le jour de la vente définitive, le bien sera',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'occupation_mandat',
                value: 'occupation_mandat_liberation',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'occupation_mandat_liberation',
          label: 'Date de libération du bien',
          type: 'DATE'
        },
        {
          conditions: [
            [
              {
                id: 'occupation_mandat',
                value: 'occupation_mandat_loue',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeExcludedInOperationConfig: true
          },
          id: 'mandat_bail',
          label: 'Contrat de bail',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'occupation_statut',
          label: 'Le bien est-il occupé par une personne autre que le propriétaire ou sa famille ?',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Occupé à titre gratuit',
                  id: 'occupation_gratuit'
                },
                {
                  label: 'Loué selon un contrat de bail',
                  id: 'location_bail'
                }
              ],
              id: 'occupation_location',
              label: 'Le bien est actuellement',
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'occupation_location',
                    type: 'EQUALS',
                    value: 'occupation_gratuit'
                  },
                  {
                    id: 'occupation_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'occupation_liste',
              label: 'Liste des occupants',
              type: 'TEXTAREA'
            },
            {
              children: [
                {
                  children: [
                    {
                      choices: [
                        {
                          label: 'Un bail professionnel',
                          id: 'professionnel'
                        },
                        {
                          label: 'Un bail commercial',
                          id: 'commercial'
                        },
                        {
                          label: 'Un bail simple',
                          id: 'simple'
                        },
                        {
                          label: 'Un bail habitation loi 1948',
                          id: '1948'
                        },
                        {
                          label: 'Un bail rural',
                          id: 'rural'
                        },
                        {
                          label: 'Autre type de bail',
                          id: 'bail_autre'
                        }
                      ],
                      id: 'location_bail_liste_location_bail_type',
                      label: 'Type de bail',
                      type: 'SELECT'
                    },
                    {
                      conditions: [
                        [
                          {
                            id: 'location_bail_liste_location_bail_type',
                            type: 'EQUALS',
                            value: 'bail_autre'
                          },
                          {
                            id: 'occupation_location',
                            type: 'EQUALS',
                            value: 'location_bail'
                          },
                          {
                            id: 'occupation_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: 'location_bail_liste_location_bail_type_autre',
                      label: 'Précisez le type de bail',
                      type: 'TEXT'
                    },
                    {
                      id: 'location_bail_liste_location_bail_date_signature',
                      label: 'Date de signature du bail',
                      type: 'DATE'
                    },
                    {
                      id: 'location_bail_liste_location_bail_locataires',
                      label: 'Liste des locataires',
                      type: 'TEXTAREA'
                    },
                    {
                      id: 'location_bail_liste_location_bail_loyer',
                      label: 'Montant du loyer',
                      type: 'PRICE'
                    },
                    {
                      id: 'location_bail_liste_location_bail_charges',
                      label: 'Montant des charges',
                      type: 'PRICE'
                    },
                    {
                      id: 'location_bail_liste_location_bail_loyer_cfp',
                      label: 'Montant du loyer',
                      suffix: 'CFP',
                      type: 'PRICE'
                    },
                    {
                      id: 'location_bail_liste_location_bail_charges_cfp',
                      label: 'Montant des charges',
                      suffix: 'CFP',
                      type: 'PRICE'
                    },
                    {
                      filters: {
                        mustBeIncludedInOperationConfig: true
                      },
                      id: 'location_bail_liste_contrat_bail',
                      label: 'Contrat de bail',
                      type: 'UPLOAD'
                    }
                  ],
                  id: 'location_bail_liste',
                  label: 'Baux',
                  repetition: {
                    label: [
                      {
                        type: 'VARIABLE',
                        value: 'location_bail_liste_location_bail_type'
                      },
                      {
                        type: 'TEXT',
                        value: '-'
                      },
                      {
                        type: 'VARIABLE',
                        value: 'location_bail_liste_location_bail_type_autre'
                      }
                    ],
                    max: 1000,
                    min: 1
                  },
                  type: 'REPEAT'
                },
                {
                  choices: [
                    {
                      label: 'Sur la totalité du bien',
                      id: 'location_totale'
                    },
                    {
                      label: 'Sur une partie du bien seulement',
                      id: 'location_partielle'
                    }
                  ],
                  id: 'location_partielle_statut',
                  label: 'La location porte',
                  type: 'SELECT'
                }
              ],
              conditions: [
                [
                  {
                    id: 'occupation_location',
                    type: 'EQUALS',
                    value: 'location_bail'
                  },
                  {
                    id: 'occupation_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: '1430a350_9e90_4867_b4c1_b21e983e69e1',
              label: 'Information sur les baux',
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'occupation_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'fbdfe40e_0599_4ad3_a567_716a8b14acbe',
          label: 'Occupation du bien',
          type: 'CATEGORY'
        }
      ],
      id: '939efe12_1fc9_4b88_8dec_4253ec9aaf58',
      label: 'Situation locative',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostic_a_jour',
          label: 'Les diagnostics sont à jour',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              label: "Un seul dossier de diagnostic est réalisé pour tout l'immeuble",
              id: 'immeuble_entier_dossier_diagnostic_unique'
            },
            {
              label: 'Un dossier de diagnostic est réalisé pour chaque logement',
              id: 'immeuble_entier_dossier_diagnostic_pluralite'
            }
          ],
          id: 'immeuble_entier_diagnostic',
          label: 'Concernant le dossier de diagnostic technique',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostics_resultats',
          label: 'Le résultat des diagnostics est indiqué dans le compromis',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostics_techniques_diagnostiqueur_unique',
                  label: 'Diagnostiqueur unique',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostics_techniques_diagnostiqueur_unique_date',
                  label: 'Date unique',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'diagnostics_techniques_diagnostiqueur_unique_nom',
                  label: 'Nom du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  id: 'diagnostics_techniques_diagnostiqueur_unique_adresse',
                  label: 'Adresse du diagnostiqueur',
                  type: 'ADDRESS'
                },
                {
                  id: 'attestation_diagnostiqueur',
                  label: 'Attestation du diagnostiqueur',
                  type: 'UPLOAD'
                },
                {
                  id: 'diagnostics_techniques_domofrance_date',
                  label: "Date d'établissement du dossier de diagnostic technique",
                  type: 'DATE'
                }
              ],
              id: 'dacc4827_1a40_41b8_acdb_fda7d472eaf1',
              label: 'Information sur le diagnostiqueur',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_amiante_statut',
                  label: 'Diagnostic réalisé',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      id: 'diagnostic_amiante_date',
                      label: 'Date du diagnostic',
                      type: 'DATE'
                    },
                    {
                      choices: [
                        {
                          label: "Il n'a pas été repéré des matériaux contenant de l'amiante",
                          id: 'absence'
                        },
                        {
                          label: "Il a été repéré des matériaux contenant de l'amiante de la liste A",
                          id: 'liste_a'
                        },
                        {
                          label: "Il a été repéré des matériaux contenant de l'amiante de la liste B",
                          id: 'liste_b'
                        },
                        {
                          label: "Des locaux ou parties de locaux n'ont pas pu être visités",
                          id: 'non_visite'
                        }
                      ],
                      id: 'diagnostic_amiante_resultat',
                      label: 'Résultat du diagnostic',
                      multiple: true,
                      type: 'SELECT'
                    },
                    {
                      id: 'diagnostic_amiante_resultat_libre',
                      label: 'Résultat du diagnostic',
                      type: 'TEXTAREA'
                    },
                    {
                      id: 'diagnostic_amiante',
                      label: 'Diagnostic Amiante',
                      type: 'UPLOAD'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'diagnostic_amiante_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  id: '0d1b244f_cf07_4c73_91e5_38df81e7428c',
                  label: 'CONDITION_BLOCK_Informations sur le diagnostic amiante',
                  type: 'CONDITION_BLOCK'
                },
                {
                  children: [
                    {
                      id: 'diagnostic_amiante_diagnostiqueur_nom',
                      label: 'Nom du diagnostiqueur',
                      type: 'TEXT'
                    },
                    {
                      id: 'diagnostic_amiante_diagnostiqueur_adresse',
                      label: 'Adresse du diagnostiqueur',
                      type: 'ADDRESS'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        id: 'diagnostics_techniques_diagnostiqueur_unique',
                        type: 'EQUALS',
                        value: 'non'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                          'OPERATION__CDC__IMMOBILIER__VENTE',
                          'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                        ]
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'diagnostics_techniques_diagnostiqueur_unique',
                        type: 'EQUALS',
                        value: 'non'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                          'OPERATION__CDC__IMMOBILIER__VENTE',
                          'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                        ]
                      }
                    ],
                    [
                      {
                        id: 'diagnostics_techniques_diagnostiqueur_unique',
                        type: 'EQUALS',
                        value: 'non'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      },
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                          'OPERATION__CDC__IMMOBILIER__VENTE',
                          'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                        ]
                      }
                    ]
                  ],
                  id: 'c602db6c_d26f_4906_a9c7_3c97f110d616',
                  label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
                  type: 'CONDITION_BLOCK'
                }
              ],
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: '1949_1997'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'avant_1949'
                  }
                ],
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ]
              ],
              id: '95e69642_a7c4_4a23_83bd_16108c706e14',
              label: 'Diagnostic Amiante',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_termites_commune',
                  label: 'Commune concernée par les termites',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'diagnostic_termites_statut',
                      label: 'Diagnostic réalisé',
                      type: 'SELECT-BINARY'
                    },
                    {
                      children: [
                        {
                          id: 'diagnostic_termites_date',
                          label: 'Date du diagnostic',
                          type: 'DATE'
                        },
                        {
                          choices: [
                            {
                              label: "Absence de traces d'infestation de termite",
                              id: 'absence'
                            },
                            {
                              label: "Présence de traces d'infestation de termite",
                              id: 'presence'
                            }
                          ],
                          id: 'diagnostic_termites_resultat',
                          label: 'Résultat du diagnostic',
                          type: 'SELECT'
                        },
                        {
                          children: [
                            {
                              id: 'diagnostic_termites_diagnostiqueur_nom',
                              label: 'Nom du diagnostiqueur',
                              type: 'TEXT'
                            },
                            {
                              id: 'diagnostic_termites_diagnostiqueur_adresse',
                              label: 'Adresse du diagnostiqueur',
                              type: 'ADDRESS'
                            }
                          ],
                          conditions: [
                            [
                              {
                                id: 'diagnostic_termites_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_termites_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_termites_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_termites_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_termites_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_termites_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_termites_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_termites_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_termites_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_termites_commune',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_termites_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_termites_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ]
                          ],
                          id: '39657acd_da10_4130_ac8e_8e807eab9a1f',
                          label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
                          type: 'CONDITION_BLOCK'
                        },
                        {
                          id: 'diagnostic_termites',
                          label: 'Diagnostic Termites',
                          type: 'UPLOAD'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'diagnostic_termites_commune',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_termites_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_termites_commune',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_termites_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_termites_commune',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_termites_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ]
                      ],
                      id: 'bb34112d_55ec_456e_a86c_71c64fc1aefa',
                      label: 'CONDITION_BLOCK_Informations sur le diagnostic termites',
                      type: 'CONDITION_BLOCK'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'diagnostic_termites_commune',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  id: '2729aaee_1943_48ac_95c6_7ffda9198f3b',
                  label: 'Zone concernée par les termites',
                  type: 'CATEGORY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_termites_informatif',
                  label: 'Contamination par les termites',
                  type: 'SELECT-BINARY'
                }
              ],
              id: '26f73f59_edf9_42c1_9dd4_2fc72fa99ee4',
              label: 'Diagnostic Termites',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_merule_commune',
                  label: 'Le bien est-il situé dans une commune concernée par la mérule ?',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'diagnostic_merule_statut',
                      label: 'Diagnostic réalisé',
                      type: 'SELECT-BINARY'
                    },
                    {
                      children: [
                        {
                          id: 'diagnostic_merule_date',
                          label: 'Date du diagnostic',
                          type: 'DATE'
                        },
                        {
                          choices: [
                            {
                              id: 'absence',
                              label: 'Absence de traces visibles de champignons lignivores'
                            },
                            {
                              id: 'presence',
                              label: 'Présence de traces visibles de champignons lignivores'
                            }
                          ],
                          id: 'diagnostic_merule_resultat',
                          label: 'Résultat du diagnostic',
                          type: 'SELECT'
                        },
                        {
                          children: [
                            {
                              id: 'diagnostic_merule_diagnostiqueur_nom',
                              label: 'Nom du diagnostiqueur',
                              type: 'TEXT'
                            },
                            {
                              id: 'diagnostic_merule_diagnostiqueur_adresse',
                              label: 'Adresse du diagnostiqueur',
                              type: 'ADDRESS'
                            }
                          ],
                          conditions: [
                            [
                              {
                                id: 'diagnostic_merule_commune',
                                value: 'oui',
                                type: 'EQUALS'
                              },
                              {
                                id: 'diagnostic_merule_statut',
                                value: 'oui',
                                type: 'EQUALS'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_merule_commune',
                                value: 'oui',
                                type: 'EQUALS'
                              },
                              {
                                id: 'diagnostic_merule_statut',
                                value: 'oui',
                                type: 'EQUALS'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_merule_commune',
                                value: 'oui',
                                type: 'EQUALS'
                              },
                              {
                                id: 'diagnostic_merule_statut',
                                value: 'oui',
                                type: 'EQUALS'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_merule_commune',
                                value: 'oui',
                                type: 'EQUALS'
                              },
                              {
                                id: 'diagnostic_merule_statut',
                                value: 'oui',
                                type: 'EQUALS'
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_merule_commune',
                                value: 'oui',
                                type: 'EQUALS'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_merule_commune',
                                value: 'oui',
                                type: 'EQUALS'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_merule_statut',
                                value: 'oui',
                                type: 'EQUALS'
                              },
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                id: 'diagnostic_merule_statut',
                                value: 'oui',
                                type: 'EQUALS'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ],
                            [
                              {
                                id: 'diagnostics_techniques_diagnostiqueur_unique',
                                type: 'EQUALS',
                                value: 'non'
                              },
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              }
                            ],
                            [
                              {
                                type: 'EQUALS_CONTRACT_MODELS',
                                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                                  'OPERATION__CDC__IMMOBILIER__VENTE',
                                  'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                                ]
                              }
                            ]
                          ],
                          id: '96e00c87_8227_42a7_9e09_a66ecf580e31',
                          label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
                          type: 'CONDITION_BLOCK'
                        },
                        {
                          id: 'diagnostic_merule',
                          label: 'Diagnostic Mérule',
                          type: 'UPLOAD'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'diagnostic_merule_commune',
                            value: 'oui',
                            type: 'EQUALS'
                          },
                          {
                            id: 'diagnostic_merule_statut',
                            value: 'oui',
                            type: 'EQUALS'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_merule_commune',
                            value: 'oui',
                            type: 'EQUALS'
                          },
                          {
                            id: 'diagnostic_merule_statut',
                            value: 'oui',
                            type: 'EQUALS'
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_merule_commune',
                            value: 'oui',
                            type: 'EQUALS'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_merule_statut',
                            value: 'oui',
                            type: 'EQUALS'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ]
                      ],
                      id: '3f5672e4_e395_4005_aa31_4de996778fdf',
                      label: 'CONDITION_BLOCK_Informations sur le diagnostic mérule',
                      type: 'CONDITION_BLOCK'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'diagnostic_merule_commune',
                        value: 'oui',
                        type: 'EQUALS'
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  id: '447dfc78_32af_437e_8188_9fe3a474fbec',
                  label: 'CONDITION_BLOCK_Zone concernée par la mérule',
                  type: 'CONDITION_BLOCK'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'diagnostic_merule_informatif',
                      label: 'Contamination par la mérule',
                      type: 'SELECT-BINARY'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'diagnostic_merule_commune',
                        value: 'oui',
                        type: 'DIFFERENT'
                      }
                    ]
                  ],
                  id: 'a35f222a_a0c9_4649_a076_91b3be594d3f',
                  label: 'CONDITION_BLOCK_Zone non concernée par la mérule',
                  type: 'CONDITION_BLOCK'
                }
              ],
              id: '5c7b2983_b191_41ea_8883_8bf605cb0bc3',
              label: 'Diagnostic Mérules',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  description: 'En cas de DPE Vierge la réponse doit être non',
                  id: 'dpe_details_statut',
                  label: 'Diagnostic réalisé',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      id: 'dpe_diagnostiqueur_date',
                      label: 'Date du diagnostic',
                      type: 'DATE'
                    },
                    {
                      description:
                        "Indiquer d'abord la lettre puis éventuellement le montant de la consommation. Exemple : E - 355",
                      id: 'dpe_consommation_energetique',
                      label: 'Performance / consommation énergétique',
                      type: 'TEXT'
                    },
                    {
                      id: 'dpe_emission_gaz',
                      label: 'Emission de gaz à effet de serre',
                      type: 'TEXT'
                    },
                    {
                      id: 'dpe_depense_annuelle',
                      label: "Montant des dépenses annuelles d'énergie",
                      type: 'TEXT'
                    },
                    {
                      id: 'dpe_depense_annuelle_annee',
                      label: "Année(s) de référence pour les dépenses d'énergie",
                      type: 'TEXT'
                    },
                    {
                      id: 'dpe',
                      label: 'Diagnostic de performance énergétique',
                      type: 'UPLOAD'
                    },
                    {
                      children: [
                        {
                          id: 'dpe_diagnostiqueur_nom',
                          label: 'Nom du diagnostiqueur',
                          type: 'TEXT'
                        },
                        {
                          id: 'dpe_diagnostiqueur_adresse',
                          label: 'Adresse du diagnostiqueur',
                          type: 'ADDRESS'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'diagnostics_techniques_diagnostiqueur_unique',
                            type: 'EQUALS',
                            value: 'non'
                          },
                          {
                            id: 'dpe_details_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'diagnostics_techniques_diagnostiqueur_unique',
                            type: 'EQUALS',
                            value: 'non'
                          },
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ],
                        [
                          {
                            id: 'dpe_details_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_TEMPLATES',
                            templates: [
                              'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                              'OPERATION__CDC__IMMOBILIER__VENTE',
                              'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                            ]
                          }
                        ],
                        [
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          },
                          {
                            type: 'EQUALS_TEMPLATES',
                            templates: [
                              'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                              'OPERATION__CDC__IMMOBILIER__VENTE',
                              'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                            ]
                          }
                        ]
                      ],
                      id: 'f0deca29_c7bd_4b3e_8157_e2bb93583cdb',
                      label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
                      type: 'CONDITION_BLOCK'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'dpe_details_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  id: '5da03117_2c92_4ee4_b6bb_482f2fb3671e',
                  label: 'CONDITION_BLOCK_Informations sur le diagnostic de performance énergétique',
                  type: 'CONDITION_BLOCK'
                }
              ],
              id: 'bd9bf0c1_046e_4a97_a980_506cd834864a',
              label: 'Diagnostic de performance Énergétique',
              type: 'CATEGORY'
            }
          ],
          id: '13258391_ee89_49f3_8b05_0d5f13808204',
          label: 'CONDITION_BLOCK_Information sur le dossier de diagnostic technique',
          type: 'CONDITION_BLOCK'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'erp_global',
              label:
                'Bien situé dans une zone couverte par un plan de prévention des risques naturels, miniers ou technologiques',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'indemnite_catastrophe',
              label: "Sinistre d'origine catastrophe naturelle survenu sur le bien",
              type: 'SELECT-BINARY'
            },
            {
              id: 'erp',
              label: 'Etat des risques',
              type: 'UPLOAD'
            }
          ],
          id: '882b9f0e_b42a_44b5_8452_cc41adcab394',
          label: 'Diagnostics environnementaux',
          type: 'CATEGORY'
        }
      ],
      id: '5170caa7_28c0_494a_91ea_dfc572ea8e0d',
      label: 'Diagnostics',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  label: '1 étoile',
                  id: '1'
                },
                {
                  label: '2 étoiles',
                  id: '2'
                },
                {
                  label: '3 étoiles',
                  id: '3'
                },
                {
                  label: '4 étoiles',
                  id: '4'
                },
                {
                  label: '5 étoiles',
                  id: '5'
                },
                {
                  label: 'Aucun classement',
                  id: 'aucun'
                }
              ],
              id: 'etat_descriptif_categorie_classement',
              label: 'Catégorie de classement',
              type: 'SELECT'
            },
            {
              id: 'etat_descriptif_arrete_prefectoral',
              label: 'Arrété préfectoral du',
              type: 'DATE'
            }
          ],
          id: '4b02e431_ee7c_4124_a074_81e2b1d2c57d',
          label: 'Renseignements Généraux',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Neuve',
                  id: 'neuve'
                },
                {
                  label: 'Récente',
                  id: 'recente'
                },
                {
                  label: 'Ancienne',
                  id: 'ancienne'
                }
              ],
              id: 'etat_descriptif_type_construction',
              label: 'Type de construction',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  label: 'Une maison',
                  id: 'maison'
                },
                {
                  label: 'Indépendante',
                  id: 'independant'
                },
                {
                  label: 'Avec jardin',
                  id: 'jardin'
                },
                {
                  label: 'Un studio',
                  id: 'studio'
                },
                {
                  label: 'Un appartement',
                  id: 'appartement'
                }
              ],
              id: 'etat_descriptif_type_bien',
              label: 'Type de bien',
              multiple: true,
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'etat_descriptif_plusieurs_logement',
              label: 'Meublé situé dans immeuble avec plusieurs logements',
              type: 'SELECT-BINARY'
            },
            {
              id: 'etat_descriptif_plusieurs_logement_nombre',
              label: 'Nombres de logements',
              type: 'NUMBER'
            },
            {
              choices: [
                {
                  label: 'Un appartement',
                  id: 'appartement'
                },
                {
                  label: 'Une villa',
                  id: 'villa'
                },
                {
                  label: 'Occupées partiellement par le propriétaire',
                  id: 'proprietaire'
                },
                {
                  label: "Occupées par d'autres locataires",
                  id: 'locataire'
                }
              ],
              id: 'etat_descriptif_plusieurs_logement_situation',
              label: 'Pièces situées dans',
              multiple: true,
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'etat_descriptif_handicap',
              label: 'Meublé accessibles aux personnes handicapées',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  label: 'Chauffage central',
                  id: 'chauffage'
                },
                {
                  label: 'Climatisation',
                  id: 'climatisation'
                },
                {
                  label: "Rafraîchissement d'air",
                  id: 'rafraichissement'
                }
              ],
              id: 'etat_descriptif_chauffage',
              label: 'Chauffage / Climatisation',
              multiple: true,
              type: 'SELECT'
            },
            {
              id: 'etat_descriptif_superficie',
              label: 'Superficie totale',
              suffix: 'm2',
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_nombre_pieces',
              label: "Nombres de pièces d'habitation",
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_nombre_salle_eau',
              label: "Nombre de salle d'eau",
              type: 'NUMBER'
            },
            {
              choices: [
                {
                  label: 'Cuisine séparée',
                  id: 'cuisine_separee'
                },
                {
                  label: 'Coin-cuisine dans la pièce principale',
                  id: 'cuisine'
                },
                {
                  label: "Existence d'une entrée",
                  id: 'entree'
                }
              ],
              id: 'etat_descriptif_pieces_caracteristique',
              label: 'Caractéristiques des pièces',
              multiple: true,
              type: 'SELECT'
            },
            {
              choices: [
                {
                  label: 'Jardin privatif',
                  id: 'jardin'
                },
                {
                  label: 'Parc privatif',
                  id: 'parc'
                },
                {
                  label: 'Cour privative',
                  id: 'cour'
                },
                {
                  label: 'Garage privatif',
                  id: 'garage'
                },
                {
                  label: 'Emplacement de voiture privatif',
                  id: 'emplacement'
                },
                {
                  label: 'Terrasse privative',
                  id: 'terrasse'
                },
                {
                  label: 'Loggia privative',
                  id: 'loggia'
                },
                {
                  label: 'Balcon privatif',
                  id: 'balcon'
                }
              ],
              id: 'etat_descriptif_jouissance_privative',
              label: 'Jouissance privative',
              multiple: true,
              type: 'SELECT'
            },
            {
              id: 'etat_descriptif_jouissance_terrasse_superficie',
              label: 'Superficie de la terrasse',
              suffix: 'm2',
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_jouissance_terrasse_vue',
              label: 'Vue de la terrasse',
              type: 'TEXT'
            },
            {
              id: 'etat_descriptif_jouissance_loggia_superficie',
              label: 'Superficie de la loggia',
              suffix: 'm2',
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_jouissance_loggia_vue',
              label: 'Vue de la loggia',
              type: 'TEXT'
            },
            {
              id: 'etat_descriptif_jouissance_balcon_superficie',
              label: 'Superficie du balcon',
              suffix: 'm2',
              type: 'NUMBER'
            },
            {
              id: 'etat_descriptif_jouissance_balcon_vue',
              label: 'Vue du balcon',
              type: 'TEXT'
            }
          ],
          id: '890df85b_be89_4852_956e_e962fd935252',
          label: 'Principales caractéristiques',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Isolé',
                  id: 'isole'
                },
                {
                  label: 'Dans une ferme',
                  id: 'ferme'
                },
                {
                  label: 'Dans un hameau',
                  id: 'hameau'
                },
                {
                  label: 'Dans un village',
                  id: 'village'
                },
                {
                  label: 'Dans une ville',
                  id: 'ville'
                }
              ],
              id: 'etat_descriptif_situation_meuble',
              label: 'Le meublé est',
              type: 'SELECT'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_ski',
                  label: 'Proximité de pistes de ski',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_ski_distance',
                  label: 'Distance des pistes de ski',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_mer',
                  label: 'Proximité de la mer',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_mer_distance',
                  label: 'Distance de la mer',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_lac',
                  label: "Proximité d'un lac",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_lac_distance',
                  label: 'Distance du lac',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_plage',
                  label: "Proximité d'une plage",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_plage_distance',
                  label: 'Distance de la plage',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_foret',
                  label: "Proximité d'une forêt",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_foret_distance',
                  label: 'Distance de la forêt',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_riviere',
                  label: "Proximité d'une rivière",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_riviere_distance',
                  label: 'Distance de la rivière',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_port',
                  label: "Proximité d'un port",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_port_distance',
                  label: 'Distance du port',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_centre_distance',
                  label: 'Distance du centre ville',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_autres',
                  label: "Autres centres d'intérêt",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_autres_distance',
                  label: "Liste et distance des autres centres d'intérêt",
                  type: 'TEXTAREA'
                }
              ],
              id: '48cfde38_72a7_4902_a65e_46d0d2047856',
              label: "Distance des centres d'intérêt touristique",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'etat_descriptif_situation_proximite_sncf_distance',
                  label: 'Distance gare SNCF',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_gare_distance',
                  label: 'Distance gare routière',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_aeroport_distance',
                  label: 'Distance aéroport',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_medecin_distance',
                  label: 'Distance Médecin',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_hopital_distance',
                  label: 'Distance Hôpital',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_supermarche_distance',
                  label: 'Distance Centre commercial/supermarché',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_restaurant_distance',
                  label: 'Distance restaurant',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_epicerie_distance',
                  label: 'Distance épicerie',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_laverie_distance',
                  label: 'Distance laverie',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_services_autres',
                  label: 'Autres services',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_services_autres_distance',
                  label: 'Distance autres services',
                  suffix: 'km',
                  type: 'NUMBER'
                }
              ],
              id: '40afe472_b31c_43b3_9084_b5d4d0b11214',
              label: 'Distance des principaux services',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_inconvenient_bruits',
                  label: 'Bruits',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_inconvenient_bruits_liste',
                  label: 'Liste des bruits',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_inconvenient_odeurs',
                  label: 'Odeurs',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_inconvenient_odeurs_liste',
                  label: 'Liste des odeurs',
                  type: 'TEXT'
                }
              ],
              id: 'abf23ccb_d921_4ca5_adb4_5ed9c5de7b18',
              label: 'Inconvénients de voisinage',
              type: 'CATEGORY'
            }
          ],
          id: 'e5ee6829_bf69_427b_8fad_ffcf86a37725',
          label: 'Situation dans la localité',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'etat_descriptif_etat_entretien',
              label: "Etat d'entretien général",
              type: 'TEXT'
            },
            {
              children: [
                {
                  children: [
                    {
                      id: 'etat_desciptif_piece_agencement_nom_piece',
                      label: 'Nom de la pièce',
                      type: 'TEXT'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_surface_piece',
                      label: 'Surface',
                      suffix: 'm2',
                      type: 'NUMBER'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_fenetres_piece',
                      label: 'Nombre de Fenêtres',
                      type: 'NUMBER'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_lits_piece',
                      label: 'Nombres de Lits',
                      type: 'NUMBER'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_mobilier_piece',
                      label: 'Mobilier',
                      type: 'TEXT'
                    },
                    {
                      choices: [
                        {
                          label: 'Nord',
                          id: 'nord'
                        },
                        {
                          label: 'Sud',
                          id: 'sud'
                        },
                        {
                          label: 'Est',
                          id: 'est'
                        },
                        {
                          label: 'Ouest',
                          id: 'ouest'
                        }
                      ],
                      id: 'etat_desciptif_piece_agencement_exposition_piece',
                      label: 'Exposition',
                      type: 'SELECT'
                    },
                    {
                      id: 'etat_desciptif_piece_agencement_vue_piece',
                      label: 'Vue',
                      type: 'TEXT'
                    },
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'etat_desciptif_piece_agencement_independance_piece',
                      label: 'Indépendance de la pièce',
                      type: 'SELECT-BINARY'
                    }
                  ],
                  id: 'etat_desciptif_piece_agencement',
                  label: 'Ajouter une pièce',
                  repetition: {
                    label: [
                      {
                        type: 'TEXT',
                        value: ' '
                      },
                      {
                        type: 'VARIABLE',
                        value: 'etat_desciptif_piece_agencement_nom_piece'
                      }
                    ],
                    max: 1000,
                    min: 1
                  },
                  type: 'REPEAT'
                }
              ],
              id: '3ac70553_95c9_42da_b765_5a5ac98ae01d',
              label: 'Agencement des pièces',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Evier avec eau chaude/froide',
                      id: 'evier'
                    },
                    {
                      label: 'VMC',
                      id: 'vmc'
                    },
                    {
                      label: 'Hotte aspirante',
                      id: 'hotte'
                    },
                    {
                      label: 'Table de cuisson',
                      id: 'cuisson'
                    },
                    {
                      label: 'Four',
                      id: 'four'
                    },
                    {
                      label: 'Four à micro-ondes',
                      id: 'microonde'
                    },
                    {
                      label: 'Réfrigérateur',
                      id: 'refrigerateur'
                    },
                    {
                      label: 'Congélateur',
                      id: 'congelateur'
                    },
                    {
                      label: 'Lave-vaisselle',
                      id: 'lave'
                    },
                    {
                      label: 'Batterie de cuisine',
                      id: 'batterie'
                    },
                    {
                      label: 'Autocuiseur',
                      id: 'autocuiseur'
                    }
                  ],
                  id: 'etat_descriptif_agencement_cuisine',
                  label: 'Equipements de la cuisine',
                  multiple: true,
                  type: 'SELECT'
                },
                {
                  id: 'etat_descriptif_agencemement_cuisine_feux',
                  label: 'Nombres de feux',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      label: 'Gaz de ville',
                      id: 'gaz'
                    },
                    {
                      label: 'Bouteille de gaz',
                      id: 'bouteille'
                    },
                    {
                      label: 'Electricité',
                      id: 'electricite'
                    },
                    {
                      label: 'Mixte',
                      id: 'mixte'
                    }
                  ],
                  id: 'etat_descriptif_agencemement_cuisine_plaque',
                  label: 'Alimentation de la plaque',
                  type: 'SELECT'
                },
                {
                  id: 'etat_descriptif_agencemement_cuisine_refrigerateur_contenance',
                  label: 'Contenance du Réfrigérateur',
                  suffix: 'litres',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_agencemement_cuisine_refrigerateur_compartiment',
                  label: 'Réfrigérateur avec compartiment conservation',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_agencemement_cuisine_congelateur_contenance',
                  label: 'Contenance du congélateur',
                  suffix: 'litres',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_agencemement_cuisine_batterie_descriptif',
                  label: 'Batterie de cuisine descriptif',
                  type: 'TEXT'
                }
              ],
              id: 'bfaeb616_8fed_49d1_aa7b_32906372c663',
              label: 'Agencement de la cuisine',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  children: [
                    {
                      id: 'etat_desciptif_sanitaire_lavabos_nombre',
                      label: 'Nombre de lavabos',
                      type: 'NUMBER'
                    },
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'etat_desciptif_sanitaire_douche',
                      label: 'Présence douche',
                      type: 'SELECT-BINARY'
                    },
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'etat_desciptif_sanitaire_baignoire',
                      label: 'Présence baignoire avec douche',
                      type: 'SELECT-BINARY'
                    }
                  ],
                  id: 'etat_desciptif_sanitaire',
                  label: "Ajouter une salle d'eau",
                  repetition: {
                    label: [
                      {
                        type: 'TEXT',
                        value: " Salle d'eau"
                      }
                    ],
                    max: 1000,
                    min: 1
                  },
                  type: 'REPEAT'
                },
                {
                  id: 'etat_desciptif_sanitaire_wc',
                  label: 'Nombre de WC intérieur au meublé',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_desciptif_sanitaire_wc_independant',
                  label: "Nombre de WC intérieur au meublé et indépendant de la salle d'eau",
                  type: 'NUMBER'
                }
              ],
              id: '53197bd6_aed2_4ce3_9271_97ee5b780e0e',
              label: 'Equipements sanitaires',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Téléphone dans le logement',
                      id: 'telephone'
                    },
                    {
                      label: 'Téléphone à proximité',
                      id: 'telephone_proximite'
                    },
                    {
                      label: 'Accès internet haut debit',
                      id: 'internet'
                    },
                    {
                      label: 'TV couleur',
                      id: 'tv'
                    },
                    {
                      label: 'Lecteur DVD',
                      id: 'lecteur'
                    },
                    {
                      label: 'Chaîne Hi-Fi avec radio',
                      id: 'radio'
                    },
                    {
                      label: 'Lave-linge électrique',
                      id: 'lave_linge'
                    },
                    {
                      label: 'Sèche-Linge électrique',
                      id: 'seche_linge'
                    },
                    {
                      label: 'Etendoir à linge',
                      id: 'etendoir'
                    },
                    {
                      label: 'Fer à repasser',
                      id: 'fer'
                    },
                    {
                      label: 'Sèche-cheveux',
                      id: 'seche_cheveux'
                    },
                    {
                      label: 'Aspirateur',
                      id: 'aspirateur'
                    },
                    {
                      label: 'Autres équipements',
                      id: 'autre'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien',
                  label: 'Equipements du bien',
                  multiple: true,
                  type: 'SELECT'
                },
                {
                  id: 'etat_descriptif_equipement_bien_telephone_numero',
                  label: 'Numéro de téléphone',
                  type: 'PHONE'
                },
                {
                  id: 'etat_descriptif_equipement_bien_telephone_distance',
                  label: 'Distance du Téléphone',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_lave_linge',
                  label: 'Lave-linge particulier au logement',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_seche_linge',
                  label: 'Sèche-linge particulier au logement',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_etendoir',
                  label: 'Etendoir à linge intérieur au logement',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_planche',
                  label: 'Planche à repasser',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_equipement_bien_autre',
                  label: 'Autre équipements à noter',
                  type: 'TEXT'
                },
                {
                  id: 'etat_descriptif_equipement_bien_loisirs_attaches',
                  label: 'Equipements de loisirs attachés',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_documentation_pratique',
                  label: 'Documentation pratique remise au locataire',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_documentation_touristique',
                  label: 'Documentation touristique remise au locataire',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_animaux',
                  label: 'Animaux domestiques acceptés',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_blanchisserie',
                  label: 'Service quotidien de blanchisserie',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_equipement_bien_menage',
                  label: 'Service quotidien de ménage',
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'd5812d36_b7c2_4ea1_83ea_f14dcd50178c',
              label: 'Equipements divers',
              type: 'CATEGORY'
            }
          ],
          id: '2db938ce_8ff2_41cf_a775_e5dfdabebd96',
          label: 'Description du meublé',
          type: 'CATEGORY'
        }
      ],
      id: '69da7cd5_a0d2_4687_b4a7_6becf06d56ba',
      label: 'Etat descriptif',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'belgique_revenu_cadastral',
          label: 'Revenu cadastral du bien',
          type: 'PRICE'
        }
      ],
      id: '3365abc5_98f7_4b3a_a306_c3bc0d5fc315',
      label: 'Revenu cadastral',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_presence_servitudes',
          label: 'Présence de servitudes',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_presence_servitudes_titre',
          label: 'Servitudes contenue au titre de propriété',
          type: 'SELECT-BINARY'
        },
        {
          id: 'belgique_presence_servitudes_titre_liste',
          label: 'Liste des servitudes',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_presence_servitudes_vendeur',
          label: 'Servitudes octroyée par le Vendeur',
          type: 'SELECT-BINARY'
        },
        {
          id: 'belgique_presence_servitudes_vendeur_liste',
          label: 'Liste des servitudes',
          type: 'TEXT'
        }
      ],
      id: '9c740b4f_ed45_4d98_9982_d6343268cacc',
      label: 'Servitudes',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_publicitaire_presence',
          label: 'Panneau publicitaire',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_publicitaire_presence_preemption',
          label: 'Le contrat contient un droit de préemption',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_presence',
          label: 'Panneaux photovoltaïques',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_proprietaire',
          label: 'Vendeur propriétaire des panneaux photovoltaïques',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_presence_vente',
          label: 'Panneaux compris dans la vente',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_certificat',
          label: 'Vendeur bénéficiaire de certificats verts',
          type: 'SELECT-BINARY'
        },
        {
          id: 'belgique_panneau_photovoltaiques_certificat_liste',
          label: 'Liste de certificats verts',
          type: 'TEXTAREA'
        }
      ],
      id: '23cba686_c30a_41d5_89c6_50e12a051a47',
      label: 'Panneaux / Enseignes',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_diu_presence',
              label: 'Bien concerné par un DIU',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_diu_liste',
              label: 'Liste des travaux concernés',
              type: 'TEXTAREA'
            }
          ],
          id: '0dcf867f_083d_432b_a447_655dd1fc6ea9',
          label: "Dossier d'intervention ultérieur",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_controle_electricite_date_1981',
              label: "Installation électrique datant d'avant le 01/10/1981",
              type: 'SELECT-BINARY'
            },
            {
              choices: [],
              id: 'belgique_pv_controle_electricite_statut',
              label: "Contrôle de l'installation électrique",
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_controle_electricite_rapport',
              label: 'Rapport de visite remis',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_pv_controle_electricite_date',
              label: 'Date de réalisation du PV de contrôle',
              type: 'DATE'
            },
            {
              id: 'belgique_pv_controle_electricite_nom',
              label: 'Nom de la société ayant réalisé le PV',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_controle_electricite_conformite',
              label: 'Installation conforme',
              type: 'SELECT-BINARY'
            },
            {
              choices: [],
              id: 'belgique_pv_controle_electricite_dispense_raison',
              label: 'Raison de la dispense',
              type: 'SELECT'
            }
          ],
          id: '5af2da2c_9aa8_4a90_9fe8_ae43afa8630f',
          label: 'Electricité',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_amiante_presence',
              label: 'Amiante utilisée lors de la construction ou présence dans certains éléments',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_amiante_liste',
              label: "Situation de l'amiante dans le bien",
              type: 'TEXT'
            }
          ],
          id: '87c5efdb_d8bc_4fb5_bbdb_02540c368d0c',
          label: 'Amiante',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_peb_effectue',
              label: 'PEB effectué',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_peb_numero',
              label: 'Numéro du PEB',
              type: 'TEXT'
            },
            {
              id: 'belgique_peb_expert',
              label: "Nom de l'expert",
              type: 'TEXT'
            },
            {
              id: 'belgique_peb_date',
              label: "Date d'établissement du PEB",
              type: 'DATE'
            },
            {
              id: 'belgique_peb_classe',
              label: 'Classe énergétique du bien',
              type: 'TEXT'
            }
          ],
          id: '6d8b7e91_b7ca_492f_ab5e_a0c31b7ef82c',
          label: 'Performance énergétique du bâtiment',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'belgique_compteur_eau_numero',
              label: "Numéro de compteur d'eau",
              type: 'TEXT'
            },
            {
              id: 'belgique_compteur_gaz_numero',
              label: 'Numéro de compteur Gaz',
              type: 'TEXT'
            },
            {
              id: 'belgique_compteur_gaz_ean',
              label: 'Code EAN Gaz',
              type: 'TEXT'
            },
            {
              id: 'belgique_compteur_electrique_numero',
              label: 'Numéro de compteur électricité',
              type: 'TEXT'
            },
            {
              id: 'belgique_compteur_electrique_ean',
              label: 'Code EAN électricité',
              type: 'TEXT'
            }
          ],
          id: 'b27e76ad_7ba4_4750_9e12_bb1aa6e03748',
          label: 'Compteurs',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [],
                  id: 'belgique_poste_toiture',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [[]],
                  id: 'belgique_poste_autre_toiture',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '50480d57_293b_44a4_a9ea_4eb37f435ab4',
              label: 'Toiture(s)',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [],
                  id: 'belgique_poste_menuiserie',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [[]],
                  id: 'belgique_poste_autre_menuiserie',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '3f758d8b_a62a_40dd_921c_cf341f4181e7',
              label: 'Menuiserie extérieure',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [],
                  id: 'belgique_poste_electrique',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [[]],
                  id: 'belgique_poste_autre_electrique',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '523d7ca6_850a_4898_ac12_8bc7ebeb9a47',
              label: 'Installation electrique',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [],
                  id: 'belgique_poste_sanitaire',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [[]],
                  id: 'belgique_poste_autre_sanitaire',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '47378088_41c6_4c4b_84ba_7848ef6ba988',
              label: 'Sanitaires',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [],
                  id: 'belgique_poste_chauffage',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [[]],
                  id: 'belgique_poste_autre_chauffage',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '040bdf8b_b263_4e4e_a8f7_e376d3423ef2',
              label: 'Chauffage central',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [],
                  id: 'belgique_poste_revetement_sol',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [[]],
                  id: 'belgique_poste_autre_revetement_sol',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '929fb2dd_69e3_4d49_b02f_f307124bfcd2',
              label: 'Revêtement sols',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [],
                  id: 'belgique_poste_revetement_mur',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [[]],
                  id: 'belgique_poste_autre_revetement_mur',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: 'ba0cbb6d_ee50_4639_ad1b_1769e10e777f',
              label: 'Revêtements murs',
              type: 'CATEGORY'
            }
          ],
          id: 'be140e99_17f2_4641_b19c_174dbd05c7c9',
          label: 'Etat des Postes principaux',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_urbanisme_statut',
              label: "Bien concerné par un permis d'urbanisme",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_permis_urbanisme_date',
              label: 'Date du permis',
              type: 'DATE'
            },
            {
              id: 'belgique_permis_urbanisme_lieu',
              label: 'Lieu de délivrance du permis',
              type: 'TEXT'
            },
            {
              id: 'belgique_permis_urbanisme_numero',
              label: 'Numéro de permis',
              type: 'TEXT'
            }
          ],
          id: '6cd17b28_b1c9_4a50_926e_705ffafe8903',
          label: "Permis d'urbanisme",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_prime_wallonnie',
              label: 'Prime de la région Wallonne utilisée',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_prime_wallonnie_date',
              label: 'Date de la prime',
              type: 'DATE'
            },
            {
              id: 'belgique_prime_wallonnie_type',
              label: 'Type de la prime',
              type: 'TEXT'
            },
            {
              id: 'belgique_prime_wallonnie_montant',
              label: 'Montant de la prime',
              type: 'PRICE'
            }
          ],
          id: '441a6e3d_d1a7_489f_82c5_011df47aff4d',
          label: 'Prime',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_location',
              label: "Bien objet d'un permis de location",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_permis_location_date',
              label: 'Date du permis de location',
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_logement_inoccupe',
              label: 'Bien objet d’un PV de constat de logement inoccupé',
              type: 'SELECT-BINARY'
            }
          ],
          id: '4fe110fa_a5e2_4b10_8f03_e46b8e37d9c9',
          label: 'Habitation durable',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'belgique_usage_bien',
              label: 'Affectation du bien',
              type: 'TEXT'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_infraction_urbanistique',
                  label: 'Bien en infraction urbanistique ou environnementale',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_infraction_urbanistique_liste',
                  label: "Type d'infraction",
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_infraction_urbanistique_presence',
                  label: 'Infraction présente lors du transfert de propriété',
                  type: 'SELECT-BINARY'
                }
              ],
              id: '588b66ec_0f26_4456_8b44_7eba2bd916c1',
              label: 'Infraction',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_insalubrite',
                  label: 'Bien déclaré inhabitable ou insalubre',
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'ab58a9ef_e235_45ac_855c_43bcea33076e',
              label: 'Insalubrité',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_inoccupe',
                  label: 'Immeuble déclaré inoccupé ou abandonné',
                  type: 'SELECT-BINARY'
                }
              ],
              id: '79f99283_e974_4243_bffd_2f3345eb6308',
              label: 'Inoccupation',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_alignement',
                  label: "Bien dans le périmètre d'un plan d'alignement",
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'aa9966fc_fd8a_4e5f_91f4_5088c6909605',
              label: 'Alignement',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_travaux_presence',
                  label: 'Présence de travaux nécessitant un permis',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_responsabilite_decennale',
                  label: 'Bien concerné par la responsabilité décennale',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_travaux_juillet_2018',
                  label: 'Travaux soumis à permis délivré après le 1er juillet 2018',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'belgique_travaux_list_belgique_travaux_permis_obtenu',
                      label: 'Permis obtenu',
                      type: 'SELECT-BINARY'
                    },
                    {
                      id: 'belgique_travaux_list_belgique_travaux_titre',
                      label: 'Titre des Travaux',
                      type: 'TEXT'
                    },
                    {
                      id: 'belgique_travaux_list_belgique_travaux_description',
                      label: 'Description des travaux',
                      type: 'TEXTAREA'
                    },
                    {
                      id: 'belgique_travaux_list_belgique_travaux_date_achevement',
                      label: "Date d'achèvement des travaux",
                      type: 'DATE'
                    },
                    {
                      conditions: [[]],
                      id: 'belgique_travaux_list_belgique_travaux_aministie',
                      label: 'Régime de régularisation / amnistie',
                      type: 'TEXTAREA'
                    }
                  ],
                  id: 'belgique_travaux_list',
                  label: 'Ajouter Travaux',
                  repetition: {
                    label: [
                      {
                        type: 'TEXT',
                        value: ' '
                      },
                      {
                        type: 'VARIABLE',
                        value: 'belgique_travaux_list_belgique_travaux_titre'
                      },
                      {
                        type: 'TEXT',
                        value: ' - '
                      },
                      {
                        type: 'VARIABLE',
                        value: 'belgique_travaux_list_belgique_travaux_date_achevement'
                      }
                    ],
                    max: 1000,
                    min: 1
                  },
                  type: 'REPEAT'
                }
              ],
              id: '0929b195_4bcf_4a1c_906b_45b733a48c81',
              label: 'Travaux',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_lotissement_statut',
                  label: 'Bien soumis à Lotissement',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_lotissement_permis_delivrance',
                  label: "Permis d'urbanisation délivré",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_lotissement_notaire_nom',
                  label: "Notaire ayant reçu l'acte de division",
                  type: 'TEXT'
                },
                {
                  id: 'belgique_lotissement_notaire_date',
                  label: "Date de l'acte de division",
                  type: 'DATE'
                },
                {
                  id: 'belgique_lotissement_destination_vendu',
                  label: 'Destination du bien vendu',
                  type: 'TEXT'
                },
                {
                  id: 'belgique_lotissement_destination_conserver',
                  label: 'Destination du bien conservé par le vendeur',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_lotissement_cs_avis',
                  label: "Condition suspensive d'absence d'avis défavorable",
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'c05a3f6c_1829_4315_98c3_5a6009a0aa6c',
              label: 'Lotissement',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_eau_usee_presence',
                  label: "Bien équipé d'un système d'épuration des eaux usées",
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_eau_usee_repartition_clause',
                  label: 'Ajouter la répartition des frais sur demande des intercommunales',
                  type: 'SELECT-BINARY'
                }
              ],
              id: '9003a7cc_d5e2_4491_8188_c684d441ff15',
              label: 'Equipement',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_zone_inondable',
                  label: 'Bien situé en zone inondable',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_zone_inondable_inonde',
                  label: 'Bien a subi une inondation',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_zone_inondable_etendue',
                  label: "Étendue de l'inondation",
                  type: 'TEXT'
                }
              ],
              id: '3e0b69e9_f600_48ee_8e4e_c16965236c70',
              label: 'Zones inondables',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_mesure_expropriation',
                  label: "Mesure d'expropriation de l'immeuble",
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'aa9b6b44_5976_42fe_93ff_72280cb9aa65',
              label: 'Expropriation',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_litige',
                  label: "Litige relatif à l'immeuble",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_litiges_liste',
                  label: 'Description des litiges',
                  type: 'TEXT'
                }
              ],
              id: '3844c679_54d3_46e0_a57a_2632776ed81a',
              label: 'Litige',
              type: 'CATEGORY'
            }
          ],
          id: 'e5ec41a1_28ce_4286_83e9_286fc1d991e2',
          label: 'Situation urbanistique',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [],
              id: 'belgique_certibeau_statut',
              label: 'Raccordement à la distribution publique de l’eau',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_realise',
              label: 'CertIBEau réalisé',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_a_realiser',
              label: 'Le CertIBEau devra être réalisé',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_certibeau_resultat',
              label: 'Résultat du CertIBEau',
              type: 'TEXTAREA'
            },
            {
              id: 'belgique_certibeau_realise_date',
              label: "Date d'établissement du CertIBEau",
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_realise_conformite',
              label: 'CertIBEau délivré conforme',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_realise_conformite_modification',
              label: "Modifications réalisés depuis l'établissement du CertIBEau",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_certibeau_realise_conformite_modification_liste',
              label: 'Liste des modifications intervenues',
              type: 'TEXTAREA'
            }
          ],
          id: 'b673feca_0085_42b2_a02a_96f9cfd5e57c',
          label: 'CertIBeau',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_detecteur_incendie_statut',
              label: "Bien équipé de détecteur d'incendie",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_detecteur_incendie_nombre',
              label: 'Nombre de détecteur',
              type: 'NUMBER'
            },
            {
              id: 'belgique_detecteur_incendie_pieces',
              label: 'Pièces équipées',
              type: 'TEXT'
            }
          ],
          id: '3bacc869_3928_46ab_9fbc_1d5c12e4864d',
          label: 'Détecteur incendie',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_citerne_presence',
              label: "Présence d'une citerne",
              type: 'SELECT-BINARY'
            },
            {
              choices: [],
              id: 'belgique_citerne_type',
              label: 'Type de citerne',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_citerne_capacite_3000',
              label: 'Capacité de la citerne supérieure à 3.000 litres',
              type: 'SELECT-BINARY'
            },
            {
              choices: [],
              id: 'belgique_citerne_enterree_aerienne',
              label: 'Situation de la citerne',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_citerne_attestation',
              label: 'Attestation de conformité / certificat de visite de contrôle',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_citerne_mazout_capacite',
              label: 'Capacité de la citerne mazout',
              type: 'TEXT'
            }
          ],
          id: 'af4a7466_b7b5_4ae8_aeca_79384df47238',
          label: 'Citerne mazout / gaz',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_environnement_statut',
              label: "Bien objet d'un permis d'environnement / déclaration de classe 3",
              type: 'SELECT-BINARY'
            },
            {
              choices: [],
              id: 'belgique_permis_environnement_statut_type',
              label: 'Type',
              type: 'SELECT'
            },
            {
              id: 'belgique_permis_environnement_type',
              label: "Objet du permis d'environnement",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_environnement_activite',
              label: 'Activité exercée imposant un tel permis ou déclaration',
              type: 'SELECT-BINARY'
            }
          ],
          id: '0e021ae1_5fc0_460f_999d_880948cd3239',
          label: "Permis d'environnement",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_copropriete_acte_base_fourniture',
              label: 'Acte de base fourni',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_copropriete_pv_ag_fourniture',
              label: "PV d'assemblée général fourni",
              type: 'SELECT-BINARY'
            }
          ],
          id: '6599aaf1_2b7c_4117_b688_8a195d002bab',
          label: 'Copropriété',
          type: 'CATEGORY'
        }
      ],
      id: '2fd4f01b_91d8_4e61_b646_7a8e0f9a7647',
      label: 'Informations Administratives',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__BIEN__MONOPROPRIETE_HORS_HABITATION',
  label: 'Bien en monopropriété - Hors habitation',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__BIEN__MONOPROPRIETE_HORS_HABITATION',
  specificTypes: ['BIEN', 'MONOPROPRIETE_HORS_HABITATION'],
  type: 'RECORD'
};
