// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordOperationSyndicGeneralFiche: LegalRecordTemplate = {
  config: {},
  form: [
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'O<PERSON>'
            }
          ],
          id: 'contrat_ajout_numero_registre',
          label: 'Ajouter le numéro du mandat de syndic',
          type: 'SELECT-BINARY'
        },
        {
          id: 'contrat_numero_registre',
          label: 'Numéro du mandat de syndic',
          register: {
            contracts: ['SYNDIC_GENERAL_CONTRAT_SYNDIC'],
            type: 'MANAGEMENT'
          },
          type: 'TEXT'
        },
        {
          id: 'contrat_date_nomination',
          label: 'Date de nomination du syndic',
          type: 'DATE'
        },
        {
          id: 'contrat_date_debut',
          label: 'Date de début du contrat',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'contrat_date_calcul_automatique',
          label: 'Calcul automatique de la durée du contrat',
          type: 'SELECT-BINARY'
        },
        {
          id: 'contrat_date_fin',
          label: 'Fin du contrat de syndic',
          type: 'DATE'
        },
        {
          id: 'contrat_duree',
          label: 'Durée du contrat de syndic',
          max: 36,
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'signature_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['UNIS_COPROPRIETE_CONTRAT_SYNDIC_TERTIAIRE']
              }
            ]
          ],
          id: 'annexe_visite_syndic_liste_equipement',
          label: 'Annexe II - Liste des équipements',
          templateId: 'annexe_visite_syndic_liste_equipement.docx',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_ANNEXE_SYNDIC'
          },
          id: 'annexe_syndic',
          label: 'Annexe contrat de syndic',
          type: 'UPLOAD'
        },
        {
          id: 'annexe_syndic_equipement',
          label: 'Annexe - Liste des équipements',
          templateId: 'annexe_syndic_equipement.docx',
          type: 'UPLOAD'
        }
      ],
      id: '9d3e4fec_d12a_4867_a1ea_4de36642268e',
      label: 'Contrat de syndic',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              description:
                'Exemple Du Lundi au Vendredi de ...h à ...h ; Les samedi-dimanche et jours fériés en fonction du syndicat des copropriétaires',
              id: 'plage_horaire_calendrier',
              label: 'Plage horaire rémunération',
              type: 'TEXTAREA'
            },
            {
              children: [
                {
                  id: 'plage_horaire_remuneration_lundi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_lundi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_lundi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_lundi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: 'ee1878e9_4081_45b7_be0a_67a34d2fbe19',
              label: 'Lundi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_remuneration_mardi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_mardi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_mardi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_mardi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: 'b15602ef_ada4_4808_a1ec_0ec427a1a621',
              label: 'Mardi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_remuneration_mercredi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_mercredi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_mercredi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_mercredi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: '06b951ea_bffe_4abe_bc96_2d1577938f81',
              label: 'Mercredi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_remuneration_jeudi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_jeudi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_jeudi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_jeudi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: '4ba92ac4_d233_4165_92c6_ebd32e149216',
              label: 'Jeudi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_remuneration_vendredi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_vendredi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_vendredi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_vendredi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: 'a9796d04_344e_42f3_8018_947def58e08c',
              label: 'Vendredi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_remuneration_samedi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_samedi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_samedi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_remuneration_samedi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: '07ebd83c_c2fb_40bc_ba5d_5c0ffbdb0a22',
              label: 'Samedi',
              type: 'CATEGORY'
            }
          ],
          id: 'f0aad046_cfdc_4cf2_808f_abb26d292a14',
          label: 'Plage horaire rémunération',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              description: 'Exemple sur les 6 jours ouvrables : Le lundi de 8h à 12h et de 14h à 17h...',
              id: 'plage_horaire_accueil_physique_calendrier',
              label: 'Plage horaire accueil physique',
              type: 'TEXTAREA'
            },
            {
              children: [
                {
                  id: 'plage_horaire_accueil_physique_lundi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_lundi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_lundi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_lundi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: '197415a0_6f8c_41d1_ae1d_c9c11f257019',
              label: 'Lundi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_accueil_physique_mardi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_mardi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_mardi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_mardi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: '514ea349_3816_431a_ab85_9c859ece851c',
              label: 'Mardi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_accueil_physique_mercredi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_mercredi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_mercredi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_mercredi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: 'd1f09d63_edc1_44f4_b2e0_05dd173bff61',
              label: 'Mercredi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_accueil_physique_jeudi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_jeudi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_jeudi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_jeudi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: 'eade089e_1c7f_4aed_ac50_9d8719f41afc',
              label: 'Jeudi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_accueil_physique_vendredi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_vendredi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_vendredi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_vendredi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: 'b464c00c_1715_4146_909b_57b071e9e650',
              label: 'Vendredi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_accueil_physique_samedi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_samedi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_samedi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_physique_samedi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: 'fd837b27_1636_41e4_8b3b_f173a67450a8',
              label: 'Samedi',
              type: 'CATEGORY'
            }
          ],
          id: '222c6495_3d7a_4c32_a0b8_85df87dd8461',
          label: 'Plage horaire accueil physique',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              description: 'Exemple sur les 6 jours ouvrables : Le lundi de 8h à 12h et de 14h à 17h...',
              id: 'plage_horaire_accueil_telephonique_calendrier',
              label: 'Plage horaire accueil téléphonique',
              type: 'TEXTAREA'
            },
            {
              children: [
                {
                  id: 'plage_horaire_accueil_telephone_lundi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_lundi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_lundi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_lundi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: 'da28d869_ea79_4ca0_8601_d7f214f39d69',
              label: 'Lundi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_accueil_telephone_mardi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_mardi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_mardi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_mardi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: '46d7a3c6_9467_4c26_9b24_46dee0934b64',
              label: 'Mardi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_accueil_telephone_mercredi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_mercredi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_mercredi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_mercredi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: '76749127_a969_4569_99d9_060730c4ca33',
              label: 'Mercredi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_accueil_telephone_jeudi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_jeudi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_jeudi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_jeudi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: 'b728c82c_ebc4_4273_a5b0_34dc7b5e48ea',
              label: 'Jeudi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_accueil_telephone_vendredi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_vendredi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_vendredi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_vendredi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: 'a9796d04_344e_42f3_8018_947def58e08c',
              label: 'Vendredi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'plage_horaire_accueil_telephone_samedi_debut_matinee',
                  label: 'Début horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_samedi_fin_matinee',
                  label: 'Fin horaire matinée',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_samedi_debut_apres_midi',
                  label: 'Début horaire après-midi',
                  type: 'TEXT'
                },
                {
                  id: 'plage_horaire_accueil_telephone_samedi_fin_apres_midi',
                  label: 'Fin horaire après-midi',
                  type: 'TEXT'
                }
              ],
              id: 'e5237079_74bc_400c_8bd7_21d0c223975f',
              label: 'Samedi',
              type: 'CATEGORY'
            }
          ],
          id: '479d36b7_e061_426b_9409_b24617973d94',
          label: 'Plage horaire accueil téléphonique',
          type: 'CATEGORY'
        }
      ],
      id: '92ad3068_7675_4ece_9ec4_ffe342310bf3',
      label: '7. Prestations',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              id: 'forfait_visite_nombre',
              label: 'Nombres de visites par le syndic',
              type: 'NUMBER'
            },
            {
              id: 'forfait_visite_duree',
              label: 'Durée minimum de la visite',
              suffix: 'heure(s)',
              type: 'NUMBER'
            },
            {
              choices: [
                {
                  id: 'sans_rapport_sans_presence',
                  label: "Sans établissement d'un rapport et sans la présence du président du conseil syndical"
                },
                {
                  id: 'sans_rapport_avec_presence',
                  label: "Sans établissement d'un rapport et avec la présence du président du conseil syndical"
                },
                {
                  id: 'avec_rapport_sans_presence',
                  label: "Avec établissement d'un rapport et sans la présence du président du conseil syndical"
                },
                {
                  id: 'avec_rapport_avec_presence',
                  label: "Avec établissement d'un rapport et avec la présence du président du conseil syndical"
                }
              ],
              id: 'forfait_visite_modalite',
              label: 'Modalité de la visite',
              type: 'SELECT'
            }
          ],
          id: 'bfdcda40_d249_4ef2_a5cc_b21ea8ac0b61',
          label: '7.1.1. Contenu du forfait',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'forfait_ag_duree',
              label: "Durée de l'AG annuelle",
              suffix: 'heure(s)',
              type: 'NUMBER'
            },
            {
              description: 'Allant de 14h à 17h...',
              id: 'forfait_ag_plage',
              label: "Plage horaire de l'AG",
              type: 'TEXTAREA'
            },
            {
              choices: [
                {
                  id: 'syndic',
                  label: 'Le Syndic'
                },
                {
                  id: 'prepose',
                  label: 'Un ou plusieurs préposé(s)'
                }
              ],
              id: 'forfait_ag_responsable',
              label: 'Assemblée générale tenue par',
              multiple: true,
              type: 'SELECT'
            }
          ],
          id: '4c109f44_9793_46f2_aa2b_dfe0e5807253',
          label: "7.1.2. Tenue de l'assemblée générale annuelle",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'forfait_option_age',
              label: "Préparation d'AG supplémentaire",
              type: 'SELECT-BINARY'
            },
            {
              id: 'forfait_option_age_nombre',
              label: "Nombre d'AG supplémentaires",
              type: 'NUMBER'
            },
            {
              id: 'forfait_option_age_duree',
              label: "Durée de l'AG supplémentaire",
              suffix: 'heure(s)',
              type: 'NUMBER'
            },
            {
              description: 'Allant de 14h à 17h...',
              id: 'forfait_option_age_plage',
              label: "Plage horaire de l'AG",
              type: 'TEXTAREA'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'forfait_option_reunion',
              label: 'Organisation de réunion avec le Conseil Syndical',
              type: 'SELECT-BINARY'
            },
            {
              id: 'forfait_option_reunion_nombre',
              label: 'Nombre de réunions',
              type: 'NUMBER'
            },
            {
              id: 'forfait_option_reunion_duree',
              label: 'Durée de la réunion',
              suffix: 'heure(s)',
              type: 'NUMBER'
            },
            {
              description: 'Allant de 14h à 17h...',
              id: 'forfait_option_reunion_plage',
              label: 'Plage horaire de la réunion',
              type: 'TEXTAREA'
            }
          ],
          id: 'caba26ea_6d57_4501_b456_c3c58f4e387d',
          label: '7.1.3. Prestations optionnelles',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'remuneration_annuelle',
              label: 'Rémunération forfaitaire annuelle (TTC)',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'avance',
                  label: "D'avance"
                },
                {
                  id: 'terme',
                  label: 'A terme échu'
                }
              ],
              id: 'remuneration_modalite',
              label: 'Modalité de paiement',
              type: 'SELECT'
            },
            {
              id: 'remuneration_periode',
              label: 'Périodicité de paiement',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'remuneration_revision',
              label: 'Rémunération révisable',
              type: 'SELECT-BINARY'
            },
            {
              id: 'remuneration_revision_date',
              label: 'Date de révision',
              type: 'TEXT'
            },
            {
              id: 'remuneration_revision_modalite',
              label: 'Modalité de révision',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'forfait',
                  label: 'Par un montant forfaitaire'
                },
                {
                  id: 'reel',
                  label: 'Par un montant réel, sur facture'
                }
              ],
              id: 'remuneration_archivage_type',
              label: "En cas d'archivage confié à un tiers, imputation de la rémunération",
              type: 'SELECT'
            },
            {
              id: 'remuneration_archivage_montant',
              label: 'Montant du forfait archivage à déduire',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'forfait',
                  label: 'Par un montant forfaitaire'
                },
                {
                  id: 'reel',
                  label: 'Par un montant réel, sur facture'
                }
              ],
              id: 'remuneration_dematerialisation_type',
              label: 'En cas de dématérialisation confié à un tiers, imputation de la rémunération',
              type: 'SELECT'
            },
            {
              id: 'remuneration_dematerialisation_montant',
              label: 'Montant du forfait dématérialisation à déduire',
              type: 'PRICE'
            }
          ],
          id: '2a89c0d7_13f1_413c_9a9e_e193ef20c3e1',
          label: '7.1.5. Rémunération principale',
          type: 'CATEGORY'
        }
      ],
      id: '21fa2f0c_651f_4656_962b_deba6353ede4',
      label: '7.1. Forfait',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  id: 'forfait',
                  label: 'Par un montant forfaitaire'
                },
                {
                  id: 'reel',
                  label: 'Par un montant réel, sur facture'
                }
              ],
              id: 'remuneration_complementaire_statut',
              label: 'Rémunération des prestations complémentaires',
              type: 'SELECT'
            },
            {
              id: 'remuneration_complementaire_horaire',
              label: 'Coût horaires des prestations particulières (TTC)',
              type: 'PRICE'
            },
            {
              id: 'remuneration_complementaire_horaire_majoration',
              label: 'Majoration des heures après 21 h',
              type: 'PRICE'
            },
            {
              id: 'remuneration_complementaire_ag_duree',
              label: "Durée de l'AG supplémentaire",
              suffix: 'heure(s)',
              type: 'NUMBER'
            }
          ],
          id: '4475b3e4_2d97_49b4_a85c_b966f39481f7',
          label: '7.2.1 Modalités de rémunération des prestations particulières',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              children: [
                {
                  description: 'Allant de 14h à 17h...',
                  id: 'remuneration_complementaire_ag_plage',
                  label: "Plage horaire de l'AG supplémentaire",
                  type: 'TEXTAREA'
                },
                {
                  choices: [
                    {
                      id: 'forfait',
                      label: 'Par un montant forfaitaire'
                    },
                    {
                      id: 'reel',
                      label: 'Par un montant réel, sur facture'
                    }
                  ],
                  id: 'remuneration_complementaire_ag_forfait',
                  label: "Tarification de l'AG supplémentaire",
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_ag_montant_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_ag_montant',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_ag_montant_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                },
                {
                  id: 'remuneration_complementaire_ag_depassement',
                  label: "Majoration pour dépassement d'horaires",
                  suffix: '%',
                  type: 'NUMBER'
                },
                {
                  id: 'remuneration_depassement_honoraires',
                  label: "Dépassement d'horaires",
                  type: 'PRICE'
                }
              ],
              id: '219b8bb0_c12a_46a3_972a_87177313ac6e',
              label: 'AG Supplémentaire',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'remuneration_complementaire_reunion_duree',
                  label: 'Durée de la réunion supplémentaire',
                  suffix: 'heure(s)',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'forfait',
                      label: 'Par un montant forfaitaire'
                    },
                    {
                      id: 'reel',
                      label: 'Par un montant réel, sur facture'
                    }
                  ],
                  id: 'remuneration_complementaire_reunion_forfait',
                  label: 'Tarification de la réunion supplémentaire',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_reunion_montant_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_reunion_montant',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_reunion_montant_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '97f57024_b051_4cd4_8606_6fd212542a00',
              label: 'Réunion supplémentaire',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'forfait',
                      label: 'Par un montant forfaitaire'
                    },
                    {
                      id: 'reel',
                      label: 'Par un montant réel, sur facture'
                    }
                  ],
                  id: 'remuneration_complementaire_visite_forfait',
                  label: 'Tarification de la visite supplémentaire',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'sans_rapport_sans_presence',
                      label: "Sans établissement d'un rapport et sans la présence du président du conseil syndical"
                    },
                    {
                      id: 'sans_rapport_avec_presence',
                      label: "Sans établissement d'un rapport et avec la présence du président du conseil syndical"
                    },
                    {
                      id: 'avec_rapport_sans_presence',
                      label: "Avec établissement d'un rapport et sans la présence du président du conseil syndical"
                    },
                    {
                      id: 'avec_rapport_avec_presence',
                      label: "Avec établissement d'un rapport et avec la présence du président du conseil syndical"
                    }
                  ],
                  id: 'remuneration_complementaire_reunion_rapport',
                  label: 'Modalité de la réunion',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_visite_montant_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_visite_montant',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_visite_montant_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '85400807_eb56_41b5_b612_e5ce92a32db9',
              label: 'Visite / rapport supplémentaire',
              type: 'CATEGORY'
            }
          ],
          id: '46e4f6f3_9db2_4215_8d7f_262a606894c4',
          label: '7.2.2. Prestations relatives aux réunions et visites supplémentaires',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_etablissement_rcp_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_etablissement_rcp',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_etablissement_rcp_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '4dea7284_e147_413f_8734_42b0660cb4b3',
              label: 'Etablissement du RCP',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_publication_rcp_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_publication_rcp',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_publication_rcp_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '5904d59f_3c4e_4bdc_9dfd_1373401edbf6',
              label: 'Publication du RCP',
              type: 'CATEGORY'
            }
          ],
          id: '8a025245_08ed_4a21_99e4_038b5d215549',
          label: "7.2.3. Prestations relatives au règlement de copropriété et à l'état descriptif de division",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [
                    {
                      id: 'forfait',
                      label: 'Par un montant forfaitaire'
                    },
                    {
                      id: 'reel',
                      label: 'Par un montant réel, sur facture'
                    }
                  ],
                  id: 'remuneration_tarification_complementaire_deplacement',
                  label: 'Tarification du déplacement',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_deplacement_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_deplacement',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_deplacement_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '301c398e_afed_42e1_92d1_14b7229c803d',
              label: 'Déplacement sur les lieux',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'forfait',
                      label: 'Par un montant forfaitaire'
                    },
                    {
                      id: 'reel',
                      label: 'Par un montant réel, sur facture'
                    }
                  ],
                  id: 'remuneration_tarification_complementaire_mesure',
                  label: 'Tarification des mesures conservatoires',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_conservatoire_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_conservatoire',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_conservatoire_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: 'e32d7c62_e32c_4166_bbe0_6cdb78952e6a',
              label: 'Prise de mesures conservatoires',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'forfait',
                      label: 'Par un montant forfaitaire'
                    },
                    {
                      id: 'reel',
                      label: 'Par un montant réel, sur facture'
                    }
                  ],
                  id: 'remuneration_tarification_complementaire_assistance',
                  label: "Tarification de l'assistance",
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_assistance_expert_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_assistance_expert',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_assistance_expert_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: 'df329811_bf60_46f7_8f20_7a71be203916',
              label: "Assistance aux mesures d'expertise",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'forfait',
                      label: 'Par un montant forfaitaire'
                    },
                    {
                      id: 'reel',
                      label: 'Par un montant réel, sur facture'
                    }
                  ],
                  id: 'remuneration_tarification_complementaire_suivi',
                  label: 'Tarification suivi du dossier',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_suivi_expert_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_suivi_expert',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_suivi_expert_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '5615bf95_83c3_492d_8495_550016521783',
              label: "Suivi de dossier auprès de l'assureur",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'remuneration_complementaire_majoration',
                  label: 'Majoration de ces prestations',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'remuneration_complementaire_majoration_montant',
                  label: 'Montant de la majoration',
                  suffix: '%',
                  type: 'NUMBER'
                }
              ],
              id: 'a51bcade_ad53_4222_b96f_68aed92e0373',
              label: 'Majoration des prestations 7.2.4',
              type: 'CATEGORY'
            }
          ],
          id: 'd37cc56e_942b_48e0_b470_df12f0902536',
          label: '7.2.4. Prestations de gestion administrative et matérielle relatives aux sinistres',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [
                    {
                      id: 'forfait',
                      label: 'Par un montant forfaitaire'
                    },
                    {
                      id: 'reel',
                      label: 'Par un montant réel, sur facture'
                    }
                  ],
                  id: 'remuneration_tarification_complementaire_mise',
                  label: 'Tarification mise en demeure',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_mise_demeure_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_mise_demeure',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_mise_demeure_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '5a4abca5_363c_4691_ab98_4938798f80d4',
              label: "Mise en demeure d'un tiers",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'forfait',
                      label: 'Par un montant forfaitaire'
                    },
                    {
                      id: 'reel',
                      label: 'Par un montant réel, sur facture'
                    }
                  ],
                  id: 'remuneration_tarification_complementaire_constitution_dossier',
                  label: 'Tarification constitution de dossier',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_constitution_dossier_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_constitution_dossier',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_constitution_dossier_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: 'd2da8801_be58_41fb_b0ef_9a812872819c',
              label: "Constitution du dossier transmis à l'avocat/huissier de justice",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'forfait',
                      label: 'Par un montant forfaitaire'
                    },
                    {
                      id: 'reel',
                      label: 'Par un montant réel, sur facture'
                    }
                  ],
                  id: 'remuneration_tarification_complementaire_constitution_dossier_avocat',
                  label: 'Tarification suivi de dossier avocat',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_suivi_dossier_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_suivi_dossier',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_suivi_dossier_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '0d1b0144_bcc6_49d0_bf74_2374ead6e20c',
              label: "Suivi du dossier transmis à l'avocat",
              type: 'CATEGORY'
            }
          ],
          id: '6b13045d_c03e_4679_9b4e_ced87d665ebb',
          label: '7.2.6. Prestations relatives aux litiges et contentieux',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_parties_communes_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_parties_communes',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_parties_communes_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '55caf8ae_58b6_4761_bc91_984f7fcc7c11',
              label: 'Acquisition de parties communes',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_comptabilite_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_comptabilite',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_comptabilite_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '6980c815_df50_4c21_b1db_6d3ff085045f',
              label: 'Reprise de comptabilité antérieure',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_representation_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_representation',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_representation_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '2f912774_2ac6_442c_b6c6_184b1e3e7c57',
              label: 'Représentation du syndicat aux assemblées extérieure',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_emprunt_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_emprunt',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_emprunt_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: 'cc83b133_fdc0_4daf_b4d5_cccd1af9cf45',
              label: "Constitution de dossier d'emprunt",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_subvention_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_subvention',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_subvention_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: 'c7c75964_ff04_4a27_8093_cadec85e8c1c',
              label: 'Constitution de dossier de subvention',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'remuneration_complementaire_immatriculation_choix',
                  label: 'Tarif indiqué',
                  type: 'SELECT'
                },
                {
                  id: 'remuneration_complementaire_immatriculation',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'remuneration_complementaire_immatriculation_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '04964f86_8e6a_4a87_bd18_f01fda7c60bb',
              label: 'Immatriculation du syndicat',
              type: 'CATEGORY'
            }
          ],
          id: 'bf5d5a5d_7e7d_4e92_9860_93847d7e5a65',
          label: '7.2.7. Autres prestations',
          type: 'CATEGORY'
        }
      ],
      id: '63da59dc_efed_4f9f_a3f7_7c0b71f26dc2',
      label: '7.2. Prestations particulières - Rémunération complémentaire',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              id: 'frais_recouvrement',
              label: 'Frais de recouvrement',
              type: 'TEXT'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_recouvrement_lrar_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  id: 'frais_recouvrement_lrar',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'frais_recouvrement_lrar_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '445289c3_6bf3_410b_b6b1_edcfd94b966c',
              label: 'Mise en demeure par LRAR',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_recouvrement_relance_simple_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  id: 'frais_recouvrement_relance_simple',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'frais_recouvrement_relance_simple_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '3a0cf883_3411_4ebc_ba9e_d9a845c296f8',
              label: 'Relance simple',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_recouvrement_mise_demeure_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  id: 'frais_recouvrement_mise_demeure',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'frais_recouvrement_mise_demeure_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '8269c753_16ab_4c74_b52f_c6dc8f244ac8',
              label: 'Relance après mise en demeure',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_recouvrement_protocol_accord_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  id: 'frais_recouvrement_protocol_accord',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'frais_recouvrement_protocol_accord_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '10059e25_b306_4b73_913b_4b2e29e5258d',
              label: "Conclusion d'un protocole d'accord",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_recouvrement_hypotheque_constitution_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  id: 'frais_recouvrement_hypotheque_constitution',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'frais_recouvrement_hypotheque_constitution_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '302af05c_a10a_4113_a94a_947207029c88',
              label: "Constitution d'hypothèque",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_recouvrement_hypotheque_mainlevee_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  id: 'frais_recouvrement_hypotheque_mainlevee',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'frais_recouvrement_hypotheque_mainlevee_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: 'bc5e26ec_7c56_40a2_a81a_cb48d2a5e5a8',
              label: "Mainlevée d'hypothèque",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_recouvrement_injonction_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  id: 'frais_recouvrement_injonction',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'frais_recouvrement_injonction_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '58b1bd5f_b7e5_41f3_9b6e_d874ad6738de',
              label: "Dépôt d'une requête en injonction de payer",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_recouvrement_auxilliaire_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  id: 'frais_recouvrement_auxilliaire',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'frais_recouvrement_auxilliaire_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '2813ca44_fc84_415d_909c_df0d6ca27c9f',
              label: "Constitution du dossier transmis à l'auxilliaire de justice",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_recouvrement_avocat_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  id: 'frais_recouvrement_avocat',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'frais_recouvrement_avocat_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '82245f3d_0783_4568_84b6_52a008622ab9',
              label: "Suivi du dossier transmis à l'avocat",
              type: 'CATEGORY'
            }
          ],
          id: 'bdbc0c92_efc8_4f87_b488_4fb425cf62e1',
          label: '9.1 Frais de recouvrement',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'frais_relance',
              label: 'Frais de relance',
              type: 'TEXT'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_etat_date_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  description: 'Montant maximum : 380 €',
                  id: 'frais_etat_date',
                  label: 'Montant TTC',
                  max: 380,
                  suffix: '€',
                  type: 'NUMBER'
                },
                {
                  description: 'Montant maximum 380€',
                  id: 'frais_etat_date_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: 'e705aa93_9c0c_4252_bc10_ec5cc10cc59a',
              label: "Etablissement de l'état daté",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_opposition_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  id: 'frais_opposition',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'frais_opposition_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: 'ba71733d_9adf_4323_969d_1e3c2d57068c',
              label: "Frais d'opposition sur mutation",
              type: 'CATEGORY'
            }
          ],
          id: '534c1afd_3c31_4f66_abe6_195a8ddca4ee',
          label: '9.2 Frais et honoraires liés aux mutations',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_delivrance_documents_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  id: 'frais_delivrance_documents',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'frais_delivrance_documents_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: 'd2704dcc_4b5f_4284_8f17_8e2d4bd1b7f2',
              label: "Copie du carnet d'entretien",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_delivrance_diagnostics_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  id: 'frais_delivrance_diagnostics',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'frais_delivrance_diagnostics_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: '06e40339_e78a_4c67_93de_e66481d51e89',
              label: 'Copie de diagnostics techniques',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_delivrance_dpe_individuel_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  id: 'frais_delivrance_dpe_individuel',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'frais_delivrance_dpe_individuel_texte',
                  label: 'Modalité de tarification',
                  type: 'TEXT'
                }
              ],
              id: 'cc41e737_2390_4b70_93e0_542fd3c635d9',
              label: 'Informations nécessaire au DPE individuel',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'chiffre',
                      label: 'Selon un montant chiffré'
                    },
                    {
                      id: 'non',
                      label: 'En texte libre'
                    },
                    {
                      id: 'prorata',
                      label: 'Au prorata du temps passé (7.2.1)'
                    }
                  ],
                  id: 'frais_delivrance_pv_ag_choix',
                  label: 'Frais indiqués',
                  type: 'SELECT'
                },
                {
                  id: 'frais_delivrance_pv_ag',
                  label: 'Montant TTC',
                  type: 'PRICE'
                },
                {
                  id: 'frais_delivrance_pv_ag_texte',
                  label: 'Modalités de tarification',
                  type: 'TEXT'
                }
              ],
              id: 'cbcf7083_f1ef_4675_b1dd_ec6a3f8cddc1',
              label: 'Copie conforme ou extrait de procès-verbal',
              type: 'CATEGORY'
            }
          ],
          id: '8d23841f_294a_4ffa_abbb_acc267120ab1',
          label: '9.3 Frais de délivrance des documents support papier',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'chiffre',
                  label: 'Selon un montant chiffré'
                },
                {
                  id: 'non',
                  label: 'En texte libre'
                },
                {
                  id: 'prorata',
                  label: 'Au prorata du temps passé (7.2.1)'
                }
              ],
              id: 'frais_convocation_choix',
              label: 'Frais indiqués',
              type: 'SELECT'
            },
            {
              id: 'frais_convocation',
              label: 'Montant TTC',
              type: 'PRICE'
            },
            {
              id: 'frais_convocation_texte',
              label: 'Modalités de tarification',
              type: 'TEXT'
            }
          ],
          id: '940388bd_81ca_480d_b2b2_50362509b231',
          label: "9.4 Préparation, convocation et tenue d'une assemblée générale",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'chiffre',
                  label: 'Selon un montant chiffré'
                },
                {
                  id: 'non',
                  label: 'En texte libre'
                },
                {
                  id: 'prorata',
                  label: 'Au prorata du temps passé (7.2.1)'
                }
              ],
              id: 'frais_reporting_choix',
              label: 'Frais indiqués',
              type: 'SELECT'
            },
            {
              id: 'frais_reporting',
              label: 'Montant TTC',
              type: 'PRICE'
            },
            {
              id: 'frais_reporting_texte',
              label: 'Modalités de tarification',
              type: 'TEXT'
            }
          ],
          id: 'ae0313ff_5f65_46c7_a49e_929f303f2dd7',
          label: '9.4 Frais de reporting particuliers à la demande du client',
          type: 'CATEGORY'
        }
      ],
      id: '84b28e98_4b7a_4826_b366_6372f81fb7f8',
      label: '9. Frais et honoraires imputables aux seuls copropriétaires',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'contrat_reddition_modalite',
          label: 'Modalité des rédditions de compte',
          type: 'TEXT'
        }
      ],
      id: 'cf94a8bf_771a_4951_a6aa_e499d60f47fe',
      label: '11. Reddition de compte',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'signature_date',
          label: 'Date de signature',
          type: 'DATE'
        },
        {
          id: 'signature_lieu',
          label: 'Lieu de signature',
          type: 'TEXT'
        }
      ],
      id: '20d7788e_f912_4608_bb90_83c65673235a',
      label: 'Signature',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'annexe_fiche_information_prix',
          label: "Annexer la fiche d'information sur le prix et les prestations",
          type: 'SELECT-BINARY'
        }
      ],
      id: '04c8bf06_268f_402e_a1de_0f2735cd966d',
      label: 'Annexe',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'annexe_syndic_remise',
          label: 'Annexe - Liste des pièces remises',
          templateId: 'pieces_remises_syndic.docx',
          type: 'UPLOAD'
        },
        {
          id: 'convocation_ag_date',
          label: "Date de l'assemblée générale",
          type: 'DATE'
        }
      ],
      id: '9c6a07de_7b41_441d_b5ab_d8e48db1190b',
      label: 'Assemblée générale',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'bordereau_syndic_sortant_nom',
          label: 'Nom du syndic sortant',
          type: 'TEXT'
        },
        {
          id: 'bordereau_syndic_sortant_date',
          label: 'Date de fin du mandat du syndic sortant',
          type: 'DATE'
        },
        {
          id: 'bordereau_syndic_sortant_email',
          label: "Email de l'interlocuteur du registre",
          type: 'EMAIL'
        },
        {
          id: 'bordereau_syndic_nouveau_date',
          label: 'Date du début du mandat du nouveau syndic',
          type: 'DATE'
        },
        {
          id: 'bordereau_date_ag',
          label: 'Date de la dernière AG',
          type: 'DATE'
        },
        {
          children: [
            {
              id: 'bordereau_remise_cle',
              label: 'Clés (indiquez le nombre)',
              type: 'NUMBER'
            },
            {
              id: 'bordereau_remise_badges',
              label: 'Badges (indiquez le nombre)',
              type: 'NUMBER'
            },
            {
              id: 'bordereau_remise_autres',
              label: 'Autre (indiquez type et nombre si besoin)',
              type: 'TEXT'
            },
            {
              id: 'bordereau_remise_carton',
              label: 'Nombre de cartons remis',
              type: 'NUMBER'
            },
            {
              id: 'bordereau_remise_cles',
              label: 'Nombre de clés USB remises',
              type: 'NUMBER'
            },
            {
              id: 'bordereau_remise_date',
              label: 'Date de la remise',
              type: 'DATE'
            },
            {
              id: 'bordereau_remise_lieu',
              label: 'Lieu de la remise',
              type: 'TEXT'
            }
          ],
          id: '82c2bcd7_d053_479f_b09b_c365b436938c',
          label: 'Remise des éléments',
          type: 'CATEGORY'
        }
      ],
      id: '495720e6_52a4_47b3_95ff_541620a236a2',
      label: 'Bordereau de transmission des pièces',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'nomination_date_ag',
          label: "Date de l'AG nommant un nouveau syndic",
          type: 'DATE'
        },
        {
          id: 'nomination_date_nouveau_contrat',
          label: "Date d'effet du nouveau contrat de syndic",
          type: 'DATE'
        },
        {
          id: 'nomination_date_ancien_contrat',
          label: "Date de fin de l'ancien contrat de syndic",
          type: 'DATE'
        }
      ],
      id: '9b4e5bc0_6cd3_4143_b4da_01135e4f83d3',
      label: 'Courrier nomination syndic',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'mandat_numero_gestion',
          label: 'Numéro de mandat',
          register: {
            contracts: ['UNIS_COPROPRIETE_MANDAT_GESTION_ASL'],
            type: 'MANAGEMENT'
          },
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'gestion_asl_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'asl',
              label: 'Association Syndicale Libre'
            },
            {
              id: 'aful',
              label: 'Association Foncière Urbaine Libre'
            }
          ],
          id: 'gestion_asl_aful',
          label: "Mandat de gestion d'une",
          type: 'SELECT'
        },
        {
          id: 'gestion_asl_description',
          label: 'Description des équipements et des immeubles',
          type: 'TEXT'
        },
        {
          description: 'chaque mois; trimestre ; semestre...',
          id: 'gestion_asl_periode_charge',
          label: 'Périodicité des appels de fonds : chaque',
          type: 'TEXT'
        },
        {
          description: '1/10e ; 1/4e... à adapter en fonction de la période de paiement',
          id: 'gestion_asl_periode_charge_montant',
          label: 'Montant des appels de fonds par rapport au budget total',
          type: 'TEXT'
        },
        {
          id: 'gestion_asl_cloture_delai',
          label: 'Délai de réddition de la clotûre de compte',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          children: [
            {
              id: 'gestion_asl_travaux_courant',
              label: "Montant maximum des travaux d'entretien pour gestion courante (HT)",
              type: 'PRICE'
            },
            {
              id: 'gestion_asl_sinistre_courant',
              label: 'Montant maximum des sinistres pour gestion courante (HT)',
              type: 'PRICE'
            },
            {
              id: 'gestion_asl_sinistre_courant_do_hors_gestion',
              label: 'Montant maximum des sinistres Dommages-Ouvrage pour gestion courante (HT)',
              type: 'PRICE'
            }
          ],
          id: '396715e6_3e4d_4d0e_96c5_8e483eda8aa0',
          label: 'Prestation de gestion courante',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'gestion_asl_travaux_urgent',
              label: 'Montant minimum des travaux urgents (HT)',
              type: 'PRICE'
            },
            {
              id: 'gestion_asl_sinistre_courant_hors_gestion',
              label: 'Montant minimum des sinistres hors gestion courante (HT)',
              type: 'PRICE'
            }
          ],
          id: 'cfcdd56f_d12c_4aac_9801_965465353095',
          label: 'Prestations particulères hors gestion courante',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'gestion_asl_remuneration_annuelle',
              label: 'Rémunération forfaitaire annuelle (HT)',
              type: 'PRICE'
            },
            {
              description: 'par trimestre ; par mois ; par semestre...',
              id: 'gestion_asl_remnueration_facturation',
              label: 'Période de facturation',
              type: 'TEXT'
            },
            {
              description: 'chaque trimestre ; chaque mois... à adapter en fonction de la période de facturation',
              id: 'gestion_asl_remnueration_paiement',
              label: 'Période de paiement',
              type: 'TEXT'
            }
          ],
          id: '57b8c01c_f661_44ed_88e6_54d4108b6804',
          label: 'Rémuneration',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'gestion_asl_remuneration_pourcentage_1',
              label: 'Pourcentage de rémunération pour les travaux entre 1500 € à 10,000 €',
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'gestion_asl_remuneration_pourcentage_2',
              label: 'Pourcentage de rémunération pour les travaux entre 10,001 € à 50,000 €',
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'gestion_asl_remuneration_pourcentage_3',
              label: 'Pourcentage de rémunération pour les travaux entre 50,001 € à 100,000 €',
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'gestion_asl_remuneration_pourcentage_4',
              label: 'Pourcentage de rémunération pour les travaux dépassant 100,001 €',
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'gestion_asl_contentieux_1',
              label: "Pourcentage de rémunération pour les recouvrements de charges jusqu'à 10,000 €",
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'gestion_asl_contentieux_2',
              label: 'Pourcentage de rémunération pour les recouvrements de charges dépassant 10,000 €',
              suffix: '%',
              type: 'NUMBER'
            }
          ],
          id: '0f2e2d90_5ebf_4875_86df_851d6370666f',
          label: 'Rémunération prestation particulières',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              description: 'Directeur - Technicien - Comptable - Assistante',
              id: 'gestion_as_bareme_unique',
              label: 'Barème unique des honoraires pour tous les postes',
              type: 'SELECT-BINARY'
            },
            {
              id: 'gestion_asl_bareme_unique_montant',
              label: 'Montant du barème unique (HT)',
              type: 'PRICE'
            },
            {
              id: 'gestion_asl_bareme_directeur',
              label: 'Directeur / Service contentieux (HT)',
              type: 'PRICE'
            },
            {
              id: 'gestion_asl_bareme_technicien',
              label: 'Technicien (HT)',
              type: 'PRICE'
            },
            {
              id: 'gestion_asl_bareme_comptable',
              label: 'Comptable (HT)',
              type: 'PRICE'
            },
            {
              id: 'gestion_asl_bareme_assistant',
              label: 'Assistant (HT)',
              type: 'PRICE'
            }
          ],
          id: '209879d7_3f85_4159_903b_4f6f5f58d836',
          label: 'Barème des honoraires',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'gestion_asl_duree_mandat',
              label: 'Durée du mandat',
              suffix: 'ans',
              type: 'NUMBER'
            },
            {
              id: 'gestion_asl_duree_mandat_mois',
              label: 'Durée du mandat',
              suffix: 'mois',
              type: 'NUMBER'
            },
            {
              id: 'gestion_asl_duree_mandat_debut',
              label: 'Date de début du mandat',
              type: 'DATE'
            }
          ],
          id: 'a2188b1c_43b3_4572_8dfb_ae896c4322b3',
          label: 'Durée du mandat',
          type: 'CATEGORY'
        }
      ],
      id: '78a0dac0_0480_4966_ab53_b9536f841e94',
      label: 'Mandat de Gestion',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__OPERATION__SYNDIC__GENERAL__FICHE',
  label: 'Information',
  mynotaryTemplate: false,
  originTemplate: null,
  specificTypes: ['OPERATION', 'SYNDIC', 'GENERAL', 'FICHE'],
  type: 'RECORD'
};
