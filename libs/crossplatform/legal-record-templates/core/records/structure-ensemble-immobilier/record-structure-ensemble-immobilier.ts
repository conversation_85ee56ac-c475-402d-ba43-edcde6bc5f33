// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordStructureEnsembleImmobilier: LegalRecordTemplate = {
  config: {
    recordLinks: [
      {
        id: 'ENSEMBLE_IMMOBILIER',
        specificTypes: ['COMPOSITION', 'ENSEMBLE_IMMOBILIER'],
        label: 'Bien'
      }
    ],
    search: ['adresse'],
    duplicate: ['adresse']
  },
  form: [
    {
      children: [
        {
          choices: [
            {
              id: 'fonds',
              label: 'Fonds de commerce'
            },
            {
              id: 'mur',
              label: 'Murs'
            },
            {
              id: 'bail',
              label: 'Droit au bail'
            },
            {
              id: 'titre',
              label: 'Titres de sociétés ou de parts'
            }
          ],
          id: 'bien_pro_type',
          label: 'Type de cession',
          multiple: true,
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'moins_10_ans',
              label: 'Il y a moins de 10 ans',
              icon: 'calendar.svg'
            },
            {
              id: 'apres_1997',
              label: 'Après le 1er juillet 1997 (date du permis)',
              icon: 'calendar.svg'
            },
            {
              id: '1949_1997',
              label: 'Après le 1er janvier 1949',
              icon: 'calendar.svg'
            },
            {
              id: 'avant_1949',
              label: 'Avant 1949',
              icon: 'calendar.svg'
            }
          ],
          id: 'construction',
          label: "L'immeuble a été construit",
          type: 'SELECT-PICTURES'
        },
        {
          choices: [
            {
              label: 'Habitation exlusivement',
              id: 'habitation'
            },
            {
              label: 'Mixte professionnel et Habitation',
              id: 'mixte'
            },
            {
              label: 'Hors habitation',
              id: 'hors_habitation'
            }
          ],
          id: 'usage',
          label: "L'immeuble a comme usage",
          type: 'SELECT'
        },
        {
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'adresse',
          label: "Adresse de l'immeuble",
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'residence_statut',
          label: 'Bien situé dans une résidence',
          type: 'SELECT-BINARY'
        },
        {
          id: 'residence_nom',
          label: 'Nom de la résidence',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'Non Tendue'
            },
            {
              id: 'oui',
              label: 'Tendue'
            },
            {
              id: 'tres_tendue',
              label: 'Très Tendue'
            }
          ],
          description: 'Code postal détecté dans une zone non tendue, tendue ou très tendue',
          id: 'zone_tendue',
          label: 'Commune située dans une zone',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'zone_tendue',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'zone_loyer_reference',
          label: 'Le bien est-il situé dans une zone soumise au loyer de référence ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'numero_fiscal',
          label: "Numéro d'identification fiscal du logement",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'presence_volume',
          label: 'Volume',
          type: 'SELECT-BINARY'
        },
        {
          id: 'numero_lot_volume',
          label: 'Numéro de volume',
          type: 'TEXT'
        },
        {
          id: 'quartier',
          label: 'Quartier',
          type: 'TEXT'
        },
        {
          children: [
            {
              id: 'cadastre_parcelles_code_departement',
              label: 'Code département',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_code_topad',
              label: 'Code Commune TOPAD',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_code_insee',
              label: 'Code Commune INSEE',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_commune',
              label: 'Nom commune',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_prefixe',
              label: 'Préfixe',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_section',
              label: 'Section',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_parcelle',
              label: 'Parcelle',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_lieudit',
              label: 'Lieudit',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_h',
              label: 'Hectare',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_a',
              label: 'Are',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_c',
              label: 'Centiare',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_cadastre_non_renove',
              label: 'Référence cadastre non rénové',
              type: 'TEXT'
            }
          ],
          id: 'cadastre_parcelles',
          label: "Références cadastrales de l'ensemble immobilier",
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: 'Section '
              },
              {
                type: 'VARIABLE',
                value: 'cadastre_parcelles_section'
              },
              {
                type: 'TEXT',
                value: ' n° '
              },
              {
                type: 'VARIABLE',
                value: 'cadastre_parcelles_parcelle'
              }
            ],
            max: 1000,
            min: 1,
            type: 'REF_CADASTRALES'
          },
          type: 'REPEAT'
        },
        {
          id: 'montant_taxe_fonciere',
          label: 'Montant de la dernière taxe foncière',
          type: 'PRICE'
        },
        {
          id: 'montant_taxe_fonciere_cfp',
          label: 'Montant de la dernière taxe foncière',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'annee_taxe_fonciere',
          label: 'Année de la dernière taxe foncière',
          type: 'YEAR'
        },
        {
          id: 'external_cadastre_situation',
          label: 'Plan de situation Cadastrale',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeExcludedInOperationConfig: true
          },
          id: 'taxe_fonciere',
          label: 'Dernière taxe foncière',
          type: 'UPLOAD'
        },
        {
          id: 'superficie',
          label: 'Superficie',
          type: 'NUMBER'
        },
        {
          id: 'enseigne_nom',
          label: 'Nom / Enseigne',
          type: 'TEXT'
        },
        {
          id: 'activite',
          label: 'Activité exercée',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cadastre_division_a_venir',
          label: 'Division cadastrale',
          type: 'SELECT-BINARY'
        },
        {
          id: 'cadastre_division_parcelle_mere',
          label: 'Parcelle mère de la division',
          type: 'TEXT'
        },
        {
          id: 'cadastre_division_geometre_nom',
          label: 'Nom du Géomètre ayant effectué la division',
          type: 'TEXT'
        },
        {
          id: 'cadastre_division_geometre_adresse',
          label: 'Adresse du Géomètre ayant effectué la division',
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cuve_stockage_petrolier',
          label: 'Cuve de stockage de produits pétroliers',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'monument_historique',
          label: 'Bien situé dans un périmètre de protection des monuments historiques',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'proximite_activites_agricoles',
          label: "Proximité d'activités agricoles - industrielles - artisanales - commerciales",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'espace_boise_classe',
          label: 'Espace boisé classé',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'sepulture',
          label: 'Présence de sépulture - cimetière',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'presence_parc_eolienne',
          label: "Présence d'un champ d'éoliennes",
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'presence_parc_eolienne',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'parc_eolienne_projet_document',
          label: 'Carte des projets éoliens',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                  'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                ]
              }
            ]
          ],
          id: 'designation_precisions',
          label: 'Ajouter des précisions sur la désignation du Bien',
          type: 'SELECT-BINARY'
        },
        {
          id: 'designation_precisions_texte',
          label: 'Précisions sur la désignation du Bien',
          type: 'TEXTAREA'
        },
        {
          filters: {
            mustBeExcludedInOperationConfig: true
          },
          id: 'assurance_maison',
          label: 'Assurance Maison',
          type: 'UPLOAD'
        }
      ],
      id: 'f3b7446e_40ea_45ad_b1ae_56ff0d76283b',
      label: 'Informations générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'designation',
          label: 'Désignation du bien',
          type: 'TEXTAREA'
        },
        {
          id: 'nombre_logement',
          label: 'Nombre total de logements',
          type: 'NUMBER'
        },
        {
          id: 'superficie_habitable',
          label: 'Superficie habitable totale',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'superficie_habitable_professionnel',
          label: 'Mesurage réalisé par un professionnel',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'superficie_habitable_professionnel_nom',
              label: 'Nom du professionnel',
              type: 'TEXT'
            },
            {
              id: 'superficie_habitable_professionnel_adresse',
              label: 'Adresse du professionnel',
              type: 'ADDRESS'
            },
            {
              id: 'superficie_habitable_professionnel_date',
              label: 'Date du mesurage',
              type: 'DATE'
            },
            {
              id: 'attestation_superficie',
              label: 'Attestation de superficie',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'superficie_habitable_professionnel',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'b92d5351_1d00_44de_b2b3_07f8a0963564',
          label: 'Mesurage',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'location_equipement_aucun',
              label: 'Aucun équipement commun'
            },
            {
              id: 'location_equipement_velo',
              label: 'Local à vélo'
            },
            {
              id: 'location_equipement_poubelle',
              label: 'Local poubelle'
            },
            {
              id: 'location_equipement_antenne',
              label: 'Antenne collective'
            },
            {
              id: 'location_equipement_laverie',
              label: 'Local Laverie'
            },
            {
              id: 'location_equipement_sport',
              label: 'Salle de sport'
            },
            {
              id: 'location_equipement_autre',
              label: 'Autre'
            }
          ],
          id: 'location_liste_equipement',
          label: "Element d'équipement de l'ensemble Immobimier",
          multiple: true,
          type: 'SELECT'
        },
        {
          id: 'location_liste_equipement_autre',
          label: "Autre élément d'équipements",
          type: 'TEXT'
        },
        {
          id: 'location_liste_annexe_privatif',
          label: "Désignation des locaux accessoires de l'immeuble à usage privatif",
          type: 'TEXT'
        }
      ],
      id: '6662b71c_5c89_4ceb_a111_03abc7901bae',
      label: "Description de l'immeuble",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  id: 'chaudiere_gaz',
                  label: 'Chaudière Gaz'
                },
                {
                  id: 'chaudiere_fioul',
                  label: 'Chaudière Fioul'
                },
                {
                  id: 'cheminee',
                  label: 'Cheminée'
                },
                {
                  id: 'poele_bois',
                  label: 'Poêle à bois'
                },
                {
                  id: 'poele_granules',
                  label: 'Poêle à granulés'
                },
                {
                  id: 'radiateur',
                  label: 'Convecteurs électriques'
                },
                {
                  id: 'pompe',
                  label: 'Pompe à Chaleur'
                },
                {
                  id: 'aucun',
                  label: 'Aucun système de chauffage'
                }
              ],
              id: 'systeme_chauffage_liste',
              label: 'Système de chauffage',
              multiple: true,
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'systeme_chauffage_entretien',
              label: 'Ramonage / Entretien effectué',
              type: 'SELECT-BINARY'
            },
            {
              id: 'systeme_chauffage_entretien_description',
              label: 'Description du dispositif',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'systeme_chauffage_fonctionnement',
              label: 'Dispositif en fonctionnement',
              type: 'SELECT-BINARY'
            },
            {
              id: 'systeme_chauffage_intervention_nom',
              label: "Nom de l'entreprise intervenue",
              type: 'TEXT'
            },
            {
              id: 'systeme_chauffage_intervention_date',
              label: "Date de l'intervention",
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'systeme_chauffage_anomalies_vendeur',
              label: 'Prise en charge des anomalies par le Vendeur',
              type: 'SELECT-BINARY'
            }
          ],
          id: '6ac484a3_14f5_4bde_9840_d7b1bf28f9aa',
          label: 'Chauffage',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'chaudiere_statut',
              label: "Présence d'une chaudière",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'gaz',
                  label: 'Gaz'
                },
                {
                  id: 'fioul',
                  label: 'Fioul'
                },
                {
                  id: 'autre',
                  label: 'Autre'
                }
              ],
              id: 'chaudiere_statut_type',
              label: 'Type de chaudière',
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'chaudiere_statut_type',
                    value: 'autre',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'chaudiere_statut_type_autre',
              label: 'Autre type',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'chaudiere_entretien_statut',
              label: 'Entretien chaudière effectué',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'chaudiere_entretien_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'chaudiere_entretien_attestation',
              label: 'Attestation entretien chaudière',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'chaudiere_entretien_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'chaudiere_entretien_facture',
              label: 'Facture entretien chaudière',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'chaudiere_entretien_vendeur',
              label: 'Entretien effectué avant la vente',
              type: 'SELECT-BINARY'
            }
          ],
          id: 'b0d3dc89_fb8c_45fb_9888_959aa2849424',
          label: 'Chaudière',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cuve_fioul_statut',
              label: 'Cuve à fioul',
              type: 'SELECT-BINARY'
            },
            {
              choices: [],
              id: 'cuve_fioul_etat',
              label: 'La cuve à fioul est',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cuve_fioul_anomalies',
              label: 'Anomalies de la cuve à relater',
              type: 'SELECT-BINARY'
            },
            {
              id: 'cuve_fioul_anomalies_liste',
              label: 'Liste des anomalies',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cuve_fioul_proprietaire',
              label: 'Cuve appartenant au Vendeur',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cuve_fioul_contrat',
              label: "Contrat d'approvisionnement",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cuve_fioul_statut_remboursement',
              label: "Remboursement du fioul restant par l'acquéreur",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'cuve_fioul_contrat',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'cuve_fioul_contrat_document',
              label: "Contrat d'approvisionnement - Fioul",
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                    ]
                  }
                ]
              ],
              id: 'cuve_fioul_precisions',
              label: 'Ajouter une précision sur la cuve à fioul',
              type: 'SELECT-BINARY'
            },
            {
              id: 'cuve_fioul_precisions_texte',
              label: 'Précisions sur la cuve à fioul',
              type: 'TEXTAREA'
            }
          ],
          id: 'a71af93d_9234_4483_8634_6bd4ee90c52a',
          label: 'Cuve',
          type: 'CATEGORY'
        }
      ],
      id: '794fdffe_5071_403b_b52c_1e058902295b',
      label: "Eléments d'équipement",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              id: 'origine_propriete_origine_proprietaire',
              label: 'Noms des propriétaires concernés par cette origine de propriété',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'origine_propriete_acquisition',
                  label: 'Acquisition'
                },
                {
                  id: 'origine_propriete_donation',
                  label: 'Donation'
                },
                {
                  id: 'origine_propriete_succession',
                  label: 'Succession'
                },
                {
                  id: 'origine_propriete_partage',
                  label: 'Partage - Licitation'
                },
                {
                  id: 'origine_propriete_echange',
                  label: 'Echange'
                },
                {
                  id: 'origine_propriete_adjudication',
                  label: 'Adjudication - Vente aux enchères'
                },
                {
                  id: 'origine_propriete_remembrement',
                  label: 'Remembrement'
                }
              ],
              id: 'origine_propriete_origine_liste',
              label: 'Le bien a été reçu par',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_donation',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_origine_donation_unique',
              label: 'Propriétaire enfant unique',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'origine_propriete_origine_donation_donateur_decede',
              label: 'Donateur décédé',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_donation_unique',
                    value: 'non',
                    type: 'EQUALS'
                  },
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_donation',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_origine_donation_deces',
              label: 'Succession du donateur reglée',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_succession',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_origine_succession_statut',
              label: 'Succession déjà réglée',
              type: 'SELECT-BINARY'
            },
            {
              id: 'origine_propriete_origine_notaire_nom',
              label: 'Nom du notaire',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_notaire_ville',
              label: 'Lieu de résidence du notaire',
              type: 'TEXT',
              uppercase: 'WORD'
            },
            {
              id: 'origine_propriete_origine_defunt_prenom',
              label: 'Prénom du défunt',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_defunt_nom',
              label: 'Nom du défunt',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_defunt_date',
              label: 'Date du décès',
              type: 'DATE'
            },
            {
              id: 'origine_propriete_origine_date',
              label: "Date de l'acte",
              type: 'DATE'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_adjudication',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_origine_tribunal',
              label: 'Ville du tribunal',
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_acquisition',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_acquisition',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_echange',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_echange',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_donation',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_donation',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_partage',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_partage',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_succession',
                    type: 'EQUALS'
                  },
                  {
                    id: 'origine_propriete_origine_succession_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_succession_pas_reglee',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_succession',
                    type: 'EQUALS'
                  },
                  {
                    id: 'origine_propriete_origine_succession_statut',
                    value: 'non',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_succession',
              label: 'Titre de propriété - Attestation de dévolution',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_adjudication',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_adjudication',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_remembrement',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_remembrement',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            }
          ],
          id: 'origine_propriete',
          label: 'Ajouter une origine de propriété',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: ' '
              },
              {
                type: 'VARIABLE',
                value: 'origine_propriete_origine_liste'
              },
              {
                type: 'TEXT',
                value: ' - '
              },
              {
                type: 'VARIABLE',
                value: 'origine_propriete_origine_date'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'servitudes',
          label: 'Servitudes',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'servitudes',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'servitudes_liste',
          label: 'Liste des servitudes',
          type: 'TEXTAREA'
        },
        {
          id: 'servitude_notaire_nom',
          label: 'Nom du notaire ayant reçu la servitude',
          type: 'TEXT'
        },
        {
          id: 'servitude_notaire_ville',
          label: 'Ville du notaire',
          type: 'TEXT'
        },
        {
          id: 'servitude_notaire_date',
          label: 'Date de constitution de la servitude',
          type: 'DATE'
        },
        {
          conditions: [
            [
              {
                id: 'servitudes',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'servitude_acte',
          label: 'Rappel de servitude',
          type: 'UPLOAD'
        }
      ],
      id: '5c820475_ea40_42f3_87fd_d939acec18bf',
      label: 'Origine de propriété',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'indigne',
          label: "Zone d'habitat indigne (permis de louer)",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'autorisation',
                  label: 'Une autorisation préalable'
                },
                {
                  id: 'declaration',
                  label: 'Une déclaration postérieure'
                }
              ],
              id: 'indigne_dualite',
              label: 'La location néccessite une',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'indigne_autorisation',
              label: 'La location nécessite-t-elle une autorisation préalable ?',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'indigne',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'indigne_autorisation',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ],
                [
                  {
                    id: 'indigne',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'indigne_dualite',
                    value: 'autorisation',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'autorisation_location',
              label: 'Autorisation de louer',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'indigne_declaration',
              label: 'La location nécessite-t-elle une déclaration préalable ?',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'indigne',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'indigne_declaration',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'declaration_prealable_location',
              label: 'Déclaration préalable de location',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'indigne',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: '647ba590_8cab_469e_858b_7de8fcc51332',
          label: "Informations sur la zone d'habitat indigne",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'zone_loyer_reference_montant',
              label: 'Montant du loyer de référence (par m2)',
              type: 'PRICE'
            },
            {
              id: 'zone_loyer_reference_montant_majore',
              label: 'Montant du loyer de référence majoré (par m2)',
              type: 'PRICE'
            }
          ],
          id: '007f5bb0_d4bb_4bf5_bc37_862536f79511',
          label: 'Informations sur le loyer de référence',
          type: 'CATEGORY'
        }
      ],
      id: '41a4dcbc_4374_43bb_a0a5_7d13ae25486f',
      label: 'Règlementations spéciales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'puits',
          label: 'Puits',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'puits_declaration_statut',
              label: 'Le puits a-t-il-été déclaré en mairie ?',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'puits',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                    ]
                  }
                ]
              ],
              id: 'puits_precisions',
              label: 'Ajouter des précisions sur le puits',
              type: 'SELECT-BINARY'
            },
            {
              id: 'puits_precisions_texte',
              label: 'Précisions sur le puits',
              type: 'TEXTAREA'
            }
          ],
          conditions: [
            [
              {
                id: 'puits',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: '51c5a72f_e3d4_4d19_83c5_e20386a15b6a',
          label: 'Informations sur le puits',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cheminee',
          label: 'Cheminée / Poêle',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cheminee_fonctionnelle_statut',
              label: 'Cheminée / Poêle fonctionnel',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'cheminee_fonctionnelle_ramonage',
                  label: "Ramonage il y a moins d'un an",
                  type: 'SELECT-BINARY'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'cheminee',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cheminee_fonctionnelle_ramonage',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cheminee_fonctionnelle_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'cheminee_facture_ramonage',
                  label: 'Facture ramonage',
                  type: 'UPLOAD'
                }
              ],
              conditions: [
                [
                  {
                    id: 'cheminee',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'cheminee_fonctionnelle_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: '48bf787c_d738_49a2_a0bb_ae959ca6da67',
              label: 'Ramonage',
              type: 'CATEGORY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'cheminee',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                      'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                    ]
                  }
                ]
              ],
              id: 'cheminee_precisions',
              label: 'Ajouter une précision sur la cheminée/poêle',
              type: 'SELECT-BINARY'
            },
            {
              id: 'cheminee_precisions_texte',
              label: 'Précisions sur la cheminée/poêle',
              type: 'TEXTAREA'
            }
          ],
          conditions: [
            [
              {
                id: 'cheminee',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'd6a70ac0_b2d7_4b80_bf2f_5cd86a2eea7c',
          label: 'Informations sur le pôele / la cheminée',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'contrats_attaches_statut',
          label: 'Contrats attachés au bien',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'contrat_attaches_list_contrat_attaches_type',
              label: 'Contrat',
              type: 'TEXT'
            },
            {
              id: 'contrat_attaches_list_contrat_attaches_prix',
              label: 'Montant',
              type: 'PRICE'
            },
            {
              id: 'contrat_attaches_list_contrat_attaches_prix_cfp',
              label: 'Montant',
              suffix: 'CFP',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'contrat_frequence_annuelle',
                  label: 'Annuelle'
                },
                {
                  id: 'contrat_frequence_mensuelle',
                  label: 'Mensuelle'
                }
              ],
              id: 'contrat_attaches_list_contrat_attaches_frequence',
              label: 'Fréquence',
              type: 'SELECT'
            },
            {
              id: 'contrat_attaches_list_contrat_attaches_document',
              label: 'Contrat',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'contrats_attaches_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'contrat_attaches_list',
          label: 'Contrats',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'contrat_attaches_list_contrat_attaches_type'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'contrat_panneau_voltaique',
          label: 'Présence de panneaux photovoltaïques',
          type: 'SELECT-BINARY'
        },
        {
          id: 'contrat_panneau_voltaique_installateur_nom',
          label: "Désignation de l'installateur des panneaux",
          type: 'TEXT'
        },
        {
          id: 'contrat_panneau_voltaique_installation_date',
          label: "Date d'installation des panneaux",
          type: 'DATE'
        },
        {
          id: 'contrat_panneau_voltaique_mise_en_service_date',
          label: 'Date de mise en service des panneaux',
          type: 'DATE'
        }
      ],
      id: 'd2ac73a3_94e0_4c69_b1fe_51de41cded79',
      label: 'Informations particulières',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'convention_anah',
          label: "Le bien fait-il l'objet d'une convention Anah ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avantage_fiscal_statut',
          label: "Présence d'un dispositif fiscal",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'avantage_fiscal_dispositif',
              label: 'Dispositif fiscal utilisé',
              type: 'TEXT'
            }
          ],
          conditions: [
            [
              {
                id: 'avantage_fiscal_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'fd3925f8_49ed_4574_ab87_3af99807efa7',
          label: 'Avantage fiscal',
          type: 'CATEGORY'
        }
      ],
      id: '57528eca_424b_456e_9a60_8a72745c7c22',
      label: 'Situation locative',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              label: "Un seul dossier de diagnostic est réalisé pour tout l'immeuble",
              id: 'immeuble_entier_dossier_diagnostic_unique'
            },
            {
              label: 'Un dossier de diagnostic est réalisé pour chaque logement',
              id: 'immeuble_entier_dossier_diagnostic_pluralite'
            }
          ],
          id: 'immeuble_entier_diagnostic',
          label: 'Concernant le dossier de diagnostic technique',
          type: 'SELECT'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostics_techniques_diagnostiqueur_unique',
              label: 'Diagnostiqueur unique',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostics_techniques_diagnostiqueur_unique_date',
              label: 'Date unique',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'diagnostics_techniques_diagnostiqueur_unique_nom',
                  label: 'Nom du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  id: 'diagnostics_techniques_diagnostiqueur_unique_adresse',
                  label: 'Adresse du diagnostiqueur',
                  type: 'ADDRESS'
                },
                {
                  id: 'diagnostics_techniques_domofrance_date',
                  label: "Date d'établissement du dossier de diagnostic technique",
                  type: 'DATE'
                }
              ],
              conditions: [
                [
                  {
                    id: 'diagnostics_techniques_diagnostiqueur_unique',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'dd370911_ece1_4858_991f_04025e08af9e',
              label: 'Information sur le diagnostiqueur',
              type: 'CATEGORY'
            },
            {
              id: 'attestation_diagnostiqueur',
              label: 'Attestation du diagnostiqueur',
              type: 'UPLOAD'
            }
          ],
          id: '078f210e_95c6_4be8_bd24_859c0b28e4d8',
          label: 'Dossier de diagnostic technique',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostic_a_jour',
          label: 'Les diagnostics sont à jour',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_plomb_statut',
              label: 'Diagnostic réalisé',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'diagnostic_plomb_date',
                  label: 'Plomb  - Date du diagnostic',
                  type: 'DATE'
                },
                {
                  choices: [
                    {
                      label: "Il n'a pas été repéré de présence de plomb",
                      id: 'absence'
                    },
                    {
                      label: 'Il a été repéré des mesures de plomb de classe 0',
                      id: 'classe_0'
                    },
                    {
                      label: 'Il a été repéré des mesures de plomb de classe 1',
                      id: 'classe_1'
                    },
                    {
                      label: 'Il a été repéré des mesures de plomb de classe 2',
                      id: 'classe_2'
                    },
                    {
                      label: 'Il a été repéré des mesures de plomb de classe 3',
                      id: 'classe_3'
                    }
                  ],
                  id: 'diagnostic_plomb_resultat',
                  label: 'Résultat du diagnostic',
                  multiple: true,
                  type: 'SELECT'
                },
                {
                  id: 'diagnostic_plomb_resultat_libre',
                  label: 'Résultat du diagnostic',
                  type: 'TEXTAREA'
                },
                {
                  id: 'diagnostic_plomb',
                  label: 'Diagnostic Plomb',
                  type: 'UPLOAD'
                }
              ],
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'avant_1949'
                  },
                  {
                    id: 'diagnostic_plomb_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'ea2f3c01_1ea6_45ac_83a9_1a4d06fe05e0',
              label: 'Informations sur le diagnostic plomb',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'diagnostic_plomb_diagnostiqueur_nom',
                  label: 'Nom du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  id: 'diagnostic_plomb_diagnostiqueur_adresse',
                  label: 'Adresse du diagnostiqueur',
                  type: 'ADDRESS'
                }
              ],
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'avant_1949'
                  },
                  {
                    id: 'diagnostics_techniques_diagnostiqueur_unique',
                    type: 'EQUALS',
                    value: 'non'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'avant_1949'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                      'OPERATION__CDC__IMMOBILIER__VENTE',
                      'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                    ]
                  }
                ]
              ],
              id: '71f6eef0_6bf9_4bdc_a1b7_74e2fae7e9aa',
              label: 'Informations sur le diagnostiqueur',
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'avant_1949'
              }
            ]
          ],
          id: '3b92a37a_2731_44cb_9d57_9f74e038f1c8',
          label: 'Diagnostic Plomb',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_amiante_statut',
              label: 'Diagnostic réalisé',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'diagnostic_amiante_date',
                  label: 'Date du diagnostic',
                  type: 'DATE'
                },
                {
                  choices: [
                    {
                      label: "Il n'a pas été repéré des matériaux contenant de l'amiante",
                      id: 'absence'
                    },
                    {
                      label: "Il a été repéré des matériaux contenant de l'amiante de la liste A",
                      id: 'liste_a'
                    },
                    {
                      label: "Il a été repéré des matériaux contenant de l'amiante de la liste B",
                      id: 'liste_b'
                    },
                    {
                      label: "Des locaux ou parties de locaux n'ont pas pu être visités",
                      id: 'non_visite'
                    }
                  ],
                  id: 'diagnostic_amiante_resultat',
                  label: 'Résultat du diagnostic',
                  multiple: true,
                  type: 'SELECT'
                },
                {
                  id: 'diagnostic_amiante_resultat_libre',
                  label: 'Résultat du diagnostic',
                  type: 'TEXTAREA'
                },
                {
                  id: 'diagnostic_amiante',
                  label: 'Diagnostic Amiante',
                  type: 'UPLOAD'
                }
              ],
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: '1949_1997'
                  },
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'avant_1949'
                  },
                  {
                    id: 'diagnostic_amiante_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: '1949_1997'
                  },
                  {
                    id: 'diagnostic_amiante_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'avant_1949'
                  },
                  {
                    id: 'diagnostic_amiante_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'e45b5be8_0ccd_4304_bcf0_735a4e968c79',
              label: 'Informations sur le diagnostic amiante',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'diagnostic_amiante_diagnostiqueur_nom',
                  label: 'Nom du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  id: 'diagnostic_amiante_diagnostiqueur_adresse',
                  label: 'Adresse du diagnostiqueur',
                  type: 'ADDRESS'
                }
              ],
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: '1949_1997'
                  },
                  {
                    id: 'diagnostics_techniques_diagnostiqueur_unique',
                    type: 'EQUALS',
                    value: 'non'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: '1949_1997'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                      'OPERATION__CDC__IMMOBILIER__VENTE',
                      'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                    ]
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'avant_1949'
                  },
                  {
                    id: 'diagnostics_techniques_diagnostiqueur_unique',
                    type: 'EQUALS',
                    value: 'non'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'avant_1949'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                      'OPERATION__CDC__IMMOBILIER__VENTE',
                      'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                    ]
                  }
                ]
              ],
              id: 'd118282d_49c3_47d8_998b_e22f8699dc1b',
              label: 'Informations sur le diagnostiqueur',
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: '1949_1997'
              }
            ],
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'avant_1949'
              }
            ]
          ],
          id: 'b274b1e2_2c33_429f_a0f4_77b0e589f2a4',
          label: 'Diagnostic Amiante',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_gaz_presence_installation',
              label: "Présence d'une installation de gaz",
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_gaz_vielle_installation',
                  label: 'Installation de plus de 15 ans',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'diagnostic_gaz_statut',
                      label: 'Diagnostic réalisé',
                      type: 'SELECT-BINARY'
                    },
                    {
                      children: [
                        {
                          id: 'diagnostic_gaz_date',
                          label: 'Date du diagnostic',
                          type: 'DATE'
                        },
                        {
                          choices: [
                            {
                              label: "L'installation de gaz ne comporte aucune anomalie",
                              id: 'sans'
                            },
                            {
                              label: 'L’installation comporte des anomalies de type A1',
                              id: '1'
                            },
                            {
                              label: 'L’installation comporte des anomalies de type A2',
                              id: '2'
                            },
                            {
                              label: 'L’installation comporte des anomalies de type DGI',
                              id: '3'
                            },
                            {
                              label: 'L’installation comporte des anomalies de type 32c',
                              id: '4'
                            }
                          ],
                          id: 'diagnostic_gaz_resultat',
                          label: 'Résultat du diagnostic',
                          multiple: true,
                          type: 'SELECT'
                        },
                        {
                          id: 'diagnostic_gaz',
                          label: 'Diagnostic Gaz',
                          type: 'UPLOAD'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'construction',
                            type: 'DIFFERENT',
                            value: 'moins_10_ans'
                          },
                          {
                            id: 'diagnostic_gaz_presence_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_gaz_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_gaz_vielle_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: '79a5999d_f149_4727_965f_b4a1c297f00b',
                      label: 'Informations sur le diagnostic gaz',
                      type: 'CATEGORY'
                    },
                    {
                      children: [
                        {
                          id: 'diagnostic_gaz_details_infos_diagnostiqueur_nom',
                          label: 'Nom du diagnostiqueur',
                          type: 'TEXT'
                        },
                        {
                          id: 'diagnostic_gaz_details_infos_diagnostiqueur_adresse',
                          label: 'Adresse du diagnostiqueur',
                          type: 'ADDRESS'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'construction',
                            type: 'DIFFERENT',
                            value: 'moins_10_ans'
                          },
                          {
                            id: 'diagnostic_gaz_presence_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_gaz_vielle_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostics_techniques_diagnostiqueur_unique',
                            type: 'EQUALS',
                            value: 'non'
                          }
                        ],
                        [
                          {
                            id: 'construction',
                            type: 'DIFFERENT',
                            value: 'moins_10_ans'
                          },
                          {
                            id: 'diagnostic_gaz_presence_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_gaz_vielle_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_TEMPLATES',
                            templates: [
                              'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                              'OPERATION__CDC__IMMOBILIER__VENTE',
                              'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                            ]
                          }
                        ]
                      ],
                      id: 'b52f639f_99c1_443e_ad29_c1a4f8f51f91',
                      label: 'Informations sur le diagnostiqueur',
                      type: 'CATEGORY'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'DIFFERENT',
                        value: 'moins_10_ans'
                      },
                      {
                        id: 'diagnostic_gaz_presence_installation',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'diagnostic_gaz_vielle_installation',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: '4fe3de85_15ab_40f1_bde9_aa9448ae24a6',
                  label: "Informations sur l'installation au gaz",
                  type: 'CATEGORY'
                }
              ],
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'DIFFERENT',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'diagnostic_gaz_presence_installation',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: '98eadfa4_3bae_42a5_bd6d_6a48bd8e6d29',
              label: 'Installation au gaz',
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'construction',
                type: 'DIFFERENT',
                value: 'moins_10_ans'
              }
            ]
          ],
          id: '00982bb7_a2e2_45d2_b0c4_e1003937b19e',
          label: 'Diagnostic Gaz',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_electrique_presence_installation',
              label: "Electricité - Présence d'une installation électrique",
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_electrique_vielle_installation',
                  label: 'Electricité - Installation de plus de 15 ans',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'diagnostic_electrique_statut',
                      label: 'Electricité - Diagnostic électrique réalisé',
                      type: 'SELECT-BINARY'
                    },
                    {
                      children: [
                        {
                          id: 'diagnostic_electrique_date',
                          label: 'Electricité - Date du diagnostic',
                          type: 'DATE'
                        },
                        {
                          choices: [
                            {
                              label: "L'installation électrique ne comporte aucune anomalie",
                              id: 'sans'
                            },
                            {
                              label: '1. L’appareil général de commande et de protection et de son accessibilité',
                              id: '1'
                            },
                            {
                              label:
                                '2. La protection différentielle à l’origine de l’installation électrique et sa sensibilité appropriée aux conditions de mise à la terre',
                              id: '2'
                            },
                            {
                              label: '3. La  prise de terre et l’installation de mise à la terre',
                              id: '3'
                            },
                            {
                              label: '4. La protection contre les surintensités adaptée à la section des conducteurs',
                              id: '4'
                            },
                            {
                              label:
                                '5. La liaison équipotentielle dans les locaux contenant une baignoire ou une douche',
                              id: '5'
                            },
                            {
                              label:
                                '6. Les règles liées aux zones dans les locaux contenant une baignoire ou une douche',
                              id: '6'
                            },
                            {
                              label: '7. Des matériels électriques présentant des risques de contacts directs',
                              id: '7'
                            },
                            {
                              label: '8.1 Des matériels électriques vétustes, inadaptés à l’usage',
                              id: '8_1'
                            },
                            {
                              label: '8.2 Des conducteurs non protégés mécaniquement',
                              id: '8_2'
                            },
                            {
                              label:
                                '9. Des appareils d’utilisation situés dans les parties communes et alimentés depuis la partie privative',
                              id: '9'
                            },
                            {
                              label: '10. La piscine privée ou le bassin de fontaine',
                              id: '10'
                            }
                          ],
                          id: 'diagnostic_electrique_anomalies',
                          label: 'Electricité - Anomalies détectées',
                          multiple: true,
                          type: 'SELECT'
                        },
                        {
                          choices: [
                            {
                              id: 'non',
                              label: 'NON'
                            },
                            {
                              id: 'oui',
                              label: 'OUI'
                            }
                          ],
                          conditions: [
                            [
                              {
                                id: 'construction',
                                type: 'DIFFERENT',
                                value: 'moins_10_ans'
                              },
                              {
                                id: 'diagnostic_electrique_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                  'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                ]
                              }
                            ]
                          ],
                          id: 'diagnostic_electrique_anomalies_autres_renvoi',
                          label: 'Autres anomalies : se référer au diagnostic',
                          type: 'SELECT-BINARY'
                        },
                        {
                          choices: [
                            {
                              label: "L'installation électrique ne fait l'objet d'aucune constatations",
                              id: 'sans'
                            },
                            {
                              label: '1. Des installations non couvertes par le présent diagnostic',
                              id: '1'
                            },
                            {
                              label: '2. Des points de contrôles n’ayant pu être vérifiés',
                              id: '2'
                            },
                            {
                              label: '3. Des constatations concernant l’installation et son environnement',
                              id: '3'
                            }
                          ],
                          id: 'diagnostic_electrique_constatations',
                          label: 'Electricité - Autres Constatations',
                          multiple: true,
                          type: 'SELECT'
                        },
                        {
                          choices: [
                            {
                              id: 'non',
                              label: 'NON'
                            },
                            {
                              id: 'oui',
                              label: 'OUI'
                            }
                          ],
                          conditions: [
                            [
                              {
                                id: 'construction',
                                type: 'DIFFERENT',
                                value: 'moins_10_ans'
                              },
                              {
                                id: 'diagnostic_electrique_presence_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_statut',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                id: 'diagnostic_electrique_vielle_installation',
                                type: 'EQUALS',
                                value: 'oui'
                              },
                              {
                                type: 'EQUALS_TEMPLATES',
                                templates: [
                                  'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                                  'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                                ]
                              }
                            ]
                          ],
                          id: 'diagnostic_electrique_constatation_autres_renvoi',
                          label: 'Autres constatations : se référer au diagnostic',
                          type: 'SELECT-BINARY'
                        },
                        {
                          id: 'diagnostic_electrique',
                          label: 'Diagnostic Electrique',
                          type: 'UPLOAD'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'construction',
                            type: 'DIFFERENT',
                            value: 'moins_10_ans'
                          },
                          {
                            id: 'diagnostic_electrique_presence_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_electrique_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_electrique_vielle_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: '815e5b13_a989_48be_85de_11703785232e',
                      label: 'Informations sur le diagnostic électrique',
                      type: 'CATEGORY'
                    },
                    {
                      children: [
                        {
                          id: 'diagnostic_electrique_details_infos_diagnostiqueur_nom',
                          label: 'Nom du diagnostiqueur',
                          type: 'TEXT'
                        },
                        {
                          id: 'diagnostic_electrique_details_infos_diagnostiqueur_adresse',
                          label: 'Adresse du diagnostiqueur',
                          type: 'ADDRESS'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'construction',
                            type: 'DIFFERENT',
                            value: 'moins_10_ans'
                          },
                          {
                            id: 'diagnostic_electrique_presence_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_electrique_vielle_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostics_techniques_diagnostiqueur_unique',
                            type: 'EQUALS',
                            value: 'non'
                          }
                        ],
                        [
                          {
                            id: 'construction',
                            type: 'DIFFERENT',
                            value: 'moins_10_ans'
                          },
                          {
                            id: 'diagnostic_electrique_presence_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_electrique_vielle_installation',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_TEMPLATES',
                            templates: [
                              'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                              'OPERATION__CDC__IMMOBILIER__VENTE',
                              'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                            ]
                          }
                        ]
                      ],
                      id: 'f9abfc16_64a2_4784_9b02_f0dbd3e2f508',
                      label: 'Informations sur le diagnostiqueur',
                      type: 'CATEGORY'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'DIFFERENT',
                        value: 'moins_10_ans'
                      },
                      {
                        id: 'diagnostic_electrique_presence_installation',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'diagnostic_electrique_vielle_installation',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: '88541d87_0b92_44b6_8452_96d1757eb8ae',
                  label: "Informations sur l'installation électrique",
                  type: 'CATEGORY'
                }
              ],
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'DIFFERENT',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'diagnostic_electrique_presence_installation',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: '7853c5ec_53f3_49a4_9e04_97fd8cbc9488',
              label: 'Installation électrique',
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'construction',
                type: 'DIFFERENT',
                value: 'moins_10_ans'
              }
            ]
          ],
          id: '0b26cdf1_6a7c_4445_ac5c_ca296daf2eda',
          label: 'Diagnostic Électrique',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_termites_commune',
              label: 'Commune concernée par les termites',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_termites_statut',
                  label: 'Diagnostic réalisé',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      id: 'diagnostic_termites_date',
                      label: 'Date du diagnostic',
                      type: 'DATE'
                    },
                    {
                      choices: [
                        {
                          label: "Absence de traces d'infestation de termite",
                          id: 'absence'
                        },
                        {
                          label: "Présence de traces d'infestation de termite",
                          id: 'presence'
                        }
                      ],
                      id: 'diagnostic_termites_resultat',
                      label: 'Résultat du diagnostic',
                      type: 'SELECT'
                    },
                    {
                      children: [
                        {
                          id: 'diagnostic_termites_diagnostiqueur_nom',
                          label: 'Nom du diagnostiqueur',
                          type: 'TEXT'
                        },
                        {
                          id: 'diagnostic_termites_diagnostiqueur_adresse',
                          label: 'Adresse du diagnostiqueur',
                          type: 'ADDRESS'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'diagnostic_termites_commune',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_termites_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostics_techniques_diagnostiqueur_unique',
                            type: 'EQUALS',
                            value: 'non'
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_termites_commune',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_termites_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_TEMPLATES',
                            templates: [
                              'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                              'OPERATION__CDC__IMMOBILIER__VENTE',
                              'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                            ]
                          }
                        ]
                      ],
                      id: 'bfd07a64_f4af_4e35_aff0_dd5adbbec423',
                      label: 'Informations sur le diagnostiqueur',
                      type: 'CATEGORY'
                    },
                    {
                      id: 'diagnostic_termites',
                      label: 'Diagnostic Termites',
                      type: 'UPLOAD'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'diagnostic_termites_commune',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'diagnostic_termites_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: '68344698_020a_4556_8027_79ea1c818e78',
                  label: 'Informations sur le diagnostic termites',
                  type: 'CATEGORY'
                }
              ],
              conditions: [
                [
                  {
                    id: 'diagnostic_termites_commune',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: '28bfa924_3604_462d_83cd_8d1ffc04a94b',
              label: 'Zone concernée par les termites',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_termites_informatif',
                  label: 'Contamination par les termites',
                  type: 'SELECT-BINARY'
                }
              ],
              conditions: [
                [
                  {
                    id: 'diagnostic_termites_commune',
                    type: 'EQUALS',
                    value: 'non'
                  }
                ]
              ],
              id: '6bdae132_155d_49a9_b930_06824d7b6034',
              label: 'Zone non concernée par les termites',
              type: 'CATEGORY'
            }
          ],
          id: 'e7dea85e_c96c_43b9_91d9_ad192427e4a5',
          label: 'Diagnostic Termites',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_merule_commune',
              label: 'Le bien est-il situé dans une commune concernée par la mérule ?',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_merule_statut',
                  label: 'Diagnostic réalisé',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      id: 'diagnostic_merule_date',
                      label: 'Date du diagnostic',
                      type: 'DATE'
                    },
                    {
                      choices: [
                        {
                          label: 'Absence de traces visibles de champignons lignivores',
                          id: 'absence'
                        },
                        {
                          label: 'Présence de traces visibles de champignons lignivores',
                          id: 'presence'
                        }
                      ],
                      id: 'diagnostic_merule_resultat',
                      label: 'Résultat du diagnostic',
                      type: 'SELECT'
                    },
                    {
                      children: [
                        {
                          id: 'diagnostic_merule_diagnostiqueur_nom',
                          label: 'Nom du diagnostiqueur',
                          type: 'TEXT'
                        },
                        {
                          id: 'diagnostic_merule_diagnostiqueur_adresse',
                          label: 'Adresse du diagnostiqueur',
                          type: 'ADDRESS'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'diagnostic_merule_commune',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_merule_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostics_techniques_diagnostiqueur_unique',
                            type: 'EQUALS',
                            value: 'non'
                          }
                        ],
                        [
                          {
                            id: 'diagnostic_merule_commune',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'diagnostic_merule_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            type: 'EQUALS_TEMPLATES',
                            templates: [
                              'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                              'OPERATION__CDC__IMMOBILIER__VENTE',
                              'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                            ]
                          }
                        ]
                      ],
                      id: 'a70b2dd6_9edf_4fb4_9137_8537f7dd41d9',
                      label: 'Informations sur le diagnostiqueur',
                      type: 'CATEGORY'
                    },
                    {
                      id: 'diagnostic_merule',
                      label: 'Diagnostic Mérule',
                      type: 'UPLOAD'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'diagnostic_merule_commune',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'diagnostic_merule_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'a4d521f7_ec10_43a2_a094_ec4cf049be1b',
                  label: 'Informations sur le diagnostic mérule',
                  type: 'CATEGORY'
                }
              ],
              conditions: [
                [
                  {
                    id: 'diagnostic_merule_commune',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'cf831870_0043_4d09_930d_3f284c4401e4',
              label: 'Zone concernée par la mérule',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_merule_informatif',
                  label: 'Contamination par la mérule',
                  type: 'SELECT-BINARY'
                }
              ],
              conditions: [
                [
                  {
                    id: 'diagnostic_merule_commune',
                    type: 'EQUALS',
                    value: 'non'
                  }
                ]
              ],
              id: 'a54b2d9d_a3d0_4503_aa42_0adf737bd851',
              label: 'Zone non concernée par la mérule',
              type: 'CATEGORY'
            }
          ],
          id: '744c9524_36ee_4f32_8ea3_f297ef453d76',
          label: 'Diagnostic Mérules',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              description: 'En cas de DPE Vierge la réponse doit être non',
              id: 'dpe_details_statut',
              label: 'Diagnostic réalisé',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'dpe_diagnostiqueur_date',
                  label: 'Date du diagnostic',
                  type: 'DATE'
                },
                {
                  description:
                    "Indiquer d'abord la lettre puis éventuellement le montant de la consommation. Exemple : E - 355",
                  id: 'dpe_consommation_energetique',
                  label: 'DPE : Performance / consommation énergétique',
                  type: 'TEXT'
                },
                {
                  id: 'dpe_emission_gaz',
                  label: 'DPE : Emission de gaz à effet de serre',
                  type: 'TEXT'
                },
                {
                  id: 'dpe_depense_annuelle',
                  label: "Montant des dépenses annuelles d'énergie",
                  type: 'TEXT'
                },
                {
                  id: 'dpe_depense_annuelle_annee',
                  label: "Année(s) de référence pour les dépenses d'énergie",
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'dpe_audit_realise',
                  label: 'Audit énergétique réalisé',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'dpe_audit_date',
                  label: "Date de l'audit énergétique",
                  type: 'DATE'
                },
                {
                  id: 'dpe_audit_diagnostiqueur',
                  label: "Nom du diagnostiqueur ayant réalisé l'audit",
                  type: 'TEXT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'dpe_audit_realise',
                        value: 'oui',
                        type: 'EQUALS'
                      },
                      {
                        id: 'dpe_details_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'dpe_audit_document',
                  label: 'Audit énergétique',
                  type: 'UPLOAD'
                },
                {
                  id: 'dpe',
                  label: 'Diagnostic de performance énergétique',
                  type: 'UPLOAD'
                },
                {
                  children: [
                    {
                      id: 'dpe_diagnostiqueur_nom',
                      label: 'Nom du diagnostiqueur',
                      type: 'TEXT'
                    },
                    {
                      id: 'dpe_diagnostiqueur_adresse',
                      label: 'Adresse du diagnostiqueur',
                      type: 'ADDRESS'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'diagnostics_techniques_diagnostiqueur_unique',
                        type: 'EQUALS',
                        value: 'non'
                      },
                      {
                        id: 'dpe_details_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'dpe_details_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CDC__IMMOBILIER__PROGRAMME',
                          'OPERATION__CDC__IMMOBILIER__VENTE',
                          'OPERATION__MA_GESTION_LOCATIVE__LOCATION'
                        ]
                      }
                    ]
                  ],
                  id: '0af500f1_feb5_40c4_9dc7_6091288da6f5',
                  label: 'Informations sur le diagnostiqueur',
                  type: 'CATEGORY'
                }
              ],
              conditions: [
                [
                  {
                    id: 'dpe_details_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: '8f0fc60e_9c20_4947_9f3c_dbc70d53485e',
              label: 'Informations sur le diagnostic de performance énergétique',
              type: 'CATEGORY'
            }
          ],
          id: '1b84cd9b_ff4a_469d_a07b_80787af603b1',
          label: 'Diagnostic de performance Énergétique',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              label: 'Au moyen d’une fosse septique',
              id: 'fosse'
            },
            {
              label: 'Par un raccordement au « tout à l’égout »',
              id: 'egout'
            },
            {
              label: 'Le bien est dépourvu de tout raccordement',
              id: 'assainissement_sans'
            }
          ],
          id: 'assainissement_statut',
          label: "Assainissement - Type d'assainissement",
          type: 'SELECT'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'assainissement_egout_statut',
              label: 'Assainissement - Contrôle réalisé',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'assainissement_egout_statut',
                    value: 'non',
                    type: 'EQUALS'
                  },
                  {
                    id: 'assainissement_statut',
                    type: 'EQUALS',
                    value: 'egout'
                  }
                ]
              ],
              id: 'assainissement_egout_a_faire',
              label: 'Assainissement - Contrôle du raccordement réalisé avant la vente',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'assainissement_egout_controle_date',
                  label: 'Assainissement - Date du contrôle',
                  type: 'DATE'
                },
                {
                  id: 'assainissement_egout_controle_nom',
                  label: 'Assainissement - Organisme ayant réalisé le contrôle',
                  type: 'TEXT'
                },
                {
                  id: 'assainissement_egout_controle_adresse',
                  label: "Assainissement - Adresse de l'organisme",
                  type: 'ADDRESS'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'assainissement_egout_controle_resultat',
                  label: 'Assainissement - Raccordement au réseau collectif conforme',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'controle_assainissement_collectif',
                  label: "Contrôle du raccordement de l'assainissement collectif",
                  type: 'UPLOAD'
                }
              ],
              conditions: [
                [
                  {
                    id: 'assainissement_egout_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'assainissement_statut',
                    type: 'EQUALS',
                    value: 'egout'
                  }
                ]
              ],
              id: '193f3198_e7aa_46a3_8dd7_6b17d0433291',
              label: "Information sur le contrôle de l'assainissement",
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'assainissement_statut',
                type: 'EQUALS',
                value: 'egout'
              }
            ]
          ],
          id: 'ff21b973_b161_48fd_8e38_7b77ff176217',
          label: 'Assainissement collectif',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'assainissement_fosse_statut',
              label: "L'assainissement a t il fait l’objet d’un contrôle par le SPANC ?",
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'assainissement_fosse_controle_date',
                  label: 'Assainissement - Date du contrôle',
                  type: 'DATE'
                },
                {
                  id: 'assainissement_fosse_controle_nom',
                  label: 'Assainissement - Organisme ayant réalisé le contrôle',
                  type: 'TEXT'
                },
                {
                  id: 'assainissement_fosse_controle_adresse',
                  label: "Assainissement - Adresse de l'organisme",
                  type: 'ADDRESS'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'assainissement_fosse_controle_resultat',
                  label: "Assainissement - Présence d'anomalies",
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'assainissement_fosse_controle_frais_vendeur',
                  label: 'Vendeur prend en charge les frais de mise en conformité',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'assainissement_fosse_controle_frais_devis',
                  label: 'Vendeur a fourni un devis de travaux de mise en conformité',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'controle_assainissement_fosse',
                  label: "Contrôle du raccordement de l'assainissement non collectif",
                  type: 'UPLOAD'
                }
              ],
              conditions: [
                [
                  {
                    id: 'assainissement_fosse_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'assainissement_statut',
                    type: 'EQUALS',
                    value: 'fosse'
                  }
                ]
              ],
              id: 'ec54333f_92b1_4960_969d_a760672ca168',
              label: "Information sur le contrôle de l'assainissement",
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'assainissement_statut',
                type: 'EQUALS',
                value: 'fosse'
              }
            ]
          ],
          id: 'f503aaa8_22cb_4696_b4e2_98cbf787cb5e',
          label: 'Assainissement non collectif',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'assainissement_precisions',
          label: "Ajouter une précision sur l'assainissement",
          type: 'SELECT-BINARY'
        },
        {
          id: 'assainissement_precisions_texte',
          label: "Précisions sur l'assainissement",
          type: 'TEXTAREA'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'salubrite_arretes_statut',
              label: "Arrêtés pris sur la salubrité / sécurité de l'immeuble",
              type: 'SELECT-BINARY'
            },
            {
              id: 'salubrite_arretes_contenu',
              label: 'Contenu des arrêtés',
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'salubrite_arretes_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'salubrite_arretes_document',
              label: "Arrêtes Salubrité / Sécurité de l'immeuble",
              type: 'UPLOAD'
            }
          ],
          id: 'cd79e9f9_0a23_4059_8291_dccae66a9af7',
          label: 'Salubrité et sécurité des immeubles',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'obligation_debroussaillement_statut',
              label: 'Bien concerné par une obligation de débroussaillement',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'obligation_debroussaillement_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'obligation_debroussaillement_doc',
              label: 'Attestation de débroussaillement',
              type: 'UPLOAD'
            }
          ],
          id: 'bd678545_12f6_4027_a47d_e47799618616',
          label: 'Obligation de débroussaillement',
          type: 'CATEGORY'
        }
      ],
      id: 'fdc7f852_8c68_428a_811d_4a99d5fa60d3',
      label: 'Diagnostics',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'piscine_statut',
          label: "Présence d'une piscine",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'piscine',
          label: 'Piscine enterrée (et non hors-sol)',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'piscine_entretien',
              label: "Présence d'un contrat d'entretien",
              type: 'SELECT-BINARY'
            },
            {
              id: 'piscine_entretien_nom',
              label: "Nom de l'entreprise du contrat d'entretien",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'avant_2004',
                  label: 'Avant 2004'
                },
                {
                  id: 'moins_10_ans',
                  label: 'Depuis - de 10 ans'
                },
                {
                  id: 'plus_10_ans',
                  label: 'Après 2004 et + de 10 ans '
                }
              ],
              id: 'piscine_installation_date',
              label: "Date d'installation de la piscine",
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'dp',
                  label: 'Déclaration préalable (piscine entre 10 m2 et 100 m2)'
                },
                {
                  id: 'permis',
                  label: 'Permis de construire (piscine > 100 m2 ou secteur sauvegardé)'
                }
              ],
              conditions: [
                [
                  {
                    id: 'piscine_installation_date',
                    value: 'moins_10_ans',
                    type: 'DIFFERENT'
                  }
                ]
              ],
              id: 'piscine_installation_autorisation',
              label: "Autorisation d'urbanisme obtenue pour la piscine",
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'piscine_installation_date',
                    value: 'moins_10_ans',
                    type: 'DIFFERENT'
                  }
                ]
              ],
              id: 'piscine_installation_autorisation_document',
              label: 'Autorisation urbanisme - Piscine',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'piscine_installation_date',
                    value: 'moins_10_ans',
                    type: 'DIFFERENT'
                  }
                ]
              ],
              id: 'piscine_installation_achevement_document',
              label: 'Achèvement des travaux - Piscine',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'piscine_installation_date',
                    value: 'moins_10_ans',
                    type: 'DIFFERENT'
                  }
                ]
              ],
              id: 'piscine_installation_travaux',
              label: 'Conformité des travaux obtenue',
              type: 'SELECT-BINARY'
            }
          ],
          id: '35fb0632_c4e3_4721_850b_50e91ff3b3fa',
          label: 'CONDITION_BLOCK_Condition_piscine',
          type: 'CONDITION_BLOCK'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Une alarme',
                  id: 'alarme'
                },
                {
                  label: 'Rideaux / Couvertures / Bâche à barres',
                  id: 'rideau'
                },
                {
                  label: 'Barrières',
                  id: 'barriere'
                },
                {
                  label: 'Un abri',
                  id: 'abri'
                },
                {
                  label: 'Aucun dispositif',
                  id: 'aucun'
                }
              ],
              id: 'piscine_securite',
              label: 'Dispositif de sécurité',
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'piscine_installation_date',
                    value: 'avant_2004',
                    type: 'EQUALS'
                  },
                  {
                    id: 'piscine_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'piscine_securite_attestation',
              label: 'Organisme attestestant du dispositif de sécurité',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'piscine_securite_justificatif',
              label: "Présence d'un justificatif",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'piscine_installation_date',
                    value: 'avant_2004',
                    type: 'EQUALS'
                  },
                  {
                    id: 'piscine_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'piscine_securite_attestation_document',
              label: 'Attestation de normes du dispositif de sécurité piscine',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'piscine_installation_date',
                    value: 'avant_2004',
                    type: 'DIFFERENT'
                  },
                  {
                    id: 'piscine_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'piscine_note_securite_document',
              label: 'Note technique - dispositif de sécurité piscine',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'piscine_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: '2977f63e_b4e2_4672_9638_8658390bece9',
          label: 'CONDITION_BLOCK_Sécurité piscine',
          type: 'CONDITION_BLOCK'
        }
      ],
      id: '147b34b6_3769_41bc_a68f_160298e9e625',
      label: 'Piscine',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            "Un volume est une division supplémentaire d'un grand ensemble immobilier. Cette information est indiquée dans le titre de propriété qui mentionne très clairement une division en volume au niveau de la description du bien.",
          id: 'presence_volume',
          label: "L'ensemble immobilier est-il compris dans une structure divisée en volume ?",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'numero_lot_volume',
              label: 'Numéro du lot volume',
              type: 'TEXT'
            },
            {
              id: 'denomination',
              label: "Dénomination de l'ensemble immobilier",
              type: 'TEXT'
            },
            {
              id: 'nombre_lots',
              label: 'Nombre de lots volume',
              type: 'NUMBER'
            },
            {
              id: 'plans_lots_volume',
              label: 'Plans des lots volume',
              type: 'UPLOAD'
            },
            {
              children: [
                {
                  id: 'eddv_list_date',
                  label: "Date de l'état descriptif ou modificatif",
                  type: 'DATE'
                },
                {
                  id: 'eddv_list_notaire_nom',
                  label: "Nom du notaire ayant rédigé l'acte",
                  type: 'TEXT'
                },
                {
                  id: 'eddv_list_notaire_ville',
                  label: "Ville du notaire ayant rédigé l'acte",
                  type: 'TEXT'
                }
              ],
              id: 'eddv_list',
              label: 'Etat Descriptif de Division en volume (EDDV) et modificatifs',
              repetition: {
                label: [
                  {
                    type: 'VARIABLE',
                    value: 'eddv_list_notaire_nom'
                  }
                ],
                max: 1000,
                min: 1
              },
              type: 'REPEAT'
            },
            {
              id: 'eddv',
              label: 'Etat descriptif de division en volume et modificatifs',
              type: 'UPLOAD'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'asl',
                      label: 'Une association syndicale libre'
                    },
                    {
                      id: 'aful',
                      label: 'Une association foncière urbaine libre'
                    },
                    {
                      id: 'aucun',
                      label: 'Aucun organe'
                    },
                    {
                      id: 'autre',
                      label: 'Un autre organe de gestion'
                    }
                  ],
                  id: 'gestion_volume_gestion_volume_type',
                  label: 'Le volume est géré par',
                  type: 'SELECT'
                },
                {
                  children: [
                    {
                      id: 'gestion_volume_gestion_volume_autre_type',
                      label: "Précisez le type de l'organe de gestion",
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_autre_nom',
                      label: 'Nom de l’organe de gestion',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_autre_adresse',
                      label: 'Adresse de l’organe de gestion',
                      type: 'ADDRESS'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_autre_president_nom',
                      label: 'Nom du gérant',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_autre_president_email',
                      label: 'Adresse email du gérant',
                      type: 'EMAIL'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_autre_president_telephone',
                      label: 'Téléphone du gérant',
                      type: 'PHONE'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_autre_statuts',
                      label: "Statuts de l'organe de gestion",
                      type: 'UPLOAD'
                    }
                  ],
                  id: 'gestion_volume_f6f5a1a5_996a_4930_a67e_e0a201ca6030',
                  label: 'Autre organe',
                  type: 'CATEGORY'
                },
                {
                  children: [
                    {
                      id: 'gestion_volume_gestion_volume_asl_nom',
                      label: 'Nom de l’association syndicale',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_asl_adresse',
                      label: 'Adresse de l’association syndicale',
                      type: 'ADDRESS'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_asl_president_nom',
                      label: 'Nom du président',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_asl_president_email',
                      label: 'Adresse email du président',
                      type: 'EMAIL'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_asl_president_telephone',
                      label: 'Téléphone du président',
                      type: 'PHONE'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_statuts_asl',
                      label: "Statuts de l'Association Syndicale Libre",
                      type: 'UPLOAD'
                    }
                  ],
                  id: 'gestion_volume_0edeac6a_a426_4794_8b36_85360292cebf',
                  label: 'Association syndicale libre',
                  type: 'CATEGORY'
                },
                {
                  children: [
                    {
                      id: 'gestion_volume_gestion_volume_aful_nom',
                      label: 'Nom de l’association foncière',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_aful_adresse',
                      label: 'Adresse de l’association foncière',
                      type: 'ADDRESS'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_aful_president_nom',
                      label: 'Nom du président',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_aful_president_email',
                      label: 'Adresse email du président',
                      type: 'EMAIL'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_aful_president_telephone',
                      label: 'Téléphone du président',
                      type: 'PHONE'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_statuts_aful',
                      label: "Statuts de l'Association Foncière Urbaine Libre",
                      type: 'UPLOAD'
                    }
                  ],
                  id: 'gestion_volume_6053428d_6a0d_4f53_8d6d_d0c8bdc8cd0d',
                  label: 'Association foncière urbaine libre',
                  type: 'CATEGORY'
                }
              ],
              id: 'gestion_volume',
              label: 'Organes de gestion',
              repetition: {
                label: [
                  {
                    type: 'VARIABLE',
                    value: 'gestion_volume_gestion_volume_autre_nom'
                  },
                  {
                    type: 'VARIABLE',
                    value: 'gestion_volume_gestion_volume_asl_nom'
                  },
                  {
                    type: 'VARIABLE',
                    value: 'gestion_volume_gestion_volume_aful_nom'
                  }
                ],
                max: 1000,
                min: 0
              },
              type: 'REPEAT'
            }
          ],
          conditions: [
            [
              {
                id: 'presence_volume',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'b070627e_70ae_4b59_87d0_562c69de7101',
          label: 'Informations volume',
          type: 'CATEGORY'
        }
      ],
      id: '58dbefc0_0bc3_4324_ab07_23738db693e5',
      label: 'Volume',
      type: 'CATEGORY'
    },
    {
      choices: [
        {
          id: 'non',
          label: 'NON'
        },
        {
          id: 'oui',
          label: 'OUI'
        }
      ],
      id: 'presence_lotissement',
      label: "L'ensemble immobilier est-il compris dans un lotissement ?",
      type: 'SELECT-BINARY'
    },
    {
      children: [
        {
          id: 'denomination_lotissement',
          label: 'Dénomination du lotissement',
          type: 'TEXT'
        },
        {
          id: 'numero_lotissement',
          label: 'Numéro de lot du lotissement',
          type: 'NUMBER'
        },
        {
          id: 'date_lotissement',
          label: 'Date de création du lotissement',
          type: 'DATE'
        },
        {
          id: 'plan_lotissement',
          label: 'Plan du lotissement',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'reglement_du_lotissement',
          label: 'Existe-il un règlement de lotissement?',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'reglement_date',
              label: 'Date du règlement de lotissement',
              type: 'DATE'
            },
            {
              id: 'reglement_lotissement',
              label: 'Reglement de lotissement',
              type: 'UPLOAD'
            }
          ],
          id: 'b5ad45d3_a92d_45d4_9f8b_4e2bd2d09670',
          label: 'Règlement de lotissement',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'presence_cahier_charges',
          label: 'Existe-t-il un cahier des charges du lotissement?',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'cahier_charges_date',
              label: 'Date du cahier des charges',
              type: 'DATE'
            },
            {
              id: 'cahier_charges',
              label: 'Cahier des charges',
              type: 'UPLOAD'
            }
          ],
          id: '1c26fb30_ddba_42f2_be0e_7b8bf776f1fb',
          label: 'Cahier des charges du lotissement',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'pieces_notaire',
          label: "Les pièces du lotissement ont elles fait l'objet d'un dépôt au rang des minutes d'un notaire ?",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'pieces_notaire_date',
              label: "Date de l'acte de dépôt",
              type: 'DATE'
            },
            {
              id: 'pieces_notaire_nom',
              label: 'Nom du notaire',
              type: 'TEXT'
            },
            {
              id: 'pieces_notaire_ville',
              label: 'Ville du notaire',
              type: 'TEXT'
            },
            {
              id: 'acte_depot',
              label: 'Acte de dépôt',
              type: 'UPLOAD'
            }
          ],
          id: '28bb666b_31f9_4969_90fd_6ad84ca1f48c',
          label: 'Acte de dépôt',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'urbanisme_descriptif_statut',
              label: "Les autorisations d'urbanisme doivent-elles être mentionnées dans la vente ?",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'lotissement_delclaration_prealable',
                  label: "D'une déclaration préalable"
                },
                {
                  id: 'lotissement_permis_amenager',
                  label: "D'un permis d'aménager"
                }
              ],
              id: 'urbanisme_statut',
              label: 'Le lotissement est issu',
              type: 'SELECT'
            },
            {
              children: [
                {
                  id: 'urbanisme_statut_date_depot',
                  label: "Date de dépôt l'autorisation d'urbanisme",
                  type: 'DATE'
                },
                {
                  id: 'urbanisme_statut_date_delivrance',
                  label: "Date de délivrance de l'autorisation d'urbanisme (expresse ou tacite)",
                  type: 'DATE'
                },
                {
                  id: 'autorisation_urbanisme',
                  label: "Recepisse de dépôt de l'autorisation d'urbanisme",
                  type: 'UPLOAD'
                },
                {
                  id: 'autorisation_urbanisme_delivrance',
                  label: "Délivrance de l'autorisation d'urbanisme",
                  type: 'UPLOAD'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'urbanisme_statut_permis_autorisation_vente',
                      label:
                        "L'administration a-t-elle autorisé la vente des lots avant l’exécution des travaux prescrits dans le permis ?",
                      type: 'SELECT-BINARY'
                    },
                    {
                      children: [
                        {
                          id: 'autorisation_urbanisme_vente_anticipee_date',
                          label: "Date de l'autorisation",
                          type: 'DATE'
                        },
                        {
                          id: 'autorisation_vente_anticipee',
                          label: 'Autorisation administrative de vente anticipée des lots',
                          type: 'UPLOAD'
                        }
                      ],
                      id: '3ac104f1_5e1e_4b33_80cf_5b625b771407',
                      label: 'Autorisation de vente anticipée',
                      type: 'CATEGORY'
                    }
                  ],
                  id: '28c756c5_80d6_4ea8_bb74_0712599ead25',
                  label: "Permis d'aménager",
                  type: 'CATEGORY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'urbanisme_statut_certificat_non_opposition',
                  label: 'Un certificat de non opposition a-t-il été délivré?',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      id: 'urbanisme_statut_certificat_non_opposition_date',
                      label: 'Date de délivrance du certificat',
                      type: 'DATE'
                    },
                    {
                      id: 'lotissement_certificat_non_opposition',
                      label: 'Certificat de non opposition',
                      type: 'UPLOAD'
                    }
                  ],
                  id: '13bddb45_ccb4_428e_a134_ab9938672422',
                  label: 'Certificat de non opposition',
                  type: 'CATEGORY'
                }
              ],
              id: '000274a8_0724_4262_8e63_8ab0e706549b',
              label: "Autorisation d'urbanisme",
              type: 'CATEGORY'
            }
          ],
          id: '1e8beda0_cb38_4406_844e_07d9544cee40',
          label: "Autorisations d'urbanisme",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [
                    {
                      id: 'asl',
                      label: 'Une association syndicale libre'
                    },
                    {
                      id: 'aful',
                      label: 'Une association foncière urbaine libre'
                    },
                    {
                      id: 'aucun',
                      label: 'Aucun organe'
                    },
                    {
                      id: 'autre',
                      label: 'Un autre organe de gestion'
                    }
                  ],
                  id: 'gestion_lotissement_gestion_lotissement_type',
                  label: 'Le lotissement est géré par',
                  type: 'SELECT'
                },
                {
                  children: [
                    {
                      id: 'gestion_lotissement_gestion_lotissement_autre_type',
                      label: "Précisez le type de l'organe de gestion",
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_autre_nom',
                      label: 'Nom de l’organe de gestion',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_autre_adresse',
                      label: 'Adresse de l’organe de gestion',
                      type: 'ADDRESS'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_autre_president_nom',
                      label: 'Nom du gérant',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_autre_president_email',
                      label: 'Adresse email du gérant',
                      type: 'EMAIL'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_autre_president_telephone',
                      label: 'Téléphone du gérant',
                      type: 'PHONE'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_autre_statuts',
                      label: "Statuts de l'organe de gestion",
                      type: 'UPLOAD'
                    }
                  ],
                  id: 'gestion_lotissement_b2814151_aad8_4c3e_9aa3_6383102c0059',
                  label: 'Autre organe',
                  type: 'CATEGORY'
                },
                {
                  children: [
                    {
                      id: 'gestion_lotissement_gestion_lotissement_asl_nom',
                      label: 'Nom de l’association syndicale',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_asl_adresse',
                      label: 'Adresse de l’association syndicale',
                      type: 'ADDRESS'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_asl_president_nom',
                      label: 'Nom du président',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_asl_president_email',
                      label: 'Adresse email du président',
                      type: 'EMAIL'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_asl_president_telephone',
                      label: 'Téléphone du président',
                      type: 'PHONE'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_statuts_asl',
                      label: "Statuts de l'Association Syndicale Libre",
                      type: 'UPLOAD'
                    }
                  ],
                  id: 'gestion_lotissement_9ead91fc_be84_4fbe_a4e5_d918cff213dc',
                  label: 'Association syndicale libre',
                  type: 'CATEGORY'
                },
                {
                  children: [
                    {
                      id: 'gestion_lotissement_gestion_lotissement_aful_nom',
                      label: 'Nom de l’association foncière',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_aful_adresse',
                      label: 'Adresse de l’association foncière',
                      type: 'ADDRESS'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_aful_president_nom',
                      label: 'Nom du président',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_aful_president_email',
                      label: 'Adresse email du président',
                      type: 'EMAIL'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_aful_president_telephone',
                      label: 'Téléphone du président',
                      type: 'PHONE'
                    },
                    {
                      id: 'gestion_lotissement_gestion_lotissement_statuts_aful',
                      label: "Statuts de l'Association Foncière Urbaine Libre",
                      type: 'UPLOAD'
                    }
                  ],
                  id: 'gestion_lotissement_e6c28f30_9ab1_493f_9893_f59103e5017a',
                  label: 'Association foncière urbaine libre',
                  type: 'CATEGORY'
                }
              ],
              id: 'gestion_lotissement',
              label: 'Organes de gestion',
              repetition: {
                label: [
                  {
                    type: 'VARIABLE',
                    value: 'gestion_lotissement_gestion_lotissement_autre_nom'
                  },
                  {
                    type: 'VARIABLE',
                    value: 'gestion_lotissement_gestion_lotissement_asl_nom'
                  },
                  {
                    type: 'VARIABLE',
                    value: 'gestion_lotissement_gestion_lotissement_aful_nom'
                  }
                ],
                max: 1000,
                min: 0
              },
              type: 'REPEAT'
            }
          ],
          id: 'aa398244_af19_450d_a452_ad378f8009bd',
          label: 'Gestion du lotissement',
          type: 'CATEGORY'
        }
      ],
      conditions: [
        [
          {
            id: 'presence_lotissement',
            value: 'oui',
            type: 'EQUALS'
          }
        ]
      ],
      id: 'a3d6ef1a_65f7_49a9_9100_8934a4862091',
      label: 'Information sur le lotissement',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  label: 'Zone non concernée par un plan de prévention des risques naturels',
                  id: 'aucun_plan'
                },
                {
                  label: "Dans le périmètre d'un plan de prévention des risques naturels prescrits",
                  id: 'plan_prescrit'
                },
                {
                  label: "Dans le périmètre d'un plan de prévention des risques naturels anticipés",
                  id: 'plan_anticipe'
                },
                {
                  label: "Dans le périmètre d'un plan de prévention des risques naturels approuvés",
                  id: 'plan_approuve'
                }
              ],
              id: 'ppr_naturels_liste',
              label: "L'immeuble est situé",
              multiple: true,
              type: 'SELECT'
            }
          ],
          id: '35c89420_7979_4135_8bd3_44eb08c79c07',
          label: 'Plan de prévention des risques naturels',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Zone non concernée par un plan de prévention des risques miniers',
                  id: 'aucun_plan'
                },
                {
                  label: "Dans le périmètre d'un plan de prévention des risques miniers prescrits",
                  id: 'plan_prescrit'
                },
                {
                  label: "Dans le périmètre d'un plan de prévention des risques miniers anticipés",
                  id: 'plan_anticipe'
                },
                {
                  label: "Dans le périmètre d'un plan de prévention des risques miniers approuvés",
                  id: 'plan_approuve'
                }
              ],
              id: 'ppr_miniers_liste',
              label: "L'immeuble est situé",
              multiple: true,
              type: 'SELECT'
            }
          ],
          id: '95a73ba7_45fa_4c6d_99a4_2b70d89ec951',
          label: 'Plan de prévention des risques miniers',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Zone non concernée par un plan de prévention des risques technologiques',
                  id: 'aucun_plan'
                },
                {
                  label: "Dans le périmètre d'un plan de prévention des risques technologiques prescrits",
                  id: 'plan_prescrit'
                },
                {
                  label: "Dans le périmètre d'un plan de prévention des risques technologiques anticipés",
                  id: 'plan_anticipe'
                },
                {
                  label: "Dans le périmètre d'un plan de prévention des risques technologiques approuvés",
                  id: 'plan_approuve'
                }
              ],
              id: 'ppr_technologiques_liste',
              label: "L'immeuble est situé",
              multiple: true,
              type: 'SELECT'
            }
          ],
          id: '5b870bee_c6b3_4551_b0ec_eafe216aa86c',
          label: 'Plan de prévention des risques technologiques',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'zone_sismicite_classe',
              label: "L'immeuble est situé dans une commune classée en zone de sismicité",
              type: 'NUMBER'
            }
          ],
          id: 'e9dd4db2_e49c_48a1_a936_60c54069b05a',
          label: 'Zone de sismicité',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'indemnite_catastrophe',
              label:
                "L'immeuble a-t-il subit un sinistre d'origine catastrophe naturelle, donnant lieu à versement d'une indemnité d'assurance ?",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'potentiel_radon_classe',
              label: "L'immeuble est-il situé dans une commune à potentiel radon classée niveau 3 ?",
              type: 'SELECT-BINARY'
            }
          ],
          id: '8d554fdb_be44_4ae3_9f40_97375fc944d7',
          label: 'Potentiel Radon',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'erp_retrait_cote_commune',
              label: 'Commune exposée au retrait des côtes',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'erp_retrait_cote_bien',
              label: 'Bien exposé au retrait des côtes',
              type: 'SELECT-BINARY'
            }
          ],
          id: '30e42c27_69d8_424e_ba19_4e5288151903',
          label: 'CONDITION_BLOCK_Retrait de côte',
          type: 'CONDITION_BLOCK'
        },
        {
          id: 'external_georisque',
          label: 'Georisques',
          type: 'UPLOAD'
        },
        {
          id: 'erp',
          label: 'Etat des risques',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'zone_bruit',
          label: "Zone soumise à un plan d'exposition au bruit des aérodromes",
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'zone_bruit',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'plan_exposition_bruit',
          label: "Plan d'exposition au bruit des aérodromes",
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostic_geotechnique',
          label: 'Diagnostic géotechnique réalisé',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'geotechnique_zone_moyen',
                  label: 'Zone à risques moyens'
                },
                {
                  id: 'geotechnique_zone_fort',
                  label: 'Zone à risques forts'
                }
              ],
              id: 'diagnostic_geotechnique_zone_type',
              label: 'Type de zone',
              type: 'SELECT'
            },
            {
              id: 'diagnostic_geotechnique_date',
              label: "Date de réalisation de l'étude géotechnique",
              type: 'DATE'
            },
            {
              id: 'diagnostic_geotechnique_diagnostiqueur_nom',
              label: 'Nom du diagnostiqueur',
              type: 'TEXT'
            },
            {
              id: 'diagnostic_geotechnique_diagnostiqueur_adresse',
              label: 'Adresse du diagnostiqueur',
              type: 'ADDRESS'
            },
            {
              id: 'etude_geotechnique',
              label: 'Etude géotechnique',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'diagnostic_geotechnique',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: '3ea15ce6_99f9_4b65_99a3_3d8151e2de9e',
          label: 'Diagnostic géotechnique',
          type: 'CATEGORY'
        }
      ],
      id: 'e079b78c_eba5_453a_98e3_d44af150b99a',
      label: 'Diagnostics environnementaux',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'construction_date_achevement',
          label: 'Date d’achèvement de la construction',
          type: 'DATE'
        },
        {
          choices: [
            {
              label: 'Un permis de construire',
              id: 'permis'
            },
            {
              label: 'Une déclaration préalable',
              id: 'declaration'
            },
            {
              label: 'Aucune autorisation obtenue',
              id: 'aucune'
            }
          ],
          id: 'construction_autorisation_administration',
          label: "Autorisation d'urbanisme",
          type: 'SELECT'
        },
        {
          children: [
            {
              id: 'construction_autorisation_date',
              label: "Date de l'autorisation",
              type: 'DATE'
            },
            {
              id: 'construction_autorisation_numero',
              label: "Numéro de l'autorisation",
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  }
                ]
              ],
              id: 'permis_construire_construction',
              label: 'Permis de construire - Ensemble Immobilier',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  }
                ]
              ],
              id: 'declaration_prealable_construction',
              label: 'Déclaration préalable - Ensemble Immobilier',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'construction_declaration_achevement_statut',
              label: "Déclaration d'achèvement déposée en mairie",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  },
                  {
                    id: 'construction_autorisation_date',
                    type: 'DATE_NOT_OLDER_THAN',
                    value: '2022-12-31'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    id: 'construction_autorisation_date',
                    type: 'DATE_NOT_OLDER_THAN',
                    value: '2022-12-31'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  },
                  {
                    id: 'construction_autorisation_date',
                    type: 'DATE_NOT_OLDER_THAN',
                    value: '2022-12-31'
                  }
                ]
              ],
              id: 'carnet_information_construction_statut',
              label: "Carnet d'information du logement effectué",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'carnet_information_construction_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  },
                  {
                    id: 'construction_autorisation_date',
                    type: 'DATE_NOT_OLDER_THAN',
                    value: '2022-12-31'
                  }
                ],
                [
                  {
                    id: 'carnet_information_construction_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    id: 'construction_autorisation_date',
                    type: 'DATE_NOT_OLDER_THAN',
                    value: '2022-12-31'
                  }
                ],
                [
                  {
                    id: 'carnet_information_construction_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  },
                  {
                    id: 'construction_autorisation_date',
                    type: 'DATE_NOT_OLDER_THAN',
                    value: '2022-12-31'
                  }
                ]
              ],
              id: 'carnet_information_construction_document',
              label: "Carnet d'information du logement",
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'construction_rga_zone_concernee',
              label: 'Bien situé dans une zone à risque Argile "Moyen" ou "Fort"',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'construction_rga_attestation_etablie',
              label: 'Attestation "Retrait / Gonflement Argile" établie',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  },
                  {
                    id: 'construction_rga_attestation_etablie',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    id: 'construction_rga_attestation_etablie',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  },
                  {
                    id: 'construction_rga_attestation_etablie',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'construction_rga_document',
              label: 'Attestation "Retrait / Gonflement Argile"',
              type: 'UPLOAD'
            },
            {
              children: [
                {
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'moins_10_ans'
                      },
                      {
                        id: 'construction_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'construction_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'construction_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'moins_10_ans'
                      },
                      {
                        id: 'construction_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'construction_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'moins_10_ans'
                      },
                      {
                        id: 'construction_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'construction_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'construction_declaration_achevement_date',
                  label: "Date de la déclaration d'achèvement",
                  type: 'DATE'
                },
                {
                  id: 'declaration_achevement_construction',
                  label: "Déclaration d'achèvement - Ensemble Immobilier",
                  type: 'UPLOAD'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'construction_certificat_conformite_statut',
                  label: 'Certificat de conformité ou de non contestation obtenu',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      id: 'construction_certificat_conformite_date',
                      label: 'Date de délivrance du certificat',
                      type: 'DATE'
                    },
                    {
                      id: 'certificat_conformite_construction',
                      label: 'Certificat de conformité - Ensemble Immobilier',
                      type: 'UPLOAD'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'moins_10_ans'
                      },
                      {
                        id: 'construction_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'construction_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'construction_certificat_conformite_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'construction_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'moins_10_ans'
                      },
                      {
                        id: 'construction_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'construction_certificat_conformite_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'construction_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'moins_10_ans'
                      },
                      {
                        id: 'construction_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'construction_certificat_conformite_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'construction_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: '651dfcf3_3835_48f3_8bdc_92de8b564c75',
                  label: 'Certificat de conformité',
                  type: 'CATEGORY'
                }
              ],
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  },
                  {
                    id: 'construction_declaration_achevement_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    id: 'construction_declaration_achevement_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  },
                  {
                    id: 'construction_declaration_achevement_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: '952524f4_46e0_49ff_99b1_b1ac532cfbcd',
              label: "Déclaration d'achèvement",
              type: 'CATEGORY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'construction_regularisation',
              label: 'Régularisation des travaux a posteriori par le Vendeur',
              type: 'SELECT-BINARY'
            }
          ],
          conditions: [
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'moins_10_ans'
              },
              {
                id: 'construction_autorisation_administration',
                type: 'EQUALS',
                value: 'declaration'
              }
            ],
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'moins_10_ans'
              },
              {
                id: 'construction_autorisation_administration',
                type: 'EQUALS',
                value: 'permis'
              }
            ]
          ],
          id: '7c582a5c_e387_4a50_a32d_bbc7168489f2',
          label: "Information sur l'autorisation",
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'construction_professionnel_statut',
          label: 'Travaux effectués par un professionnel',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              children: [
                {
                  id: 'construction_professionnel_list_construction_professionnel_nom',
                  label: 'Nom du professionnel',
                  type: 'TEXT'
                },
                {
                  id: 'construction_professionnel_list_construction_professionnel_adresse',
                  label: 'Adresse du professionnel',
                  type: 'ADDRESS'
                },
                {
                  id: 'construction_professionnel_list_construction_professionnel_travaux',
                  label: 'Travaux réalisés',
                  type: 'TEXT'
                }
              ],
              id: 'construction_professionnel_list',
              label: 'Professionnels',
              repetition: {
                label: [
                  {
                    type: 'VARIABLE',
                    value: 'construction_professionnel_list_construction_professionnel_nom'
                  }
                ],
                max: 1000,
                min: 1
              },
              type: 'REPEAT'
            },
            {
              id: 'assurance_decennale_construction',
              label: 'Assurance Décennale - Ensemble Immobilier',
              type: 'UPLOAD'
            },
            {
              id: 'assurance_dommage_construction',
              label: 'Assurance Dommage-Ouvrage - Ensemble Immobilier',
              type: 'UPLOAD'
            },
            {
              id: 'factures_entreprises_construction',
              label: 'Factures entreprises - Ensemble Immobilier',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'moins_10_ans'
              },
              {
                id: 'construction_professionnel_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: '04c64f81_176a_44a5_8704_15132933b014',
          label: 'Informations sur les professionnels',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'construction_travaux_proprietaire',
          label: 'Un particulier a effectué lui-même certains travaux',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'construction_particulier_travaux',
              label: 'Travaux réalisés',
              type: 'TEXTAREA'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'construction_particulier_assurance_dommage_statut',
              label: "Souscription d'une assurance dommage ouvrage par le particulier",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_particulier_assurance_dommage_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'construction_travaux_proprietaire',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'construction_particulier_assurance_dommage',
              label: 'Assurance Dommage-Ouvrage Particulier non professionnel',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'moins_10_ans'
              },
              {
                id: 'construction_travaux_proprietaire',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'f78f2bc1_107f_4854_b218_8d538f2ee9f2',
          label: 'Informations sur les travaux',
          type: 'CATEGORY'
        }
      ],
      conditions: [
        [
          {
            id: 'construction',
            type: 'EQUALS',
            value: 'moins_10_ans'
          }
        ]
      ],
      id: '30bc94db_ba83_43e5_91a7_062a47be49d0',
      label: 'Construction initiale',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'travaux_statut',
          label: 'Présence de travaux',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'travaux_simple_dix_ans',
          label: 'Travaux de moins de 10 ans',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'exterieur',
              label: "L'aspect extérieur du bâti"
            },
            {
              id: 'structure',
              label: 'La structure du bâtiment'
            },
            {
              id: 'aucun',
              label: 'Aucun des deux'
            }
          ],
          id: 'travaux_simple_objet',
          label: 'Les travaux affectent',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'travaux_simple_urbanisme',
          label: "Travaux faisant l'objet d'une autorisation d'urbanisme",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'travaux_simple_pro',
          label: 'Travaux réalisés par un professionnel',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'travaux_list_travaux_date_achevement',
              label: 'Date d’achèvement des travaux',
              type: 'DATE'
            },
            {
              id: 'travaux_list_travaux_titre',
              label: 'Titre des travaux',
              type: 'TEXT'
            },
            {
              id: 'travaux_list_travaux_description',
              label: 'Descriptif des travaux',
              type: 'TEXTAREA'
            },
            {
              choices: [
                {
                  label: 'Un permis de construire',
                  id: 'permis'
                },
                {
                  label: 'Une déclaration préalable',
                  id: 'declaration'
                },
                {
                  label: 'Aucune autorisation obtenue',
                  id: 'aucune'
                },
                {
                  label: "Travaux sans autorisation spécifique / travaux à l'identique",
                  id: 'non_necessaire'
                }
              ],
              id: 'travaux_list_travaux_autorisation_administration',
              label: "Autorisations d'urbanisme",
              type: 'SELECT'
            },
            {
              children: [
                {
                  id: 'travaux_list_travaux_autorisation_date',
                  label: "Date de l'autorisation",
                  type: 'DATE'
                },
                {
                  id: 'travaux_list_travaux_autorisation_numero',
                  label: "Numéro de l'autorisation",
                  type: 'TEXT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'travaux_list_permis_construire',
                  label: 'Permis de construire',
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'travaux_list_declaration_prealable',
                  label: 'Déclaration préalable',
                  type: 'UPLOAD'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'travaux_list_travaux_declaration_achevement_statut',
                  label: "Déclaration d'achèvement déposée en mairie",
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      conditions: [
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'declaration'
                          },
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'permis'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'declaration'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'permis'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: 'travaux_list_travaux_declaration_achevement_date',
                      label: "Date de la déclaration d'achèvement",
                      type: 'DATE'
                    },
                    {
                      id: 'travaux_list_declaration_achevement',
                      label: "Déclaration d'achèvement",
                      type: 'UPLOAD'
                    },
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'travaux_list_travaux_certificat_conformite_statut',
                      label: 'Certificat de conformité ou de non contestation obtenu',
                      type: 'SELECT-BINARY'
                    },
                    {
                      children: [
                        {
                          id: 'travaux_list_travaux_certificat_conformite_date',
                          label: 'Date de délivrance du certificat',
                          type: 'DATE'
                        },
                        {
                          id: 'travaux_list_certificat_conformite',
                          label: 'Certificat de conformité',
                          type: 'UPLOAD'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'declaration'
                          },
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'permis'
                          },
                          {
                            id: 'travaux_list_travaux_certificat_conformite_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'declaration'
                          },
                          {
                            id: 'travaux_list_travaux_certificat_conformite_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'permis'
                          },
                          {
                            id: 'travaux_list_travaux_certificat_conformite_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: 'travaux_list_2c85e685_4de2_4b01_a136_52138b0e6e9f',
                      label: 'Certificat de conformité',
                      type: 'CATEGORY'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_list_travaux_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_list_travaux_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_list_travaux_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'travaux_list_f2ef621b_d880_43eb_947d_7575ef787055',
                  label: "Déclaration d'achèvement",
                  type: 'CATEGORY'
                }
              ],
              conditions: [
                [
                  {
                    id: 'travaux_list_travaux_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'travaux_list_travaux_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_0c3973c2_a449_498a_8c8e_53710f41431c',
              label: "Information sur l'autorisation",
              type: 'CATEGORY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_regularisation',
              label: 'Régularisation des travaux a posteriori par le Vendeur',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'travaux_list_travaux_date_achevement',
                    type: 'DATE_NOT_OLDER_THAN',
                    value: '2022-12-31'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_travaux_energetique',
              label: 'Les travaux ont-ils porté sur une rénovation énergétique?',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'travaux_list_travaux_date_achevement',
                    type: 'DATE_NOT_OLDER_THAN',
                    value: '2022-12-31'
                  },
                  {
                    id: 'travaux_list_travaux_energetique',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_carnet_information_travaux_statut',
              label: "Carnet d'information du logement effectué",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'travaux_list_carnet_information_travaux_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'travaux_list_travaux_date_achevement',
                    type: 'DATE_NOT_OLDER_THAN',
                    value: '2022-12-31'
                  },
                  {
                    id: 'travaux_list_travaux_energetique',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_carnet_information_travaux_document',
              label: "Carnet d'information du logement",
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_rga_renovation',
              label: 'Travaux portant sur une rénovation complète du Bien',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_rga_zone_concernee',
              label: 'Bien situé dans une zone à risque Argile "Moyen" ou "Fort"',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_rga_attestation_etablie',
              label: 'Attestation "Retrait / Gonflement Argile" établie',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'travaux_list_travaux_rga_attestation_etablie',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_travaux_rga_document',
              label: 'Attestation "Retrait / Gonflement Argile"',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_professionnel_statut',
              label: 'Travaux réalisé par un professionnel',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'travaux_list_travaux_professionnel_nom',
                  label: 'Nom du professionnel',
                  type: 'TEXT'
                },
                {
                  id: 'travaux_list_travaux_professionnel_adresse',
                  label: 'Adresse du professionnel',
                  type: 'ADDRESS'
                },
                {
                  id: 'travaux_list_facture_entreprise_travaux',
                  label: "Factures de l'entreprise",
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'travaux_list_travaux_date_achevement',
                        type: 'NOT_OLDER_THAN_N_MONTHS',
                        value: '120'
                      },
                      {
                        id: 'travaux_list_travaux_professionnel_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'travaux_list_assurance_decennale_travaux',
                  label: 'Assurance décennale',
                  type: 'UPLOAD'
                }
              ],
              conditions: [
                [
                  {
                    id: 'travaux_list_travaux_professionnel_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_d261eb4b_9eed_4dfd_8d26_10600ec09532',
              label: 'Informations sur le professionnel',
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'travaux_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'travaux_list',
          label: 'Travaux',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: ' '
              },
              {
                type: 'VARIABLE',
                value: 'travaux_list_travaux_titre'
              },
              {
                type: 'TEXT',
                value: ' - '
              },
              {
                type: 'VARIABLE',
                value: 'travaux_list_travaux_date_achevement'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        }
      ],
      id: '770ad293_1199_49cd_a899_ea77904c6c7a',
      label: 'Travaux',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'complexe_preemption',
          label:
            "L'ensemble immobilier comporte-t-il plus de 5 logements à usage d'habitation ou professionnel et habitation, dont au moins un est loué ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'complexe_preemption',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'complexe_preemption_acquereur',
          label: "L'Acquéreur est-il parent du Vendeur ou un organisme HLM ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'complexe_preemption',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                id: 'complexe_preemption_acquereur',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'complexe_preemption_acquereur_engagement',
          label: "L'Acquéreur s'engage-t-il à proroger le bail pour une durée de 6 ans à compter de la vente ?",
          type: 'SELECT-BINARY'
        }
      ],
      id: '971864ef_6a74_41c4_8f4d_98b02f4f6f5d',
      label: "Dispositions sur l'ensemble immobilier",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'visite_prix',
          label: 'Prix du bien',
          type: 'PRICE'
        },
        {
          id: 'visite_honoraires',
          label: 'Montant des honoraires',
          type: 'PRICE'
        }
      ],
      id: 'f31b41b8_1c1e_44c3_b4d6_0883e228b553',
      label: 'Informations',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  label: 'Isolé',
                  id: 'isole'
                },
                {
                  label: 'Dans une ferme',
                  id: 'ferme'
                },
                {
                  label: 'Dans un hameau',
                  id: 'hameau'
                },
                {
                  label: 'Dans un village',
                  id: 'village'
                },
                {
                  label: 'Dans une ville',
                  id: 'ville'
                }
              ],
              id: 'etat_descriptif_situation_meuble',
              label: 'Le meublé est',
              type: 'SELECT'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_ski',
                  label: 'Proximité de pistes de ski',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_ski_distance',
                  label: 'Distance des pistes de ski',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_mer',
                  label: 'Proximité de la mer',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_mer_distance',
                  label: 'Distance de la mer',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_lac',
                  label: "Proximité d'un lac",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_lac_distance',
                  label: 'Distance du lac',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_plage',
                  label: "Proximité d'une plage",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_plage_distance',
                  label: 'Distance de la plage',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_foret',
                  label: "Proximité d'une forêt",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_foret_distance',
                  label: 'Distance de la forêt',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_riviere',
                  label: "Proximité d'une rivière",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_riviere_distance',
                  label: 'Distance de la rivière',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_port',
                  label: "Proximité d'un port",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_port_distance',
                  label: 'Distance du port',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_centre_distance',
                  label: 'Distance du centre ville',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_autres',
                  label: "Autres centres d'intérêt",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_autres_distance',
                  label: "Liste et distance des autres centres d'intérêt",
                  type: 'TEXTAREA'
                }
              ],
              id: '62071f48_a7fa_4567_85a6_71fadddee8c6',
              label: "Distance des centres d'intérêt touristique",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'etat_descriptif_situation_proximite_sncf_distance',
                  label: 'Distance gare SNCF',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_gare_distance',
                  label: 'Distance gare routière',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_aeroport_distance',
                  label: 'Distance aéroport',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_medecin_distance',
                  label: 'Distance Médecin',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_hopital_distance',
                  label: 'Distance Hôpital',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_supermarche_distance',
                  label: 'Distance Centre commercial/supermarché',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_restaurant_distance',
                  label: 'Distance restaurant',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_epicerie_distance',
                  label: 'Distance épicerie',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_laverie_distance',
                  label: 'Distance laverie',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_services_autres',
                  label: 'Autres services',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_services_autres_distance',
                  label: 'Distance autres services',
                  suffix: 'km',
                  type: 'NUMBER'
                }
              ],
              id: '3e56906e_8a79_46cb_b976_1002785f1c59',
              label: 'Distance des principaux services',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_inconvenient_bruits',
                  label: 'Bruits',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_inconvenient_bruits_liste',
                  label: 'Liste des bruits',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_inconvenient_odeurs',
                  label: 'Odeurs',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_inconvenient_odeurs_liste',
                  label: 'Liste des odeurs',
                  type: 'TEXT'
                }
              ],
              id: '7920a03e_bee2_494a_9ad6_e3f477082254',
              label: 'Inconvénients de voisinage',
              type: 'CATEGORY'
            }
          ],
          id: '2d9f74a9_1c81_4bfa_ac90_c38aab82e365',
          label: 'Situation dans la localité',
          type: 'CATEGORY'
        }
      ],
      id: '1b7f8f88_22a2_4b8d_867a_9f808c8e5cb5',
      label: 'Etat descriptif',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__STRUCTURE__ENSEMBLE_IMMOBILIER',
  label: 'Monopropriété',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__STRUCTURE__ENSEMBLE_IMMOBILIER',
  specificTypes: ['STRUCTURE', 'ENSEMBLE_IMMOBILIER'],
  type: 'RECORD'
};
