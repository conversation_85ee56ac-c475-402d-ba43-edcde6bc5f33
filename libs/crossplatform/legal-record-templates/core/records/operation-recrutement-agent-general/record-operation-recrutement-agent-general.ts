// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordOperationRecrutementAgentGeneral: LegalRecordTemplate = {
  config: {},
  form: [
    {
      children: [
        {
          choices: [
            {
              id: 'pp',
              label: 'Propriétés Privées'
            },
            {
              id: 'immoreseau',
              label: 'Immoréseau'
            }
          ],
          id: 'structure',
          label: 'Structure',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'portage',
              label: 'Portage Salarial'
            },
            {
              id: 'agent',
              label: 'Agent Commercial'
            }
          ],
          id: 'objet_contrat',
          label: 'Objet du contrat',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'contrat_effet_signature',
          label: 'Contrat prenant effet à la signature',
          type: 'SELECT-BINARY'
        },
        {
          id: 'contrat_effet_signature_date',
          label: "Date de prise d'effet",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'droit_suite',
          label: 'Droit de suite',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'trois_mois',
              label: '3 mois'
            },
            {
              id: 'six_mois',
              label: '6 mois'
            }
          ],
          id: 'droit_suite_duree',
          label: 'Durée du droit de suite',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'clause_ncn_post_contractuelle',
          label: 'Clause de non-concurrence post-contractuelle',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'six_mois',
              label: '6 mois'
            },
            {
              id: 'un_an',
              label: '1 an'
            },
            {
              id: 'deux_ans',
              label: '2 an'
            }
          ],
          id: 'clause_ncn_post_contractuelle_duree',
          label: 'Durée de la clause de non concurrence',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'titulaire_carte_t',
          label: 'Titulaire de Carte T',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'base',
              label: 'Contrat de base'
            },
            {
              id: 'specifique',
              label: 'Contrat spécifique'
            },
            {
              id: 'base_int',
              label: 'Contrat de base Int'
            },
            {
              id: 'specifique_int',
              label: 'Contrat Spécifique Int'
            },
            {
              id: 'specifique_v2',
              label: 'Contrat Spécifique V2'
            }
          ],
          id: 'workflow',
          label: 'Niveau de validation en fonction du contrat',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'integration',
          label: 'Intégration',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'debutant',
              label: 'Débutant'
            },
            {
              id: 'confirme',
              label: 'Confirmé'
            }
          ],
          id: 'niveau',
          label: 'Niveau',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'annuel',
              label: 'Annuel'
            },
            {
              id: 'mensuel',
              label: 'Mensuel'
            }
          ],
          id: 'paiement_type',
          label: 'Type de paiement',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cooptation',
          label: 'Conseiller coopté',
          type: 'SELECT-BINARY'
        },
        {
          id: 'cooptation_nom_prenom',
          label: 'Coopté par (prénom nom)',
          type: 'TEXT'
        },
        {
          id: 'animation_nom_prenom',
          label: 'Animé par (prénom nom)',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'residentiel',
              label: 'Résidentiel'
            },
            {
              id: 'business',
              label: 'Business'
            },
            {
              id: 'double',
              label: 'Business et Résidentiel'
            }
          ],
          id: 'activites_liste',
          label: 'Activités',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'conseiller',
              label: 'du Conseiller'
            },
            {
              id: 'manager',
              label: 'du Manager'
            }
          ],
          id: 'charte_liste',
          label: 'Type de charte',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'pro',
              label: 'Espace Pro'
            },
            {
              id: 'agent',
              label: 'Espace Agent'
            }
          ],
          id: 'espace_pro_type',
          label: 'Espace pro type',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'bareme_minimum',
          label: 'Barème Minimum',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'autorisation_cj',
          label: 'Autorisation de consultation du casier judiciaire',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'starter',
              label: 'Starter 2016'
            },
            {
              id: 'premium',
              label: 'Premium 2016'
            },
            {
              id: 'pro',
              label: 'Pro'
            },
            {
              id: 'business',
              label: 'Business'
            },
            {
              id: 'starter_double',
              label: 'Starter Double Activité'
            },
            {
              id: 'premium_double',
              label: 'Premium Double Activité'
            },
            {
              id: 'pro_80',
              label: 'Pro spécifique 80'
            },
            {
              id: 'pro_85',
              label: 'Pro spécifique 85'
            }
          ],
          id: 'pack_pp',
          label: 'Pack PP',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'en_cours',
              label: 'Offre pro en cours'
            },
            {
              id: 'exceptionnelle',
              label: 'Offre pro exceptionnelle'
            },
            {
              id: 'aucune',
              label: 'Aucune'
            }
          ],
          id: 'offre_application',
          label: 'Offre à appliquer',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'offre_1',
              label: 'Offre en cours 1'
            },
            {
              id: 'offre_2',
              label: 'Offre en cours 2'
            },
            {
              id: 'offre_3',
              label: 'Offre en cours 3'
            },
            {
              id: 'offre_4',
              label: 'Offre en cours 4'
            }
          ],
          id: 'offres_promotionnelles',
          label: 'Offres promotionnelles',
          type: 'SELECT'
        },
        {
          children: [
            {
              id: 'offre_promotionnelle_1',
              label: "Date de l'offre",
              type: 'TEXT'
            }
          ],
          conditions: [
            [
              {
                id: 'offres_promotionnelles',
                value: 'offre_1',
                type: 'EQUALS'
              }
            ]
          ],
          id: '51a0rt43_dp21_4dfd_4tcf_e792f8d2fpo2',
          label: 'Offre promotionnelle 1',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'offre_promotionnelle_2',
              label: "Date de l'offre",
              type: 'TEXT'
            }
          ],
          conditions: [
            [
              {
                id: 'offres_promotionnelles',
                value: 'offre_2',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'fo21rt43_vps3_mlq2_cp34_e792f8d2fpl4',
          label: 'Offre promotionnelle 2',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'offre_promotionnelle_3',
              label: "Date de l'offre",
              type: 'TEXT'
            }
          ],
          conditions: [
            [
              {
                id: 'offres_promotionnelles',
                value: 'offre_3',
                type: 'EQUALS'
              }
            ]
          ],
          id: '51a0fi55_dp21_pl99_pl66_e792f8d2ghj65',
          label: 'Offre promotionnelle 3',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'offre_promotionnelle_4',
              label: "Date de l'offre",
              type: 'TEXT'
            }
          ],
          conditions: [
            [
              {
                id: 'offres_promotionnelles',
                value: 'offre_4',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'flm22guy32_dp21_fml3_fhg3_fpl2f8d2gplm2',
          label: 'Offre promotionnelle 4',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'contrat_cadre',
          label: "Contrat au profit d'un cadre",
          type: 'SELECT-BINARY'
        },
        {
          id: 'date_declaration_embauche',
          label: "Date de déclaration préalable à l'embauche",
          type: 'DATE'
        },
        {
          id: 'ville_declaration_embauche',
          label: "Ville de l'URSAFF recevant la déclaration préalable",
          type: 'TEXT'
        },
        {
          id: 'numero_declaration_embauche',
          label: "Numéro de déclaration à l'URSAFF",
          type: 'TEXT'
        },
        {
          id: 'date_depart',
          label: 'Date de départ du contrat',
          type: 'DATE'
        },
        {
          id: 'heure_depart',
          label: 'Heure de début du contrat',
          suffix: 'heures',
          type: 'NUMBER'
        },
        {
          id: 'date_fin',
          label: 'Date de fin du contrat',
          type: 'DATE'
        },
        {
          id: 'heure_fin',
          label: 'Heure de fin du contrat',
          suffix: 'heures',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'duree_legale',
              label: 'Durée classique - 151,67 heures par mois'
            },
            {
              id: 'duree_forfait',
              label: 'Forfait horaires'
            },
            {
              id: 'duree_forfait_jours',
              label: 'Forfait Jours'
            }
          ],
          id: 'duree_travail',
          label: 'Durée du travail',
          type: 'SELECT'
        },
        {
          id: 'contrat_duree',
          label: 'Durée du contrat',
          type: 'TEXT'
        },
        {
          description:
            "1 jour par semaine (avec un maximum de 2 semaines) si le contrat a une durée initiale de 6 mois ou moins. 1 mois maximum si le contrat a une durée initiale de plus de 6 mois. Attention, pour être valable, le renouvellement de la période d’essai prévu au contrat de travail sur son principe doit être expressément accepté par le salarié avant le terme de la période initiale. Sur ce point la jurisprudence est particulièrement sévère, exigeant que le salarié mentionne en toutes lettres avec sa signature qu’il donne son accord exprès au renouvellement de l’essai (Cour de cassation du 25 novembre 2009 - pourvoi n°08-43008). La présente formule est un CDD à terme précis, lorsque le contrat ne comporte pas de terme précis, la période d'essai est calculée par rapport à la durée minimale du contrat.",
          id: 'contrat_essai_duree',
          label: "Durée de la période d'essai",
          type: 'TEXT'
        },
        {
          id: 'contrat_essai_duree_cdi',
          label: "Durée de la période d'essai",
          type: 'TEXT'
        },
        {
          description:
            'La durée totale du contrat, renouvellement compris, ne doit pas dépasser la durée maximale légale de 18 mois (art. L. 1243-13 CT). La durée du renouvellement proprement dite peut être inférieure, égale ou supérieure à celle de la période initiale.',
          id: 'contrat_prevenance_delai',
          label: 'Délai de prévenance avant renouvellement',
          suffix: 'jours',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'complet',
              label: 'Service complet - interdiction de travailler pour des propriétaires'
            },
            {
              id: 'permanent',
              label: 'Service permanent - travail au domicile autorisé'
            },
            {
              id: 'partiel',
              label: 'Service partiel - emploi extérieur autorisé'
            }
          ],
          id: 'type_service_gardien',
          label: 'Type de service du Gardien',
          type: 'SELECT'
        },
        {
          description:
            'La rémunération forfaitaire attachée au forfait mensuel inclut la rémunération majorée des heures supplémentaires dans la limite de la durée du travail. Le contingent d’heures supplémentaires de 220 heures par an est applicable à ces salariés mobiles non cadres.',
          id: 'duree_travail_autre',
          label: "Nombres d'heures au forfait",
          type: 'TEXT'
        },
        {
          description:
            'se référer à l’article 19-9 de la convention collective de l’immobilier relatif au reposant sur un décompte annuel en journée',
          id: 'duree_travail_autre_jours',
          label: 'Nombres de jours au forfait',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'journalier',
              label: 'Journalier'
            },
            {
              id: 'hebdomadaire',
              label: 'Hebdomadaire'
            }
          ],
          id: 'rapport_journalier_hebdomadaire',
          label: 'Les rapports du négociateur seront',
          type: 'SELECT-BINARY'
        },
        {
          id: 'zone_geographique_limite',
          label: "Limite géographique de l'activité du négociateur",
          type: 'TEXT'
        },
        {
          description:
            'Pour le négociateur non cadre, ce salaire ne pourra pas être inférieur au SMIC mensuel. Pour le négociateur cadre, étant classé dans la grille de l’annexe II à la CCNI, il bénéficie du salaire minimum prévu par la grille « salaires ». Pour connaître le salaire plancher, se référer au dernier avenant « salaires » signé.',
          id: 'avance_commission',
          label: 'Montant des avances sur commission, en brut mensuel',
          type: 'PRICE'
        },
        {
          description:
            'Pour le négociateur VRP non cadre, ce salaire ne pourra pas être inférieur au salaire minimum brut conventionnel de 1500€ par mois (avenant n°98 du 1er-03-2023). Pour le négociateur immobilier cadre (VRP ou non), étant classé dans la grille de l’annexe II à la CCNI, il bénéficie du salaire minimum prévu, pour son niveau, par la grille « salaires ». Pour connaître le salaire plancher, pour son niveau, se référer au dernier  avenant « salaires » signé.',
          id: 'avance_commission_vrp',
          label: 'Montant des avances sur commission, en brut mensuel',
          type: 'PRICE'
        },
        {
          description: 'A titre indicatif, la moyenne du taux global est de 30%.',
          id: 'pourcentage_remuneration',
          label: "Pourcentage de rémunération sur le chiffre d'affaires H.T.",
          suffix: '%',
          type: 'NUMBER'
        },
        {
          description: 'Exemple pour un taux global de 30 %, 27.03% correspondent à la commission proprement dite',
          id: 'pourcentage_commission',
          label: 'Pourcentage de commission',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          description: 'Exemple pour un taux global de 30 %, 2.70% correspondent aux congés payés',
          id: 'pourcentage_conges_payes',
          label: 'Pourcentage de congés payés',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          description: 'Exemple pour un taux global de 30 %, 0.27% correspondent aux congés payés de congés payés',
          id: 'pourcentage_conges_payes_conges_payes',
          label: 'Pourcentage de congés payés sur les congés payés',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description: 'Au cas où l’objectif sur 4 trimestres civils serait dépassé',
          id: 'complement_remuneration',
          label: 'Ajouter une clause sur le complément de rémunération',
          type: 'SELECT-BINARY'
        },
        {
          id: 'pourcentage_remuneration_complementaire',
          label: 'Pourcentage de rémunération supplémentaire',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'frais_charge_salarie',
              label: 'Indemnité forfaitaire - frais à la charge du négociateur'
            },
            {
              id: 'frais_remboursement',
              label: 'Remboursement des frais sur justificatifs'
            },
            {
              id: 'frais_aucun_remboursement',
              label: 'Aucun frais remboursés mais prise en charge de certaines dépenses'
            }
          ],
          description:
            'Indemnité forfaitaire valable à la double condition : d’une part, que cette somme forfaitaire ne soit pas manifestement disproportionnée au regard du montant réel des frais engagés ; -d’autre part, que la rémunération proprement dite du travail reste chaque mois au moins égale au SMIC',
          id: 'remboursement_frais_professionnels',
          label: 'Remboursement des frais professionnels',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'frais_charge_salarie',
              label: 'Indemnité forfaitaire - frais à la charge du négociateur'
            },
            {
              id: 'frais_remboursement',
              label: 'Remboursement des frais sur justificatifs'
            },
            {
              id: 'frais_aucun_remboursement',
              label: 'Aucun frais remboursés mais prise en charge de certaines dépenses'
            }
          ],
          description:
            "Si vous envisagez d’appliquer la déduction forfaitaire de 28% pour frais professionnels des VRP (taux applicable pour 2024) il convient de consulter :- Notre circulaire UNIS relative à la mise en œuvre de la DFS à jour de l'année 2024. -Le bulletin officiel de la Sécurité sociale (BOSS) mis à jour en permanence par l'administration Rubrique FRAIS PROFESSIONNEL - Chapitre 9 « Déduction forfaitaire pour frais professionnels ». Après information préalable du salarié concernant les conséquences de l’application de la DFS sur ses droits (droits aux IJSS et à l’assurance retraite calculés sur une assiette de charges sociales diminuée de 30%), Pour appliquer la DFS il est nécessaire d’obtenir soit un accord du CSE (cet accord est pérenne), soit l’accord du salarié tous les ans. Voir dans notre circulaire UNIS DFS ci-dessus le modèle de consultation du salarié. Attention, ce montant de 28% baisse de 2 % chaque année",
          id: 'remboursement_frais_professionnels_vrp',
          label: 'Remboursement des frais professionnels',
          type: 'SELECT'
        },
        {
          description:
            'Indemnité forfaitaire valable à condition que cette somme forfaitaire ne soit pas manifestement disproportionnée au regard du montant réel des frais engagés.',
          id: 'frais_rembourse_forfait',
          label: 'Montant du remboursement forfaitaire',
          type: 'PRICE'
        },
        {
          id: 'frais_rembourses_liste',
          label: 'Liste des frais remboursés',
          type: 'TEXT'
        },
        {
          id: 'frais_rembourses_plafond',
          label: 'Plafonnement des frais remboursés',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'telephone',
              label: 'Téléphone portable'
            },
            {
              id: 'vehicule',
              label: 'Véhicule de fonction'
            },
            {
              id: 'transport',
              label: 'Titre de transport'
            }
          ],
          id: 'remboursement_mise_disposition',
          label: 'Elements mis à disposition du négociateur',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'redevance_statut',
          label: "Une redevance est due au titre de la mise à disposition d'équipement",
          type: 'SELECT-BINARY'
        },
        {
          id: 'redevance_modalite',
          label: 'Modalité de paiement de la redevance',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'non_discrimination',
          label: "Clause d'engagement de non discrimination",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'loyaute',
          label: "Clause d'engagement de loyauté",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'non_concurrence',
          label: 'Clause de non concurrence',
          type: 'SELECT-BINARY'
        },
        {
          description:
            'L’article 9 du Statut du négociateur immobilier (Annexe IV à la CCNI) limite la clause de non-concurrence à une durée maximale de 24 mois après la cessation du contrat de travail. L’employeur qui insère une telle clause dans un contrat de travail doit avoir conscience qu’il devra payer la contrepartie financière relative à l’interdiction aussi longtemps que cette interdiction devra jouer',
          id: 'duree_concurrence_salarie',
          label: 'Durée de la non concurrence, si rupture par le salarié',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          id: 'duree_concurrence_employeur',
          label: "Durée de la non concurrence, si rupture par l'employeur",
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          description:
            'Les partenaires sociaux ont fixé un taux de 20%. Ce taux est assez faible comparé à ce que les partenaires sociaux ont pu négocier dans d’autres branches professionnelles. Pour ne pas que ce taux relativement faible soit invalidé, il convient notamment de rester raisonnable sur certains paramètres, tels que celui de la zone géographique couverte par l’interdiction, ou celui de la durée de l’interdiction',
          id: 'concurrence_compensation',
          label: 'Montant forfaitaire mensuel',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          id: 'non_concurrence_zone_limite',
          label: 'Limite de la zone géographique pour la non-concurrence',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'limitation_secteur',
          label: "Limitation du secteur d'activité du Mandataire",
          type: 'SELECT-BINARY'
        },
        {
          id: 'limitation_secteur_liste',
          label: 'Liste du secteur limité',
          type: 'TEXT'
        },
        {
          id: 'liste_departement',
          label: "Liste des départements relevant du secteur d'excercice",
          type: 'TEXTAREA'
        },
        {
          id: 'communes_non_concurrence',
          label: 'Communes concernées par la zone de non concurrence',
          type: 'TEXTAREA'
        },
        {
          id: 'cap_base',
          label: 'Montant de base du CAP du Market Center',
          type: 'PRICE'
        },
        {
          id: 'cap_reduit',
          label: "Montant réduit du CAP (après intégration d'une Mega Team)",
          type: 'PRICE'
        },
        {
          id: 'cap_reduit_office',
          label: "Montant réduit du CAP (après intégration d'une Mega Team Office)",
          type: 'PRICE'
        },
        {
          id: 'cap_base_cfp',
          label: 'Montant de base du CAP du Market Center',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'cap_reduit_cfp',
          label: "Montant réduit du CAP (après intégration d'une Mega Team)",
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'cap_reduit_office_cfp',
          label: "Montant réduit du CAP (après intégration d'une Mega Team Office)",
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'redevance',
          label: 'Montant hors taxe de la redevance payée par le Mandataire - ressources logistiques',
          type: 'PRICE'
        },
        {
          id: 'redevance_cfp',
          label: 'Montant hors taxe de la redevance payée par le Mandataire',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'welcome_pack_montant',
          label: 'Montant du Welcome Pack (HT)',
          type: 'PRICE'
        },
        {
          id: 'welcome_pack_liste',
          label: 'Liste des prestations du Welcome Pack',
          type: 'TEXTAREA'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DEONTOLOGIE'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'deontologie',
          label: 'Code de déontologie - agent immobilier',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_TRACFIN_2'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'tracfin3',
          label: 'Dispositif TRACFIN',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_DROIT_IMAGE_KW'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'droit_image',
          label: "Droit à l'image",
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_CHARTE_CULTURE_KW'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'charte_culture',
          label: 'Charte culture',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'demande_rcp',
          label: "Demande d'assurance RCP",
          templateId: 'demandeRcpKw.pdf',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mandat_sepa_kw',
          label: 'Mandat SEPA',
          templateId: 'mandatSepaKw.pdf',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'fiche_info_conseil',
          label: 'Fiche info conseil GALIAN',
          templateId: 'ficheInfoConseil.pdf',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'duree_type',
          label: 'Contrat à durée indéterminée',
          type: 'SELECT-BINARY'
        },
        {
          id: 'duree_type_libre',
          label: 'Durée libre du contrat',
          type: 'TEXT'
        },
        {
          id: 'date_debut',
          label: "Date du début du mandat d'agent commercial",
          type: 'DATE'
        },
        {
          id: 'date_mandat_initial',
          label: 'Date du mandat initial',
          type: 'DATE'
        },
        {
          id: 'date_mandat_vigueur',
          label: "Date d'entrée en vigueur de l'avenant",
          type: 'DATE'
        },
        {
          id: 'retrocession_montant',
          label: 'Montant de la rétrocession',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          id: 'forfait_mise_disposition',
          label: 'Forfait de mise à disposition des locaux',
          type: 'PRICE'
        },
        {
          id: 'forfait_mise_disposition_cfp',
          label: 'Forfait de mise à disposition des locaux',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'signature_electronique',
              label: 'Signature électronique',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'signature_electronique_apporteur',
              label: 'Signature électronique',
              type: 'SELECT-BINARY'
            }
          ],
          id: '7eb2chy4_03rf_vo21_pl33_965rfd12jyt2',
          label: 'Signature',
          type: 'CATEGORY'
        }
      ],
      id: '51a0daf4_bb19_471d_9ecf_e792f8d2d057',
      label: 'Informations Générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'homme',
              label: 'Monsieur'
            },
            {
              id: 'femme',
              label: 'Madame'
            }
          ],
          id: 'employe_remplace_civilite',
          label: 'Civilité',
          type: 'SELECT'
        },
        {
          id: 'employe_remplace_prenom',
          label: 'Prénom',
          type: 'TEXT'
        },
        {
          id: 'employe_remplace_nom',
          label: 'Nom',
          type: 'TEXT'
        },
        {
          description:
            "Remplacement d'un salarié absent (maladie, congés payés…) ; Remplacement d'un salarié passé provisoirement à temps partiel (congé parental d’éducation…) ; Dans l'attente de l'entrée en service effective d'un salarié recruté par contrat à durée indéterminée ; Attente de la suppression définitive du poste du salarié ayant quitté définitivement l’entreprise ; Accroissement temporaire de l’activité de l’entreprise.",
          id: 'employe_remplace_motif',
          label: "Motif de l'absence de l'employé",
          type: 'TEXT'
        }
      ],
      id: '7eb287ca_bfd3_4569_a045_936ecd127a0c',
      label: 'Employé à remplacer',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'contrat_montant_coefficient',
          label: 'Montant du coefficient',
          type: 'TEXT'
        },
        {
          description:
            'La durée minimale légale hebdomadaire du travail à temps partiel est fixée à 24 heures par l’article L3123-14-1 du code du travail, néanmoins, à titre dérogatoire les partenaires sociaux de la branche ont négocié des dérogations conventionnelles à la durée minimale. Les durées minimales dérogatoires sont établies en fonction de la taille des résidences. Les seuils suivants sont prévus : -de 1 à 29 lots, la durée contractuelle hebdomadaire de travail est au minimum de 2 heures ; -de 30 à 59 lots la durée contractuelle hebdomadaire de travail est au minimum de 7 heures  -à partir de 60 lots, la durée contractuelle hebdomadaire de travail est au minimum de 14 heures Il est précisé que le nombre de lots s’apprécie au jour de la conclusion du contrat de travail. Toutefois, en cas d’embauche d’un salarié à temps partiel pour travailler dans une résidence qui emploie déjà un salarié (à temps plein ou à temps partiel), la durée contractuelle hebdomadaire minimale sera celle prévue pour les résidences relevant du seuil inférieur à celui dont elle relève. Enfin, la dérogation individuelle telle que prévues par l’article L3123-14-2 du code du travail est également utilisable. Cela signifie qu’il est possible de déroger à ces durées minimales conventionnelles sur demande motivée du salarié.',
          id: 'contrat_heures_hebdomadaire',
          label: "Nombres d'heures hebdomadaires",
          suffix: 'heures',
          type: 'NUMBER'
        },
        {
          description:
            'Si les tâches demandées au remplaçant sont identiques à celle du salarié titulaire, le coefficient hiérarchique doit être le même que celui du salarié remplacé, par conséquent la pesée de poste réalisée pour le titulaire est à reprendre in extenso. En revanche, en cas de remplacement partiel une nouvelle pesée correspondant précisément au poste occupé est à effectuer.',
          id: 'contrat_montant_coefficient_hierarchique',
          label: 'Montant du coefficient hiérarchique',
          type: 'TEXT'
        },
        {
          id: 'contrat_montant_coefficient_hierarchique_cdi',
          label: 'Montant du coefficient hiérarchique',
          type: 'TEXT'
        },
        {
          children: [
            {
              id: 'coefficient_echelon_relationnel',
              label: 'Echelon',
              type: 'TEXT'
            },
            {
              id: 'coefficient_valorisation_relationnel',
              label: 'Valorisation (points)',
              type: 'TEXT'
            }
          ],
          id: 'd35488da_0941_4efd_810c_e4130a09794e',
          label: 'Critère - Relationnel',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'coefficient_echelon_technique',
              label: 'Echelon',
              type: 'TEXT'
            },
            {
              id: 'coefficient_valorisation_technique',
              label: 'Valorisation (points)',
              type: 'TEXT'
            }
          ],
          id: '4616bdce_ad2b_46e1_803e_af045363c111',
          label: 'Critère - Technique',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'coefficient_echelon_administratif',
              label: 'Echelon',
              type: 'TEXT'
            },
            {
              id: 'coefficient_valorisation_administratif',
              label: 'Valorisation (points)',
              type: 'TEXT'
            }
          ],
          id: '2df3c709_2058_45cc_b79a_0370160f4b87',
          label: 'Critère - Administratif',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'coefficient_echelon_supervision',
              label: 'Echelon',
              type: 'TEXT'
            },
            {
              id: 'coefficient_valorisation_supervision',
              label: 'Valorisation (points)',
              type: 'TEXT'
            }
          ],
          id: '72009945_9a09_4cd0_9455_501cc01f56e6',
          label: 'Critère - Supervision',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'coefficient_echelon_autonomie',
              label: 'Echelon',
              type: 'TEXT'
            },
            {
              id: 'coefficient_valorisation_autonomie',
              label: 'Valorisation (points)',
              type: 'TEXT'
            }
          ],
          id: 'a5cc1125_855d_4fe5_95d7_88d5f455f194',
          label: 'Critère - Autonomie',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'coefficient_echelon_formation',
              label: 'Echelon',
              type: 'TEXT'
            },
            {
              id: 'coefficient_valorisation_formation',
              label: 'Valorisation (points)',
              type: 'TEXT'
            }
          ],
          id: 'e0825dad_27f0_4377_8eb1_e93fafcb391f',
          label: 'Critère - Formation',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'fonction_taches_generales',
              label: 'Tâches générales',
              type: 'TEXT'
            },
            {
              id: 'fonction_travaux_specialises',
              label: 'Travaux spécialisés',
              type: 'TEXT'
            }
          ],
          id: '58087da5_4f70_4b57_b2dd_415dd839711a',
          label: 'Fonction',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'programme_quotidien',
              label: 'Programme chaque jour',
              type: 'TEXT'
            },
            {
              id: 'programme_hebdomadaire',
              label: 'Programme chaque semaine',
              type: 'TEXT'
            },
            {
              id: 'programme_mensuel',
              label: 'Programme chaque mois',
              type: 'TEXT'
            }
          ],
          id: '557e64f7_f9af_4fb0_b583_8d424565ca89',
          label: 'Programme de travail',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'lundi',
                  label: 'Lundi'
                },
                {
                  id: 'mardi',
                  label: 'Mardi'
                },
                {
                  id: 'mercredi',
                  label: 'mercredi'
                },
                {
                  id: 'jeudi',
                  label: 'Jeudi'
                },
                {
                  id: 'vendredi',
                  label: 'Vendredi'
                },
                {
                  id: 'samedi',
                  label: 'Samedi matin'
                },
                {
                  id: 'pause',
                  label: 'Temps pause'
                }
              ],
              id: 'programme_plage_travail',
              label: 'Ajouter une plage de travail',
              multiple: true,
              type: 'PICK_LIST'
            },
            {
              children: [
                {
                  id: 'programme_heure_debut_lundi',
                  label: 'Heure de début',
                  suffix: 'heures',
                  type: 'NUMBER'
                },
                {
                  id: 'programme_heure_fin_lundi',
                  label: 'Heure de fin',
                  suffix: 'heures',
                  type: 'NUMBER'
                }
              ],
              id: '67e64aad_1096_417a_b000_f46c367aa95e',
              label: 'Lundi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'programme_heure_debut_mardi',
                  label: 'Heure de début',
                  suffix: 'heures',
                  type: 'NUMBER'
                },
                {
                  id: 'programme_heure_fin_mardi',
                  label: 'Heure de fin',
                  suffix: 'heures',
                  type: 'NUMBER'
                }
              ],
              id: '51ccebe6_1e3d_4cf8_9f50_4d30e44dc0b3',
              label: 'Mardi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'programme_heure_debut_mercredi',
                  label: 'Heure de début',
                  suffix: 'heures',
                  type: 'NUMBER'
                },
                {
                  id: 'programme_heure_fin_mercredi',
                  label: 'Heure de fin',
                  suffix: 'heures',
                  type: 'NUMBER'
                }
              ],
              id: '7173e66b_d5e3_467d_926c_c30a64ad53a7',
              label: 'Mercredi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'programme_heure_debut_jeudi',
                  label: 'Heure de début',
                  suffix: 'heures',
                  type: 'NUMBER'
                },
                {
                  id: 'programme_heure_fin_jeudi',
                  label: 'Heure de fin',
                  suffix: 'heures',
                  type: 'NUMBER'
                }
              ],
              id: '0d654edc_5dad_4064_b81a_68e1345f904a',
              label: 'Jeudi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'programme_heure_debut_vendredi',
                  label: 'Heure de début',
                  suffix: 'heures',
                  type: 'NUMBER'
                },
                {
                  id: 'programme_heure_fin_vendredi',
                  label: 'Heure de fin',
                  suffix: 'heures',
                  type: 'NUMBER'
                }
              ],
              id: '8514aba4_95ff_41e0_af73_1cc935fcd22a',
              label: 'Vendredi',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'programme_heure_debut_samedi',
                  label: 'Heure de début',
                  suffix: 'heures',
                  type: 'NUMBER'
                },
                {
                  id: 'programme_heure_fin_samedi',
                  label: 'Heure de fin',
                  suffix: 'heures',
                  type: 'NUMBER'
                }
              ],
              id: '5cd2414c_7b1e_4047_b3aa_956c3b22ac02',
              label: 'Samedi Matin',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'programme_heure_debut_pause',
                  label: 'Heure de début',
                  suffix: 'heures',
                  type: 'NUMBER'
                },
                {
                  id: 'programme_heure_fin_pause',
                  label: 'Heure de fin',
                  suffix: 'heures',
                  type: 'NUMBER'
                }
              ],
              id: '76991363_ad6f_43e2_b6f4_e698cc5da648',
              label: 'Temps de pause',
              type: 'CATEGORY'
            }
          ],
          id: '4764cef5_3c1b_4c05_8b67_3a038b03ced3',
          label: 'Répartition du travail',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'taux_emploi',
              label: "Taux d'emploi",
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'salaire_brut_mensuel_montant',
              label: 'Montant du salaire brut mensuel conventionnel',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'salaire_brut_mensuel_supplementaire',
              label: 'Ajouter un salaire supplémentaire contractuel',
              type: 'SELECT-BINARY'
            },
            {
              id: 'salaire_brut_mensuel_supplementaire_montant',
              label: 'Montant du salaire supplémentaire',
              type: 'PRICE'
            },
            {
              id: 'salaire_brut_coupure',
              label: 'Nombre de journées comprenant une coupure supérieure à 2 heures',
              suffix: 'jours',
              type: 'NUMBER'
            },
            {
              id: 'salaire_brut_avec_avantage',
              label: 'Montant du salaire total, sous diminution des avantages en nature',
              type: 'PRICE'
            }
          ],
          id: 'f61aafa1_f189_453d_ab67_563fb6aec24a',
          label: 'Salaire global',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              description:
                'L’amplitude de la journée de travail, convenue au contrat de travail, ne peut excéder treize heures incluant quatre heures de temps de repos pris en une ou deux fois (une des périodes devant être au moins égale à ¾ du temps de repos total), soit une période d’exécution des tâches et de permanence de neuf heures.',
              id: 'ouverture_loge_amplitude',
              label: 'Amplitude horaire de la journée de travail',
              suffix: 'heures',
              type: 'NUMBER'
            },
            {
              id: 'ouverture_loge_amplitude_cdi',
              label: 'Amplitude horaire de la journée de travail',
              suffix: 'heures',
              type: 'NUMBER'
            },
            {
              description:
                "Les heures d'ouverture de la loge sont précisées dans le contrat de travail, dans le respect de l'amplitude diminuée des heures de repos et éventuellement du temps d'exécution des tâches matinales ou tardives, telles que par exemple le service des portes et des ordures ménagères (article 18-4 Convention collective). Bien que l’article 18-4 de la Convention collective soit ainsi libellé, il est déconseillé de prévoir des heures d’ouverture de la loge calquant l’amplitude diminuée des heures de repos. En effet, le salarié ne pourra pas être à la fois en permanence à la loge et en train d’exécuter des tâches dans les parties communes. Il y a là un risque de conflit avec les occupants de l’immeuble s’ils trouvent la loge fermée alors qu’elle devrait être ouverte compte tenu des « horaires d’ouverture » affichées.",
              id: 'ouverture_loge_semaine',
              label: 'Heures d’ouverture de la loge',
              type: 'TEXT'
            },
            {
              description: 'De XXX heures à XXX heures',
              id: 'ouverture_loge_semaine_cdi',
              label: 'Heures d’ouverture de la loge',
              type: 'TEXT'
            },
            {
              description: 'De XXX heures à XXX heures',
              id: 'ouverture_loge_pause',
              label: 'Temps de pause',
              type: 'TEXT'
            },
            {
              description: 'De XXX heures à XXX heures',
              id: 'ouverture_loge_samedi',
              label: 'Heures d’ouverture de la loge le samedi',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              description: 'Eventuellement permanences jours fériés ; etc...',
              id: 'ouverture_loge_autre_statut',
              label: "Autre période d'ouverture de la loge",
              type: 'SELECT-BINARY'
            },
            {
              id: 'ouverture_loge_autre_liste',
              label: "Liste des autres périodes d'ouverture",
              type: 'TEXT'
            },
            {
              description:
                'La période d’exécution des tâches et de permanence (amplitude des journées de travail minorée des périodes de repos) ne peut excéder une durée hebdomadaire équivalente à 47h30.',
              id: 'ouverture_loge_permanence',
              label: "Période d'exécution des tâches et permanence hebdomadaire",
              suffix: 'heures',
              type: 'NUMBER'
            }
          ],
          id: '771d16e2_9994_43df_b409_a902dad31712',
          label: 'Ouverture de la loge',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              description:
                'Le temps de repos peut, en outre, être limité à 3 heures dans une amplitude de 13 heures, pour les salariés de catégorie B à service complet ou permanent qui, dans ce cas, bénéficient de quatre demies-journées consécutives incluant la journée complète du dimanche (au lieu du samedi après-midi ou du lundi matin, comme prévu à l’article 18-3 §3, Dans ce cas, notons que la période d’exécution des tâches et de permanence est de dix heures. ',
              id: 'repos_heure_debut',
              label: 'Heure de début du repos hebdomadaire',
              suffix: 'heures',
              type: 'NUMBER'
            },
            {
              id: 'repos_heure_debut_cdi',
              label: 'Heure de début du repos hebdomadaire',
              suffix: 'heures',
              type: 'NUMBER'
            },
            {
              id: 'repos_heure_fin',
              label: 'Heure de fin du repos hebdomadaire',
              suffix: 'heures',
              type: 'NUMBER'
            }
          ],
          id: '485ace0e_1705_4848_8433_59d3a555099c',
          label: 'Repos hebdomadaire',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'logement_fonction_adresse',
              label: 'Adresse du logement de fonction',
              type: 'ADDRESS'
            },
            {
              id: 'logement_fonction_description',
              label: 'Description du logement de fonction',
              type: 'TEXT'
            },
            {
              id: 'logement_fonction_superficie',
              label: 'Superficie du logement',
              suffix: 'm2',
              type: 'NUMBER'
            },
            {
              id: 'logement_categorie',
              label: 'Catégorie du logement',
              type: 'TEXT'
            },
            {
              id: 'logement_avantage_nature',
              label: "Evaluation de l'avantage en nature procuré par le logement",
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'logement_compteur_presence',
              label: "Le logement est équipé d'un compteur individuel",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'syndicat',
                  label: 'Syndicat'
                },
                {
                  id: 'gardien',
                  label: 'Gardien'
                }
              ],
              id: 'logement_taxe_habitation',
              label: "La taxe d'habitation est à la charge du",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'syndicat',
                  label: 'Syndicat'
                },
                {
                  id: 'gardien',
                  label: 'Gardien'
                }
              ],
              id: 'logement_assurance',
              label: "L'assurance habitation est à la charge du",
              type: 'SELECT-BINARY'
            }
          ],
          id: 'fc9f240d_b03d_4155_8ce9_252076420bc2',
          label: 'Logement de fonction',
          type: 'CATEGORY'
        }
      ],
      id: '88028ccd_cef1_4ed2_a239_d99714f29852',
      label: 'Dispositions particulières',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'lettre_mission_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        },
        {
          id: 'ref_client_lettre_mission_sepa',
          label: 'Ref client',
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['RECRUTEMENT_AGENT_LETTRE_MISSION_OPERIO']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_LETTRE_MISSION_ANNEXE_1'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'doc_lettre_mission_repartition_travaux',
          label: 'Répartition détaillée des travaux',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['RECRUTEMENT_AGENT_LETTRE_MISSION_OPERIO']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_LETTRE_MISSION_ANNEXE_2'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'doc_lettre_mission_repartition_cgu',
          label: "Conditions générales d'exécution",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['RECRUTEMENT_AGENT_LETTRE_MISSION_OPERIO']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_LETTRE_MISSION_ANNEXE_3'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'doc_lettre_mission_complementaires',
          label: 'Missions complémentaires',
          type: 'UPLOAD'
        },
        {
          id: 'lettre_mission_mandat_sepa_rum',
          label: 'Référence Unique de Mandat (RUM)',
          type: 'TEXT'
        }
      ],
      id: 'b1fb7faf_9928_47a2_b725_1c61d38ade75',
      label: 'Lettre de mission',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'mandat_sepa_rum',
          label: 'Référence Unique de Mandat (RUM)',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandat_sepa_electronique',
          label: 'Le mandat de prélèvement est-il signé de manière électronique ?',
          type: 'SELECT-BINARY'
        }
      ],
      id: 'f2705fb2_1d67_4d76_84a0_083f20f8cc8e',
      label: 'Mandat de Prélèvement SEPA',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'c2i_date',
          label: "Date d'effet du contrat",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'c2i_portage',
          label: 'Le Mandataire opte-t-il pour le portage salarial ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'c2i_portage_societe',
          label: 'Nom de la société choisie par le Mandataire',
          type: 'TEXT'
        },
        {
          id: 'c2i_marque_nom_domaine',
          label: "Nom de domaine de l'affilié",
          type: 'TEXT'
        },
        {
          id: 'c2i_redevance_logiciel',
          label: 'Montant de la redevance logiciel',
          type: 'PRICE'
        },
        {
          id: 'c2i_redevance_communication',
          label: 'Montant de la redevance communication web',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'c2i_electronique',
          label: 'Le contrat est-il signé de manière électronique ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avenant_contrat_agent_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '57ff25b3_741f_4d31_9af2_9b23ff6a893b',
      label: 'Informations Générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'annexe_mandat_secteur_market_center',
          label: "Secteur d'implantation des activités du Market Center",
          type: 'TEXT'
        },
        {
          id: 'annexe_mandat_secteur_client_market_center',
          label: 'Situation du Market Center pour réception clientèle',
          type: 'TEXT'
        },
        {
          id: 'annexe_forfait_mensuel_prepose',
          label: 'Forfait mensuel HT pour le préposé du Mega Agent',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'signature_electronique_annexe_non_carte',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '096eef36_f151_4d08_b49a_1ee075bb8fc0',
      label: "Annexe au Mandat d'Agent Commercial",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'bareme_conseille',
          label: 'Barème conseillé',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pack_pp',
                value: 'starter',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'pack_starter_document',
          label: 'Pack Starter 2016',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pack_pp',
                value: 'premium',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'pack_premium_document',
          label: 'Pack Premium 2016',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pack_pp',
                value: 'pro',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'pack_pro_document',
          label: 'Pack Pro',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pack_pp',
                value: 'business',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'pack_business_document',
          label: 'Pack Business',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pack_pp',
                value: 'starter_double',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'pack_starter_double_document',
          label: 'Pack Starter double activité',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pack_pp',
                value: 'premium_double',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'pack_premium_double_document',
          label: 'Pack Premium double activité',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pack_pp',
                value: 'pro_80',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'pack_pro_80_document',
          label: 'Pack Pro 80',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pack_pp',
                value: 'pro_80',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'pack_pro_80_offre_document',
          label: 'Offre Pack Pro 80',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pack_pp',
                value: 'pro_85',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'pack_pro_85_document',
          label: 'Pack Pro 85',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pack_pp',
                value: 'pro_85',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'pack_pro_85_offre_document',
          label: 'Offre Pack Pro 85',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'offre_application',
                value: 'exceptionnelle',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'pack_pro_exceptionnelle_document',
          label: 'Offre exceptionnelle pro',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'offres_promotionnelles',
                value: 'offre_1',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'offre_promotionnelle_1_document',
          label: 'Offre promotionnelle',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'offres_promotionnelles',
                value: 'offre_2',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'offre_promotionnelle_2_document',
          label: 'Offre promotionnelle',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'offres_promotionnelles',
                value: 'offre_3',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'offre_promotionnelle_3_document',
          label: 'Offre promotionnelle',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'offres_promotionnelles',
                value: 'offre_4',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'offre_promotionnelle_4_document',
          label: 'Offre promotionnelle',
          type: 'UPLOAD'
        },
        {
          id: 'plan_formation',
          label: 'Plan de formation',
          type: 'UPLOAD'
        },
        {
          id: 'convention_adhesion',
          label: "Convention d'adhésion",
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'c2i_doc_deontologie',
          label: 'Code de déontologie Immobilier',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'c2i_doc_deontologie_groupe',
          label: 'Code de déontologie Groupe C2i',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'c2i_doc_remuneration',
          label: 'Rémunération Groupe C2i',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'c2i_doc_cahier_charge_booster',
          label: 'Cahier des charges du booster',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'c2i_doc_territoire',
          label: 'Territoire proactif en exclusivité',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_C2I_CERTIFICAT_MARQUE'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'c2i_marque_doc_certificat',
          label: "Certificat d'enregistrement Marque contractuelle",
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'c2i_marque_doc_territoire',
          label: 'Territoire',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'c2i_marque_doc_charte',
          label: 'Charte graphique',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'c2i_marque_doc_image',
          label: "Droit à l'image",
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'c2i_marque_doc_explicatif_licence',
          label: 'Explicatif licence de marque',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'c2i_marque_doc_quotas',
          label: 'Quotas',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'regle_collaboration_interconseiller',
          label: 'Règles de collaboration interconseiller',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'performance_mandataire',
          label: 'Performance du Mandataire',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'contrat_prestation',
          label: 'Contrat de prestation',
          type: 'UPLOAD'
        }
      ],
      id: '53a0202b_b344_4b0a_9b33_cb008d9e52a3',
      label: 'Documents',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__OPERATION__RECRUTEMENT__AGENT__GENERAL',
  label: 'Recrutement Agent - Général',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__OPERATION__RECRUTEMENT__AGENT__GENERAL',
  specificTypes: ['OPERATION', 'RECRUTEMENT', 'AGENT', 'GENERAL'],
  type: 'RECORD'
};
