// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordPersonneFisica: LegalRecordTemplate = {
  config: {
    tags: {
      email: {
        questionId: 'email',
        order: 0
      },
      teléfono: {
        questionId: 'telephone',
        format: 'PHONE',
        order: 1
      }
    },
    recordLinks: [],
    search: ['nom', 'prenoms', 'email', 'telephone'],
    duplicate: ['nom', 'prenoms']
  },
  form: [
    {
      children: [
        {
          choices: [
            {
              id: 'monsieur',
              label: '<PERSON><PERSON><PERSON>'
            },
            {
              id: 'madame',
              label: '<PERSON><PERSON><PERSON>'
            }
          ],
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'sexe',
          label: 'Civilidad',
          type: 'SELECT'
        },
        {
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'nom',
          label: 'Nombre',
          type: 'TEXT',
          uppercase: 'UPPERCASE'
        },
        {
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'prenoms',
          label: 'Apellidos',
          type: 'TEXT',
          uppercase: 'WORD'
        },
        {
          id: 'informations_personnelles_date_naissance',
          label: 'Fecha de nacimiento',
          type: 'DATE'
        },
        {
          id: 'informations_personnelles_pays_naissance',
          label: 'Nacionalidad',
          type: 'TEXT'
        },
        {
          id: 'informations_personnelles_ville_naissance',
          label: 'Ville de naissance',
          type: 'TEXT',
          uppercase: 'WORD'
        },
        {
          id: 'informations_personnelles_nationalite',
          label: 'Pays de nationalité',
          type: 'COUNTRY'
        },
        {
          id: 'informations_personnelles_profession',
          label: 'Profesión',
          type: 'TEXT',
          uppercase: 'WORD'
        },
        {
          id: 'adresse',
          label: 'Dirección',
          type: 'TEXT'
        },
        {
          id: 'email',
          label: 'Correo electrónico',
          type: 'EMAIL'
        },
        {
          description: 'Es preferible un teléfono móvil para las firmas electrónicas.',
          id: 'telephone',
          label: 'Número de teléfono',
          type: 'PHONE'
        },
        {
          id: 'carte_identite_numero',
          label: 'Provisto de D.N.I. n°',
          type: 'TEXT'
        },
        {
          id: 'carte_professionnelle',
          label: 'Experto Inmobiliario n°',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'celibataire',
              label: 'Soltero'
            },
            {
              id: 'marie',
              label: 'Casado'
            },
            {
              id: 'divorce',
              label: 'Divorciado'
            },
            {
              id: 'veuf',
              label: 'Viudo'
            }
          ],
          id: 'situation_maritale',
          label: 'Estado civil',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'monsieur',
              label: 'Señor'
            },
            {
              id: 'madame',
              label: 'Señora'
            }
          ],
          id: 'sexe_conjoint',
          label: 'Civilidad del cónyuge',
          type: 'SELECT'
        },
        {
          id: 'nom_conjoint',
          label: 'Nombre del cónyuge',
          type: 'TEXT',
          uppercase: 'UPPERCASE'
        },
        {
          id: 'prenoms_conjoint',
          label: 'Apellidos del cónyuge',
          type: 'TEXT',
          uppercase: 'WORD'
        },
        {
          id: 'carte_identite_numero_conjoint',
          label: 'Provisto de D.N.I. del cónyuge n°',
          type: 'TEXT'
        },
        {
          children: [
            {
              id: 'societe_nom',
              label: 'Denominación de la sociedad',
              type: 'TEXT'
            },
            {
              id: 'societe_nif',
              label: 'N° NIF',
              type: 'TEXT'
            },
            {
              id: 'societe_adresse',
              label: 'Dirección',
              type: 'TEXT'
            }
          ],
          id: '09dd4120_3fiu4_hdt4_0jf4_f347d7386oi9',
          label: 'Empresa',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'banque_iban',
              label: 'IBAN',
              type: 'TEXT'
            },
            {
              id: 'banque_bic',
              label: 'BIC/SWIFT',
              type: 'TEXT'
            },
            {
              id: 'banque_bic_intermediario',
              label: 'BIC/SWIFT de banco intermediario',
              type: 'TEXT'
            },
            {
              id: 'banque_nom',
              label: 'Nombre del banco',
              type: 'TEXT'
            },
            {
              id: 'banque_adresse',
              label: 'Dirección del banco',
              type: 'TEXT'
            },
            {
              id: 'banque_banco',
              label: 'N° Banco',
              type: 'TEXT'
            },
            {
              id: 'banque_entidad',
              label: 'N° Entidad',
              type: 'TEXT'
            },
            {
              id: 'banque_cuenta',
              label: 'N° Cuenta',
              type: 'TEXT'
            }
          ],
          id: '09dd4120_34rg5_37y1_pog2_f347d73tgf4z',
          label: 'Banco',
          type: 'CATEGORY'
        }
      ],
      id: '09dd4120_3423_48ce_9fb3_f347d7386239',
      label: 'Informationes',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__PERSONNE__FISICA',
  label: 'FISICA',
  mynotaryTemplate: false,
  originTemplate: null,
  specificTypes: ['PERSONNE', 'FISICA'],
  type: 'RECORD'
};
