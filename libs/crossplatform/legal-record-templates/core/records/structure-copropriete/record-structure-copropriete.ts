// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordStructureCopropriete: LegalRecordTemplate = {
  config: {
    recordLinks: [
      {
        id: 'COPROPRIETE',
        specificTypes: ['COMPOSITION', 'COPROPRIETE'],
        label: 'Lots'
      }
    ],
    search: ['adresse', 'denomination'],
    duplicate: ['adresse']
  },
  form: [
    {
      children: [
        {
          id: 'integration_designation_copropriete',
          label: 'Désignation de la copropriété',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'monopropriete',
          label: 'Structure en monopropriété',
          type: 'SELECT-BINARY'
        },
        {
          filters: {
            preEtatDate: true
          },
          id: 'denomination',
          label: 'Nom de la copropriété',
          type: 'TEXT'
        },
        {
          id: 'date_achevement',
          label: "Date d'achèvement de l'immeuble",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'moins_10_ans',
              label: 'Il y a moins de 10 ans',
              icon: 'calendar.svg'
            },
            {
              id: 'apres_1997',
              label: 'Après le 1er juillet 1997 (date du permis)',
              icon: 'calendar.svg'
            },
            {
              id: '1949_1997',
              label: 'Après le 1er janvier 1949',
              icon: 'calendar.svg'
            },
            {
              id: 'avant_1949',
              label: 'Avant 1949',
              icon: 'calendar.svg'
            }
          ],
          id: 'construction',
          label: 'Date de construction',
          type: 'SELECT-PICTURES'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'construction_15_ans',
          label: 'Copropriété de plus de 15 ans',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              label: 'Habitation exlusivement',
              id: 'habitation'
            },
            {
              label: 'Mixte professionnel et Habitation',
              id: 'mixte'
            },
            {
              label: 'Hors habitation',
              id: 'hors_habitation'
            }
          ],
          id: 'usage',
          label: 'Usage de la copropriété',
          type: 'SELECT'
        },
        {
          id: 'usage_libre',
          label: 'Usage de la copropriété',
          type: 'TEXT'
        },
        {
          id: 'designation',
          label: 'Désignation de la copropriété',
          type: 'TEXTAREA'
        },
        {
          id: 'nombre_lots',
          label: 'Nombre de lots dans la copropriété',
          type: 'NUMBER'
        },
        {
          id: 'nombre_lots_annexes',
          label: 'Nombre de lots annexes dans la copropriété',
          type: 'NUMBER'
        },
        {
          id: 'nombre_batiments',
          label: 'Nombre de bâtiments',
          type: 'NUMBER'
        },
        {
          id: 'reference_batiments',
          label: 'Réference du bâtiment',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description: 'Si le vendeur n’a pas ces informations alors demander les coordonnées du syndic',
          id: 'difficultes',
          label: 'La copropriété est en difficulté financière',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'electricite',
              label: 'Electrique'
            },
            {
              id: 'gaz',
              label: 'Gaz'
            },
            {
              id: 'fioul',
              label: 'Fioul'
            },
            {
              id: 'aucun',
              label: 'Aucun'
            }
          ],
          id: 'chauffage',
          label: 'Type de chauffage',
          type: 'SELECT'
        },
        {
          filters: {
            preEtatDate: true,
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'adresse',
          label: 'Adresse',
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'Non Tendue'
            },
            {
              id: 'oui',
              label: 'Tendue'
            },
            {
              id: 'tres_tendue',
              label: 'Très Tendue'
            }
          ],
          description: 'Code postal détecté dans une zone non tendue, tendue ou très tendue',
          id: 'zone_tendue',
          label: 'Commune située dans une zone',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'zone_tendue',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'zone_loyer_reference',
          label: 'La copropriété est-elle située dans une zone soumise au loyer de référence ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'copropriete_cadastre',
          label: 'Ajouter les références cadastrales',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'copropriete_double_cadastre',
          label: 'La copropriété est-elle assise sur deux communes ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'copropriete_cadastre_indication',
          label: 'Indiquer les références cadastrales au contrat',
          type: 'SELECT-BINARY'
        },
        {
          id: 'quartier',
          label: 'Quartier',
          type: 'TEXT'
        },
        {
          children: [
            {
              id: 'cadastre_parcelles_code_departement',
              label: 'Code département',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_code_topad',
              label: 'Code Commune TOPAD',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_code_insee',
              label: 'Code Commune INSEE',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_commune',
              label: 'Nom commune',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_prefixe',
              label: 'Préfixe',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_section',
              label: 'Section',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_parcelle',
              label: 'Parcelle',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_lieudit',
              label: 'Lieudit',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_h',
              label: 'Hectare',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_a',
              label: 'Are',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_c',
              label: 'Centiare',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_cadastre_non_renove',
              label: 'Référence cadastre non rénové',
              type: 'TEXT'
            }
          ],
          id: 'cadastre_parcelles',
          label: 'Ajouter les références cadastrales',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: 'Section '
              },
              {
                type: 'VARIABLE',
                value: 'cadastre_parcelles_section'
              },
              {
                type: 'TEXT',
                value: ' n° '
              },
              {
                type: 'VARIABLE',
                value: 'cadastre_parcelles_parcelle'
              }
            ],
            max: 1000,
            min: 1,
            type: 'REF_CADASTRALES'
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'copropriete_cadastre_renove',
          label: 'Des anciennes références cadastrales sont-elles à mentionner ?',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'cadastre_parcelles_renove_code_departement',
              label: 'Code département',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_code_topad',
              label: 'Code Commune TOPAD',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_code_insee',
              label: 'Code Commune INSEE',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_commune',
              label: 'Nom commune',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_prefixe',
              label: 'Préfixe',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_section',
              label: 'Section',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_parcelle',
              label: 'Parcelle',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_lieudit',
              label: 'Lieudit',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_renove_h',
              label: 'Hectare',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_renove_a',
              label: 'Are',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_renove_c',
              label: 'Centiare',
              type: 'NUMBER'
            }
          ],
          id: 'cadastre_parcelles_renove',
          label: 'Anciennes Références cadastrales',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: 'Section '
              },
              {
                type: 'VARIABLE',
                value: 'cadastre_parcelles_renove_section'
              },
              {
                type: 'TEXT',
                value: ' n° '
              },
              {
                type: 'VARIABLE',
                value: 'cadastre_parcelles_renove_parcelle'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          children: [
            {
              id: 'cadastre_parcelles_2_code_departement',
              label: 'Code département',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_2_code_topad',
              label: 'Code Commune TOPAD',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_2_code_insee',
              label: 'Code Commune INSEE',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_2_commune',
              label: 'Nom commune',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_2_prefixe',
              label: 'Préfixe',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_2_section',
              label: 'Section',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_2_parcelle',
              label: 'Parcelle',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_2_lieudit',
              label: 'Lieudit',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelles_2_h',
              label: 'Hectare',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_2_a',
              label: 'Are',
              type: 'NUMBER'
            },
            {
              id: 'cadastre_parcelles_2_c',
              label: 'Centiare',
              type: 'NUMBER'
            }
          ],
          conditions: [[]],
          id: 'cadastre_parcelles_2',
          label: 'Références cadastrales de la copropriété de la seconde commune',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: 'Section '
              },
              {
                type: 'VARIABLE',
                value: 'cadastre_parcelles_2_section'
              },
              {
                type: 'TEXT',
                value: ' n° '
              },
              {
                type: 'VARIABLE',
                value: 'cadastre_parcelles_2_parcelle'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          id: 'cadastre_parcelle_libre',
          label: 'Parcelle cadastrale',
          type: 'TEXT'
        },
        {
          id: 'cadastre_parcelle_section_libre',
          label: 'Section cadastrale',
          type: 'TEXT'
        },
        {
          id: 'cadastre_parcelle_numero_libre',
          label: 'Numero cadastral',
          type: 'TEXT'
        },
        {
          id: 'programme_origine_date',
          label: "Date de l'origine de propriété",
          type: 'DATE'
        },
        {
          id: 'programme_origine_notaire',
          label: 'Nom et prénom du notaire',
          type: 'TEXT'
        },
        {
          id: 'programme_origine_propriete_complete',
          label: 'Origine de propriété',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cuve_stockage_petrolier',
          label: 'Cuve de stockage de produits pétroliers',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'monument_historique',
          label: 'Coproprieté située dans un périmètre de protection des monuments historiques',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'proximite_activites_agricoles',
          label: "Proximité d'activités agricoles - industrielles - artisanales - commerciales",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'espace_boise_classe',
          label: 'Espace boisé classé',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'presence_parc_eolienne',
          label: "Présence d'un champ d'éoliennes",
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'presence_parc_eolienne',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'parc_eolienne_projet_document',
          label: 'Carte des projets éoliens',
          type: 'UPLOAD'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'servitudes',
              label: 'Présence de servitudes',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'servitudes_note',
              label: 'Les servitudes sont-elles reprises dans une note annexée ?',
              type: 'SELECT-BINARY'
            },
            {
              id: 'servitude_type',
              label: 'Type de servitude',
              type: 'TEXT'
            },
            {
              id: 'servitude_notaire_nom',
              label: 'Nom du notaire ayant reçu la servitude',
              type: 'TEXT'
            },
            {
              id: 'servitude_notaire_ville',
              label: 'Ville du notaire',
              type: 'TEXT'
            },
            {
              id: 'servitude_notaire_date',
              label: 'Date de constitution de la servitude',
              type: 'DATE'
            },
            {
              conditions: [
                [
                  {
                    id: 'servitudes',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'servitudes_liste',
              label: 'Liste des servitudes',
              type: 'TEXTAREA'
            },
            {
              id: 'servitude_description_litterale',
              label: 'Reprise littérale de la servitude',
              type: 'TEXTAREA'
            }
          ],
          id: '12aeb43f_7dbe_48cb_9e0a_34bcb30b4b44',
          label: 'Servitudes',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              description:
                'immeubles situés dans des zones spécifiques (zone protégée par un plan de sauvegarde et de mise en valeur; monuments historiques...) pouvant impliquer des contraintes au pour travaux intérieurs et extérieurs.',
              id: 'situation_particuliere',
              label: 'Ajouter une situation particulière',
              type: 'SELECT-BINARY'
            },
            {
              description: 'Reprendre l’article correspondant dans le titre de propriété',
              id: 'situation_particuliere_description',
              label: 'Description de la situation particulière',
              type: 'TEXT'
            }
          ],
          id: '81e53c34_dc8a_4397_9dec_438630db58dd',
          label: 'Situation particulière',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'statut_reglement_date',
              label: 'Date de signature des statuts et règlement de copropriété',
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'reglement_statut',
              label: 'Règlement de copropriété déjà reçu par notaire',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'reglement_statut',
                    value: 'non',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'reglement_projet_date',
              label: 'Date du projet du règlement de copropriété',
              type: 'DATE'
            },
            {
              conditions: [
                [
                  {
                    id: 'reglement_statut',
                    value: 'non',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'reglement_projet_geometre',
              label: 'Nom du géometre établissant le projet',
              type: 'TEXT'
            },
            {
              id: 'reglement_geometre_nom',
              label: "Nom du géometre ayant réalisé l'EDD",
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'reglement_statut',
                    value: 'non',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'reglement_projet_geometre_adresse',
              label: 'Adresse du géometre établissant le projet',
              type: 'ADDRESS'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'reglement_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'reglement_spf',
              label: 'Règlement publié au SPF',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'rcp',
                      label: 'RCP'
                    },
                    {
                      id: 'edd',
                      label: 'EDD'
                    }
                  ],
                  id: 'reglement_list_reglement_objet_modificatif',
                  label: 'Objet du modificatif',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'reglement_list_reglement_date',
                  label: 'Date du règlement ou modificatif',
                  type: 'DATE'
                },
                {
                  id: 'reglement_list_reglement_date_texte',
                  label: 'Date du règlement ou modificatif',
                  type: 'TEXT'
                },
                {
                  id: 'reglement_list_reglement_notaire_nom',
                  label: "Nom du notaire ayant rédigé l'acte",
                  type: 'TEXT'
                },
                {
                  id: 'reglement_list_reglement_notaire_ville',
                  label: "Ville du notaire ayant rédigé l'acte",
                  type: 'TEXT'
                },
                {
                  id: 'reglement_list_reglement_notaire_spf',
                  label: 'Service de publicité foncière',
                  type: 'TEXT'
                },
                {
                  id: 'reglement_list_reglement_notaire_spf_date',
                  label: 'Date de publication au SPF',
                  type: 'DATE'
                },
                {
                  id: 'reglement_list_reglement_notaire_spf_volume',
                  label: 'Volume',
                  type: 'TEXT'
                },
                {
                  id: 'reglement_list_reglement_notaire_spf_numero',
                  label: 'Numéro',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'reglement_list_reglement_attestation_rectificative',
                  label: "Cet acte a fait l'objet d'une attestation rectificative",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'reglement_list_reglement_attestation_rectificative_date',
                  label: "Date de l'attestation rectificative",
                  type: 'DATE'
                },
                {
                  id: 'reglement_list_reglement_attestation_rectificative_date_publication',
                  label: "Date de publication de l'attestation rectificative",
                  type: 'DATE'
                },
                {
                  id: 'reglement_list_reglement_attestation_rectificative_volume',
                  label: 'Volume - attestation rectificative',
                  type: 'TEXT'
                },
                {
                  id: 'reglement_list_reglement_attestation_rectificative_numero',
                  label: 'Numéro - attestation rectificative',
                  type: 'TEXT'
                }
              ],
              id: 'reglement_list_reglement',
              label: 'RCP / EDD et modificatifs',
              repetition: {
                label: [
                  {
                    type: 'TEXT',
                    value: ' Maître '
                  },
                  {
                    type: 'VARIABLE',
                    value: 'reglement_list_reglement_notaire_nom'
                  }
                ],
                max: 1000,
                min: 1
              },
              type: 'REPEAT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'reglement_copropriete_fonctionnelle',
              label: 'Copropriété fonctionnelle (Première AG déjà tenue et budget établi)',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'frais_constitution_copropriete',
              label: 'Récupération des frais de constitution de copropriété',
              type: 'SELECT-BINARY'
            },
            {
              id: 'frais_constitution_copropriete_montant',
              label: 'Montant des frais de constitution de copropriété',
              type: 'PRICE'
            }
          ],
          id: '0a8b6d9d_745f_4de5_803f_dc6b480c6aa0',
          label: 'RCP / EDD',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'ascenseur',
              label: 'Ascenseur dans la copropriété',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'integration_ascenseur',
              label: 'INTEGRATION - Ascenseur dans la copropriété',
              type: 'SELECT-BINARY'
            },
            {
              id: 'integration_clause_ascenseur',
              label: 'Clause Ascenseur',
              type: 'TEXTAREA'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'ascenseur_date_2000',
              label: 'Ascenseur installé après le 27 Août 2000',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'ascenseur_rapport_effectue',
              label: 'Rapport de mise en conformité effectué',
              type: 'SELECT-BINARY'
            },
            {
              id: 'ascenseur_rapport_effectue_nom',
              label: 'Nom de la société effectuant le rapport Ascenseur',
              type: 'TEXT'
            },
            {
              id: 'ascenseur_rapport_effectue_date',
              label: 'Date du rapport Ascenseur',
              type: 'DATE'
            },
            {
              id: 'ascenseur_entretien_nom',
              label: "Nom de la société chargée de l'entretien des ascenseurs",
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'ascenseur_rapport_effectue',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'ascenseur_rapport_effectue_document',
              label: 'Rapport conformité ascenseur',
              type: 'UPLOAD'
            }
          ],
          id: 'df7f19a2_408c_438d_ab51_ab9c74ecde92',
          label: 'Ascenseur',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'emprunt_collectif',
          label: 'Un emprunt collectif est-il souscrit par la copropriété ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'immatriculation_statut',
          label: 'Syndicat des copropriétaires immatriculé',
          type: 'SELECT-BINARY'
        },
        {
          description: 'exemple : AB1-999-255',
          id: 'numero_immatriculation',
          label: "Numéro d'immmatriculation du syndicat",
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'immatriculation_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'immatriculation_attestation',
          label: "Attestation d'immatriculation",
          type: 'UPLOAD'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'carnet_entretien_statut',
              label: "Présence d'un carnet d'entretien",
              type: 'SELECT-BINARY'
            },
            {
              conditionalTitles: [
                {
                  conditions: [
                    [
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME',
                          'OPERATION__CLESENCE__IMMOBILIER__VENTE',
                          'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF',
                          'OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF'
                        ]
                      }
                    ]
                  ],
                  title: "Document ALUR - Carnet d'entretien"
                }
              ],
              conditions: [
                [
                  {
                    id: 'carnet_entretien_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ],
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ]
              ],
              id: 'carnet_entretien_document',
              label: "Carnet d'entretien",
              type: 'UPLOAD'
            }
          ],
          id: '308d98bc_a4dc_42c0_a93e_27c242fc8fc0',
          label: "Carnet d'entretien",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'fiche_synthetique_statut',
              label: "Présence d'une fiche synthétique de la copropriété",
              type: 'SELECT-BINARY'
            },
            {
              conditionalTitles: [
                {
                  conditions: [
                    [
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME',
                          'OPERATION__CLESENCE__IMMOBILIER__VENTE',
                          'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF',
                          'OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF'
                        ]
                      }
                    ]
                  ],
                  title: 'Document ALUR - Fiche Synthétique'
                }
              ],
              conditions: [
                [
                  {
                    id: 'fiche_synthetique_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ],
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ]
              ],
              id: 'document_fiche_synthetique',
              label: 'Fiche synthétique',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'reglement_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'fiche_synthetique_date',
              label: "Date d'établissement de la fiche synthétique de la copropriété",
              type: 'DATE'
            },
            {
              id: 'fiche_synthetique_date_pvci',
              label: "Date d'établissement de la fiche synthétique de la copropriété",
              type: 'DATE'
            }
          ],
          id: '8e62210d_e943_40b1_b187_b3d9a41dadcf',
          label: 'Fiche synthétique',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'assemblees_generales_date_ancienne',
              label: 'Date de la dernière Assemblée Générale des copropriétaires',
              type: 'DATE'
            },
            {
              id: 'assemblees_generales_date_future',
              label: 'Date de la prochaine Assemblée Générale des copropriétaires',
              type: 'DATE'
            }
          ],
          id: 'a3140c63_ade5_4e56_a576_a202dd057769',
          label: 'Assemblée générale',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'syndic_presence',
              label: "Présence d'un syndic",
              type: 'SELECT-BINARY'
            },
            {
              id: 'syndic_pv_remis_nombre',
              label: 'Nombre de procès-verbaux remis',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'syndic_pv_ag_trois_annees',
              label: 'PV d’AG des 3 dernières années',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'syndic_pv_ag_un_seul',
              label: "Un seul PV remis à l'Acquéreur",
              type: 'SELECT-BINARY'
            },
            {
              id: 'syndic_pv_un_seul_remis_date',
              label: 'Date du procès-verbal',
              type: 'TEXT'
            },
            {
              id: 'syndic_pv_remis_date',
              label: 'Date des procès-verbaux',
              type: 'TEXT'
            },
            {
              children: [
                {
                  id: 'syndic_pv_liste_date',
                  label: 'Date',
                  type: 'DATE'
                }
              ],
              id: 'syndic_pv_liste',
              label: "Liste des PV d'AG",
              repetition: {
                label: [
                  {
                    type: 'TEXT',
                    value: 'Date '
                  },
                  {
                    type: 'VARIABLE',
                    value: 'syndic_pv_liste_date'
                  }
                ],
                max: 1000,
                min: 1
              },
              type: 'REPEAT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'syndic_provisoire',
              label: 'Syndic provisoire',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'syndic_benevole_statut',
              label: 'Syndic Bénévole',
              type: 'SELECT-BINARY'
            },
            {
              id: 'syndic_coordonnees',
              label: 'Coordonnées du syndic',
              type: 'TEXT'
            },
            {
              id: 'syndic_nom_libre',
              label: 'Nom du syndic',
              type: 'TEXT'
            },
            {
              id: 'syndic_nom_libre_adresse',
              label: 'Adresse du syndic',
              type: 'ADDRESS'
            },
            {
              id: 'syndic_nom_telephone',
              label: 'Numéro de téléphone du Syndic',
              type: 'PHONE'
            },
            {
              id: 'syndic_assurance',
              label: "Organisme d'assurance de la copropriété",
              type: 'TEXT'
            },
            {
              id: 'syndic_assurance_adresse',
              label: "Adresse de la compagnie d'assurance",
              type: 'ADDRESS'
            },
            {
              id: 'syndic_assurance_numero',
              label: "Numéro de police d'assurance",
              type: 'TEXT'
            },
            {
              id: 'syndic_assurance_intermediaire_nom',
              label: "Nom de l'intermédiaire",
              type: 'TEXT'
            },
            {
              id: 'syndic_absence_liste_alur',
              label: "Liste des documents ALUR remis à l'Acquéreur",
              type: 'TEXTAREA'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Un syndic professionnel',
                      id: 'pro'
                    },
                    {
                      label: 'Un syndic bénévole',
                      id: 'benevole'
                    }
                  ],
                  id: 'gestion_syndic_gestion_nature_syndic',
                  label: 'La copropriété est gérée par',
                  type: 'SELECT'
                },
                {
                  children: [
                    {
                      id: 'gestion_syndic_gestion_copropriete_syndic_pro_nom',
                      label: 'Nom du syndic',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_syndic_gestion_copropriete_syndic_pro_adresse',
                      label: 'Adresse du syndic',
                      type: 'ADDRESS'
                    },
                    {
                      id: 'gestion_syndic_gestion_copropriete_syndic_pro_email',
                      label: 'Email du syndic',
                      type: 'EMAIL'
                    },
                    {
                      id: 'gestion_syndic_gestion_copropriete_syndic_pro_telephone',
                      label: 'Numéro de téléphone du syndic',
                      type: 'PHONE'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'gestion_syndic_gestion_nature_syndic',
                        type: 'EQUALS',
                        value: 'pro'
                      },
                      {
                        id: 'syndic_presence',
                        value: 'oui',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'gestion_syndic_ee3a2379_b0e3_4a0e_970e_325fabcd383b',
                  label: 'Informations sur le syndic professionnel',
                  type: 'CATEGORY'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'homme',
                          label: 'Monsieur'
                        },
                        {
                          id: 'femme',
                          label: 'Madame'
                        }
                      ],
                      id: 'gestion_syndic_gestion_copropriete_syndic_benevole_civilite',
                      label: 'Civilité',
                      type: 'SELECT'
                    },
                    {
                      id: 'gestion_syndic_gestion_copropriete_syndic_benevole_nom',
                      label: 'Nom',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_syndic_gestion_copropriete_syndic_benevole_prenom',
                      label: 'Prénom',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_syndic_gestion_copropriete_syndic_benevole_adresse',
                      label: 'Adresse',
                      type: 'ADDRESS'
                    },
                    {
                      id: 'gestion_syndic_gestion_copropriete_syndic_benevole_email',
                      label: 'Email',
                      type: 'EMAIL'
                    },
                    {
                      id: 'gestion_syndic_gestion_copropriete_syndic_benevole_telephone',
                      label: 'Telephone',
                      type: 'PHONE'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'gestion_syndic_gestion_nature_syndic',
                        type: 'EQUALS',
                        value: 'benevole'
                      },
                      {
                        id: 'syndic_presence',
                        value: 'oui',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'gestion_syndic_e1d97075_6f0c_4367_a844_80ac84c2db07',
                  label: 'Informations sur le syndic bénévole',
                  type: 'CATEGORY'
                }
              ],
              conditions: [
                [
                  {
                    id: 'syndic_presence',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'gestion_syndic',
              label: 'Syndics',
              repetition: {
                label: [
                  {
                    type: 'VARIABLE',
                    value: 'gestion_syndic_gestion_copropriete_syndic_pro_nom'
                  },
                  {
                    type: 'TEXT',
                    value: ' '
                  },
                  {
                    type: 'VARIABLE',
                    value: 'gestion_syndic_gestion_copropriete_syndic_benevole_nom'
                  }
                ],
                max: 1000,
                min: 0
              },
              type: 'REPEAT'
            }
          ],
          id: '7811284d_2b67_4e67_be69_34da4c6ec68e',
          label: 'Information sur le Syndic de Copropriété',
          type: 'CATEGORY'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'external_cadastre_situation',
          label: 'Plan Cadastral',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'servitudes',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'servitude_acte',
          label: 'Rappel de servitude',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'coordonnees_informations_syndic',
          label: 'Coordonnées et informations du Syndic',
          type: 'UPLOAD'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME',
                      'OPERATION__CLESENCE__IMMOBILIER__VENTE',
                      'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF',
                      'OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF'
                    ]
                  }
                ]
              ],
              title: 'Document ALUR - Règlement de copropriété et modificatifs'
            }
          ],
          conditions: [
            [
              {
                conditions: [
                  {
                    id: 'reglement_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME',
                  'OPERATION__CLESENCE__IMMOBILIER__VENTE',
                  'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF',
                  'OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF'
                ]
              }
            ],
            [
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: [
                  'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME',
                  'OPERATION__CLESENCE__IMMOBILIER__VENTE',
                  'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF',
                  'OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF'
                ]
              }
            ]
          ],
          id: 'reglement_copropriete',
          label: 'Règlement de copropriété, EDD et modificatifs',
          type: 'UPLOAD'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: [
                      'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME',
                      'OPERATION__CLESENCE__IMMOBILIER__VENTE',
                      'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF',
                      'OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF'
                    ]
                  }
                ]
              ],
              title: "Document ALUR - PV d'AG des trois dernières années"
            }
          ],
          conditions: [
            [
              {
                id: 'reglement_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'syndic_presence',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'pv_ag',
          label: "Procès verbaux d'assemblée générale des trois dernières années",
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'plans_lots_copropriete',
          label: 'Plans des lots',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'plan_masse',
          label: 'Plan de masse',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'plan_stationnement',
          label: 'Plan des stationnement',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'plan_sous_sol',
          label: 'Plan des sous-sols',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'plan_etages',
          label: 'Plan des étages',
          type: 'UPLOAD'
        },
        {
          id: 'plan_ensemble_copropriete',
          label: 'Plan d’ensemble de la copropriété',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'notice_descriptive',
          label: 'Notice descriptive',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'echeancier',
          label: 'Echéancier',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'infos_client_vefa',
          label: 'Infos client en VEFA',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'syndic_presence',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeExcludedInOperationConfig: true
          },
          id: 'carnet_entretien',
          label: "Carnet d'entretien",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'syndic_presence',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeExcludedInOperationConfig: true
          },
          id: 'fiche_synthetique',
          label: 'Fiche synthetique',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'location_equipement_aucun',
              label: 'Aucun équipement commun'
            },
            {
              id: 'location_equipement_velo',
              label: 'Local à vélo'
            },
            {
              id: 'location_equipement_poubelle',
              label: 'Local poubelle'
            },
            {
              id: 'location_equipement_antenne',
              label: 'Antenne collective'
            },
            {
              id: 'location_equipement_laverie',
              label: 'Local Laverie'
            },
            {
              id: 'location_equipement_sport',
              label: 'Salle de sport'
            },
            {
              id: 'location_equipement_autre',
              label: 'Autre'
            }
          ],
          id: 'location_liste_equipement',
          label: "Element d'équipement de la copropriété",
          multiple: true,
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'location_liste_equipement',
                value: 'location_equipement_autre',
                type: 'CONTAINS'
              }
            ]
          ],
          id: 'location_liste_equipement_autre',
          label: "Autre élément d'équipements",
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'reglement_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'domofrance_fiche_synthetique',
          label: 'Fiche synthétique de la copropriété',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'reglement_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'domofrance_edd',
          label: 'Règlements de copropriété et modificatifs',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'reglement_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'domofrance_pv',
          label: 'Procès verbaux des trois dernières années',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'reglement_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'domofrance_carnet_entretien',
          label: "Carnet d'entretien de l'immeuble",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'reglement_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'domofrance_liste_travaux',
          label: 'Liste des travaux réalisés sur les parties communes depuis les cinq dernières années',
          type: 'UPLOAD'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'ago',
                  label: 'Ordinaire'
                },
                {
                  id: 'age',
                  label: 'Extraordinaire'
                }
              ],
              id: 'pv_ag_liste_pv_ag_type',
              label: 'Assemblée générale',
              type: 'SELECT'
            },
            {
              id: 'pv_ag_liste_date_pv_ag_liste',
              label: "Date de l'assemblée générale",
              type: 'DATE'
            },
            {
              id: 'pv_ag_liste_pv_ag_doc',
              label: "Procès verbal d'assemblée générale",
              type: 'UPLOAD'
            }
          ],
          id: 'pv_ag_liste',
          label: "Liste des procès verbaux d'assemblée générale",
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: 'PV en date du '
              },
              {
                type: 'VARIABLE',
                value: 'pv_ag_liste_date_pv_ag_liste'
              }
            ],
            max: 1000,
            min: 0
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'fibre_optique',
          label: 'Le copropriété est-elle reliée à la fibre optique ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'adresse',
                valuePath: 'zip',
                value: '35000',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'adresse',
                valuePath: 'zip',
                value: '35200',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'adresse',
                valuePath: 'zip',
                value: '35700',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_PSMV_RENNES'
          },
          id: 'psmv_rennes',
          label: 'Plan de sauvegarde et de mise en valeur - Rennes',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'adresse',
                valuePath: 'zip',
                value: '35400',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_MEUBLE_STMALO'
          },
          id: 'meuble_stmalo',
          label: 'Délibération - Meublé de Tourisme St Malo',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'conseil_syndical',
          label: 'Conseil syndical',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'concierge',
          label: 'Concierge',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avance_tresorerie',
          label: 'Avance de trésorerie',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'fonds_roulement',
          label: 'Fonds de roulement',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'fonds_prevoyance',
          label: 'Fonds de prévoyance',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'cdc_edd_rcp',
              label: 'Règlement de copropriété / EDD et modificatifs',
              type: 'UPLOAD'
            },
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'cdc_pv_ag',
              label: "PV d'AG des 3 dernières années",
              type: 'UPLOAD'
            },
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'cdc_fiche_synthetique',
              label: 'Fiche synthétique',
              type: 'UPLOAD'
            },
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'cdc_carnet_entretien',
              label: "Carnet d'entretien",
              type: 'UPLOAD'
            }
          ],
          id: 'b8c14024_bb79_4a53_a2db_974639ce8014',
          label: 'CONDITION_BLOCK_CDC_DOCUMENT',
          type: 'CONDITION_BLOCK'
        }
      ],
      id: 'a74f4b49_ddee_4b7c_8f18_73be227f3151',
      label: 'Informations générales sur la Copropriété',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description: "Un volume est une division supplémentaire d'un grand ensemble immobilier",
          id: 'presence_volume',
          label: 'Structure divisée en volume',
          type: 'SELECT-BINARY'
        },
        {
          id: 'volume_clause_libre',
          label: 'Clause sur la division en volumes',
          type: 'TEXTAREA'
        },
        {
          children: [
            {
              id: 'numero_lot_volume',
              label: 'Numéro du volume',
              type: 'TEXT'
            },
            {
              filters: {
                preEtatDate: true
              },
              id: 'denomination',
              label: "Dénomination de l'ensemble immobilier",
              type: 'TEXT'
            },
            {
              id: 'nombre_lots',
              label: 'Nombre de lots volume',
              type: 'NUMBER'
            },
            {
              filters: {
                mustBeExcludedInOperationConfig: true
              },
              id: 'plans_lots_volume',
              label: 'Plans des lots volume',
              type: 'UPLOAD'
            },
            {
              children: [
                {
                  id: 'eddv_list_date',
                  label: "Date de l'état descriptif ou modificatif",
                  type: 'DATE'
                },
                {
                  id: 'eddv_list_notaire_nom',
                  label: "Nom du notaire ayant rédigé l'acte",
                  type: 'TEXT'
                },
                {
                  id: 'eddv_list_notaire_ville',
                  label: "Ville du notaire ayant rédigé l'acte",
                  type: 'TEXT'
                }
              ],
              id: 'eddv_list',
              label: 'Etat Descriptif de Division en volume (EDDV) et modificatifs',
              repetition: {
                label: [
                  {
                    type: 'VARIABLE',
                    value: 'eddv_list_notaire_nom'
                  }
                ],
                max: 1000,
                min: 1
              },
              type: 'REPEAT'
            },
            {
              filters: {
                mustBeExcludedInOperationConfig: true
              },
              id: 'eddv',
              label: 'Etat descriptif de division en volume et modificatifs',
              type: 'UPLOAD'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'asl',
                      label: 'Une association syndicale libre'
                    },
                    {
                      id: 'aful',
                      label: 'Une association foncière urbaine libre'
                    },
                    {
                      id: 'aucun',
                      label: 'Aucun organe'
                    },
                    {
                      id: 'autre',
                      label: 'Un autre organe de gestion'
                    }
                  ],
                  id: 'gestion_volume_gestion_volume_type',
                  label: 'Le volume est géré par',
                  type: 'SELECT'
                },
                {
                  children: [
                    {
                      conditions: [
                        [
                          {
                            id: 'gestion_volume_gestion_volume_type',
                            type: 'EQUALS',
                            value: 'autre'
                          },
                          {
                            id: 'presence_volume',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: 'gestion_volume_gestion_volume_autre_type',
                      label: "Autre type de l'organe de gestion",
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_autre_nom',
                      label: 'Nom de l’organe de gestion',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_autre_adresse',
                      label: 'Adresse de l’organe de gestion',
                      type: 'ADDRESS'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_autre_president_nom',
                      label: 'Nom du gérant',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_autre_president_email',
                      label: 'Adresse email du gérant',
                      type: 'EMAIL'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_autre_president_telephone',
                      label: 'Téléphone du gérant',
                      type: 'PHONE'
                    },
                    {
                      conditions: [
                        [
                          {
                            id: 'gestion_volume_gestion_volume_type',
                            type: 'EQUALS',
                            value: 'autre'
                          },
                          {
                            id: 'presence_volume',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: 'gestion_volume_gestion_volume_autre_statuts',
                      label: "Statuts de l'organe de gestion",
                      type: 'UPLOAD'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'gestion_volume_gestion_volume_type',
                        type: 'EQUALS',
                        value: 'autre'
                      },
                      {
                        id: 'presence_volume',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'gestion_volume_f6f5a1a5_996a_4930_a67e_e0a201ca6030',
                  label: 'Autre organe',
                  type: 'CATEGORY'
                },
                {
                  children: [
                    {
                      id: 'gestion_volume_gestion_volume_asl_nom',
                      label: 'Nom de l’association syndicale',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_asl_adresse',
                      label: 'Adresse de l’association syndicale',
                      type: 'ADDRESS'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_asl_president_nom',
                      label: 'Nom du président',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_asl_president_email',
                      label: 'Adresse email du président',
                      type: 'EMAIL'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_asl_president_telephone',
                      label: 'Téléphone du président',
                      type: 'PHONE'
                    },
                    {
                      conditions: [
                        [
                          {
                            id: 'gestion_volume_gestion_volume_type',
                            type: 'EQUALS',
                            value: 'asl'
                          },
                          {
                            id: 'presence_volume',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: 'gestion_volume_gestion_volume_statuts_asl',
                      label: "Statuts de l'Association Syndicale Libre",
                      type: 'UPLOAD'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'gestion_volume_gestion_volume_type',
                        type: 'EQUALS',
                        value: 'asl'
                      },
                      {
                        id: 'presence_volume',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'gestion_volume_0edeac6a_a426_4794_8b36_85360292cebf',
                  label: 'Association syndicale libre',
                  type: 'CATEGORY'
                },
                {
                  children: [
                    {
                      id: 'gestion_volume_gestion_volume_aful_nom',
                      label: 'Nom de l’association foncière',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_aful_adresse',
                      label: 'Adresse de l’association foncière',
                      type: 'ADDRESS'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_aful_president_nom',
                      label: 'Nom du président',
                      type: 'TEXT'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_aful_president_email',
                      label: 'Adresse email du président',
                      type: 'EMAIL'
                    },
                    {
                      id: 'gestion_volume_gestion_volume_aful_president_telephone',
                      label: 'Téléphone du président',
                      type: 'PHONE'
                    },
                    {
                      conditions: [
                        [
                          {
                            id: 'gestion_volume_gestion_volume_type',
                            type: 'EQUALS',
                            value: 'aful'
                          },
                          {
                            id: 'presence_volume',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: 'gestion_volume_gestion_volume_statuts_aful',
                      label: "Statuts de l'Association Foncière Urbaine Libre",
                      type: 'UPLOAD'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'gestion_volume_gestion_volume_type',
                        type: 'EQUALS',
                        value: 'aful'
                      },
                      {
                        id: 'presence_volume',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'gestion_volume_6053428d_6a0d_4f53_8d6d_d0c8bdc8cd0d',
                  label: 'Association foncière urbaine libre',
                  type: 'CATEGORY'
                }
              ],
              id: 'gestion_volume',
              label: 'Organes de gestion',
              repetition: {
                label: [
                  {
                    type: 'VARIABLE',
                    value: 'gestion_volume_gestion_volume_autre_nom'
                  },
                  {
                    type: 'VARIABLE',
                    value: 'gestion_volume_gestion_volume_asl_nom'
                  },
                  {
                    type: 'VARIABLE',
                    value: 'gestion_volume_gestion_volume_aful_nom'
                  }
                ],
                max: 1000,
                min: 0
              },
              type: 'REPEAT'
            }
          ],
          conditions: [
            [
              {
                id: 'presence_volume',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'b070627e_70ae_4b59_87d0_562c69de7101',
          label: 'Informations volume',
          type: 'CATEGORY'
        }
      ],
      id: '58dbefc0_0bc3_4324_ab07_23738db693e5',
      label: 'Volume',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'presence_lotissement',
          label: "Présence d'un lotissement",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'presence_asa',
          label: 'La copropriété est-elle comprise dans une Association Syndicale Autorisée ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'asl_presence',
          label: "Présence d'une ASL ou AFUL",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'asl',
              label: 'Association syndicale libre'
            },
            {
              id: 'aful',
              label: 'Association foncière urbaine libre'
            }
          ],
          id: 'asl_aful_binary',
          label: "Type d'association",
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'asl_presence_active',
          label: 'ASL ou AFUL encore active',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'lotissement_10_ans',
          label: 'Le lotissement a-t-il plus de 10 ans ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'presence_asa_nom',
          label: "Nom de l'Association Syndicale Autorisée",
          type: 'TEXT'
        },
        {
          id: 'presence_asa_gerant',
          label: "Nom du gérant de l'Association Syndicale Autorisée",
          type: 'TEXT'
        },
        {
          id: 'presence_asa_gerant_adresse',
          label: "Adresse du gérant de l'association syndicale Autorisée",
          type: 'ADDRESS'
        },
        {
          id: 'presence_asa_notaire_nom',
          label: "Nom du notaire ayant reçu le cahier des charges de l'ASA",
          type: 'TEXT'
        },
        {
          id: 'presence_asa_notaire_adresse',
          label: "Adresse du notaire ayant reçu le cahier des charges de l'ASA",
          type: 'ADDRESS'
        },
        {
          id: 'presence_asa_notaire_date',
          label: "Date de l'acte de dépôt du cahier des charges de l'ASA",
          type: 'DATE'
        },
        {
          children: [
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'cahier_charges_scaprim',
              label: 'Cahier des charges du lotissement',
              type: 'UPLOAD'
            },
            {
              id: 'denomination_lotissement',
              label: 'Dénomination du lotissement',
              type: 'TEXT'
            },
            {
              id: 'numero_lotissement',
              label: 'Numéro de lot du lotissement',
              type: 'NUMBER'
            },
            {
              id: 'date_lotissement',
              label: 'Date de création du lotissement',
              type: 'DATE'
            },
            {
              filters: {
                mustBeExcludedInOperationConfig: true
              },
              id: 'plan_lotissement',
              label: 'Plan du lotissement',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'reglement_du_lotissement',
              label: 'Règlement de lotissement',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'reglement_date',
                  label: 'Date du règlement de lotissement',
                  type: 'DATE'
                },
                {
                  filters: {
                    mustBeExcludedInOperationConfig: true
                  },
                  id: 'reglement_lotissement',
                  label: 'Reglement de lotissement',
                  type: 'UPLOAD'
                }
              ],
              conditions: [
                [
                  {
                    id: 'presence_lotissement',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'reglement_du_lotissement',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'b5ad45d3_a92d_45d4_9f8b_4e2bd2d09670',
              label: 'Règlement de lotissement',
              type: 'CATEGORY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'presence_cahier_charges',
              label: 'Cahier des charges du lotissement',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'cahier_charges_date',
                  label: 'Date du cahier des charges',
                  type: 'DATE'
                },
                {
                  filters: {
                    mustBeExcludedInOperationConfig: true
                  },
                  id: 'cahier_charges',
                  label: 'Cahier des charges',
                  type: 'UPLOAD'
                }
              ],
              conditions: [
                [
                  {
                    id: 'presence_cahier_charges',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'presence_lotissement',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: '1c26fb30_ddba_42f2_be0e_7b8bf776f1fb',
              label: 'Cahier des charges du lotissement',
              type: 'CATEGORY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'pieces_notaire',
              label: 'Pièces du lotissement déposées chez un notaire',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'pieces_notaire_date',
                  label: "Date de l'acte de dépôt",
                  type: 'DATE'
                },
                {
                  id: 'pieces_notaire_nom',
                  label: 'Nom du notaire',
                  type: 'TEXT'
                },
                {
                  id: 'pieces_notaire_ville',
                  label: 'Ville du notaire',
                  type: 'TEXT'
                },
                {
                  filters: {
                    mustBeExcludedInOperationConfig: true
                  },
                  id: 'acte_depot',
                  label: 'Acte de dépôt',
                  type: 'UPLOAD'
                },
                {
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'piece_lotissement_total',
                  label: "Statuts, Procès verbaux de l'ASL",
                  type: 'UPLOAD'
                },
                {
                  id: 'pieces_lotissement_notaire_spf',
                  label: 'Service de publicité foncière',
                  type: 'TEXT'
                },
                {
                  id: 'pieces_lotissement_notaire_spf_date',
                  label: 'Date de publication au SPF',
                  type: 'DATE'
                },
                {
                  id: 'pieces_lotissement_notaire_spf_volume',
                  label: 'Volume',
                  type: 'TEXT'
                },
                {
                  id: 'pieces_lotissement_notaire_spf_numero',
                  label: 'Numéro',
                  type: 'TEXT'
                }
              ],
              id: '28bb666b_31f9_4969_90fd_6ad84ca1f48c',
              label: 'Acte de dépôt',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'urbanisme_descriptif_statut',
                  label: "Les autorisations d'urbanisme doivent-elles être mentionnées dans la vente ?",
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'lotissement_delclaration_prealable',
                      label: "D'une déclaration préalable"
                    },
                    {
                      id: 'lotissement_permis_amenager',
                      label: "D'un permis d'aménager"
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'presence_lotissement',
                        value: 'oui',
                        type: 'EQUALS'
                      },
                      {
                        id: 'urbanisme_descriptif_statut',
                        value: 'oui',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'urbanisme_statut',
                  label: 'Le lotissement est issu',
                  type: 'SELECT'
                },
                {
                  children: [
                    {
                      id: 'urbanisme_statut_date_depot',
                      label: "Date de dépôt l'autorisation d'urbanisme",
                      type: 'DATE'
                    },
                    {
                      id: 'urbanisme_statut_date_delivrance',
                      label: "Date de délivrance de l'autorisation d'urbanisme (expresse ou tacite)",
                      type: 'DATE'
                    },
                    {
                      filters: {
                        mustBeExcludedInOperationConfig: true
                      },
                      id: 'autorisation_urbanisme',
                      label: "Recepisse de dépôt de l'autorisation d'urbanisme",
                      type: 'UPLOAD'
                    },
                    {
                      filters: {
                        mustBeExcludedInOperationConfig: true
                      },
                      id: 'autorisation_urbanisme_delivrance',
                      label: "Délivrance de l'autorisation d'urbanisme",
                      type: 'UPLOAD'
                    },
                    {
                      children: [
                        {
                          choices: [
                            {
                              id: 'non',
                              label: 'NON'
                            },
                            {
                              id: 'oui',
                              label: 'OUI'
                            }
                          ],
                          id: 'urbanisme_statut_permis_autorisation_vente',
                          label:
                            "L'administration a-t-elle autorisé la vente des lots avant l’exécution des travaux prescrits dans le permis ?",
                          type: 'SELECT-BINARY'
                        },
                        {
                          children: [
                            {
                              id: 'autorisation_urbanisme_vente_anticipee_date',
                              label: "Date de l'autorisation",
                              type: 'DATE'
                            },
                            {
                              filters: {
                                mustBeExcludedInOperationConfig: true
                              },
                              id: 'autorisation_vente_anticipee',
                              label: 'Autorisation administrative de vente anticipée des lots',
                              type: 'UPLOAD'
                            }
                          ],
                          conditions: [
                            [
                              {
                                id: 'presence_lotissement',
                                value: 'oui',
                                type: 'EQUALS'
                              },
                              {
                                id: 'urbanisme_descriptif_statut',
                                value: 'oui',
                                type: 'EQUALS'
                              },
                              {
                                id: 'urbanisme_statut',
                                value: 'lotissement_permis_amenager',
                                type: 'EQUALS'
                              },
                              {
                                id: 'urbanisme_statut_permis_autorisation_vente',
                                value: 'oui',
                                type: 'EQUALS'
                              }
                            ]
                          ],
                          id: '3ac104f1_5e1e_4b33_80cf_5b625b771407',
                          label: 'Autorisation de vente anticipée',
                          type: 'CATEGORY'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'presence_lotissement',
                            value: 'oui',
                            type: 'EQUALS'
                          },
                          {
                            id: 'urbanisme_descriptif_statut',
                            value: 'oui',
                            type: 'EQUALS'
                          },
                          {
                            id: 'urbanisme_statut',
                            value: 'lotissement_permis_amenager',
                            type: 'EQUALS'
                          }
                        ]
                      ],
                      id: '28c756c5_80d6_4ea8_bb74_0712599ead25',
                      label: "Permis d'aménager",
                      type: 'CATEGORY'
                    },
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'urbanisme_statut_certificat_non_opposition',
                      label: 'Un certificat de non opposition a-t-il été délivré?',
                      type: 'SELECT-BINARY'
                    },
                    {
                      children: [
                        {
                          id: 'urbanisme_statut_certificat_non_opposition_date',
                          label: 'Date de délivrance du certificat',
                          type: 'DATE'
                        },
                        {
                          filters: {
                            mustBeExcludedInOperationConfig: true
                          },
                          id: 'lotissement_certificat_non_opposition',
                          label: 'Certificat de non opposition',
                          type: 'UPLOAD'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'presence_lotissement',
                            value: 'oui',
                            type: 'EQUALS'
                          },
                          {
                            id: 'urbanisme_statut_certificat_non_opposition',
                            value: 'oui',
                            type: 'EQUALS'
                          }
                        ]
                      ],
                      id: '13bddb45_ccb4_428e_a134_ab9938672422',
                      label: 'Certificat de non opposition',
                      type: 'CATEGORY'
                    }
                  ],
                  id: '000274a8_0724_4262_8e63_8ab0e706549b',
                  label: "Autorisation d'urbanisme",
                  type: 'CATEGORY'
                }
              ],
              id: '1e8beda0_cb38_4406_844e_07d9544cee40',
              label: "Autorisations d'urbanisme",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'prensence_lotissement_asl_libre',
                  label: 'Le lotissement est-il concerné par une ASL ?',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'presence_asl_gerant',
                  label: "Nom du gérant de l'ASL",
                  type: 'TEXT'
                },
                {
                  id: 'presence_asl_gerant_adresse',
                  label: "Adresse du gérant de l'ASL",
                  type: 'ADDRESS'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'presence_lotissement_association_statut',
                  label: "L'association des propriétaires du lotissement est-elle encore active ?",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'gestion_asl_siege',
                  label: "Siège de l'ASL",
                  type: 'ADDRESS'
                },
                {
                  id: 'gestion_asl_representant',
                  label: "Représentant de l'ASL",
                  type: 'TEXT'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'asl',
                          label: 'Une association syndicale libre'
                        },
                        {
                          id: 'aful',
                          label: 'Une association foncière urbaine libre'
                        },
                        {
                          id: 'aucun',
                          label: 'Aucun organe'
                        },
                        {
                          id: 'autre',
                          label: 'Un autre organe de gestion'
                        }
                      ],
                      id: 'gestion_lotissement_gestion_lotissement_type',
                      label: 'Gestion par',
                      type: 'SELECT'
                    },
                    {
                      children: [
                        {
                          conditions: [
                            [
                              {
                                id: 'gestion_lotissement_gestion_lotissement_type',
                                value: 'autre',
                                type: 'EQUALS'
                              },
                              {
                                id: 'presence_lotissement',
                                value: 'oui',
                                type: 'EQUALS'
                              }
                            ]
                          ],
                          id: 'gestion_lotissement_gestion_lotissement_autre_type',
                          label: "Précisez le type de l'organe de gestion",
                          type: 'TEXT'
                        },
                        {
                          id: 'gestion_lotissement_gestion_lotissement_autre_nom',
                          label: 'Nom de l’organe de gestion',
                          type: 'TEXT'
                        },
                        {
                          id: 'gestion_lotissement_gestion_lotissement_autre_adresse',
                          label: 'Adresse de l’organe de gestion',
                          type: 'ADDRESS'
                        },
                        {
                          id: 'gestion_lotissement_gestion_lotissement_autre_president_nom',
                          label: 'Nom du gérant',
                          type: 'TEXT'
                        },
                        {
                          id: 'gestion_lotissement_gestion_lotissement_autre_president_email',
                          label: 'Adresse email du gérant',
                          type: 'EMAIL'
                        },
                        {
                          id: 'gestion_lotissement_gestion_lotissement_autre_president_telephone',
                          label: 'Téléphone du gérant',
                          type: 'PHONE'
                        },
                        {
                          conditions: [
                            [
                              {
                                id: 'gestion_lotissement_gestion_lotissement_type',
                                value: 'autre',
                                type: 'EQUALS'
                              },
                              {
                                id: 'presence_lotissement',
                                value: 'oui',
                                type: 'EQUALS'
                              }
                            ]
                          ],
                          id: 'gestion_lotissement_gestion_lotissement_autre_statuts',
                          label: "Statuts de l'organe de gestion",
                          type: 'UPLOAD'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'gestion_lotissement_gestion_lotissement_type',
                            value: 'autre',
                            type: 'EQUALS'
                          },
                          {
                            id: 'presence_lotissement',
                            value: 'oui',
                            type: 'EQUALS'
                          }
                        ]
                      ],
                      id: 'gestion_lotissement_b2814151_aad8_4c3e_9aa3_6383102c0059',
                      label: 'Autre organe',
                      type: 'CATEGORY'
                    },
                    {
                      children: [
                        {
                          id: 'gestion_lotissement_gestion_lotissement_asl_nom',
                          label: 'Nom de l’association syndicale',
                          type: 'TEXT'
                        },
                        {
                          id: 'gestion_lotissement_gestion_lotissement_asl_adresse',
                          label: 'Adresse de l’association syndicale',
                          type: 'ADDRESS'
                        },
                        {
                          id: 'gestion_lotissement_gestion_lotissement_asl_president_nom',
                          label: 'Nom du président',
                          type: 'TEXT'
                        },
                        {
                          id: 'gestion_lotissement_gestion_lotissement_asl_president_email',
                          label: 'Adresse email du président',
                          type: 'EMAIL'
                        },
                        {
                          id: 'gestion_lotissement_gestion_lotissement_asl_president_telephone',
                          label: 'Téléphone du président',
                          type: 'PHONE'
                        },
                        {
                          conditions: [
                            [
                              {
                                id: 'gestion_lotissement_gestion_lotissement_type',
                                value: 'asl',
                                type: 'EQUALS'
                              },
                              {
                                id: 'presence_lotissement',
                                value: 'oui',
                                type: 'EQUALS'
                              }
                            ]
                          ],
                          id: 'gestion_lotissement_gestion_lotissement_statuts_asl',
                          label: "Statuts de l'Association Syndicale Libre",
                          type: 'UPLOAD'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'gestion_lotissement_gestion_lotissement_type',
                            value: 'asl',
                            type: 'EQUALS'
                          },
                          {
                            id: 'presence_lotissement',
                            value: 'oui',
                            type: 'EQUALS'
                          }
                        ]
                      ],
                      id: 'gestion_lotissement_9ead91fc_be84_4fbe_a4e5_d918cff213dc',
                      label: 'Association syndicale libre',
                      type: 'CATEGORY'
                    },
                    {
                      children: [
                        {
                          id: 'gestion_lotissement_gestion_lotissement_aful_nom',
                          label: 'Nom de l’association foncière',
                          type: 'TEXT'
                        },
                        {
                          id: 'gestion_lotissement_gestion_lotissement_aful_adresse',
                          label: 'Adresse de l’association foncière',
                          type: 'ADDRESS'
                        },
                        {
                          id: 'gestion_lotissement_gestion_lotissement_aful_president_nom',
                          label: 'Nom du président',
                          type: 'TEXT'
                        },
                        {
                          id: 'gestion_lotissement_gestion_lotissement_aful_president_email',
                          label: 'Adresse email du président',
                          type: 'EMAIL'
                        },
                        {
                          id: 'gestion_lotissement_gestion_lotissement_aful_president_telephone',
                          label: 'Téléphone du président',
                          type: 'PHONE'
                        },
                        {
                          conditions: [
                            [
                              {
                                id: 'gestion_lotissement_gestion_lotissement_type',
                                value: 'aful',
                                type: 'EQUALS'
                              },
                              {
                                id: 'presence_lotissement',
                                value: 'oui',
                                type: 'EQUALS'
                              }
                            ]
                          ],
                          id: 'gestion_lotissement_gestion_lotissement_statuts_aful',
                          label: "Statuts de l'Association Foncière Urbaine Libre",
                          type: 'UPLOAD'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'gestion_lotissement_gestion_lotissement_type',
                            value: 'aful',
                            type: 'EQUALS'
                          },
                          {
                            id: 'presence_lotissement',
                            value: 'oui',
                            type: 'EQUALS'
                          }
                        ]
                      ],
                      id: 'gestion_lotissement_e6c28f30_9ab1_493f_9893_f59103e5017a',
                      label: 'Association foncière urbaine libre',
                      type: 'CATEGORY'
                    }
                  ],
                  id: 'gestion_lotissement',
                  label: 'Organes de gestion',
                  repetition: {
                    label: [
                      {
                        type: 'VARIABLE',
                        value: 'gestion_lotissement_gestion_lotissement_autre_nom'
                      },
                      {
                        type: 'VARIABLE',
                        value: 'gestion_lotissement_gestion_lotissement_asl_nom'
                      },
                      {
                        type: 'VARIABLE',
                        value: 'gestion_lotissement_gestion_lotissement_aful_nom'
                      }
                    ],
                    max: 1000,
                    min: 0
                  },
                  type: 'REPEAT'
                }
              ],
              id: 'aa398244_af19_450d_a452_ad378f8009bd',
              label: 'Gestion du lotissement - des espaces communs',
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'presence_lotissement',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'a3d6ef1a_65f7_49a9_9100_8934a4862091',
          label: 'Information sur le lotissement',
          type: 'CATEGORY'
        }
      ],
      id: '69a4d5c9_d1d5_4d9a_b85d_d76ad6d06731',
      label: 'Lotissement',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'zac_presence',
          label: "Présence d'une ZAC",
          type: 'SELECT-BINARY'
        },
        {
          id: 'zac_denomination',
          label: 'Nom de la ZAC',
          type: 'TEXT'
        },
        {
          id: 'zac_titre',
          label: 'Extrait du titre de propriété relatif à cette ZAC',
          type: 'TEXT'
        }
      ],
      id: 'd508c276_7812_4245_9214_2b521b08f53e',
      label: "Zone d'aménagement concernée",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'piscine_statut',
          label: "Présence d'une piscine",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'piscine',
          label: 'Piscine enterrée (et non hors-sol)',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'piscine_entretien',
              label: "Présence d'un contrat d'entretien",
              type: 'SELECT-BINARY'
            },
            {
              id: 'piscine_entretien_nom',
              label: "Nom de l'entreprise du contrat d'entretien",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'avant_2004',
                  label: 'Avant 2004'
                },
                {
                  id: 'moins_10_ans',
                  label: 'Depuis - de 10 ans'
                },
                {
                  id: 'plus_10_ans',
                  label: 'Après 2004 et + de 10 ans '
                }
              ],
              id: 'piscine_installation_date',
              label: "Date d'installation de la piscine",
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'dp',
                  label: 'Déclaration préalable (piscine entre 10 m2 et 100 m2)'
                },
                {
                  id: 'permis',
                  label: 'Permis de construire (piscine > 100 m2 ou secteur sauvegardé)'
                }
              ],
              conditions: [
                [
                  {
                    id: 'piscine_installation_date',
                    value: 'moins_10_ans',
                    type: 'DIFFERENT'
                  }
                ]
              ],
              id: 'piscine_installation_autorisation',
              label: "Autorisation d'urbanisme obtenue pour la piscine",
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'piscine_installation_date',
                    value: 'moins_10_ans',
                    type: 'DIFFERENT'
                  }
                ]
              ],
              id: 'piscine_installation_autorisation_document',
              label: 'Autorisation urbanisme - Piscine',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'piscine_installation_date',
                    value: 'moins_10_ans',
                    type: 'DIFFERENT'
                  }
                ]
              ],
              id: 'piscine_installation_achevement_document',
              label: 'Achèvement des travaux - Piscine',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'piscine_installation_date',
                    value: 'moins_10_ans',
                    type: 'DIFFERENT'
                  }
                ]
              ],
              id: 'piscine_installation_travaux',
              label: 'Conformité des travaux obtenue',
              type: 'SELECT-BINARY'
            }
          ],
          id: '6f3c2b05_3025_4921_afeb_eb0d6a2da894',
          label: 'CONDITION_BLOCK_Condition_piscine',
          type: 'CONDITION_BLOCK'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Une alarme',
                  id: 'alarme'
                },
                {
                  label: 'Rideaux / Couvertures / Bâche à barres',
                  id: 'rideau'
                },
                {
                  label: 'Une barrière',
                  id: 'barriere'
                },
                {
                  label: 'Un abri',
                  id: 'abri'
                },
                {
                  label: 'Aucun dispositif',
                  id: 'aucun_dispositif'
                }
              ],
              id: 'piscine_securite',
              label: 'Dispositif de sécurité',
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'piscine_installation_date',
                    value: 'avant_2004',
                    type: 'EQUALS'
                  },
                  {
                    id: 'piscine_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'piscine_securite_attestation',
              label: 'Organisme attestestant du dispositif de sécurité',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'piscine_securite_justificatif',
              label: "Présence d'un justificatif",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'piscine_installation_date',
                    value: 'avant_2004',
                    type: 'EQUALS'
                  },
                  {
                    id: 'piscine_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'piscine_securite_attestation_document',
              label: 'Attestation de normes du dispositif de sécurité piscine',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'piscine_installation_date',
                    value: 'avant_2004',
                    type: 'DIFFERENT'
                  },
                  {
                    id: 'piscine_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'piscine_note_securite_document',
              label: 'Note technique - dispositif de sécurité piscine',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'piscine_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: '2394aeed_d4ea_4780_a17c_0c317600f9ed',
          label: 'CONDITION_BLOCK_Sécurité piscine',
          type: 'CONDITION_BLOCK'
        }
      ],
      id: '59b14f6c_841f_4818_869e_f45cb814a2e0',
      label: 'Piscine',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description: 'En cas de DPE Vierge la réponse doit être non',
          id: 'dpe_details_statut',
          label: 'Diagnostic collectif réalisé',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'dpe_diagnostiqueur_date',
              label: 'Date du diagnostic',
              type: 'DATE'
            },
            {
              id: 'dpe_resultat_libre',
              label: 'Résultat',
              type: 'TEXT'
            },
            {
              description:
                "Indiquer d'abord la lettre puis éventuellement le montant de la consommation. Exemple : E - 355",
              id: 'dpe_consommation_energetique',
              label: 'Performance / consommation énergétique',
              type: 'TEXT'
            },
            {
              id: 'dpe_emission_gaz',
              label: 'Emission de gaz à effet de serre',
              type: 'TEXT'
            },
            {
              id: 'dpe_depense_annuelle',
              label: "Montant des dépenses annuelles d'énergie",
              type: 'TEXT'
            },
            {
              id: 'dpe_depense_annuelle_basse',
              label: "Montant minimum des dépenses annuelles d'énergie",
              type: 'PRICE'
            },
            {
              id: 'dpe_depense_annuelle_haute',
              label: "Montant maximum des dépenses annuelles d'énergie",
              type: 'PRICE'
            },
            {
              id: 'dpe_depense_annuelle_annee',
              label: "Année(s) de référence pour les dépenses d'énergie",
              type: 'TEXT'
            },
            {
              id: 'dpe_diagnostic_document',
              label: 'DPE Collectif',
              type: 'UPLOAD'
            },
            {
              children: [
                {
                  id: 'dpe_diagnostiqueur_nom',
                  label: 'Nom du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  id: 'diagnostics_techniques_diagnostiqueur_dpe_societe',
                  label: 'Société du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  id: 'dpe_diagnostiqueur_adresse',
                  label: 'Adresse du diagnostiqueur',
                  type: 'ADDRESS'
                }
              ],
              id: '9ff2f33c_5bf2_4b0e_b59e_961c58fcb855',
              label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
              type: 'CONDITION_BLOCK'
            }
          ],
          conditions: [
            [
              {
                id: 'dpe_details_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: '644d3d9e_d8a2_411f_889b_8f33f8f368f3',
          label: 'CONDITION_BLOCK_Informations sur le diagnostic de performance énergétique',
          type: 'CONDITION_BLOCK'
        }
      ],
      id: '8c61a4fb_f47f_4e4e_b764_2e1437064ac1',
      label: 'Diagnostic de performance Énergétique',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostics_resultats',
          label: 'Résultat des diagnostics indiqué au compromis',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'domofrance_parties_communes_diag_amiante',
          label: 'Un diagnostic amiante a-t-il été réalisé dans les parties communes ?',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'parties_communes_diag_plomb',
              label: 'Diagnostic réalisé dans les parties communes',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'parties_communes_diag_plomb_diagnostiqueur_nom',
                  label: 'Nom du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'exim',
                      label: "EX'IM"
                    },
                    {
                      id: 'environnement',
                      label: 'AC ENVIRONNEMENT'
                    },
                    {
                      id: 'allo',
                      label: 'ALLO DIAGNOSTIC'
                    },
                    {
                      id: 'adx',
                      label: 'ADX GROUPE - ALLODIAGNOSTIC'
                    },
                    {
                      id: 'aed',
                      label: 'AED GROUPE - MERIGNAC'
                    }
                  ],
                  id: 'diagnostic_plomb_valloire',
                  label: 'Nom du diagnostiqueur',
                  type: 'SELECT'
                },
                {
                  id: 'parties_communes_diag_plomb_diagnostiqueur_adresse',
                  label: 'Adresse du diagnostiqueur',
                  type: 'ADDRESS'
                },
                {
                  id: 'parties_communes_diag_plomb_date',
                  label: 'Date du diagnostic',
                  type: 'DATE'
                },
                {
                  choices: [
                    {
                      label: "Il n'a pas été repéré de présence de plomb",
                      id: 'absence'
                    },
                    {
                      label: 'Il a été repéré des mesures de plomb de classe 0',
                      id: 'classe_0'
                    },
                    {
                      label: 'Il a été repéré des mesures de plomb de classe 1',
                      id: 'classe_1'
                    },
                    {
                      label: 'Il a été repéré des mesures de plomb de classe 2',
                      id: 'classe_2'
                    },
                    {
                      label: 'Il a été repéré des mesures de plomb de classe 3',
                      id: 'classe_3'
                    }
                  ],
                  id: 'parties_communes_diag_plomb_resultat',
                  label: 'Résultat du diagnostic',
                  multiple: true,
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'parties_communes_diagnostic_plomb_resultat_simple',
                  label: 'Le diagnostic fait-il apparaitre la présence de plomb',
                  type: 'SELECT-BINARY'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'parties_communes_diag_plomb',
                        value: 'oui',
                        type: 'EQUALS'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'parties_communes_diag_plomb',
                        value: 'oui',
                        type: 'EQUALS'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'parties_communes_diag_plomb',
                        value: 'oui',
                        type: 'EQUALS'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'diagnostic_parties_communes_plomb',
                  label: 'Diagnostic Plomb Parties Communes',
                  type: 'UPLOAD'
                }
              ],
              id: '194557be_a5f8_4fcc_8ba8_910235a215f7',
              label: 'Informations sur le diagnostic plomb',
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'avant_1949'
              }
            ],
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
              }
            ]
          ],
          id: '5a28d281_0333_4401_b09d_25a114accaad',
          label: 'Diagnostic Plomb Parties Communes',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'parties_communes_diag_amiante',
              label: 'Diagnostic réalisé dans les parties communes',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'parties_communes_diag_amiante_diagnostiqueur_nom',
                  label: 'Nom du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'exim',
                      label: "EX'IM"
                    },
                    {
                      id: 'environnement',
                      label: 'AC ENVIRONNEMENT'
                    },
                    {
                      id: 'allo',
                      label: 'ALLO DIAGNOSTIC'
                    },
                    {
                      id: 'adx',
                      label: 'ADX GROUPE - ALLODIAGNOSTIC'
                    },
                    {
                      id: 'aed',
                      label: 'AED GROUPE - MERIGNAC'
                    }
                  ],
                  id: 'diagnostic_amiante_valloire',
                  label: 'Nom du diagnostiqueur',
                  type: 'SELECT'
                },
                {
                  id: 'parties_communes_diag_amiante_diagnostiqueur_adresse',
                  label: 'Adresse du diagnostiqueur',
                  type: 'ADDRESS'
                },
                {
                  id: 'parties_communes_diag_amiante_date',
                  label: 'Date du diagnostic',
                  type: 'DATE'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'parties_communes_diagnostic_amiante_resultat_simple',
                  label: "Le diagnostic fait-il apparaitre la présence d'amiante",
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      label: "Il n'a pas été repéré des matériaux contenant de l'amiante",
                      id: 'absence'
                    },
                    {
                      label: "Il a été repéré des matériaux contenant de l'amiante de la liste A",
                      id: 'liste_a'
                    },
                    {
                      label: "Il a été repéré des matériaux contenant de l'amiante de la liste B",
                      id: 'liste_b'
                    },
                    {
                      label: "Des locaux ou parties de locaux n'ont pas pu être visités",
                      id: 'non_visite'
                    }
                  ],
                  id: 'parties_communes_diag_amiante_resultat',
                  label: 'Résultat du diagnostic',
                  multiple: true,
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'parties_communes_diag_amiante',
                        value: 'oui',
                        type: 'EQUALS'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        id: 'parties_communes_diag_amiante',
                        value: 'oui',
                        type: 'EQUALS'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        id: 'parties_communes_diag_amiante',
                        value: 'oui',
                        type: 'EQUALS'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: '1949_1997'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'parties_communes_diag_amiante',
                        value: 'oui',
                        type: 'EQUALS'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        id: 'parties_communes_diag_amiante',
                        value: 'oui',
                        type: 'EQUALS'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'avant_1949'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'parties_communes_diag_amiante',
                        value: 'oui',
                        type: 'EQUALS'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'diagnostic_parties_communes_amiante',
                  label: 'Diagnostic Amiante Parties Communes',
                  type: 'UPLOAD'
                }
              ],
              id: '5939db60_f3d6_4113_9a0f_27af52ad96e9',
              label: 'Informations sur le diagnostic amiante',
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: '1949_1997'
              }
            ],
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'avant_1949'
              }
            ],
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
              }
            ]
          ],
          id: '149400fb_8489_4b85_8a83_3839d3a38b03',
          label: 'Diagnostic Amiante Parties Communes',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'parties_communes_termites_concernee',
              label: 'Zone concernée par les termites',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_termites_informatif_effectue',
              label: 'Diagnostic tout de même effectué',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'parties_communes_termites_statut',
                  label: 'Diagnostic effectué dans les parties communes',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      id: 'parties_communes_diag_termites_diagnostiqueur_nom',
                      label: 'Nom du diagnostiqueur',
                      type: 'TEXT'
                    },
                    {
                      id: 'parties_communes_diag_termites_diagnostiqueur_adresse',
                      label: 'Adresse du diagnostiqueur - diagnostic Termites',
                      type: 'ADDRESS'
                    },
                    {
                      id: 'parties_communes_diag_termites_date',
                      label: 'Date du diagnostic',
                      type: 'DATE'
                    },
                    {
                      choices: [
                        {
                          label: "Absence de traces d'infestation de termite",
                          id: 'absence'
                        },
                        {
                          label: "Présence de traces d'infestation de termite",
                          id: 'presence'
                        }
                      ],
                      id: 'parties_communes_diag_termites_resultat',
                      label: 'Résultat du diagnostic',
                      type: 'SELECT'
                    },
                    {
                      conditions: [
                        [
                          {
                            id: 'parties_communes_termites_statut',
                            value: 'oui',
                            type: 'EQUALS'
                          }
                        ],
                        [
                          {
                            type: 'EQUALS_CONTRACT_MODELS',
                            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                          }
                        ]
                      ],
                      filters: {
                        mustBeExcludedInOperationConfig: true
                      },
                      id: 'diagnostic_parties_communes_termites',
                      label: 'Diagnostic Termites parties communes',
                      type: 'UPLOAD'
                    }
                  ],
                  id: 'f023e700_f08b_46f6_86fa_e65b3f3f0ef8',
                  label: 'Informations sur le diagnostic termites',
                  type: 'CATEGORY'
                }
              ],
              id: '65554673_cc86_4f99_a1a9_f762204aeca3',
              label: 'Zone concernée par les termites',
              type: 'CATEGORY'
            }
          ],
          id: '5f9d8c53_e481_4291_aa42_209fdf55e418',
          label: 'Diagnostic Termites Parties Communes',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'parties_communes_merules_concernee',
              label: "Zone susceptible d'être concernée par les mérules",
              type: 'SELECT-BINARY'
            }
          ],
          id: '04e28a55_78fa_4ea4_9a60_d95ce0131170',
          label: 'Diagnostic Merules - Parties Communes',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'scaprim_assainissement',
          label: "Assainissement - Un rapport sur le réseau d'assainissement a-t-il été effectué ?",
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'scaprim_assainissement',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'scaprim_assainissement_date',
          label: 'Assainissement - Date du rapport',
          type: 'DATE'
        },
        {
          choices: [
            {
              label: 'Assainissement individuel (fosse septique...)',
              id: 'fosse'
            },
            {
              label: 'Assainissement collectif (tout à l’égout...)',
              id: 'egout'
            },
            {
              label: "Le type de raccordement n'est pas connu",
              id: 'inconnu'
            }
          ],
          id: 'assainissement_statut',
          label: "Assainissement - Type d'assainissement",
          type: 'SELECT'
        },
        {
          choices: [
            {
              label: 'Au moyen d’une fosse septique',
              id: 'fosse'
            },
            {
              label: 'Par un raccordement au « tout à l’égout »',
              id: 'egout'
            }
          ],
          id: 'assainissement_statut_simple',
          label: "Assainissement - Type d'assainissement",
          type: 'SELECT'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'assainissement_egout_statut',
              label: 'Assainissement - Contrôle réalisé',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'assainissement_egout_a_faire',
              label: 'Assainissement - Contrôle du raccordement réalisé avant la vente',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'assainissement_egout_controle_date',
                  label: 'Assainissement - Date du contrôle',
                  type: 'DATE'
                },
                {
                  id: 'assainissement_egout_controle_nom',
                  label: 'Assainissement - Organisme ayant réalisé le contrôle',
                  type: 'TEXT'
                },
                {
                  id: 'assainissement_egout_controle_adresse',
                  label: "Assainissement - Adresse de l'organisme",
                  type: 'ADDRESS'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'assainissement_egout_controle_resultat',
                  label: 'Assainissement - Raccordement au réseau collectif conforme',
                  type: 'SELECT-BINARY'
                },
                {
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'controle_assainissement_collectif',
                  label: "Contrôle du raccordement de l'assainissement collectif",
                  type: 'UPLOAD'
                }
              ],
              conditions: [
                [
                  {
                    id: 'assainissement_egout_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'assainissement_statut',
                    type: 'EQUALS',
                    value: 'egout'
                  },
                  {
                    id: 'assainissement_statut_simple',
                    value: 'egout',
                    type: 'EQUALS'
                  }
                ],
                [
                  {
                    id: 'assainissement_egout_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'assainissement_statut',
                    type: 'EQUALS',
                    value: 'egout'
                  }
                ],
                [
                  {
                    id: 'assainissement_egout_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'assainissement_statut_simple',
                    value: 'egout',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: '71a73495_ea30_4da9_aead_c72ab7db72bf',
              label: "Information sur le contrôle de l'assainissement",
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'assainissement_statut',
                type: 'EQUALS',
                value: 'egout'
              }
            ],
            [
              {
                id: 'assainissement_statut_simple',
                value: 'egout',
                type: 'EQUALS'
              }
            ]
          ],
          id: '02eb05e5_91aa_42bb_8d60_f63e5c1b3028',
          label: 'Assainissement collectif',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'assainissement_fosse_statut',
              label: "L'assainissement a t-il fait l’objet d’un contrôle par le SPANC ?",
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'assainissement_fosse_controle_date',
                  label: 'Assainissement - Date du contrôle',
                  type: 'DATE'
                },
                {
                  id: 'assainissement_fosse_controle_nom',
                  label: 'Assainissement - Organisme ayant réalisé le contrôle',
                  type: 'TEXT'
                },
                {
                  id: 'assainissement_fosse_controle_adresse',
                  label: "Assainissement - Adresse de l'organisme",
                  type: 'ADDRESS'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'assainissement_fosse_controle_resultat',
                  label: "Assainissement - Présence d'anomalies",
                  type: 'SELECT-BINARY'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'assainissement_fosse_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'controle_assainissement_fosse',
                  label: "Contrôle du raccordement de l'assainissement non collectif",
                  type: 'UPLOAD'
                }
              ],
              id: '793b04a5_8a89_41d9_8605_0188ef43b063',
              label: "Information sur le contrôle de l'assainissement",
              type: 'CATEGORY'
            }
          ],
          id: '0fbfacea_e016_44be_a39a_6bb42130ecc1',
          label: 'Assainissement non collectif',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'assainissement_precisions',
          label: "Ajouter une précision sur l'assainissement",
          type: 'SELECT-BINARY'
        },
        {
          id: 'assainissement_precisions_texte',
          label: "Précisions sur l'assainissement",
          type: 'TEXTAREA'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diags_parties_communes_diagnostic_dtg',
              label: 'Diagnostic technique global/du bâti établi',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diags_parties_communes_dtg_date_mise_copropriete',
              label: 'Mise en copropriété effectuée avant le 1er Janvier 2017',
              type: 'SELECT-BINARY'
            },
            {
              description: 'Prendre toute la clause sur le Diagnostic Technique',
              id: 'integration_diags_parties_communes_diagnostic_dtg_clause',
              label: 'Clause DTG/DTB',
              type: 'TEXTAREA'
            },
            {
              choices: [
                {
                  label: 'Immeuble de plus de 15 ans, mise en copropriété avant 2017',
                  id: 'avant_2017'
                },
                {
                  label: 'Immeuble de plus de 10 ans, mise en copropriété après 2017',
                  id: 'apres_2017'
                },
                {
                  label: 'Vente de plus de 10 logements',
                  id: '10_logements'
                }
              ],
              id: 'diags_parties_communes_diagnostic_dtg_justification',
              label: 'DTG/DTB réalisé dans le cadre',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  label: 'Immeuble de plus de 15 ans, mise en copropriété avant 2017',
                  id: 'avant_2017'
                },
                {
                  label: 'Immeuble de plus de 10 ans, mise en copropriété après 2017',
                  id: 'apres_2017'
                },
                {
                  label: "Conformément à l'article 2-2 de l'accord collectif",
                  id: 'accord_2005'
                },
                {
                  label: 'Vente de plus de 10 logements',
                  id: '10_logements'
                }
              ],
              id: 'diags_parties_communes_diagnostic_dtg_justification_accord_2005',
              label: 'DTG/DTB réalisé dans le cadre',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diags_parties_communes_diagnostic_dtg_dpe_a_part',
              label: 'Le DPE collectif a été réalisé à part',
              type: 'SELECT-BINARY'
            },
            {
              id: 'diags_parties_communes_diagnostic_dtg_resultats',
              label: 'Résultat du diagnostic technique global',
              type: 'TEXTAREA'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diags_parties_communes_diagnostic_dtg_2014',
              label: 'Diagnostic technique global établi avant 2014 ?',
              type: 'SELECT-BINARY'
            },
            {
              id: 'diags_parties_communes_diagnostic_dtg_date',
              label: 'Date de réalisation du diagnostic technique global',
              type: 'DATE'
            },
            {
              id: 'diags_parties_communes_diagnostic_dtg_nom',
              label: 'DTG/DTB - Nom du diagnostiqueur',
              type: 'TEXT'
            },
            {
              id: 'diags_parties_communes_diagnostic_dtg_adresse',
              label: 'DTG/DTB - Adresse du diagnostiqueur',
              type: 'ADDRESS'
            },
            {
              id: 'diags_parties_communes_diagnostic_dtg_dpe_nom',
              label: 'DPE Collectif - Nom du diagnostiqueur',
              type: 'TEXT'
            },
            {
              id: 'diags_parties_communes_diagnostic_dtg_dpe_adresse',
              label: 'DPE Collectif - Adresse du diagnostiqueur',
              type: 'ADDRESS'
            },
            {
              id: 'diags_parties_communes_diagnostic_dtg_dpe_date',
              label: 'DPE Collectif - Date du diagnostic',
              type: 'DATE'
            },
            {
              conditionalTitles: [
                {
                  conditions: [
                    [
                      {
                        type: 'EQUALS_TEMPLATES',
                        templates: [
                          'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME',
                          'OPERATION__CLESENCE__IMMOBILIER__VENTE',
                          'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF',
                          'OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF'
                        ]
                      }
                    ]
                  ],
                  title: 'Document ALUR - Diagnostic Technique Global'
                }
              ],
              conditions: [
                [
                  {
                    id: 'diags_parties_communes_diagnostic_dtg',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ]
              ],
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'diagnostic_parties_communes_diagnostic_dtg',
              label: 'Diagnostic Technique Global',
              type: 'UPLOAD'
            },
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'diagnostic_parties_communes_diagnostic_dtg_brs',
              label: 'Diagnostic Technique Global',
              type: 'UPLOAD'
            },
            {
              id: 'diagnostic_parties_communes_diagnostic_dtg_liste_pieces',
              label: 'Liste des pièces composant le dossier',
              type: 'TEXTAREA'
            }
          ],
          id: 'b3625e66_64d9_4bc3_9fe2_f8319d1e885d',
          label: 'Diagnostic technique',
          type: 'CATEGORY'
        },
        {
          conditions: [
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: '1949_1997'
              },
              {
                id: 'parties_communes_diag_amiante',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'avant_1949'
              },
              {
                id: 'parties_communes_diag_amiante',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'construction',
                type: 'EQUALS',
                value: 'avant_1949'
              },
              {
                id: 'parties_communes_diag_plomb',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'parties_communes_diag_amiante',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
              }
            ],
            [
              {
                id: 'parties_communes_diag_plomb',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
              }
            ],
            [
              {
                id: 'parties_communes_termites_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          filters: {
            mustBeExcludedInOperationConfig: true
          },
          id: 'attestation_diagnostiqueur',
          label: 'Attestation Diagnostiqueur',
          type: 'UPLOAD'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'plan_pluriannuel_travaux_lot_200',
              label: 'La copropriété comporte plus de 200 lots (habitations, bureaux, commerces)',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'plan_pluriannuel_travaux_lot_50',
              label: 'La copropriété comporte plus de 50 lots',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'plan_pluriannuel_travaux_adopte',
              label: 'Plan pluriannuel adopté',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'plan_pluriannuel_travaux_adopte',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'plan_pluriannuel_travaux_document',
              label: 'Plan pluriannuel de travaux',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'plan_pluriannuel_travaux_adopte',
                    value: 'non',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'plan_pluriannuel_travaux_projet_etabli',
              label: 'Projet de plan pluriannuel établi',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'plan_pluriannuel_travaux_elaboration',
              label: "Plan pluriannuel en cours d'élaboration",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'plan_pluriannuel_travaux_adopte',
                    value: 'non',
                    type: 'EQUALS'
                  },
                  {
                    id: 'plan_pluriannuel_travaux_projet_etabli',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'plan_pluriannuel_travaux_projet_document',
              label: 'Projet de plan pluriannuel de travaux',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'plan_pluriannuel_travaux_projet_convocation',
              label: 'Assemblée générale non encore convoquée',
              type: 'SELECT-BINARY'
            }
          ],
          id: '773d8919_72bd_40cf_9c8e_cf84d315630c',
          label: 'Plan pluriannuel de travaux',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'erp_global',
              label:
                'La copropriété est-elle située dans une zone couverte par un plan de prévention des risques naturels, miniers ou technologiques?',
              type: 'SELECT-BINARY'
            },
            {
              id: 'erp_diagnostiqueur_nom',
              label: 'Etat des risques établi par',
              type: 'TEXT'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Zone non concernée par un plan de prévention des risques naturels',
                      id: 'aucun_plan'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques naturels prescrits",
                      id: 'plan_prescrit'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques naturels anticipés",
                      id: 'plan_anticipe'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques naturels approuvés",
                      id: 'plan_approuve'
                    }
                  ],
                  id: 'ppr_naturels_liste',
                  label: 'Risques naturels',
                  multiple: true,
                  type: 'SELECT'
                },
                {
                  id: 'ppr_naturels_liste_risque',
                  label: 'Risques naturels pris en compte',
                  type: 'TEXTAREA'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'ppr_naturel_travaux',
                  label: 'Prescription de travaux dans le règlement du PPRn',
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'f02cbb0a_4d6f_4bb7_bfcf_4e79d68087d5',
              label: 'Plan de prévention des risques naturels',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Zone non concernée par un plan de prévention des risques miniers',
                      id: 'aucun_plan'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques miniers prescrits",
                      id: 'plan_prescrit'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques miniers anticipés",
                      id: 'plan_anticipe'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques miniers approuvés",
                      id: 'plan_approuve'
                    }
                  ],
                  id: 'ppr_miniers_liste',
                  label: 'Risques miniers',
                  multiple: true,
                  type: 'SELECT'
                }
              ],
              id: '598c0199_6d2b_4e0d_8bdc_edff1a964e02',
              label: 'Plan de prévention des risques miniers',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Zone non concernée par un plan de prévention des risques technologiques',
                      id: 'aucun_plan'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques technologiques prescrits",
                      id: 'plan_prescrit'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques technologiques anticipés",
                      id: 'plan_anticipe'
                    },
                    {
                      label: "Dans le périmètre d'un plan de prévention des risques technologiques approuvés",
                      id: 'plan_approuve'
                    }
                  ],
                  id: 'ppr_technologiques_liste',
                  label: 'Risques technologiques',
                  multiple: true,
                  type: 'SELECT'
                }
              ],
              id: '046cebe4_f6e1_4d06_9d1f_c061c79ae1c4',
              label: 'Plan de prévention des risques technologiques',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'zone_sismicite_classe',
                  label: 'Zone de sismicité',
                  type: 'NUMBER'
                }
              ],
              id: 'f85e3ed6_a098_4c74_ad67_04256bac8f01',
              label: 'Zone de sismicité',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'pollution_sols_sis',
                  label: "Terrain situé en secteur d'information sur les sols",
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'fc7aa6ca_4bb6_462c_8444_33cef2a514c5',
              label: 'Pollution des sols',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'potentiel_radon_classe',
                  label: 'Commune à potentiel radon classée niveau 3',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'potentiel_radon_classe_fiche',
                  label: 'Fiche information Radon',
                  type: 'UPLOAD'
                },
                {
                  id: 'potentiel_radon_potentiel_radon_classe_libre',
                  label: 'La commune se trouve en zone RADON',
                  type: 'TEXT'
                },
                {
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'carte_radon',
                  label: 'Carte RADON',
                  type: 'UPLOAD'
                }
              ],
              id: '4e4f15bf_9caa_43c8_ab7d_c4e85aa046b6',
              label: 'Potentiel Radon',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'erp_retrait_cote_commune',
                  label: 'Commune exposée au retrait des côtes',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'erp_retrait_cote_bien',
                  label: 'Bien exposé au retrait des côtes',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'erp_retrait_cote_bien_fiche',
                  label: 'Retrait de côtes - Extrait des prescriptions applicables',
                  type: 'UPLOAD'
                }
              ],
              id: '30e42c27_69d8_424e_ba19_4e5288151903',
              label: 'CONDITION_BLOCK_Retrait de côte',
              type: 'CONDITION_BLOCK'
            },
            {
              filters: {
                mustBeExcludedInOperationConfig: true
              },
              id: 'erp',
              label: 'Etat des risques',
              type: 'UPLOAD'
            },
            {
              filters: {
                mustBeExcludedInOperationConfig: true
              },
              id: 'external_georisque',
              label: 'Georisques - Environnement - Copropriété',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_geotechnique',
              label: 'Diagnostic géotechnique réalisé',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'geotechnique_zone_moyen',
                      label: 'Zone à risques moyens'
                    },
                    {
                      id: 'geotechnique_zone_fort',
                      label: 'Zone à risques forts'
                    }
                  ],
                  id: 'diagnostic_geotechnique_zone_type',
                  label: 'Type de zone',
                  type: 'SELECT'
                },
                {
                  id: 'diagnostic_geotechnique_date',
                  label: "Date de réalisation de l'étude géotechnique",
                  type: 'DATE'
                },
                {
                  id: 'diagnostic_geotechnique_diagnostiqueur_nom',
                  label: 'Nom du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  id: 'diagnostic_geotechnique_diagnostiqueur_adresse',
                  label: 'Adresse du diagnostiqueur',
                  type: 'ADDRESS'
                },
                {
                  filters: {
                    mustBeExcludedInOperationConfig: true
                  },
                  id: 'etude_geotechnique',
                  label: 'Etude géotechnique - Copropriété',
                  type: 'UPLOAD'
                }
              ],
              conditions: [
                [
                  {
                    id: 'diagnostic_geotechnique',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: '28dedf40_7215_404a_bca7_3d8952f93e0d',
              label: 'Diagnostic géotechnique',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'zone_bruit_diagnostiqueur_nom',
                  label: 'Etat des nuisances sonores établi par',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'zone_bruit',
                  label: "Zone soumise à un plan d'exposition au bruit des aérodromes",
                  type: 'SELECT-BINARY'
                },
                {
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'plan_exposition_bruit_libre',
                  label: 'Etat des nuisances sonores et aeriennes - Copropriété',
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'zone_bruit',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  filters: {
                    mustBeExcludedInOperationConfig: true
                  },
                  id: 'plan_exposition_bruit',
                  label: "Plan d'exposition au bruit des aérodromes - Copropriété",
                  type: 'UPLOAD'
                },
                {
                  id: 'plan_exposition_bruit_zonage',
                  label: "Classement de la zone concernant le plan d'exposition au bruit des aérodromes",
                  type: 'TEXT'
                }
              ],
              id: '944b9023_3a06_4c20_b10f_4e04ef1ff219',
              label: "Plan d'exposition des bruits",
              type: 'CATEGORY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'zone_argiles',
              label: 'Zone aléa argiles',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'zone_argile_faible',
                  label: "Zone d'exposition faible"
                },
                {
                  id: 'zone_argile_moyen',
                  label: "Zone d'exposition moyenne"
                },
                {
                  id: 'zone_argile_forte',
                  label: "Zone d'exposition forte"
                },
                {
                  id: 'zone_argile_inconnue',
                  label: "Zone d'exposition non classée"
                }
              ],
              id: 'zone_argiles_type',
              label: "Classement de la zone concernant l'aléa argile",
              type: 'SELECT'
            },
            {
              id: 'note_environnement_copropriete',
              label: "Note sur l'environnement",
              type: 'UPLOAD'
            }
          ],
          id: 'c83dfbd8_fe70_4ba5_9e7f_e813a1ac2341',
          label: 'Diagnostics environnementaux de la Copropriété',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'obligation_debroussaillement_statut',
              label: 'Copropriété concernée par une obligation de débroussaillement',
              type: 'SELECT-BINARY'
            }
          ],
          id: '9c410192_dd8f_4165_90d7_60b81a8c7443',
          label: 'Obligation de débroussaillement',
          type: 'CATEGORY'
        }
      ],
      id: '9660c552_3e32_4ff9_879a_02e317bfabe9',
      label: 'Diagnostics des parties communes - Copropriété',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'st_malo_extrait_reglement',
          label: 'Ajouter un extrait du règlement de copropriété (règlementation des locations saisonnières - St Malo)',
          type: 'SELECT-BINARY'
        },
        {
          id: 'st_malo_extrait_reglement_description',
          label: 'Extrait du règlement de copropriété relatif à la location saisonnière',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'indigne',
          label: "La copropriété est-elle située dans une zone d'habitat indigne (permis de louer) ?",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'autorisation',
                  label: 'Une autorisation préalable'
                },
                {
                  id: 'declaration',
                  label: 'Une déclaration postérieure'
                }
              ],
              id: 'indigne_dualite',
              label: 'La location néccessite une',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'indigne_autorisation',
              label: 'La location nécessite-t-elle une autorisation préalable ?',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'indigne_declaration',
              label: 'La location nécessite-t-elle une déclaration préalable ?',
              type: 'SELECT-BINARY'
            }
          ],
          conditions: [
            [
              {
                id: 'indigne',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: '647ba590_8cab_469e_858b_7de8fcc51332',
          label: "Informations sur la zone d'habitat indigne",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'zone_loyer_reference_montant',
              label: 'Montant du loyer de référence (par m2)',
              type: 'PRICE'
            },
            {
              id: 'zone_loyer_reference_montant_majore',
              label: 'Montant du loyer de référence majoré (par m2)',
              type: 'PRICE'
            }
          ],
          id: '007f5bb0_d4bb_4bf5_bc37_862536f79511',
          label: 'Informations sur le loyer de référence',
          type: 'CATEGORY'
        }
      ],
      id: '41a4dcbc_4374_43bb_a0a5_7d13ae25486f',
      label: 'Règlementations spéciales sur la Copropriété',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'construction_date_achevement',
          label: 'Date d’achèvement de la construction',
          type: 'DATE'
        },
        {
          choices: [
            {
              label: 'Un permis de construire',
              id: 'permis'
            },
            {
              label: 'Une déclaration préalable',
              id: 'declaration'
            },
            {
              label: 'Aucune autorisation obtenue',
              id: 'aucune'
            }
          ],
          id: 'construction_autorisation_administration',
          label: "Autorisation d'urbanisme",
          type: 'SELECT'
        },
        {
          children: [
            {
              id: 'construction_autorisation_date',
              label: "Date de l'autorisation",
              type: 'DATE'
            },
            {
              id: 'construction_autorisation_commune',
              label: "Commune délivrant l'autorisation",
              type: 'TEXT'
            },
            {
              id: 'construction_autorisation_numero',
              label: "Numéro de l'autorisation",
              type: 'TEXT'
            },
            {
              id: 'construction_autorisation_beneficiaire',
              label: "Nom du bénéficiaire de l'autorisation",
              type: 'TEXT'
            },
            {
              id: 'construction_autorisation_huissier',
              label: "Nom de l'huissier ayant constaté l'affichage des autorisations",
              type: 'TEXT'
            },
            {
              id: 'construction_autorisation_huissier_commune',
              label: "Commune de l'huissier ayant constaté l'affichage des autorisations",
              type: 'TEXT'
            },
            {
              id: 'construction_autorisation_huissier_date',
              label: "Date du constat de l'huissier ayant constaté l'affichage des autorisations",
              type: 'TEXT'
            },
            {
              id: 'construction_autorisation_notaire_depot',
              label: "Notaire ayant reçu l'acte de dépôt du programme",
              type: 'TEXT'
            },
            {
              id: 'construction_autorisation_notaire_depot_commune',
              label: "Commune du notaire ayant reçu l'acte de dépôt du programme",
              type: 'TEXT'
            },
            {
              id: 'construction_autorisation_notaire_depot_date',
              label: "Date de l'acte de dépôt du programme",
              type: 'DATE'
            },
            {
              id: 'construction_autorisation_notaire_depot_denomination',
              label: 'Dénomination du programme',
              type: 'TEXT'
            },
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'ad_permis_construire_construction',
              label: 'Permis de construire - Copropriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ],
                [
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ],
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ]
              ],
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'permis_construire_construction',
              label: 'Permis de construire - Copropriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ],
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  },
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  }
                ],
                [
                  {
                    id: 'construction_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ]
              ],
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'declaration_prealable_construction',
              label: 'Déclaration préalable - Copropriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'construction',
                    type: 'EQUALS',
                    value: 'moins_10_ans'
                  }
                ],
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                  }
                ]
              ],
              id: 'carnet_information_construction',
              label: "Carnet d'information du logement",
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'construction_declaration_achevement_statut',
              label: "Déclaration d'achèvement déposée en mairie",
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'construction_declaration_achevement_date',
                  label: "Date de la déclaration d'achèvement",
                  type: 'DATE'
                },
                {
                  id: 'declaration_achevement_construction',
                  label: "Déclaration d'achèvement - Copropriété",
                  type: 'UPLOAD'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'construction_certificat_conformite_statut',
                  label: 'Certificat de conformité ou de non contestation obtenu',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      id: 'construction_certificat_conformite_date',
                      label: 'Date de délivrance du certificat',
                      type: 'DATE'
                    },
                    {
                      filters: {
                        mustBeIncludedInOperationConfig: true
                      },
                      id: 'certificat_conformite_construction',
                      label: 'Certificat de conformité - Copropriété',
                      type: 'UPLOAD'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'moins_10_ans'
                      },
                      {
                        id: 'construction_certificat_conformite_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'moins_10_ans'
                      },
                      {
                        id: 'construction_certificat_conformite_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'construction',
                        type: 'EQUALS',
                        value: 'moins_10_ans'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        id: 'construction_certificat_conformite_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  id: 'ae183191_c71c_4e85_8fe8_188ccac926ef',
                  label: 'Certificat de conformité',
                  type: 'CATEGORY'
                }
              ],
              id: '0118fb46_6a72_4afc_a9b5_84a7810a04b7',
              label: "Déclaration d'achèvement",
              type: 'CATEGORY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'construction_regularisation',
              label: 'Régularisation des travaux a posteriori par le Vendeur',
              type: 'SELECT-BINARY'
            },
            {
              id: 'construction_autorisation_assurance_nom',
              label: "Nom de la société d'assurance du constructeur",
              type: 'TEXT'
            },
            {
              id: 'construction_autorisation_assurance_date',
              label: "Date de l'assurance dommage-ouvrage",
              type: 'DATE'
            },
            {
              id: 'construction_autorisation_assurance_adresse',
              label: "Adresse de la société d'assurance du constructeur",
              type: 'ADDRESS'
            },
            {
              id: 'construction_autorisation_assurance_numero',
              label: "Numéro du contrat d'assurance",
              type: 'TEXT'
            }
          ],
          id: '8711e154_40d4_44d0_abdb_afb0c67aacf6',
          label: "Information sur l'autorisation",
          type: 'CATEGORY'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'assurance_decennale_construction',
          label: 'Assurance Décennale - Copropriété',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'assurance_dommage_construction',
          label: 'Assurance Dommage-Ouvrage - Copropriété',
          type: 'UPLOAD'
        }
      ],
      conditions: [
        [
          {
            id: 'construction',
            type: 'EQUALS',
            value: 'moins_10_ans'
          }
        ],
        [
          {
            type: 'EQUALS_CONTRACT_MODELS',
            model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
          }
        ]
      ],
      id: '70a7d44e_3882_46b2_8d88_82a3638f8740',
      label: 'Construction initiale - Copropriété',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'travaux_statut',
          label: 'Présence de travaux',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'travaux_list_travaux_date_achevement',
              label: 'Année d’achèvement des travaux',
              type: 'DATE'
            },
            {
              id: 'travaux_list_travaux_titre',
              label: 'Titre des travaux',
              type: 'TEXT'
            },
            {
              id: 'travaux_list_travaux_description',
              label: 'Descriptif des travaux',
              type: 'TEXTAREA'
            },
            {
              choices: [
                {
                  label: 'Un permis de construire',
                  id: 'permis'
                },
                {
                  label: 'Une déclaration préalable',
                  id: 'declaration'
                },
                {
                  label: 'Aucune autorisation obtenue',
                  id: 'aucune'
                },
                {
                  label: "Travaux sans autorisation spécifique / travaux à l'identique",
                  id: 'non_necessaire'
                }
              ],
              id: 'travaux_list_travaux_autorisation_administration',
              label: "Autorisations d'urbanisme",
              type: 'SELECT'
            },
            {
              children: [
                {
                  id: 'travaux_list_travaux_autorisation_date',
                  label: "Date de l'autorisation",
                  type: 'DATE'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'travaux_list_permis_construire',
                  label: 'Permis de construire',
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'travaux_list_declaration_prealable',
                  label: 'Déclaration préalable',
                  type: 'UPLOAD'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'travaux_list_travaux_declaration_achevement_statut',
                  label: "Déclaration d'achèvement déposée en mairie",
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      conditions: [
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'declaration'
                          },
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'permis'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'declaration'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'permis'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: 'travaux_list_travaux_declaration_achevement_date',
                      label: "Date de la déclaration d'achèvement",
                      type: 'DATE'
                    },
                    {
                      id: 'travaux_list_declaration_achevement',
                      label: "Déclaration d'achèvement",
                      type: 'UPLOAD'
                    },
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'travaux_list_travaux_certificat_conformite_statut',
                      label: 'Certificat de conformité ou de non contestation obtenu',
                      type: 'SELECT-BINARY'
                    },
                    {
                      children: [
                        {
                          id: 'travaux_list_travaux_certificat_conformite_date',
                          label: 'Date de délivrance du certificat',
                          type: 'DATE'
                        },
                        {
                          id: 'travaux_list_certificat_conformite',
                          label: 'Certificat de conformité',
                          type: 'UPLOAD'
                        }
                      ],
                      conditions: [
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'declaration'
                          },
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'permis'
                          },
                          {
                            id: 'travaux_list_travaux_certificat_conformite_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'declaration'
                          },
                          {
                            id: 'travaux_list_travaux_certificat_conformite_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'travaux_list_travaux_autorisation_administration',
                            type: 'EQUALS',
                            value: 'permis'
                          },
                          {
                            id: 'travaux_list_travaux_certificat_conformite_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_list_travaux_declaration_achevement_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'travaux_statut',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ]
                      ],
                      id: 'travaux_list_79914f8f_045d_4c1b_ae34_0a70fb1d060e',
                      label: 'Certificat de conformité',
                      type: 'CATEGORY'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_list_travaux_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'declaration'
                      },
                      {
                        id: 'travaux_list_travaux_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'travaux_list_travaux_autorisation_administration',
                        type: 'EQUALS',
                        value: 'permis'
                      },
                      {
                        id: 'travaux_list_travaux_declaration_achevement_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'travaux_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'travaux_list_1c79fd2c_0e80_4412_88f5_34819af5bdf0',
                  label: "Déclaration d'achèvement",
                  type: 'CATEGORY'
                }
              ],
              conditions: [
                [
                  {
                    id: 'travaux_list_travaux_autorisation_administration',
                    type: 'EQUALS',
                    value: 'declaration'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'travaux_list_travaux_autorisation_administration',
                    type: 'EQUALS',
                    value: 'permis'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_471da26e_b35b_405d_aef5_60d3b889a0b2',
              label: "Information sur l'autorisation",
              type: 'CATEGORY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'travaux_list_travaux_regularisation',
              label: 'Régularisation des travaux a posteriori par le Vendeur',
              type: 'SELECT-BINARY'
            },
            {
              id: 'travaux_list_travaux_professionnel_nom',
              label: 'Nom du professionnel intervenu',
              type: 'TEXT'
            },
            {
              id: 'travaux_list_travaux_do_assurance',
              label: 'Nom Assurance Dommages-ouvrage',
              type: 'TEXT'
            },
            {
              id: 'travaux_list_travaux_do_assurance_numero',
              label: "Numéro de la police d'assurance D-O",
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'travaux_list_travaux_date_achevement',
                    type: 'NOT_OLDER_THAN_N_MONTHS',
                    value: '120'
                  },
                  {
                    id: 'travaux_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'travaux_list_assurance_decennale_travaux',
              label: 'Assurance décennale',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'travaux_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'travaux_list',
          label: 'Travaux',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: ' '
              },
              {
                type: 'VARIABLE',
                value: 'travaux_list_travaux_titre'
              },
              {
                type: 'TEXT',
                value: ' - '
              },
              {
                type: 'VARIABLE',
                value: 'travaux_list_travaux_date_achevement'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        }
      ],
      id: '69fa46e8_94bc_48c2_a668_f31855e41ca4',
      label: 'Travaux - Copropriété',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'assemblees_generales_date_total',
          label: 'Date des trois dernières Assemblée Générale des copropriétaires',
          type: 'TEXT'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'copropriete_budget_previsionnel',
          label: 'Budget prévisionnel',
          type: 'UPLOAD'
        }
      ],
      id: '58024a17_8086_42a5_a1bf_7892a9c56b2b',
      label: 'Informations Générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'fonds_alur_presence',
          label: "Présence d'un fonds ALUR",
          type: 'SELECT-BINARY'
        }
      ],
      id: 'e61c8c4f_2cdb_4ec8_b58a_c084c368773d',
      label: 'Fonds ALUR',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          conditions: [
            [
              {
                id: 'adresse',
                valuePath: 'zip',
                value: '44%',
                type: 'LIKE'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'reglement_sanitaire_loire_atlantique',
          label: 'Règlement Sanitaire Loire-Atlantique',
          type: 'UPLOAD'
        }
      ],
      id: '0fb481bd_b134_4bfa_8b50_4de8a009a3e7',
      label: 'Documents',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              id: 'carnet_entretien_numero_assurance',
              label: 'Numéro de Police du contrat',
              type: 'TEXT'
            },
            {
              id: 'carnet_entretien_nom_adresse_courtier',
              label: 'Nom et adresse du courtier ou agent',
              type: 'TEXT'
            },
            {
              id: 'carnet_entretien_nom_compagnie',
              label: 'Nom de la compagnie',
              type: 'TEXT'
            },
            {
              id: 'carnet_entretien_risques',
              label: 'Risques couverts',
              type: 'TEXT'
            },
            {
              id: 'carnet_entretien_assurance_date_echeance',
              label: "Date d'échéance du contrat",
              type: 'DATE'
            }
          ],
          id: '58ff02a9_5231_46a7_bfaa_915c07ca1e5c',
          label: "Contrat d'assurance",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              children: [
                {
                  id: 'carnet_entretien_contrat_maintenance_liste_carnet_entretien_contrat_maintenance_reference',
                  label: 'Référence du contrat',
                  type: 'TEXT'
                },
                {
                  id: 'carnet_entretien_contrat_maintenance_liste_carnet_entretien_contrat_maintenance_nom_adresse',
                  label: 'Nom et adresse de la société',
                  type: 'TEXT'
                },
                {
                  id: 'carnet_entretien_contrat_maintenance_liste_carnet_entretien_contrat_maintenance_objet',
                  label: 'Objet du contrat',
                  type: 'TEXT'
                },
                {
                  id: 'carnet_entretien_contrat_maintenance_liste_carnet_entretien_contrat_maintenance_date',
                  label: "Date d'échéance du contrat",
                  type: 'DATE'
                }
              ],
              id: 'carnet_entretien_contrat_maintenance_liste',
              label: 'Ajouter un contrat de maintenance/entretien',
              repetition: {
                label: [
                  {
                    type: 'TEXT',
                    value: ' '
                  },
                  {
                    type: 'VARIABLE',
                    value: 'carnet_entretien_contrat_maintenance_liste_carnet_entretien_contrat_maintenance_objet'
                  }
                ],
                max: 1000,
                min: 0
              },
              type: 'REPEAT'
            }
          ],
          id: '2cd4723a_b6ce_4a5a_b4e8_ccbf6f7c0fdf',
          label: "Contrat d'entretien",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              children: [
                {
                  id: 'carnet_entretien_travaux_liste_carnet_entretien_travaux_liste_objet',
                  label: 'Objet des travaux',
                  type: 'TEXT'
                },
                {
                  id: 'carnet_entretien_travaux_liste_carnet_entretien_travaux_liste_date',
                  label: 'Date de réalisation des travaux',
                  type: 'DATE'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'carnet_entretien_travaux_liste_carnet_entretien_travaux_liste_couverture',
                  label: 'Couverture garantie décennale',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'carnet_entretien_travaux_liste_carnet_entretien_travaux_liste_entreprise',
                  label: "Nom et adresse de l'entreprise",
                  type: 'TEXT'
                }
              ],
              id: 'carnet_entretien_travaux_liste',
              label: "Ajouter des travaux importants sur l'immeuble",
              repetition: {
                label: [
                  {
                    type: 'TEXT',
                    value: ' '
                  },
                  {
                    type: 'VARIABLE',
                    value: 'carnet_entretien_travaux_liste_carnet_entretien_travaux_liste_objet'
                  }
                ],
                max: 1000,
                min: 0
              },
              type: 'REPEAT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'carnet_entretien_travaux_dommages_ouvrages',
              label: 'Dommages-ouvrages en cours',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'carnet_entretien_travaux_dommages_ouvrages_nom',
                  label: 'Nom de la compagnie',
                  type: 'TEXT'
                },
                {
                  id: 'carnet_entretien_travaux_dommages_ouvrages_nom_representant',
                  label: 'Nom et adresse du représentant',
                  type: 'TEXT'
                },
                {
                  id: 'carnet_entretien_travaux_dommages_ouvrages_numero_police',
                  label: 'Numéro police',
                  type: 'TEXT'
                },
                {
                  id: 'carnet_entretien_travaux_dommages_ouvrages_date_fin',
                  label: 'Date de fin de couverture',
                  type: 'DATE'
                },
                {
                  id: 'carnet_entretien_travaux_dommages_ouvrages_objet',
                  label: 'Objet du contrat',
                  type: 'TEXT'
                }
              ],
              id: '78dc18ed_4c99_4912_b2ea_54d9a2ec0ffa',
              label: 'Garantie Dommages-ouvrage',
              type: 'CATEGORY'
            }
          ],
          id: '23e3e917_a3ea_4f26_8cb9_b68e5e41c693',
          label: 'Travaux',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'carnet_entretien_pluriannuel_statut',
              label: "Une AG a décidé d'un programme pluriannuel de travaux",
              type: 'SELECT-BINARY'
            },
            {
              id: 'carnet_entretien_pluriannuel_date_ag',
              label: "Date de l'AG",
              type: 'DATE'
            },
            {
              id: 'carnet_entretien_pluriannuel_nature',
              label: 'Nature des travaux',
              type: 'TEXT'
            },
            {
              id: 'carnet_entretien_pluriannuel_date_prevsionnelle',
              label: "Date prévisionnelle d'exécution",
              type: 'DATE'
            }
          ],
          id: '65eae93b_5fa5_4851_a123_23af4c2cee58',
          label: 'Programme pluriannuel de travaux',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'carnet_entretien_informations_complementaire_etude_techniques',
              label: 'Etudes techniques réalisées',
              type: 'TEXT'
            },
            {
              id: 'carnet_entretien_informations_complementaire_diag_techniques',
              label: 'Diagnostics techniques réalisés (amiante, plomb, termites, etc)',
              type: 'TEXT'
            }
          ],
          id: '252e9d81_0f30_4c23_80a8_1674dcd0af01',
          label: 'Informations complémentaires',
          type: 'CATEGORY'
        }
      ],
      id: '8955323d_38f1_4b9e_a04d_8399fb3289b8',
      label: "Carnet d'entretien",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  label: 'Isolé',
                  id: 'isole'
                },
                {
                  label: 'Dans une ferme',
                  id: 'ferme'
                },
                {
                  label: 'Dans un hameau',
                  id: 'hameau'
                },
                {
                  label: 'Dans un village',
                  id: 'village'
                },
                {
                  label: 'Dans une ville',
                  id: 'ville'
                }
              ],
              id: 'etat_descriptif_situation_meuble',
              label: 'Le meublé est',
              type: 'SELECT'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_ski',
                  label: 'Proximité de pistes de ski',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_ski_distance',
                  label: 'Distance des pistes de ski',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_mer',
                  label: 'Proximité de la mer',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_mer_distance',
                  label: 'Distance de la mer',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_lac',
                  label: "Proximité d'un lac",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_lac_distance',
                  label: 'Distance du lac',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_plage',
                  label: "Proximité d'une plage",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_plage_distance',
                  label: 'Distance de la plage',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_foret',
                  label: "Proximité d'une forêt",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_foret_distance',
                  label: 'Distance de la forêt',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_riviere',
                  label: "Proximité d'une rivière",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_riviere_distance',
                  label: 'Distance de la rivière',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_port',
                  label: "Proximité d'un port",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_port_distance',
                  label: 'Distance du port',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_centre_distance',
                  label: 'Distance du centre ville',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_autres',
                  label: "Autres centres d'intérêt",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_autres_distance',
                  label: "Liste et distance des autres centres d'intérêt",
                  type: 'TEXTAREA'
                }
              ],
              id: '3c95a3aa_7afc_4cba_8ff7_b5eb42712e51',
              label: "Distance des centres d'intérêt touristique",
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'etat_descriptif_situation_proximite_sncf_distance',
                  label: 'Distance gare SNCF',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_gare_distance',
                  label: 'Distance gare routière',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_aeroport_distance',
                  label: 'Distance aéroport',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_medecin_distance',
                  label: 'Distance Médecin',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_hopital_distance',
                  label: 'Distance Hôpital',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_supermarche_distance',
                  label: 'Distance Centre commercial/supermarché',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_restaurant_distance',
                  label: 'Distance restaurant',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_epicerie_distance',
                  label: 'Distance épicerie',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  id: 'etat_descriptif_situation_proximite_laverie_distance',
                  label: 'Distance laverie',
                  suffix: 'km',
                  type: 'NUMBER'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_proximite_services_autres',
                  label: 'Autres services',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_proximite_services_autres_distance',
                  label: 'Distance autres services',
                  suffix: 'km',
                  type: 'NUMBER'
                }
              ],
              id: '35d80ad5_d3b3_4690_a3ab_fffffb64df8d',
              label: 'Distance des principaux services',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_inconvenient_bruits',
                  label: 'Bruits',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_inconvenient_bruits_liste',
                  label: 'Liste des bruits',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'etat_descriptif_situation_inconvenient_odeurs',
                  label: 'Odeurs',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'etat_descriptif_situation_inconvenient_odeurs_liste',
                  label: 'Liste des odeurs',
                  type: 'TEXT'
                }
              ],
              id: '2ad1175d_2d1f_4dbf_bcf8_05040f2c1b99',
              label: 'Inconvénients de voisinage',
              type: 'CATEGORY'
            }
          ],
          id: '41eeed16_3c31_4970_bb1a_1fabf4948626',
          label: 'Situation dans la localité',
          type: 'CATEGORY'
        }
      ],
      id: 'be6c6698_a5c6_4eed_a772_235c25919aa7',
      label: 'Etat descriptif',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__STRUCTURE__COPROPRIETE',
  label: 'Copropriété',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__STRUCTURE__COPROPRIETE',
  specificTypes: ['STRUCTURE', 'COPROPRIETE'],
  type: 'RECORD'
};
