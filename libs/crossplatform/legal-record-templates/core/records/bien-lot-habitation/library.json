{"questionTemplates": {"CONSTRUCTION": {"type": "SELECT-PICTURES", "choices": [{"id": "moins_10_ans", "label": "Il y a moins de 10 ans", "icon": "calendar.svg"}, {"id": "apres_1997", "label": "Après le 1er juillet 1997 (date du permis)", "icon": "calendar.svg"}, {"id": "1949_1997", "label": "Après le 1er janvier 1949", "icon": "calendar.svg"}, {"id": "avant_1949", "label": "Avant 1949", "icon": "calendar.svg"}]}, "USAGE": {"type": "SELECT", "choices": [{"label": "Habitation exlusivement", "id": "usage_habitation"}, {"label": "Mixte professionnel et Habitation", "id": "usage_mixte"}]}, "OCCUPATION": {"type": "SELECT", "choices": [{"label": "Occupé à titre gratuit", "id": "occupation_gratuit"}, {"label": "<PERSON><PERSON> selon un contrat de bail", "id": "location_bail"}]}, "BAIL_TYPE": {"type": "SELECT", "choices": [{"label": "Un bail d'habitation", "id": "habitation"}, {"label": "Un bail professionnel", "id": "professionnel"}, {"label": "Un bail commercial", "id": "commercial"}, {"label": "Un bail simple", "id": "simple"}, {"label": "Un bail habitation loi 1948", "id": "1948"}, {"label": "Un bail rural", "id": "rural"}, {"label": "Autre type de bail", "id": "bail_autre"}]}, "LOCATION_PARTIELLE": {"type": "SELECT", "choices": [{"label": "Sur la totalité du bien", "id": "location_totale"}, {"label": "Sur une partie du bien seulement", "id": "location_partielle"}]}, "PLOMB": {"type": "SELECT", "choices": [{"label": "Il n'a pas été repéré de présence de plomb", "id": "absence"}, {"label": "Il a été repéré des mesures de plomb de classe 0", "id": "classe_0"}, {"label": "Il a été repéré des mesures de plomb de classe 1", "id": "classe_1"}, {"label": "Il a été repéré des mesures de plomb de classe 2", "id": "classe_2"}, {"label": "Il a été repéré des mesures de plomb de classe 3", "id": "classe_3"}], "multiple": true}, "AMIANTE": {"type": "SELECT", "choices": [{"label": "Il n'a pas été repéré des matériaux contenant de l'amiante", "id": "absence"}, {"label": "Il a été repéré des matériaux contenant de l'amiante de la liste A", "id": "liste_a"}, {"label": "Il a été repéré des matériaux contenant de l'amiante de la liste B", "id": "liste_b"}, {"label": "Des locaux ou parties de locaux n'ont pas pu être visités", "id": "non_visite"}], "multiple": true}, "TERMITES": {"type": "SELECT", "choices": [{"label": "Absence de traces d'infestation de termite", "id": "absence"}, {"label": "Présence de traces d'infestation de termite", "id": "presence"}]}, "MERULE": {"type": "SELECT", "choices": [{"label": "Absence de traces visibles de champignons lignivores", "id": "absence"}, {"label": "Présence de traces visibles de champignons lignivores", "id": "presence"}]}, "ELECTRICITE_ANOMALIES": {"type": "SELECT", "choices": [{"label": "L'installation électrique ne comporte aucune anomalie", "id": "sans"}, {"label": "1. L’appareil général de commande et de protection et de son accessibilité", "id": "1"}, {"label": "2. La protection différentielle à l’origine de l’installation électrique et sa sensibilité appropriée aux conditions de mise à la terre", "id": "2"}, {"label": "3. La  prise de terre et l’installation de mise à la terre", "id": "3"}, {"label": "4. La protection contre les surintensités adaptée à la section des conducteurs", "id": "4"}, {"label": "5. La liaison équipotentielle dans les locaux contenant une baignoire ou une douche", "id": "5"}, {"label": "6. Les règles liées aux zones dans les locaux contenant une baignoire ou une douche", "id": "6"}, {"label": "7. Des matériels électriques présentant des risques de contacts directs", "id": "7"}, {"label": "8.1 Des matériels électriques vétustes, inadaptés à l’usage", "id": "8_1"}, {"label": "8.2 Des conducteurs non protégés mécaniquement", "id": "8_2"}, {"label": "9. Des appareils d’utilisation situés dans les parties communes et alimentés depuis la partie privative", "id": "9"}, {"label": "10. La piscine privée ou le bassin de fontaine", "id": "10"}], "multiple": true}, "ELECTRICITE_CONSTATATIONS": {"type": "SELECT", "choices": [{"label": "L'installation électrique ne fait l'objet d'aucune constatations", "id": "sans"}, {"label": "1. Des installations non couvertes par le présent diagnostic", "id": "1"}, {"label": "2. Des points de contrôles n’ayant pu être vérifiés", "id": "2"}, {"label": "3. Des constatations concernant l’installation et son environnement", "id": "3"}], "multiple": true}, "GAZ": {"type": "SELECT", "choices": [{"label": "L'installation de gaz ne comporte aucune anomalie", "id": "sans"}, {"label": "L’installation comporte des anomalies de type A1", "id": "1"}, {"label": "L’installation comporte des anomalies de type A2", "id": "2"}, {"label": "L’installation comporte des anomalies de type DGI", "id": "3"}, {"label": "L’installation comporte des anomalies de type 32c", "id": "4"}], "multiple": true}, "TRAVAUX_AUTORISATION": {"type": "SELECT", "choices": [{"label": "Un permis de construire", "id": "permis"}, {"label": "Une déclaration préalable", "id": "declaration"}, {"label": "Aucune autorisation obtenue", "id": "aucune"}, {"label": "Travaux sans autorisation spécifique / travaux à l'identique", "id": "non_necessaire"}]}, "CONTRAT_FREQUENCE": {"type": "SELECT", "choices": [{"id": "contrat_frequence_annuelle", "label": "<PERSON><PERSON><PERSON>"}, {"id": "contrat_frequence_mensuelle", "label": "Mensuellement"}]}, "TVA": {"type": "SELECT", "choices": [{"id": "tva_20", "label": "20 %"}, {"id": "tva_5", "label": "5,5 %"}]}, "DISPONIBILITE": {"type": "SELECT", "choices": [{"id": "disponibilite_libre", "label": "Libre"}, {"id": "disponibilite_reserve", "label": "Reservé"}, {"id": "disponibilite_prereserve", "label": "Préreservé"}]}, "ORIGINE": {"type": "SELECT", "choices": [{"id": "origine_propriete_acquisition", "label": "Acquisition"}, {"id": "origine_propriete_donation", "label": "Donation"}, {"id": "origine_propriete_succession", "label": "Succession"}, {"id": "origine_propriete_partage", "label": "Partage - Licitation"}, {"id": "origine_propriete_echange", "label": "Echange"}, {"id": "origine_propriete_adjudication", "label": "Adjudication - Vente aux enchères"}, {"id": "origine_propriete_remembrement", "label": "Remembrement"}]}, "TYPE_CHAUFFAGE": {"type": "SELECT", "choices": [{"id": "chauffage_electricite", "label": "Electricité"}, {"id": "chauffage_gaz", "label": "Gaz"}, {"id": "chauffage_collectif", "label": "Collectif"}, {"id": "chauffage_autre", "label": "Autre mode :"}]}, "CARACTERISTIQUES": {"type": "SELECT", "choices": [{"id": "caracteristique_neuf", "label": "<PERSON><PERSON><PERSON>"}, {"id": "caracteristique_ancien", "label": "Ancien"}, {"id": "caracteristique_vefa", "label": "En état futur d'achèvement"}, {"id": "caracteristique_renover", "label": "A rénover"}]}, "OCCUPATION_MANDAT": {"type": "SELECT", "choices": [{"id": "occupation_mandat_libre", "label": "Libre de toute occupation"}, {"id": "occupation_mandat_liberation", "label": "Libre à la date"}, {"id": "occupation_mandat_loue", "label": "Loué selon état locatif figurant en annexe"}]}, "MANDAT_SUPERFICIE": {"type": "SELECT", "choices": [{"id": "mandat_superficie_mandant", "label": "Le Mandant"}, {"id": "mandat_superficie_mandataire", "label": "Le Mandataire"}, {"id": "mandat_superficie_diagnostiqueur", "label": "Un Diagnostiqueur"}]}, "NATURE_BIEN": {"type": "SELECT", "choices": [{"id": "nature_bien_appartement", "label": "Appartement"}, {"id": "nature_bien_duplex", "label": "Duplex"}, {"id": "nature_bien_triplex", "label": "<PERSON>x"}, {"id": "nature_bien_maison", "label": "Maison en copropriété"}, {"id": "nature_bien_autre", "label": "<PERSON><PERSON>"}]}, "PARKING": {"type": "SELECT", "choices": []}, "DIAG_MLS": {"type": "SELECT", "choices": [{"id": "diag_mls_present", "label": "Le Mandant remet le diagnostic à la signature du mandat"}, {"id": "diag_mls_mandant", "label": "Le Mandant établit le diagnostic avant le compromis"}, {"id": "diag_mls_mandataire", "label": "Le Mandant charge le Mandataire d'établir le diagnostic"}]}, "NATURE_BIEN_LEIZEE": {"type": "SELECT", "choices": [{"id": "leizee_appartement", "label": "Appartement"}, {"id": "leizee_batiment", "label": "Bâtiment"}, {"id": "leizee_chalet", "label": "<PERSON><PERSON>"}, {"id": "leizee_chambre", "label": "Chambre"}, {"id": "leizee_chambre_double_medicalisee", "label": "Chambre double médicalisée"}, {"id": "leizee_chambre_etudiante", "label": "Chambre étudiante"}, {"id": "leizee_chambre_medicalisee", "label": "Chambre médicalisée"}, {"id": "leizee_duplex", "label": "Duplex"}, {"id": "leizee_loft", "label": "Loft"}, {"id": "leizee_maison", "label": "<PERSON><PERSON>"}, {"id": "leizee_terrain", "label": "Terrain"}, {"id": "leizee_triplex", "label": "<PERSON>x"}]}, "ANNEXES_TYPE_LEIZEE": {"type": "SELECT", "choices": [{"id": "annexe_leizee_abri", "label": "Abri"}, {"id": "annexe_leizee_auvent", "label": "Auvent"}, {"id": "annexe_leizee_balcon", "label": "Balcon"}, {"id": "annexe_leizee_casier_ski", "label": "<PERSON><PERSON><PERSON>"}, {"id": "annexe_leizee_cave", "label": "Cave"}, {"id": "annexe_leizee_cour", "label": "Cour"}, {"id": "annexe_leizee_jardin", "label": "Jardin"}, {"id": "annexe_leizee_local", "label": "Local"}, {"id": "annexe_leizee_local_velo", "label": "Local à vélo"}, {"id": "annexe_leizee_loggia", "label": "Loggia"}, {"id": "annexe_leizee_nom", "label": "Nom"}, {"id": "annexe_leizee_parcelle", "label": "<PERSON><PERSON><PERSON>"}, {"id": "annexe_leizee_patio", "label": "<PERSON><PERSON>"}, {"id": "annexe_leizee_porche", "label": "Porche"}, {"id": "annexe_leizee_rangement_exterieur", "label": "Rangement extérieur"}, {"id": "annexe_leizee_remise", "label": "Remise"}, {"id": "annexe_leizee_rentabilite", "label": "Rentabilité en %"}, {"id": "annexe_leizee_sechoir", "label": "Séchoir"}, {"id": "annexe_leizee_solarium", "label": "Solarium"}, {"id": "annexe_leizee_stationnement_1", "label": "Stationnement 1"}, {"id": "annexe_leizee_stationnement_2", "label": "Stationnement 2"}, {"id": "annexe_leizee_stationnement_3", "label": "Stationnement 3"}, {"id": "annexe_leizee_stationnement_4", "label": "Stationnement 4"}, {"id": "annexe_leizee_surface_defiscalisable", "label": "Surface défiscalisable"}, {"id": "annexe_leizee_terrasse", "label": "Terrasse"}, {"id": "annexe_leizee_varangue", "label": "Varangue"}, {"id": "annexe_leizee_veranda", "label": "Véranda"}]}, "DESTINATION": {"type": "SELECT", "choices": [{"id": "destination_principale", "label": "Résidence principale"}, {"id": "destination_secondaire", "label": "Résidence secondaire"}, {"id": "destination_locatif", "label": "Investissement locatif"}]}, "SANIBROYEUR": {"type": "SELECT", "choices": [{"id": "obtenue", "label": "Une autorisation a été obtenue"}, {"id": "en_cours", "label": "Une autorisation est en cours"}, {"id": "aucune", "label": "Aucune autorisation n'a été obtenue"}]}, "VENDEUR_ACQUREUR": {"type": "SELECT", "choices": [{"id": "vendeur", "label": "<PERSON>endeur"}, {"id": "ac<PERSON><PERSON>", "label": "de l'Acquéreur"}]}, "COPRO_AUTORISATION": {"type": "SELECT", "choices": [{"id": "autorisation_non_necessaire", "label": "L'autorisation de l'assemblée générale n'est pas nécessaire"}, {"id": "autorisation_obtenue", "label": "L'autorisation de l'assemblée générale est obtenue"}, {"id": "autorisation_a_obtenir", "label": "L'autorisation de l'assemblée générale sera demandée par le Vendeur"}, {"id": "autorisation_non_obtenue", "label": "L'autorisation de l'assemblée générale na pas été obtenue"}]}, "VALLOIRE_DIAGNOSTIQUEUR": {"type": "SELECT", "choices": [{"id": "exim", "label": "EX'IM"}, {"id": "environnement", "label": "AC ENVIRONNEMENT"}, {"id": "allo", "label": "ALLO DIAGNOSTIC"}, {"id": "adx", "label": "ADX GROUPE - ALLODIAGNOSTIC"}, {"id": "aed", "label": "AED GROUPE - MERIGNAC"}]}, "TRAVAUX_SIMPLE_OBJET_LISTE": {"type": "SELECT", "choices": [{"id": "exterieur", "label": "L'aspect extérieur du bâti"}, {"id": "structure", "label": "La structure du bâtiment"}, {"id": "aucun", "label": "Aucun des deux"}]}, "RISQUES_NATURELS": {"type": "SELECT", "choices": [{"label": "Zone non concernée par un plan de prévention des risques naturels", "id": "aucun_plan"}, {"label": "Périmètre d'un plan de prévention des risques naturels prescrits", "id": "plan_prescrit"}, {"label": "Périmètre d'un plan de prévention des risques naturels anticipés", "id": "plan_anticipe"}, {"label": "Périmètre d'un plan de prévention des risques naturels approuvés", "id": "plan_approuve"}], "multiple": true}, "RISQUES_MINIERS": {"type": "SELECT", "choices": [{"label": "Zone non concernée par un plan de prévention des risques miniers", "id": "aucun_plan"}, {"label": "Périmètre d'un plan de prévention des risques miniers prescrits", "id": "plan_prescrit"}, {"label": "Périmètre d'un plan de prévention des risques miniers anticipés", "id": "plan_anticipe"}, {"label": "Périmètre d'un plan de prévention des risques miniers approuvés", "id": "plan_approuve"}], "multiple": true}, "RISQUES_TECHNOLOGIQUES": {"type": "SELECT", "choices": [{"label": "Zone non concernée par un plan de prévention des risques technologiques", "id": "aucun_plan"}, {"label": "Périmètre d'un plan de prévention des risques technologiques prescrits", "id": "plan_prescrit"}, {"label": "Périmètre d'un plan de prévention des risques technologiques anticipés", "id": "plan_anticipe"}, {"label": "Périmètre d'un plan de prévention des risques technologiques approuvés", "id": "plan_approuve"}], "multiple": true}, "PRICE_CFP": {"type": "PRICE", "suffix": "CFP"}, "MANDAT_TYPE": {"type": "SELECT", "choices": [{"id": "simple", "label": "de location simple"}, {"id": "semi", "label": "de location semi-exclusif"}, {"id": "exclusif", "label": "de location exclusif"}, {"id": "gestion", "label": "de gestion"}]}, "EQUIPEMENTS": {"type": "PICK_LIST", "choices": [{"id": "cable", "label": "Raccordement au câble"}, {"id": "adsl", "label": "Raccordement à l'ADSL"}, {"id": "fibre", "label": "Raccordement à la fibre"}, {"id": "aucun", "label": "Aucun équipement"}, {"id": "autre", "label": "Autre équipement"}], "multiple": true}, "INDIVIDUEL_COLLECTIF": {"type": "SELECT", "choices": [{"id": "individuel", "label": "Individuel"}, {"id": "collectif", "label": "Collectif"}]}, "ORIGINE_GIBOIRE": {"type": "SELECT", "choices": [{"id": "origine_propriete_acquisition", "label": "Acquisition"}, {"id": "origine_propriete_vefa", "label": "VEFA"}, {"id": "origine_propriete_donation", "label": "Donation"}, {"id": "origine_propriete_succession", "label": "Succession"}, {"id": "origine_propriete_partage", "label": "Partage - Licitation"}, {"id": "origine_propriete_echange", "label": "Echange"}, {"id": "origine_propriete_adjudication", "label": "Adjudication - Vente aux enchères"}, {"id": "origine_propriete_remembrement", "label": "Remembrement"}]}, "LOUE_LIBRE": {"type": "SELECT-BINARY", "choices": [{"id": "libre", "label": "Libre"}, {"id": "loue", "label": "<PERSON><PERSON>"}]}, "CHAUDIERE_TYPE": {"type": "SELECT", "choices": [{"id": "gaz", "label": "Gaz"}, {"id": "fioul", "label": "<PERSON><PERSON><PERSON>"}, {"id": "autre", "label": "<PERSON><PERSON>"}]}, "CHEMINEE_PERIL": {"type": "SELECT", "choices": [{"id": "arrete", "label": "Un arrêté de péril imminent a été pris"}, {"id": "arrete_complementaire", "label": "Un arrêté de péril imminent complémentaire a été pris"}, {"id": "levee", "label": "La procédure engagée a été levée"}], "multiple": true}, "CHEMINEE_STATUT_SIMPLE": {"type": "SELECT", "choices": [{"id": "hs", "label": "Cheminée Hors Service"}, {"id": "ramonage_fait", "label": "<PERSON><PERSON><PERSON><PERSON> fon<PERSON>, ramonage effectué"}, {"id": "ramonage_a_faire", "label": "<PERSON>emin<PERSON> fonctionnelle, ramonage à faire"}]}, "INDIGNE_DUALITE": {"type": "SELECT", "choices": [{"id": "autorisation", "label": "Une autorisation préalable"}, {"id": "declaration", "label": "Une déclaration postérieure"}]}, "capacite_personne] [@NUMBER": {"type": "SELECT", "choices": []}, "MEUBLE_SITUATION": {"type": "SELECT", "choices": [{"label": "Isolé", "id": "isole"}, {"label": "Dans une ferme", "id": "ferme"}, {"label": "Dans un hameau", "id": "hameau"}, {"label": "Dans un village", "id": "village"}, {"label": "Dans une ville", "id": "ville"}]}, "CLASSEMENT_CATEGORIE": {"type": "SELECT", "choices": [{"label": "1 étoile", "id": "1"}, {"label": "2 étoiles", "id": "2"}, {"label": "3 étoiles", "id": "3"}, {"label": "4 étoiles", "id": "4"}, {"label": "5 étoiles", "id": "5"}, {"label": "Aucun classement", "id": "aucun"}]}, "ETAT_DESCRIPTIF_TYPE_CONSTRUCTION": {"type": "SELECT", "choices": [{"label": "Neuve", "id": "neuve"}, {"label": "Ré<PERSON><PERSON>", "id": "recente"}, {"label": "Ancienne", "id": "ancienne"}]}, "ETAT_DESCRIPTIF_TYPE_BIEN": {"type": "SELECT", "choices": [{"label": "Une maison", "id": "maison"}, {"label": "Indépendante", "id": "independant"}, {"label": "Avec jardin", "id": "jardin"}, {"label": "Un studio", "id": "studio"}, {"label": "Un appartement", "id": "appartement"}], "multiple": true}, "ETAT_DESCRIPTIF_PIECES_SITUATION": {"type": "SELECT", "choices": [{"label": "Un appartement", "id": "appartement"}, {"label": "Une villa", "id": "villa"}, {"label": "Occupées partiellement par le propriétaire", "id": "proprietaire"}, {"label": "Occupées par d'autres locataires", "id": "locataire"}], "multiple": true}, "PIECES_CARACTERISTIQUES": {"type": "SELECT", "choices": [{"label": "<PERSON><PERSON><PERSON><PERSON>", "id": "cuisine_separee"}, {"label": "Coin-cuisine dans la pièce principale", "id": "cuisine"}, {"label": "Existence d'une entrée", "id": "entree"}], "multiple": true}, "JOUISSANCE_PRIVATIVE": {"type": "SELECT", "choices": [{"label": "Jardin privatif", "id": "jardin"}, {"label": "Parc privatif", "id": "parc"}, {"label": "Cour privative", "id": "cour"}, {"label": "Garage privatif", "id": "garage"}, {"label": "Emplacement de voiture privatif", "id": "emplacement"}, {"label": "Terrasse privative", "id": "terrasse"}, {"label": "Loggia privative", "id": "loggia"}, {"label": "Balcon privatif", "id": "balcon"}], "multiple": true}, "EXPOSITION": {"type": "SELECT", "choices": [{"label": "Nord", "id": "nord"}, {"label": "Sud", "id": "sud"}, {"label": "Est", "id": "est"}, {"label": "Ouest", "id": "ouest"}]}, "ETAT_DESCRIPTIF_AGENCEMENT_CUISINE": {"type": "SELECT", "choices": [{"label": "Evier avec eau chaude/froide", "id": "evier"}, {"label": "VMC", "id": "vmc"}, {"label": "<PERSON><PERSON> aspi<PERSON>e", "id": "hotte"}, {"label": "Table de cuisson", "id": "cuisson"}, {"label": "Four", "id": "four"}, {"label": "Four à micro-ondes", "id": "microonde"}, {"label": "Réfrigérateur", "id": "refrigerateur"}, {"label": "<PERSON><PERSON><PERSON><PERSON>", "id": "congelateur"}, {"label": "Lave-vais<PERSON>le", "id": "lave"}, {"label": "Batterie de cuisine", "id": "batterie"}, {"label": "Autocuiseur", "id": "autocuiseur"}], "multiple": true}, "ALIMENTATION_PLAQUE": {"type": "SELECT", "choices": [{"label": "Gaz de ville", "id": "gaz"}, {"label": "Bouteille de gaz", "id": "bouteille"}, {"label": "Electricité", "id": "electricite"}, {"label": "Mixte", "id": "mixte"}]}, "ETAT_DESCRIPTIF_EQUIPEMENT_BIEN": {"type": "SELECT", "choices": [{"label": "Téléphone dans le logement", "id": "telephone"}, {"label": "Téléphone à proximité", "id": "telephone_proximite"}, {"label": "Accès internet haut debit", "id": "internet"}, {"label": "TV couleur", "id": "tv"}, {"label": "Lecteur DVD", "id": "lecteur"}, {"label": "Chaîne Hi-Fi avec radio", "id": "radio"}, {"label": "Lave-linge électrique", "id": "lave_linge"}, {"label": "Sèche-Linge électrique", "id": "seche_linge"}, {"label": "Etendoir à linge", "id": "etendoir"}, {"label": "Fer à repasser", "id": "fer"}, {"label": "Sèche-cheveux", "id": "seche_cheveux"}, {"label": "As<PERSON>rateur", "id": "aspirateur"}, {"label": "Autres équipements", "id": "autre"}], "multiple": true}, "ETAT_DESCRIPTIF_CHAUFFAGE": {"type": "SELECT", "choices": [{"label": "Chauffage central", "id": "chauffage"}, {"label": "Climatisation", "id": "climatisation"}, {"label": "Rafraîchissement d'air", "id": "rafraichissement"}], "multiple": true}, "NUMBER_CDC_PARKING": {"type": "NUMBER", "max": 3}, "NUMBER_CDC_CAVE": {"type": "NUMBER", "max": 3}, "STATUT_PV_ELECTRIQUE": {"type": "SELECT", "choices": [{"label": "PV de contrôle établi", "id": "etabli"}, {"label": "PV de contrôle non établi", "id": "non_etabli"}, {"label": "Dispense de contrôle", "id": "dispense"}]}, "ELECTRICITE_DISPENSE": {"type": "SELECT", "choices": [{"label": "Démolition du bâtiment", "id": "demolition"}, {"label": "Rénovation de l'installation", "id": "renovation"}]}, "BELGIQUE_CERTIBEAU_STATUT": {"type": "SELECT", "choices": [{"label": "Raccordement antérieur au 1er Juin 2021", "id": "raccordement_ante_2021"}, {"label": "Raccordement postérieur au 1er Juin 2021", "id": "raccordement_post_2021"}, {"label": "Immeuble sur plan", "id": "plan"}, {"label": "Local où l'eau est fournie au public", "id": "public"}]}, "GAZ_MAZOUT": {"type": "SELECT-BINARY", "choices": [{"label": "GAZ", "id": "gaz"}, {"label": "MAZOUT", "id": "mazout"}]}, "PERMIS_DECLARATION": {"type": "SELECT-BINARY", "choices": [{"label": "PERMIS", "id": "permis"}, {"label": "DECLARATION", "id": "declaration"}]}, "ETAT_POSTE_PRINCIPAUX": {"type": "SELECT", "choices": [{"label": "<PERSON><PERSON><PERSON>", "id": "neuf"}, {"label": "Excellent", "id": "excellent"}, {"label": "Remis à neuf", "id": "remis_neuf"}, {"label": "Bon état", "id": "bon_etat"}, {"label": "A rafraîchir", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "A rénover", "id": "renover"}, {"label": "A réhabiliter", "id": "rehabiliter"}, {"label": "A démolir", "id": "demolir"}, {"label": "<PERSON><PERSON>", "id": "autre"}]}, "AERIENNE_ENTERREE": {"type": "SELECT", "choices": [{"label": "Aérienne", "id": "aerienne"}, {"label": "Enterrée", "id": "enterree"}]}, "BELGIQUE_SITUATION_BIEN": {"type": "SELECT", "choices": [{"label": "En région Wallonne", "id": "wallonne"}, {"label": "En région Bruxelles-Capitale", "id": "bruxelles"}]}, "ZONE_TENDUE_LISTE": {"type": "SELECT", "choices": [{"id": "non", "label": "Non Tendue"}, {"id": "oui", "label": "Tendue"}, {"id": "tres_tendue", "label": "<PERSON><PERSON><PERSON>"}], "description": "Code postal détecté dans une zone non tendue, tendue ou très tendue"}, "LISTE_CHAUFFAGE": {"type": "SELECT", "choices": [{"id": "chaudiere_gaz", "label": "Chaudière G<PERSON>"}, {"id": "chaudiere_fioul", "label": "<PERSON><PERSON><PERSON>"}, {"id": "cheminee", "label": "Cheminée"}, {"id": "poele_bois", "label": "<PERSON><PERSON><PERSON> à bois"}, {"id": "poele_granules", "label": "Poêle à granulés"}, {"id": "radiateur", "label": "Convecteurs électriques"}, {"id": "pompe", "label": "Pompe à Chaleur"}, {"id": "aucun", "label": "Aucun système de chauffage"}], "multiple": true}, "ETAT_CUVE_FIOUL": {"type": "SELECT", "choices": []}, "ASSAINISSEMENT_JO": {"type": "SELECT", "choices": [{"id": "unitaire", "label": "PARIS Uniquement - Raccordement au réseau unitaire - certificat délivré"}, {"id": "realise", "label": "Contrôle effectué"}, {"id": "non_realise", "label": "Contrôle non réalisé"}]}, "SERVITUDE_CONSTITUER_TYPE": {"type": "SELECT", "choices": [{"id": "echelle", "label": "Tour d'échelle"}, {"id": "surplomb", "label": "Surplomb"}, {"id": "pignon", "label": "<PERSON><PERSON>"}, {"id": "eaux", "label": "Ecoulement des eaux pluviales"}, {"id": "passage", "label": "Passage"}, {"id": "accrochage", "label": "Accrochage"}, {"id": "vue", "label": "<PERSON><PERSON>"}, {"id": "boite", "label": "Boîtes aux lettres"}, {"id": "autre", "label": "Autre servitude"}]}, "SERVITUDE_CHARGE_LISTE": {"type": "SELECT", "choices": [{"id": "vendeur", "label": "<PERSON>eur/Promettant"}, {"id": "ac<PERSON><PERSON>", "label": "De l'Acquéreur/Bénéficiaire"}, {"id": "proprietaire", "label": "Du propriétaire du fonds dominant"}, {"id": "moitie", "label": "Partagés par moitié entre propriétaires"}]}, "CONVENTION_ENTRETIEN_LISTE": {"type": "SELECT", "choices": [{"id": "gouttieres_toitures", "label": "Toiture d'un seul tenant et gouttières communes"}, {"id": "toitures", "label": "Toitures d'un seul tenant"}, {"id": "gouttieres", "label": "Gouttières communes"}, {"id": "facade", "label": "Façade commune"}, {"id": "descente_gouttiere", "label": "Descente de gouttière à hauteur de limite"}, {"id": "autre", "label": "Autres conventions"}], "multiple": true}, "CONDITIONS_PARTICULIERES_SERVITUDES_LISTE": {"type": "SELECT", "choices": [{"id": "indivision", "label": "Chemin en indivision"}, {"id": "candelabre", "label": "Candélabre"}, {"id": "antenne", "label": "Antenne collective"}, {"id": "panneau", "label": "Panneau de signalisation"}, {"id": "telecom", "label": "Coffret Telecom"}, {"id": "autre", "label": "Autres conditions"}], "multiple": true}, "USAGE_GESTION": {"type": "SELECT", "choices": [{"id": "habitation", "label": "Habitation"}, {"id": "mixte", "label": "Mixte"}, {"id": "commercial", "label": "Commercial"}, {"id": "professionnel", "label": "Professionnel"}]}}, "conditions": {"AVANTAGE_FISCAL": [{"id": "avantage_fiscal_statut", "type": "EQUALS", "value": "oui"}], "PARTIES_COMMUNES_SPECIALES": [{"id": "parties_communes_speciales_statut", "type": "EQUALS", "value": "oui"}], "MESURAGE_SURFACE_HABITABLE_PRO": [{"id": "surface_habitable_professionnel", "type": "EQUALS", "value": "oui"}], "MESURAGE_CARREZ": [{"id": "mesurage_carrez_statut", "type": "EQUALS", "value": "oui"}], "CHEMINEE": [{"id": "cheminee_statut", "type": "EQUALS", "value": "oui"}], "CHEMINEE_FONCTIONNELLE": [{"id": "cheminee_fonctionnelle_statut", "type": "EQUALS", "value": "oui"}], "CHEMINEE_FONCTIONNELLE_RAMONAGE": [{"id": "cheminee_fonctionnelle_ramonage", "type": "EQUALS", "value": "oui"}], "CONTRATS_ATTACHES": [{"id": "contrats_attaches_statut", "type": "EQUALS", "value": "oui"}], "OCCUPATION": [{"conditions": [{"id": "occupation_statut", "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__VENTE_ANCIEN", "OPERATION__ICM__DOSSIER_DE_VENTE_ICM", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_NEUF", "OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO", "OPERATION__KEREDES__IMMOBILIER_BRS", "OPERATION__BEAUX_VILLAGES__IMMOBILIER", "OPERATION__CANNISIMMO__IMMOBILIER", "OPERATION__SANTONI__IMMOBILIER", "OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESSIONNEL_AJP", "OPERATION__ORPI__IMMOBILIER__VENTE", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION", "OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION", "OPERATION__HERMES__IMMOBILIER__TRANSACTION_PRO", "OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE", "OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE", "OPERATION__ERA__IMMOBILIER__VENTE", "OPERATION__IMOCONSEIL__IMMOBILIER__VENTE", "OPERATION__GIBOIRE__IMMOBILIER__VENTE", "OPERATION__UNIS__IMMOBILIER__HABITATION", "OPERATION__UNIS__IMMOBILIER__COMMERCIAL", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE", "OPERATION__BENEDIC__IMMOBILIER", "OPERATION__EFFICITY__IMMOBILIER", "OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION", "OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE", "OPERATION__I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL", "OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_ACHEVE", "OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE", "OPERATION__COLDWELL_BANKER__IMMOBILIER", "OPERATION__FOLLIOT__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER", "OPERATION__IMMOBILIER__VENTE_VIAGER", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VIAGER", "OPERATION__IMMOBILIER__SOCIAL_VENTE", "OPERATION__CDC__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME", "OPERATION__CLESENCE__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF", "OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF", "OPERATION__IMMOBILIER__SOCIAL_PROGRAMME", "OPERATION__CDC__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE", "OPERATION__ALLOWA__IMMOBILIER", "OPERATION__PROPRIETES_PRIVEES__IMMOBILIER", "OPERATION__PROPRIETES_PRIVEES__IMMORESEAU", "OPERATION__PROPRIETES_PRIVEES__PAUL_PARKER", "OPERATION__PROPRIETES_PRIVEES__REZOXIMO"]}], "OCCUPATION_GRATUIT": [{"id": "occupation_location", "type": "EQUALS", "value": "occupation_gratuit"}], "LOCATION": [{"id": "occupation_location", "type": "EQUALS", "value": "location_bail"}], "LOCATION_BAIL_TYPE_AUTRE": [{"id": "location_bail_type", "type": "EQUALS", "value": "bail_autre"}], "AVANT_1949": [{"id": "construction", "type": "EQUALS", "value": "avant_1949"}], "MOINS_10_ANS": [{"id": "construction", "type": "EQUALS", "value": "moins_10_ans"}], "1949_1997": [{"id": "construction", "type": "EQUALS", "value": "1949_1997"}], "DIAGNOSTIQUEUR_UNIQUE": [{"id": "diagnostics_techniques_diagnostiqueur_unique", "type": "EQUALS", "value": "oui"}], "PAS_DIAGNOSTIQUEUR_UNIQUE": [{"id": "diagnostics_techniques_diagnostiqueur_unique", "type": "EQUALS", "value": "non"}], "DPE": [{"id": "dpe_details_statut", "type": "EQUALS", "value": "oui"}], "PLOMB": [{"id": "diagnostic_plomb_statut", "type": "EQUALS", "value": "oui"}], "AMIANTE": [{"id": "diagnostic_amiante_statut", "type": "EQUALS", "value": "oui"}], "PRESENCE_GAZ": [{"id": "diagnostic_gaz_presence_installation", "type": "EQUALS", "value": "oui"}], "GAZ_VIEILLE_INSTALLATION": [{"id": "diagnostic_gaz_vielle_installation", "type": "EQUALS", "value": "oui"}], "GAZ": [{"id": "diagnostic_gaz_statut", "type": "EQUALS", "value": "oui"}], "ELECTRIQUE_VIEILLE_INSTALLATION": [{"id": "diagnostic_electrique_vielle_installation", "type": "EQUALS", "value": "oui"}], "ELECTRIQUE": [{"id": "diagnostic_electrique_statut", "type": "EQUALS", "value": "oui"}], "TERMITES": [{"id": "diagnostic_termites_statut", "type": "EQUALS", "value": "oui"}], "MERULE": [{"id": "diagnostic_merule_statut", "type": "EQUALS", "value": "oui"}], "TRAVAUX": [{"id": "travaux_statut", "type": "EQUALS", "value": "oui"}], "TRAVAUX_PERMIS": [{"id": "travaux_autorisation_administration", "type": "EQUALS", "value": "permis"}], "TRAVAUX_DECLARATION": [{"id": "travaux_autorisation_administration", "type": "EQUALS", "value": "declaration"}], "TRAVAUX_DECLARATION_ACHEVEMENT": [{"id": "travaux_declaration_achevement_statut", "type": "EQUALS", "value": "oui"}], "TRAVAUX_CERTIFICAT_CONFORMITE": [{"id": "travaux_certificat_conformite_statut", "type": "EQUALS", "value": "oui"}], "TRAVAUX_MOINS_10_ANS": [{"id": "travaux_date_achevement", "type": "NOT_OLDER_THAN_N_MONTHS", "value": "120"}], "TRAVAUX_COPROPRIETE_AUTORISATION": [{"id": "travaux_copropriete_autorisation", "type": "EQUALS", "value": "oui"}], "TRAVAUX_COPROPRIETE_AUTORISATION_OBTENUE": [{"id": "travaux_copropriete_autorisation_statut", "type": "EQUALS", "value": "oui"}], "ANTERIEURE_MOINS_18_MOIS": [{"id": "loue_anterieurement_moins_18_mois", "value": "oui", "type": "EQUALS"}], "ANTERIEURE_TRAVAUX": [{"id": "loue_anterieurement_travaux", "value": "oui", "type": "EQUALS"}], "SAISONNIER_RESIDENCE": [{"id": "saisonnier_residence", "value": "oui", "type": "EQUALS"}], "OP_DONATION": [{"id": "origine_liste", "value": "origine_propriete_donation", "type": "EQUALS"}], "OP_DONATION_UNIQUE": [{"id": "origine_donation_unique", "value": "non", "type": "EQUALS"}], "OP_SUCCESSION": [{"id": "origine_liste", "value": "origine_propriete_succession", "type": "EQUALS"}], "OP_ADJUDICATION": [{"id": "origine_liste", "value": "origine_propriete_adjudication", "type": "EQUALS"}], "OP_ACQUISITION": [{"id": "origine_liste", "value": "origine_propriete_acquisition", "type": "EQUALS"}], "OP_ECHANGE": [{"id": "origine_liste", "value": "origine_propriete_echange", "type": "EQUALS"}], "OP_PARTAGE": [{"id": "origine_liste", "value": "origine_propriete_partage", "type": "EQUALS"}], "OP_SUCCESSION_TERMINEE": [{"id": "origine_succession_statut", "value": "oui", "type": "EQUALS"}], "OP_SUCCESSION_NON_TERMINEE": [{"id": "origine_succession_statut", "value": "non", "type": "EQUALS"}], "OP_REMEMBREMENT": [{"id": "origine_liste", "value": "origine_propriete_remembrement", "type": "EQUALS"}], "CHAUFFAGE_AUTRE": [{"id": "mode_chauffage", "value": "chauffage_autre", "type": "EQUALS"}], "EAU_CHAUFFAGE_AUTRE": [{"id": "eau_mode_chauffage", "value": "chauffage_autre", "type": "EQUALS"}], "CUISSON_CHAUFFAGE_AUTRE": [{"id": "cuisson_mode_chauffage", "value": "chauffage_autre", "type": "EQUALS"}], "OCCUPATION_MANDAT_LIBERATION": [{"id": "occupation_mandat", "value": "occupation_mandat_liberation", "type": "EQUALS"}], "OCCUPATION_MANDAT_LOUE": [{"id": "occupation_mandat", "value": "occupation_mandat_loue", "type": "EQUALS"}], "NATURE_AUTRE": [{"id": "nature_bien", "value": "nature_bien_autre", "type": "EQUALS"}], "GARAGE": [{"id": "programme_garage", "value": "oui", "type": "EQUALS"}], "BALCON": [{"id": "programme_desimo_balcon", "value": "oui", "type": "EQUALS"}], "JARDIN": [{"id": "programme_desimo_jardin", "value": "oui", "type": "EQUALS"}], "TERRASSE": [{"id": "programme_desimo_terrasse", "value": "oui", "type": "EQUALS"}], "CAVE": [{"id": "programme_cave", "value": "oui", "type": "EQUALS"}], "CELLIER": [{"id": "programme_cellier", "value": "oui", "type": "EQUALS"}], "LEIZEE_ANNEXE_ABRI": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_abri", "type": "EQUALS"}], "LEIZEE_ANNEXE_CASIER": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_casier_ski", "type": "EQUALS"}], "LEIZEE_ANNEXE_CAVE": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_cave", "type": "EQUALS"}], "LEIZEE_ANNEXE_LOCAL": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_local", "type": "EQUALS"}], "LEIZEE_ANNEXE_LOCAL_VELO": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_local_velo", "type": "EQUALS"}], "LEIZEE_ANNEXE_RANGEMENT": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_rangement_exterieur", "type": "EQUALS"}], "LEIZEE_ANNEXE_REMISE": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_remise", "type": "EQUALS"}], "LEIZEE_ANNEXE_SECHOIR": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_sechoir", "type": "EQUALS"}], "LEIZEE_ANNEXE_STATIONNEMENT": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_stationnement_1", "type": "EQUALS"}, {"id": "", "value": ""}], "LEIZEE_ANNEXE_AUVENT": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_auvent", "type": "EQUALS"}], "LEIZEE_ANNEXE_BALCON": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_balcon", "type": "EQUALS"}], "LEIZEE_ANNEXE_COUR": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_cour", "type": "EQUALS"}], "LEIZEE_ANNEXE_JARDIN": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_jardin", "type": "EQUALS"}], "LEIZEE_ANNEXE_LOGGIA": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_loggia", "type": "EQUALS"}], "LEIZEE_ANNEXE_SOLARIUM": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_solarium", "type": "EQUALS"}], "LEIZEE_ANNEXE_TERRASSE": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_terrasse", "type": "EQUALS"}], "LEIZEE_ANNEXE_VARANGUE": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_varangue", "type": "EQUALS"}], "LEIZEE_ANNEXE_VERANDA": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_veranda", "type": "EQUALS"}], "LEIZEE_ANNEXE_STATIONNEMENT_1": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_stationnement_1", "type": "EQUALS"}], "LEIZEE_ANNEXE_STATIONNEMENT_2": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_stationnement_2", "type": "EQUALS"}], "LEIZEE_ANNEXE_STATIONNEMENT_3": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_stationnement_3", "type": "EQUALS"}], "LEIZEE_ANNEXE_STATIONNEMENT_4": [{"id": "programme_annexes_leizee_type", "value": "annexe_leizee_stationnement_4", "type": "EQUALS"}], "INDIGNE": [{"id": "indigne", "value": "oui", "type": "EQUALS"}], "INDIGNE_AUTORISATION": [{"id": "indigne_autorisation", "value": "oui", "type": "EQUALS"}], "INDIGNE_DECLARATION": [{"id": "indigne_declaration", "value": "oui", "type": "EQUALS"}], "PIERRE_GAZ": [{"id": "diagnostic_gaz_vielle_installation", "value": "oui", "type": "EQUALS"}], "PIERRE_ELEC": [{"id": "diagnostic_electrique_vielle_installation", "value": "oui", "type": "EQUALS"}], "PIERRE_ASSAINISSEMENT": [{"id": "assainissement_statut", "value": "non", "type": "EQUALS"}], "PIERRE_PLOMB": [{"id": "construction", "value": "avant_1949", "type": "EQUALS"}], "PIERRE_AMIANTE": [{"id": "construction", "value": "1949_1997", "type": "EQUALS"}, {"id": "pierre_amiante_rehabilitation", "value": "non", "type": "EQUALS"}], "PIERRE_TERMITES": [{"id": "diagnostic_zone_termites_statut", "value": "oui", "type": "EQUALS"}], "PIERRE_AMIANTE_2": [{"id": "construction", "value": "avant_1949", "type": "EQUALS"}, {"id": "pierre_amiante_rehabilitation", "value": "non", "type": "EQUALS"}], "AD_PLAN_LOT": [{"id": "ad_plan_lot", "value": "oui", "type": "EQUALS"}], "GAZ_NORMES": [{"id": "diagnostic_gaz_travaux", "value": "oui", "type": "EQUALS"}], "ELEC_NORMES": [{"id": "diagnostic_electrique_travaux", "value": "oui", "type": "EQUALS"}], "ASSAINISSEMENT_EGOUT": [{"id": "assainissement_statut", "value": "oui", "type": "EQUALS"}], "AD_BAIL_ANTERIEUR": [{"id": "ad_bail_anterieur", "value": "oui", "type": "EQUALS"}], "ASSAINISSEMENT_EGOUT_CONTROLE": [{"id": "assainissement_egout_statut", "value": "oui", "type": "EQUALS"}], "ASSAINISSEMENT_FOSSE": [{"id": "assainissement_statut", "value": "non", "type": "EQUALS"}], "ASSAINISSEMENT_FOSSE_CONTROLE": [{"id": "assainissement_fosse_statut", "value": "oui", "type": "EQUALS"}], "TRAVAUX_ASSURANCE_PARTICULIER": [{"id": "travaux_particulier_assurance_dommage_statut", "value": "oui", "type": "EQUALS"}], "ELEC_15_ANS": [{"id": "diagnostic_electrique_vielle_installation", "value": "non", "type": "EQUALS"}], "TERMITES_FACULTATIF": [{"id": "diagnostic_termites_informatif_effectue", "value": "oui", "type": "EQUALS"}], "ASSAINISSEMENT_DEMANDE": [{"id": "assainissement_egout_demande", "value": "oui", "type": "EQUALS"}], "ATTESTATION_SANIBROYEUR": [{"id": "sanibroyeur_autorisation", "value": "obtenue", "type": "EQUALS"}], "TRAVAUX_AUTORISATION_GLOBAL_AG": [{"id": "travaux_copropriete_autorisation_global", "value": "autorisation_obtenue", "type": "EQUALS"}], "AD_BAIL": [{"id": "ad_bail", "value": "non", "type": "EQUALS"}], "PLOMB_ETABLI": [{"conditions": [{"id": "diagnostic_plomb_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__VENTE_ANCIEN", "OPERATION__ICM__DOSSIER_DE_VENTE_ICM", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_NEUF", "OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO", "OPERATION__KEREDES__IMMOBILIER_BRS", "OPERATION__BEAUX_VILLAGES__IMMOBILIER", "OPERATION__CANNISIMMO__IMMOBILIER", "OPERATION__SANTONI__IMMOBILIER", "OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESSIONNEL_AJP", "OPERATION__ORPI__IMMOBILIER__VENTE", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION", "OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION", "OPERATION__HERMES__IMMOBILIER__TRANSACTION_PRO", "OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE", "OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE", "OPERATION__ERA__IMMOBILIER__VENTE", "OPERATION__IMOCONSEIL__IMMOBILIER__VENTE", "OPERATION__UNIS__IMMOBILIER__HABITATION", "OPERATION__UNIS__IMMOBILIER__COMMERCIAL", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE", "OPERATION__BENEDIC__IMMOBILIER", "OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION", "OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE", "OPERATION__I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL", "OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_ACHEVE", "OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE", "OPERATION__COLDWELL_BANKER__IMMOBILIER", "OPERATION__FOLLIOT__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER", "OPERATION__IMMOBILIER__VENTE_VIAGER", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VIAGER", "OPERATION__IMMOBILIER__LOCATION", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION", "OPERATION__MA_GESTION_LOCATIVE__LOCATION", "OPERATION__MA_REGIE__LOCATION", "OPERATION__AJP__DOSSIER_DE_LOCATION_AJP", "OPERATION__AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP", "OPERATION__ERA__IMMOBILIER__LOCATION", "OPERATION__ORPI__IMMOBILIER__LOCATION", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION", "OPERATION__UNIS__LOCATION__HABITATION", "OPERATION__UNIS__LOCATION__COMMERCIAL", "OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_PRO"]}], "NOT_VENTE_ANCIEN": [{"type": "DIFFERENT_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__VENTE_ANCIEN", "OPERATION__ICM__DOSSIER_DE_VENTE_ICM", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_NEUF", "OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO", "OPERATION__KEREDES__IMMOBILIER_BRS", "OPERATION__BEAUX_VILLAGES__IMMOBILIER", "OPERATION__CANNISIMMO__IMMOBILIER", "OPERATION__SANTONI__IMMOBILIER", "OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESSIONNEL_AJP", "OPERATION__ORPI__IMMOBILIER__VENTE", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION", "OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION", "OPERATION__HERMES__IMMOBILIER__TRANSACTION_PRO", "OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE", "OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE", "OPERATION__ERA__IMMOBILIER__VENTE", "OPERATION__IMOCONSEIL__IMMOBILIER__VENTE", "OPERATION__UNIS__IMMOBILIER__HABITATION", "OPERATION__UNIS__IMMOBILIER__COMMERCIAL", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE", "OPERATION__BENEDIC__IMMOBILIER", "OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION", "OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE", "OPERATION__I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL", "OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_ACHEVE", "OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE", "OPERATION__COLDWELL_BANKER__IMMOBILIER", "OPERATION__FOLLIOT__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL", "OPERATION__IMMOBILIER__VENTE_BIEN_PROFESSIONNEL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_BIENS_PROFESSIONNELS", "OPERATION__EFFICITY__IMMOBILIER_COMMERCIAL", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_PRO", "OPERATION__SEXTANT__IMMOBILIER_COMMERCIAL", "OPERATION__FOLLIOT__IMMOBILIER_PRO", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER", "OPERATION__IMMOBILIER__VENTE_VIAGER", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VIAGER", "OPERATION__IMMOBILIER__SOCIAL_VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME", "OPERATION__CLESENCE__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF", "OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF", "OPERATION__IMMOBILIER__SOCIAL_PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE", "OPERATION__IMMOBILIER__LOCATION", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION", "OPERATION__MA_GESTION_LOCATIVE__LOCATION", "OPERATION__MA_REGIE__LOCATION", "OPERATION__AJP__DOSSIER_DE_LOCATION_AJP", "OPERATION__AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP", "OPERATION__ERA__IMMOBILIER__LOCATION", "OPERATION__ORPI__IMMOBILIER__LOCATION", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION", "OPERATION__UNIS__LOCATION__HABITATION", "OPERATION__UNIS__LOCATION__COMMERCIAL", "OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_PRO", "OPERATION__ALLOWA__IMMOBILIER"]}], "AMIANTE_ETABLI": [{"conditions": [{"id": "diagnostic_amiante_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__VENTE_ANCIEN", "OPERATION__ICM__DOSSIER_DE_VENTE_ICM", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_NEUF", "OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO", "OPERATION__KEREDES__IMMOBILIER_BRS", "OPERATION__BEAUX_VILLAGES__IMMOBILIER", "OPERATION__CANNISIMMO__IMMOBILIER", "OPERATION__SANTONI__IMMOBILIER", "OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESSIONNEL_AJP", "OPERATION__ORPI__IMMOBILIER__VENTE", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION", "OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION", "OPERATION__HERMES__IMMOBILIER__TRANSACTION_PRO", "OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE", "OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE", "OPERATION__ERA__IMMOBILIER__VENTE", "OPERATION__IMOCONSEIL__IMMOBILIER__VENTE", "OPERATION__UNIS__IMMOBILIER__HABITATION", "OPERATION__UNIS__IMMOBILIER__COMMERCIAL", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE", "OPERATION__BENEDIC__IMMOBILIER", "OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION", "OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE", "OPERATION__I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL", "OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_ACHEVE", "OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE", "OPERATION__COLDWELL_BANKER__IMMOBILIER", "OPERATION__FOLLIOT__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER", "OPERATION__IMMOBILIER__VENTE_VIAGER", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VIAGER", "OPERATION__IMMOBILIER__LOCATION", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION", "OPERATION__MA_GESTION_LOCATIVE__LOCATION", "OPERATION__MA_REGIE__LOCATION", "OPERATION__AJP__DOSSIER_DE_LOCATION_AJP", "OPERATION__AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP", "OPERATION__ERA__IMMOBILIER__LOCATION", "OPERATION__ORPI__IMMOBILIER__LOCATION", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION", "OPERATION__UNIS__LOCATION__HABITATION", "OPERATION__UNIS__LOCATION__COMMERCIAL", "OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_PRO"]}], "GAZ_ETABLI": [{"conditions": [{"id": "diagnostic_gaz_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__VENTE_ANCIEN", "OPERATION__ICM__DOSSIER_DE_VENTE_ICM", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_NEUF", "OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO", "OPERATION__KEREDES__IMMOBILIER_BRS", "OPERATION__BEAUX_VILLAGES__IMMOBILIER", "OPERATION__CANNISIMMO__IMMOBILIER", "OPERATION__SANTONI__IMMOBILIER", "OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESSIONNEL_AJP", "OPERATION__ORPI__IMMOBILIER__VENTE", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION", "OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION", "OPERATION__HERMES__IMMOBILIER__TRANSACTION_PRO", "OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE", "OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE", "OPERATION__ERA__IMMOBILIER__VENTE", "OPERATION__IMOCONSEIL__IMMOBILIER__VENTE", "OPERATION__UNIS__IMMOBILIER__HABITATION", "OPERATION__UNIS__IMMOBILIER__COMMERCIAL", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE", "OPERATION__BENEDIC__IMMOBILIER", "OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION", "OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE", "OPERATION__I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL", "OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_ACHEVE", "OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE", "OPERATION__COLDWELL_BANKER__IMMOBILIER", "OPERATION__FOLLIOT__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER", "OPERATION__IMMOBILIER__VENTE_VIAGER", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VIAGER", "OPERATION__IMMOBILIER__LOCATION", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION", "OPERATION__MA_GESTION_LOCATIVE__LOCATION", "OPERATION__MA_REGIE__LOCATION", "OPERATION__AJP__DOSSIER_DE_LOCATION_AJP", "OPERATION__AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP", "OPERATION__ERA__IMMOBILIER__LOCATION", "OPERATION__ORPI__IMMOBILIER__LOCATION", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION", "OPERATION__UNIS__LOCATION__HABITATION", "OPERATION__UNIS__LOCATION__COMMERCIAL", "OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_PRO"]}], "ELEC_ETABLI": [{"conditions": [{"id": "diagnostic_electrique_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__VENTE_ANCIEN", "OPERATION__ICM__DOSSIER_DE_VENTE_ICM", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_NEUF", "OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO", "OPERATION__KEREDES__IMMOBILIER_BRS", "OPERATION__BEAUX_VILLAGES__IMMOBILIER", "OPERATION__CANNISIMMO__IMMOBILIER", "OPERATION__SANTONI__IMMOBILIER", "OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESSIONNEL_AJP", "OPERATION__ORPI__IMMOBILIER__VENTE", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION", "OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION", "OPERATION__HERMES__IMMOBILIER__TRANSACTION_PRO", "OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE", "OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE", "OPERATION__ERA__IMMOBILIER__VENTE", "OPERATION__IMOCONSEIL__IMMOBILIER__VENTE", "OPERATION__UNIS__IMMOBILIER__HABITATION", "OPERATION__UNIS__IMMOBILIER__COMMERCIAL", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE", "OPERATION__BENEDIC__IMMOBILIER", "OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION", "OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE", "OPERATION__I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL", "OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_ACHEVE", "OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE", "OPERATION__COLDWELL_BANKER__IMMOBILIER", "OPERATION__FOLLIOT__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER", "OPERATION__IMMOBILIER__VENTE_VIAGER", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VIAGER", "OPERATION__IMMOBILIER__LOCATION", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION", "OPERATION__MA_GESTION_LOCATIVE__LOCATION", "OPERATION__MA_REGIE__LOCATION", "OPERATION__AJP__DOSSIER_DE_LOCATION_AJP", "OPERATION__AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP", "OPERATION__ERA__IMMOBILIER__LOCATION", "OPERATION__ORPI__IMMOBILIER__LOCATION", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION", "OPERATION__UNIS__LOCATION__HABITATION", "OPERATION__UNIS__LOCATION__COMMERCIAL", "OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_PRO"]}], "DPE_ETABLI": [{"conditions": [{"id": "dpe_details_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESSIONNEL_AJP", "OPERATION__HERMES__IMMOBILIER__TRANSACTION_PRO", "OPERATION__UNIS__IMMOBILIER__COMMERCIAL", "OPERATION__I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_ACHEVE", "OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER", "OPERATION__IMMOBILIER__VENTE_VIAGER", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VIAGER", "OPERATION__IMMOBILIER__LOCATION", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION", "OPERATION__MA_GESTION_LOCATIVE__LOCATION", "OPERATION__MA_REGIE__LOCATION", "OPERATION__AJP__DOSSIER_DE_LOCATION_AJP", "OPERATION__AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP", "OPERATION__ERA__IMMOBILIER__LOCATION", "OPERATION__ORPI__IMMOBILIER__LOCATION", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION", "OPERATION__UNIS__LOCATION__HABITATION", "OPERATION__UNIS__LOCATION__COMMERCIAL", "OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_PRO"]}], "SUPERFICIE": [{"conditions": [{"id": "programme_superficie_statut", "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__PSLA_PRELIMINAIRE", "OPERATION__OZANAM__IMMOBILIER__PSLA_PRELIMINAIRE", "OPERATION__OZANAM__IMMOBILIER__PSLA_PROGRAMME", "OPERATION__IMMOBILIER__VENTE_NEUF", "OPERATION__DESIMO__IMMOBILIER__VENTE", "OPERATION__DESIMO__IMMOBILIER__PROGRAMME", "OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME", "OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION", "OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME", "OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE"]}], "NOT_NEUF": [{"type": "DIFFERENT_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__PSLA_PRELIMINAIRE", "OPERATION__OZANAM__IMMOBILIER__PSLA_PRELIMINAIRE", "OPERATION__OZANAM__IMMOBILIER__PSLA_PROGRAMME", "OPERATION__IMMOBILIER__VENTE_NEUF", "OPERATION__DESIMO__IMMOBILIER__VENTE", "OPERATION__DESIMO__IMMOBILIER__PROGRAMME", "OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME", "OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION", "OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME", "OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE"]}], "AD_DIAG_UNIQUE": [{"conditions": [{"id": "diagnostics_techniques_diagnostiqueur_unique", "type": "EQUALS", "value": "non"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AGENCE_DIRECTE__IMMOBILIER"]}], "NOT_AGENCE_DIRECTE": [{"type": "DIFFERENT_TEMPLATES", "templates": ["OPERATION__AGENCE_DIRECTE__IMMOBILIER"]}], "PLOMB_ETABLI_SOCIAL": [{"conditions": [{"id": "diagnostic_plomb_statut", "type": "EQUALS", "value": "oui"}, {"id": "programme_diagnostic_resultat", "recordPath": ["OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__SOCIAL_VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME", "OPERATION__CLESENCE__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF", "OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF", "OPERATION__IMMOBILIER__SOCIAL_PROGRAMME", "OPERATION__LFEUR__IMMOBILIER_VENTE", "OPERATION__LFEUR__IMMOBILIER_PROGRAMME"]}], "PLOMB_ETABLI_LOCATION": [{"conditions": [{"id": "diagnostic_plomb_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__LOCATION__FICHES", "LOCATION", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__LOCATION", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION", "OPERATION__MA_GESTION_LOCATIVE__LOCATION", "OPERATION__MA_REGIE__LOCATION", "OPERATION__AJP__DOSSIER_DE_LOCATION_AJP", "OPERATION__AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP", "OPERATION__ERA__IMMOBILIER__LOCATION", "OPERATION__ORPI__IMMOBILIER__LOCATION", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION", "OPERATION__UNIS__LOCATION__HABITATION", "OPERATION__UNIS__LOCATION__COMMERCIAL", "OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_PRO"]}], "AMIANTE_ETABLI_SOCIAL": [{"conditions": [{"id": "diagnostic_amiante_statut", "type": "EQUALS", "value": "oui"}, {"id": "programme_diagnostic_resultat", "recordPath": ["OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__SOCIAL_VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME", "OPERATION__CLESENCE__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF", "OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF", "OPERATION__IMMOBILIER__SOCIAL_PROGRAMME", "OPERATION__LFEUR__IMMOBILIER_VENTE", "OPERATION__LFEUR__IMMOBILIER_PROGRAMME"]}], "AMIANTE_ETABLI_LOCATION": [{"conditions": [{"id": "diagnostic_amiante_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__LOCATION__FICHES", "LOCATION", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__VENTE_ANCIEN", "OPERATION__ICM__DOSSIER_DE_VENTE_ICM", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_NEUF", "OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO", "OPERATION__KEREDES__IMMOBILIER_BRS", "OPERATION__BEAUX_VILLAGES__IMMOBILIER", "OPERATION__CANNISIMMO__IMMOBILIER", "OPERATION__SANTONI__IMMOBILIER", "OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESSIONNEL_AJP", "OPERATION__ORPI__IMMOBILIER__VENTE", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION", "OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION", "OPERATION__HERMES__IMMOBILIER__TRANSACTION_PRO", "OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE", "OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE", "OPERATION__ERA__IMMOBILIER__VENTE", "OPERATION__IMOCONSEIL__IMMOBILIER__VENTE", "OPERATION__UNIS__IMMOBILIER__HABITATION", "OPERATION__UNIS__IMMOBILIER__COMMERCIAL", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE", "OPERATION__BENEDIC__IMMOBILIER", "OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION", "OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE", "OPERATION__I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL", "OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_ACHEVE", "OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE", "OPERATION__COLDWELL_BANKER__IMMOBILIER", "OPERATION__FOLLIOT__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL", "OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER", "OPERATION__IMMOBILIER__VENTE_VIAGER", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VIAGER", "OPERATION__IMMOBILIER__LOCATION", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION", "OPERATION__MA_GESTION_LOCATIVE__LOCATION", "OPERATION__MA_REGIE__LOCATION", "OPERATION__AJP__DOSSIER_DE_LOCATION_AJP", "OPERATION__AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP", "OPERATION__ERA__IMMOBILIER__LOCATION", "OPERATION__ORPI__IMMOBILIER__LOCATION", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION", "OPERATION__UNIS__LOCATION__HABITATION", "OPERATION__UNIS__LOCATION__COMMERCIAL", "OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_PRO"]}], "GAZ_ETABLI_SOCIAL": [{"conditions": [{"id": "diagnostic_gaz_statut", "type": "EQUALS", "value": "oui"}, {"id": "programme_diagnostic_resultat", "recordPath": ["OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__SOCIAL_VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME", "OPERATION__CLESENCE__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF", "OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF", "OPERATION__IMMOBILIER__SOCIAL_PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE"]}], "GAZ_ETABLI_LOCATION": [{"conditions": [{"id": "diagnostic_gaz_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__LOCATION__FICHES", "LOCATION", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__LOCATION", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION", "OPERATION__MA_GESTION_LOCATIVE__LOCATION", "OPERATION__MA_REGIE__LOCATION", "OPERATION__AJP__DOSSIER_DE_LOCATION_AJP", "OPERATION__AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP", "OPERATION__ERA__IMMOBILIER__LOCATION", "OPERATION__ORPI__IMMOBILIER__LOCATION", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION", "OPERATION__UNIS__LOCATION__HABITATION", "OPERATION__UNIS__LOCATION__COMMERCIAL", "OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_PRO"]}], "ELEC_ETABLI_SOCIAL": [{"conditions": [{"id": "diagnostic_electrique_statut", "type": "EQUALS", "value": "oui"}, {"id": "programme_diagnostic_resultat", "recordPath": ["OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__SOCIAL_VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME", "OPERATION__CLESENCE__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF", "OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF", "OPERATION__IMMOBILIER__SOCIAL_PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE"]}], "ELEC_ETABLI_LOCATION": [{"conditions": [{"id": "diagnostic_electrique_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__LOCATION__FICHES", "LOCATION", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__LOCATION", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION", "OPERATION__MA_GESTION_LOCATIVE__LOCATION", "OPERATION__MA_REGIE__LOCATION", "OPERATION__AJP__DOSSIER_DE_LOCATION_AJP", "OPERATION__AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP", "OPERATION__ERA__IMMOBILIER__LOCATION", "OPERATION__ORPI__IMMOBILIER__LOCATION", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION", "OPERATION__UNIS__LOCATION__HABITATION"]}], "DPE_ETABLI_SOCIAL": [{"conditions": [{"id": "dpe_details_statut", "type": "EQUALS", "value": "oui"}, {"id": "programme_diagnostic_resultat", "recordPath": ["OPERATION__IMMOBILIER__PROGRAMME_SOCIAL__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__SOCIAL_VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME", "OPERATION__CLESENCE__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF", "OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF", "OPERATION__IMMOBILIER__SOCIAL_PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE"]}], "DPE_ETABLI_LOCATION": [{"conditions": [{"id": "dpe_details_statut", "type": "EQUALS", "value": "oui"}, {"id": "diagnostic_resultats_statut", "recordPath": ["OPERATION__IMMOBILIER__LOCATION__FICHES", "LOCATION", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__UNIS__LOCATION__COMMERCIAL", "OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_PRO"]}], "PLOMB_SANS_RESULTAT": [{"conditions": [{"id": "diagnostic_plomb_statut", "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__ALLOWA__IMMOBILIER"]}], "AMIANTE_SANS_RESULTAT": [{"conditions": [{"id": "diagnostic_amiante_statut", "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__ALLOWA__IMMOBILIER"]}], "GAZ_SANS_RESULTAT": [{"conditions": [{"id": "diagnostic_gaz_statut", "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__ALLOWA__IMMOBILIER"]}], "ELEC_SANS_RESULTAT": [{"conditions": [{"id": "diagnostic_electrique_statut", "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__ALLOWA__IMMOBILIER"]}], "DPE_SANS_RESULTAT": [{"conditions": [{"id": "dpe_details_statut", "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMMOBILIER__VENTE_ANCIEN", "OPERATION__ICM__DOSSIER_DE_VENTE_ICM", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_NEUF", "OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO", "OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO", "OPERATION__KEREDES__IMMOBILIER_BRS", "OPERATION__BEAUX_VILLAGES__IMMOBILIER", "OPERATION__CANNISIMMO__IMMOBILIER", "OPERATION__SANTONI__IMMOBILIER", "OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__ORPI__IMMOBILIER__VENTE", "OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION", "OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION", "OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE", "OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE", "OPERATION__ERA__IMMOBILIER__VENTE", "OPERATION__IMOCONSEIL__IMMOBILIER__VENTE", "OPERATION__UNIS__IMMOBILIER__HABITATION", "OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE", "OPERATION__BENEDIC__IMMOBILIER", "OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION", "OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE", "OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE", "OPERATION__COLDWELL_BANKER__IMMOBILIER", "OPERATION__FOLLIOT__IMMOBILIER", "OPERATION__KELLER_WILLIAMS__IMMOBILIER", "OPERATION__ALLOWA__IMMOBILIER", "OPERATION__COLDWELL_BANKER__IMMOBILIER", "OPERATION__IMOCONSEIL__IMMOBILIER__VENTE", "OPERATION__IMMOBILIER__LOCATION", "OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION", "OPERATION__MA_GESTION_LOCATIVE__LOCATION", "OPERATION__MA_REGIE__LOCATION", "OPERATION__AJP__DOSSIER_DE_LOCATION_AJP", "OPERATION__AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP", "OPERATION__ERA__IMMOBILIER__LOCATION", "OPERATION__ORPI__IMMOBILIER__LOCATION", "OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION", "OPERATION__UNIS__LOCATION__HABITATION", "OPERATION__EFFICITY__IMMOBILIER__LOCATION__HABITATION"]}], "DESIGNATION_MODIFIEE": [{"id": "designation_modifiee", "type": "EQUALS", "value": "oui"}], "SANIBROYEUR": [{"id": "sanibroyeur_presence", "type": "EQUALS", "value": "oui"}], "ASSAINISSEMENT_COLLECTIF": [{"id": "assainissement_statut", "value": "oui", "type": "EQUALS"}], "ASSAINISSEMENT_NON_COLLECTIF": [{"id": "assainissement_statut", "value": "non", "type": "EQUALS"}], "CONTROLE_COLLECTIF_NON_EFFECTUE": [{"id": "assainissement_egout_statut", "value": "non", "type": "EQUALS"}], "EGOUT_NON_CONFORME": [{"id": "assainissement_egout_controle_resultat", "value": "non", "type": "EQUALS"}], "CONTROLE_COLLECTIF_AVANT_VENTE": [{"id": "assainissement_egout_a_faire", "value": "oui", "type": "EQUALS"}], "OCCUPATION_AD": [{"conditions": [{"id": "ad_bail", "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AGENCE_DIRECTE__IMMOBILIER"]}], "PLOMB_LFEUR": [{"conditions": [{"id": "diagnostic_plomb_statut", "type": "EQUALS", "value": "oui"}, {"id": "programme_diagnostic_resultat", "recordPath": ["OPERATION__LFEUR__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE"]}], "AMIANTE_LFEUR": [{"conditions": [{"id": "diagnostic_amiante_statut", "type": "EQUALS", "value": "oui"}, {"id": "programme_diagnostic_resultat", "recordPath": ["OPERATION__LFEUR__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE"]}], "GAZ_LFEUR": [{"conditions": [{"id": "diagnostic_gaz_statut", "type": "EQUALS", "value": "oui"}, {"id": "programme_diagnostic_resultat", "recordPath": ["OPERATION__LFEUR__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE"]}], "ELEC_LFEUR": [{"conditions": [{"id": "diagnostic_electrique_statut", "type": "EQUALS", "value": "oui"}, {"id": "programme_diagnostic_resultat", "recordPath": ["OPERATION__LFEUR__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE"]}], "DPE_LFEUR": [{"conditions": [{"id": "dpe_details_statut", "type": "EQUALS", "value": "oui"}, {"id": "programme_diagnostic_resultat", "recordPath": ["OPERATION__LFEUR__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__LFEUR__IMMOBILIER__PROGRAMME", "OPERATION__LFEUR__IMMOBILIER__VENTE"]}], "OP_PAS_SUCCESSION_TERMINEE": [{"id": "origine_succession_statut", "value": "oui", "type": "EQUALS"}], "OP_PAS_SUCCESSION": [{"id": "origine_liste", "value": "origine_propriete_succession", "type": "DIFFERENT"}], "?OP_PAS_SUCCESSION": [{"id": "origine_liste", "value": "origine_propriete_succession", "type": "DIFFERENT"}], "ZONE_TERMITES": [{"type": "DIFFERENT_TEMPLATES", "templates": ["OPERATION__AQUITANIS__VENTE_ANCIEN", "OPERATION__AQUITANIS__PROGRAMME_ANCIEN"]}, {"id": "diagnostic_zone_termites_statut", "value": "oui", "type": "EQUALS"}], "ZONE_MERULE": [{"id": "diagnostic_merule_commune", "value": "oui", "type": "EQUALS"}], "CHEMINEE_NON_RAMONEE": [{"id": "cheminee_fonctionnelle_ramonage", "value": "non", "type": "EQUALS"}], "3F_PLAN_PERSONNALISE": [{"id": "plan_vente_personnalise", "recordPath": ["OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__CONTRAT_PRELIMINAIRE", "CONTRAT_PRELIMINAIRE", "0"], "type": "EQUALS", "value": "oui"}], "3F_PLAN_PERSONNALISE_VEFA": [{"id": "plan_vente_personnalise", "recordPath": ["OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__CONTRAT_RESERVATION", "CONTRAT_RESA", "0"], "type": "EQUALS", "value": "oui"}], "3F_PLAN_PERSONNALISE_PSLA": [{"id": "plan_vente_personnalise", "recordPath": ["OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE__CONTRAT_PRELIMINAIRE", "CONTRAT_PRELIMINAIRE", "0"], "type": "EQUALS", "value": "oui"}], "FACTURE_TRAVAUX": [{"id": "travaux_facture", "value": "oui", "type": "EQUALS"}], "ZONE_TENDUE": [{"id": "zone_tendue_statut", "value": "oui", "type": "EQUALS"}], "ZONE_REFERENCE": [{"id": "zone_loyer_reference", "value": "oui", "type": "EQUALS"}], "DPE_ANNEXE_IDENTIQUE": [{"id": "diagnostic_annexe", "value": "oui", "type": "DIFFERENT"}], "DPE_FICHE_CLOTURE": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["IMMOBILIER_VENTE_ANCIEN_FICHE_CLOTURE"]}], "MANDAT_TYPE_GESTION": [{"id": "mandat_type", "value": "gestion", "type": "EQUALS"}], "MANDAT_TYPE_TRANSACTION": [{"id": "mandat_type", "value": "gestion", "type": "DIFFERENT"}], "COMPLEMENT": [{"id": "loyer_complement", "type": "EQUALS", "value": "oui"}], "TRAVAUX_2023": [{"id": "travaux_date_achevement", "type": "DATE_NOT_OLDER_THAN", "value": "2022-12-31"}], "TRAVAUX_ENERGETIQUE": [{"id": "travaux_energetique", "value": "oui", "type": "EQUALS"}], "CIL_TRAVAUX": [{"id": "carnet_information_travaux_statut", "value": "oui", "type": "EQUALS"}], "CUVE_CONTRAT": [{"id": "cuve_fioul_contrat", "value": "oui", "type": "EQUALS"}], "ENTRETIEN_CHAUDIERE": [{"id": "chaudiere_entretien_statut", "value": "oui", "type": "EQUALS"}], "BAIL_SAISONNIER": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["IMMOBILIER_LOCATION_BAIL_SAISONNIER"]}], "MESURAGE_PAS_PRO": [{"id": "superficie_carrez_professionnel", "type": "EQUALS", "value": "non"}], "OP_SUCCESSION_SIMPLE": [{"id": "origine_liste_giboire", "value": "origine_propriete_succession", "type": "EQUALS"}], "BIEN_LOUE_GIBOIRE": [{"id": "bien_location_statut", "value": "loue", "type": "EQUALS"}], "DESIGNATION_MODIFIEE_COPROPRIETE": [{"id": "designation_modifiee_edd", "type": "EQUALS", "value": "oui"}], "CHEMINEE_PERIL": [{"id": "cheminee_zone_arrete_peril_statut", "type": "CONTAINS", "value": "arrete"}], "CHEMINEE_PERIL_COMPLEMENTAIRE": [{"id": "cheminee_zone_arrete_peril_statut", "type": "CONTAINS", "value": "arrete_complementaire"}], "CHEMINEE_PERIL_LEVEE": [{"id": "cheminee_zone_arrete_peril_statut", "type": "CONTAINS", "value": "levee"}], "CHEMINEE_ZONE_PERIL": [{"id": "cheminee_zone_arrete_peril", "type": "EQUALS", "value": "oui"}], "FUMEE": [{"id": "detecteur_fumee_presence", "value": "oui", "type": "EQUALS"}], "ENTRETIEN_CHEMINEE_GIBOIRE": [], "PROGRAMME_CLESENCE_PSLA": [{"conditions": [{"id": "clesence_psla", "type": "DIFFERENT", "value": "non"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__CLESENCE__IMMOBILIER__PROGRAMME", "OPERATION__CLESENCE__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF", "OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF"]}], "PAS_PROGRAMME": [{"type": "DIFFERENT_TEMPLATES", "templates": ["OPERATION__CLESENCE__IMMOBILIER__PROGRAMME", "OPERATION__CLESENCE__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF", "OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF"]}], "PROGRAMME_CLESENCE_PAS_PSLA": [{"conditions": [{"id": "clesence_psla", "type": "DIFFERENT", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__CLESENCE__IMMOBILIER__PROGRAMME", "OPERATION__CLESENCE__IMMOBILIER__VENTE", "OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF", "OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF"]}], "INDIGNE_AUTORISATION_DUALITE": [{"id": "indigne_dualite", "value": "autorisation", "type": "EQUALS"}], "SECURITE_SALUBRITE": [{"id": "salubrite_arretes_statut", "value": "oui", "type": "EQUALS"}], "SERVITUDES": [], "DIAGNOSTIQUEUR_LIBRE": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__CDC__IMMOBILIER__PROGRAMME", "OPERATION__CDC__IMMOBILIER__VENTE", "OPERATION__MA_GESTION_LOCATIVE__LOCATION"]}], "VENTE_SOCIAL_LIBRE": [{"id": "occupation_sociale", "recordPath": ["OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__FICHE_VENTE", "VENTE", "0"], "type": "DIFFERENT", "value": "oui"}], "VENTE_SOCIAL_OCCUPE": [{"id": "occupation_sociale", "recordPath": ["OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__FICHE_VENTE", "VENTE", "0"], "type": "DIFFERENT", "value": "non"}], "CDC_PARKING_UN": [{"id": "cdc_parking_nombre", "value": 1, "type": "EQUALS"}], "CDC_PARKING_DEUX": [{"id": "cdc_parking_nombre", "value": 2, "type": "EQUALS"}], "CDC_PARKING_TROIS": [{"id": "cdc_parking_nombre", "value": 3, "type": "EQUALS"}], "CDC_CAVE_UN": [{"id": "cdc_cave_nombre", "value": 1, "type": "EQUALS"}], "CDC_CAVE_DEUX": [{"id": "cdc_cave_nombre", "value": 2, "type": "EQUALS"}], "CDC_CAVE_TROIS": [{"id": "cdc_cave_nombre", "value": 3, "type": "EQUALS"}], "CDC_PRIX_PARKING": [{"id": "programme_prix_vente_parking", "value": "oui", "type": "EQUALS"}], "CDC_PRIX_CAVE": [{"id": "programme_prix_vente_cave", "value": "oui", "type": "EQUALS"}], "ATTESTATION_40_M2": [{"id": "dpe_40_m2_statut", "value": "oui", "type": "EQUALS"}], "ZONE_TERMITES_PROGRAMME_CDC": [{"id": "programme_zone_termites", "recordPath": ["OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "ZONE_MERULE_PROGRAMME_CDC": [{"id": "programme_zone_merules", "recordPath": ["OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "PERMIS_NON_OBTENU": [{"id": "belgique_travaux_permis_obtenu", "value": "non", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_TOITURE": [{"id": "belgique_poste_toiture", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_MENUISERIE": [{"id": "belgique_poste_menuiserie", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_ELECTRIQUE": [{"id": "belgique_poste_electrique", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_SANITAIRE": [{"id": "belgique_poste_sanitaire", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_CHAUFFAGE": [{"id": "belgique_poste_chauffage", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_REVETEMENT_SOL": [{"id": "belgique_poste_revetement_sol", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_REVETEMENT_MUR": [{"id": "belgique_poste_revetement_mur", "value": "autre", "type": "EQUALS"}], "CDC_PSLA_PARKING": [{"id": "programme_garage", "value": "oui", "type": "EQUALS"}], "DEVIS_TRAVAUX": [{"id": "devis_travaux_modificatif", "value": "oui", "type": "EQUALS"}], "DEBROUSSAILLEMENT": [{"id": "obligation_debroussaillement_statut", "value": "oui", "type": "EQUALS"}], "CHAUDIERE_AUTRE": [{"id": "chaudiere_statut_type", "value": "autre", "type": "EQUALS"}], "ASSAINISSEMENT_COLLECTIF_CDC": [{"id": "programme_assainissement", "recordPath": ["OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "collectif"}], "ASSAINISSEMENT_JO": [{"id": "assainissement_egout_statut_jo", "value": "realise", "type": "EQUALS"}], "RACCORDEMENT_PARISIEN": [{"id": "assainissement_egout_statut_jo", "value": "unitaire", "type": "EQUALS"}], "SERVITUDE_RELATER_PUBLIEE": [{"id": "servitude_relater_publiee", "value": "oui", "type": "EQUALS"}], "SERVITUDE_ECHELLE": [{"id": "servitudes_constituer_type", "value": "echelle", "type": "EQUALS"}], "SERVITUDE_EAUX": [{"id": "servitudes_constituer_type", "value": "eaux", "type": "EQUALS"}], "SERVITUDE_PASSAGE": [{"id": "servitudes_constituer_type", "value": "passage", "type": "EQUALS"}], "SERVITUDE_SURPLOMB": [{"id": "servitudes_constituer_type", "value": "surplomb", "type": "EQUALS"}], "SERVITUDE_PIGNON": [{"id": "servitudes_constituer_type", "value": "pignon", "type": "EQUALS"}], "SERVITUDE_ACCROCHAGE": [{"id": "servitudes_constituer_type", "value": "accrochage", "type": "EQUALS"}], "SERVITUDE_VUE": [{"id": "servitudes_constituer_type", "value": "vue", "type": "EQUALS"}], "SERVITUDE_BOITE": [{"id": "servitudes_constituer_type", "value": "boite", "type": "EQUALS"}], "SERVITUDE_AUTRE": [{"id": "servitudes_constituer_type", "value": "autre", "type": "EQUALS"}], "CONVENTION_ENTRETIEN_GOUTTIERES_TOITURES": [{"id": "convention_entretien_liste", "value": "gouttieres_toitures", "type": "CONTAINS"}], "CONVENTION_ENTRETIEN_TOITURES": [{"id": "convention_entretien_liste", "value": "toitures", "type": "CONTAINS"}], "CONVENTION_ENTRETIEN_GOUTTIERES": [{"id": "convention_entretien_liste", "value": "gouttieres", "type": "CONTAINS"}], "CONVENTION_ENTRETIEN_AUTRE": [{"id": "convention_entretien_liste", "value": "autre", "type": "CONTAINS"}], "CONDITIONS_PARTICULIERE_CHEMIN": [{"id": "condition_particuliere_servitude_liste", "value": "indivision", "type": "CONTAINS"}], "CONDITIONS_PARTICULIERE_AUTRE": [{"id": "condition_particuliere_servitude_liste", "value": "autre", "type": "CONTAINS"}], "CONVENTION_ENTRETIEN": [{"id": "convention_entretien", "value": "oui", "type": "CONTAINS"}], "AUDIT": [{"id": "dpe_audit_realise", "value": "oui", "type": "EQUALS"}], "OPERATION_TEMPLATE_AFFICHAGE": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__CDC__IMMOBILIER__PROGRAMME", "OPERATION__CDC__IMMOBILIER__VENTE"]}], "TEMPLATE_PRECISION_DEROGATION": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP"]}], "PPRN_SIMPLE": [{"id": "erp_pprn_bien", "type": "EQUALS", "value": "oui"}], "PPRM_SIMPLE": [{"id": "erp_pprm_bien", "type": "EQUALS", "value": "oui"}], "PPRT_SIMPLE": [{"id": "erp_pprt_bien", "type": "EQUALS", "value": "oui"}], "RETRAIT_COTE": [{"id": "erp_retrait_cote_bien", "type": "EQUALS", "value": "oui"}], "RADON": [{"id": "potentiel_radon_classe", "type": "EQUALS", "value": "oui"}], "INDEMINTE_CATASTROPHE": [{"id": "indemnite_catastrophe", "type": "EQUALS", "value": "oui"}], "SISMICITE_2": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 2}], "SISMICITE_3": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 3}], "SISMICITE_4": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 4}], "SISMICITE_5": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 5}], "ASSAINISSEMENT_SPECIFIQUE_PROGRAMME_PAVILLON": [{"id": "programme_assainissement_specifique_pavillon", "recordPath": ["OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "ASSAINISSEMENT_SPECIFIQUE_PROGRAMME_COLLECTIF": [{"id": "programme_assainissement_specifique", "recordPath": ["OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__FICHE_PROGRAMME", "CONDITIONS_GENERALES", "0"], "type": "EQUALS", "value": "oui"}], "ZONE_TERMITES_OBLIGATOIRE": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AQUITANIS__VENTE_ANCIEN", "OPERATION__AQUITANIS__PROGRAMME_ANCIEN"]}], "NUMERO_UG": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__CDC__IMMOBILIER__PROGRAMME", "OPERATION__CDC__IMMOBILIER__VENTE"]}], "NUMERO_REFERENCE": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AQUITANIS__PROGRAMME_ANCIEN", "OPERATION__AQUITANIS__VENTE_ANCIEN", "OPERATION__AQUITANIS__PROGRAMME_BRS", "OPERATION__AQUITANIS__RESERVATION_BRS"]}], "OPERATION_ELECTRICITE_AUTRES": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP"]}], "PSLA_3F": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["I3F_IMMOBILIER_PSLA_PRELIMINAIRE_PSLA_CHILLY"]}], "OPERATION_FONCTIONNELLE": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__CDC__IMMOBILIER__PROGRAMME", "OPERATION__CDC__IMMOBILIER__VENTE"]}], "CHEMINEE_PLAN_PROTECTION": [{"id": "cheminee_plan_protection_atmosphere", "type": "EQUALS", "value": "oui"}], "PRIX_REMISE": [{"id": "programme_prix_vente_remise", "type": "EQUALS", "value": "oui"}], "TEMPLATE_GESTION": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__BENEDIC__DOSSIER_DE_LOCATION_BENEDIC"]}], "DESIGNATION_TRAVAUX": [{"id": "designation_modifiee", "type": "EQUALS", "value": "oui"}], "PREPARATION_EFFICITY": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["EFFICITY__TRANSACTION__PREPARATION_COMPROMIS"]}]}}