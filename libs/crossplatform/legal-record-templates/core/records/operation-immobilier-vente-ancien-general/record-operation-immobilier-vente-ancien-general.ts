// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordOperationImmobilierVenteAncienGeneral: LegalRecordTemplate = {
  config: {},
  form: [
    {
      children: [
        {
          choices: [
            {
              id: 'viager',
              label: 'Vente en Viager'
            },
            {
              id: 'terme',
              label: 'Vente à Terme'
            },
            {
              id: 'usufruit',
              label: 'Vente de la Nue-Propriété'
            }
          ],
          id: 'vc_type_vente',
          label: 'Type de vente :',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'vc_type_vente',
                value: 'terme',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'vc_type_vente',
                value: 'viager',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'vc_type_occupee',
          label: 'Est-ce une vente occupée ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'vc_type_vente',
                value: 'viager',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'vc_viager_mixte',
          label: 'Le viager est-il mixte ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'vc_type_vente',
                value: 'viager',
                type: 'EQUALS'
              },
              {
                id: 'vc_viager_mixte',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'vc_viager_mixte_liste',
          label: 'Partie "occupée" du viager',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'avenant_prix',
              label: 'Modification du prix de vente'
            },
            {
              id: 'avenant_pret',
              label: "Modification du délai d'obtention du prêt"
            },
            {
              id: 'avenant_pret_montant',
              label: 'Modification des caractéristiques du prêt'
            },
            {
              id: 'avenant_signature',
              label: 'Modification de la date extrême de signature'
            },
            {
              id: 'avenant_honoraires',
              label: 'Modification des honoraires'
            },
            {
              id: 'avenant_bien',
              label: 'Modification de la désignation du bien'
            },
            {
              id: 'avenant_libre',
              label: 'Modification libre'
            }
          ],
          id: 'avenant_liste',
          label: "L'avenant porte sur",
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'pierre_remise_cause_tva',
          label: "Souhaitez vous insérer une clause d'avertissement sur la remise en cause de TVA ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'pierre_vendeur_fiscalite_lmnp',
              label: 'En loueur meublé non professionnel'
            },
            {
              id: 'pierre_vendeur_fiscalite_lmp',
              label: 'En loueur meublé professionnel'
            },
            {
              id: 'pierre_vendeur_fiscalite_nu',
              label: 'En loueur nu'
            },
            {
              id: 'pierre_vendeur_fiscalite_professionnel',
              label: 'Simplement en tant que professionnel'
            }
          ],
          id: 'pierre_fiscalite_vendeur',
          label: 'Le Vendeur est fiscalement déclaré',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'pierre_vendeur_comptable',
          label: 'Les coordonées du comptable du vendeur doivent-elles être indiquées au contrat ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'pierre_vendeur_comptable_nom',
          label: 'Nom du comptable',
          type: 'TEXT'
        },
        {
          id: 'pierre_vendeur_comptable_email',
          label: 'Adresse email du comptable',
          type: 'EMAIL'
        },
        {
          choices: [
            {
              id: 'pierre_situation_locative_1',
              label: 'Vente libre de toute gestion avec signature après échéance du bail et TVA à la charge du vendeur'
            },
            {
              id: 'pierre_situation_locative_2',
              label:
                'Vente libre de toute gestion avec signature avant l’échéance du bail et Résiliation du bail postérieure prévue au préalable. TVA à la charge de l’acquéreur)'
            },
            {
              id: 'pierre_situation_locative_3',
              label: 'Vente avec poursuite du bail'
            },
            {
              id: 'pierre_situation_locative_4',
              label: 'Vente avec poursuite du mandat de gestion'
            },
            {
              id: 'pierre_situation_locative_5',
              label: ' Vente avec résiliation du mandat de gestion'
            },
            {
              id: 'pierre_situation_locative_6',
              label: 'Libre de toute gestion'
            }
          ],
          id: 'pierre_situation_locative',
          label: 'Situation locative',
          type: 'SELECT'
        },
        {
          id: 'pierre_situation_locative_designation_bailleur',
          label: 'Désignation du titulaire du bail',
          type: 'TEXT'
        },
        {
          id: 'pierre_situation_locative_echeance_bail',
          label: "Date d'échéance du bail",
          type: 'DATE'
        },
        {
          id: 'pierre_situation_locative_denonciation_bail',
          label: 'Date de dénonciation du bail commercial',
          type: 'DATE'
        },
        {
          conditions: [
            [
              {
                id: 'pierre_situation_locative',
                value: 'pierre_situation_locative_2',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'pierre_situation_locative_charges_augmentatives',
          label: 'Montant des charges augmentatives du prix',
          type: 'PRICE'
        },
        {
          id: 'pierre_situation_locative_designation_gestion',
          label: 'Désignation du titulaire du mandat de gestion',
          type: 'TEXT'
        },
        {
          id: 'pierre_situation_locative_echeance_gestion',
          label: "Date d'échéance du mandat de gestion",
          type: 'DATE'
        },
        {
          id: 'pierre_situation_locative_echeance_liberation',
          label: 'Date de libération des lieux',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'pierre_vendeur_tva',
          label: 'Le Vendeur récupère-t-il la TVA ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'pierre_vendeur_tva_justificatif',
          label: "Le Vendeur peut-il justifier la fin du délai d'engagement de location ?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'pierre_montant_tva',
          label: 'Montant de la TVA récupérée',
          type: 'PRICE'
        },
        {
          id: 'pierre_tva_demarrage',
          label: "Date de démarrage de l'amortissement de la TVA",
          type: 'DATE'
        },
        {
          id: 'pierre_date_ancien_op',
          label: "Date d'acquisition des précédents propriétaires",
          type: 'DATE'
        },
        {
          id: 'pierre_date_dat',
          label: "Date de la déclaration d'achèvement des travaux",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'estimation_valeur_fixe',
              label: 'Par une valeur fixe'
            },
            {
              id: 'estimation_valeur_fourchette',
              label: 'Entre deux estimations'
            }
          ],
          id: 'estimation_staut',
          label: 'Comment est valorisé le bien',
          type: 'SELECT'
        },
        {
          id: 'estimation_fixe',
          label: 'Valorisation du bien',
          type: 'PRICE'
        },
        {
          id: 'estimation_basse',
          label: 'Valorisation basse du bien',
          type: 'PRICE'
        },
        {
          id: 'estimation_haute',
          label: 'Valorisation haute du bien',
          type: 'PRICE'
        },
        {
          description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
          id: 'estimation_fixe_cfp',
          label: 'Valorisation du bien',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
          id: 'estimation_basse_cfp',
          label: 'Valorisation basse du bien',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
          id: 'estimation_haute_cfp',
          label: 'Valorisation haute du bien',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'estimation_signature_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'estimation_clause_particuliere',
          label: 'Clause particulière supplémentaire',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'estimation_clause_particuliere_liste_estimation_clause_particuliere_liste_titre',
              label: 'Titre de la clause',
              type: 'TEXT'
            },
            {
              id: 'estimation_clause_particuliere_liste_estimation_clause_particuliere_liste_contenu',
              label: 'Contenu de la clause',
              type: 'TEXTAREA'
            }
          ],
          id: 'estimation_clause_particuliere_liste',
          label: 'Clause particulière',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'estimation_clause_particuliere_liste_estimation_clause_particuliere_liste_titre'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          id: 'estimation_bien',
          label: 'Estimation',
          optional: true,
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'vente_scaprim_pp',
          label: 'Le Bien est-il vendu en pleine propriété ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'vente_scaprim_libre',
          label: 'Le Bien sera-t-il vendu libre ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'scaprim_lasaygues',
              label: 'LASAYGUES ET ASSOCIES SELARL'
            },
            {
              id: 'scaprim_prevot',
              label: 'SCP PREVOT GERAUDIE BLANC CHAU'
            }
          ],
          id: 'vente_scaprim_notaire',
          label: 'Le Notaire du Vendeur est',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'vente_scaprim_acquereur_notaire',
          label: "L'Acquéreur est-il représenté par un autre Notaire ?",
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'vente_scaprim_acquereur_notaire',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'vente_scaprim_acquereur_notaire_nom',
          label: "Nom du notaire de l'Acquéreur",
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'vente_scaprim_acquereur_notaire',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'vente_scaprim_acquereur_notaire_adresse',
          label: "Adresse du notaire de l'Acquéreur",
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'physique',
              label: 'Personne physique'
            },
            {
              id: 'morale',
              label: 'Société'
            }
          ],
          id: 'todo_vendeur_liste',
          label: 'Type de Vendeur',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'capable',
              label: 'Pleine capacité'
            },
            {
              id: 'mineur',
              label: 'Mineur'
            },
            {
              id: 'sauvegarde',
              label: 'Sauvegarde de justice'
            },
            {
              id: 'curatelle',
              label: 'Curatelle'
            },
            {
              id: 'tutelle',
              label: 'Tutelle'
            }
          ],
          id: 'todo_vendeur_capacite',
          label: 'Capacité du Vendeur',
          multiple: true,
          type: 'PICK_LIST'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'residence_principale',
          label: 'Le bien est la résidence principale de tous les vendeurs',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'residence_principale_statut',
          label: "Le bien est la résidence principale d'au moins un vendeur",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'residence_principal_fiscal',
          label: 'Vendeur résident fiscal français',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'todo_vendeur_mariage',
          label: 'Vendeur marié',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'todo_vente_demembrement',
          label: 'Bien détenu en démembrement',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'compromis_recommande_electronique',
          label: 'Notifications par voie électronique',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'compromis_signature_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'professionnelles',
              label: 'Professionnelles'
            },
            {
              id: 'personnelles',
              label: 'Personnelles'
            }
          ],
          id: 'compromis_acquereur_pro',
          label: "L'Acquéreur agit dans le cadre de ses activités",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'compromis_acquereur_primo_accedant',
          label: 'Acquéreur primo-accédant',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'compromis_acquereur_pro_frais_reduits',
          label: "Engagement de vente ou de construction par l'Acquéreur",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'construction',
              label: 'Engagement de construire (fiscalité à 125€ fixe)'
            },
            {
              id: 'revente',
              label: 'Engagement de revente (fiscalité à 0,715%)'
            }
          ],
          id: 'compromis_acquereur_pro_frais_reduits_type',
          label: "Type d'engagement pris par l'Acquéreur",
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'purge_electronique',
              label: 'Par recommandé électronique'
            },
            {
              id: 'purge_papier',
              label: 'Par recommandé papier'
            },
            {
              id: 'purge_main_propre',
              label: 'Par remise en main propre'
            },
            {
              id: 'purge_aucune',
              label: 'Aucune car droit de rétractation non applicable'
            }
          ],
          conditions: [
            [
              {
                id: 'compromis_acquereur_pro',
                value: 'personnelles',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'compromis_purge',
          label: "La purge du délai de rétractation de l'Acquéreur s'effectue",
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'purge_electronique',
              label: 'Par recommandé électronique'
            },
            {
              id: 'purge_papier',
              label: 'Par recommandé papier'
            },
            {
              id: 'purge_aucune',
              label: 'Aucune car droit de rétractation non applicable'
            }
          ],
          conditions: [
            [
              {
                id: 'compromis_acquereur_pro',
                value: 'personnelles',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'compromis_purge_libre',
          label: "La purge du délai de rétractation de l'Acquéreur s'effectue",
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            "Les coordonnées pourront toujours être renseignées dans la fiche mais n'apparaîtront pas dans le contrat.",
          id: 'coordonnees',
          label: 'Coordonnées des parties masquées',
          type: 'SELECT-BINARY'
        },
        {
          after: {
            type: 'N_DAYS_FROM_NOW',
            value: '0'
          },
          id: 'date_butoir',
          label: 'Date butoir de signature de la vente définitive',
          type: 'DATE'
        },
        {
          id: 'date_signature_contrat',
          label: 'Date de signature du compromis',
          type: 'DATE'
        },
        {
          id: 'date_jouissance_anticipee',
          label: 'Date de prise de possession du bien',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'signature_vente_definitive',
          label: 'Vente définitive signée',
          type: 'SELECT-BINARY'
        },
        {
          id: 'date_signature_vente_definitive',
          label: "Date de signature de l'acte de vente définitif",
          type: 'DATE'
        },
        {
          id: 'date_annulation_vente_definitive',
          label: "Date d'annulation de la vente",
          type: 'DATE'
        },
        {
          description:
            'Indiquer ici en quelques lignes si le projet de votre acquéreur est cohérent au vu de son profil ou de son mode de vie et de ses revenus',
          id: 'coherence_operation_generale',
          label: "Cohérence générale de l'opération",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'suspicion_tracfin',
          label: 'Dans le cadre de ce dossier, avez-vous une suspicion de blanchiment d’argent ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'suspicion_tracfin',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          description:
            'Si vous avez une suspicion sur ce dossier vous pouvez établir une classification du risque via la cartographie TRACFIN disponible sur Izicontrat ou contacter le service juridique',
          id: 'suspicion_tracfin_liste',
          label: "Elements laissant croire à du blanchiment d'argent",
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['PROPRIETES_PRIVEES_IMMOBILIER_CONSTITUTION_DOSSIER']
              }
            ]
          ],
          id: 'fiche_renseignement_acquereur',
          label: 'Fiche de renseignement acquéreur',
          type: 'UPLOAD'
        },
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_PROCURATION_ALLOWA'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'procuration_allowa',
          label: 'Procuration PV',
          type: 'UPLOAD'
        },
        {
          id: 'usufruit_titulaire',
          label: "Désignation du titulaire de l'usufruit",
          type: 'TEXT'
        },
        {
          id: 'usufruit_debut',
          label: "Date de début de l'usufruit",
          type: 'DATE'
        },
        {
          id: 'usufruit_fin',
          label: "Date d'extinction de l'usufruit",
          type: 'DATE'
        },
        {
          id: 'usufruit_activite',
          label: "Activité exercée par l'usufruitier",
          type: 'TEXT'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'lia_doc',
          label: "Lettre d'intention d'achat",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'depot_garantie_versement',
                value: 'depot_garantie_agence',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_PP_RIB_SEQUESTRE'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'rib_pp',
          label: 'RIB compte sequestre',
          type: 'UPLOAD'
        },
        {
          id: 'fiche_acquereur_pp',
          label: 'Fiche acquéreur',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'principale',
              label: 'Résidence Principale'
            },
            {
              id: 'secondaire',
              label: 'Résidence Secondaire'
            },
            {
              id: 'pro',
              label: 'Locaux professionnels'
            },
            {
              id: 'locatif',
              label: 'Investissement locatif'
            },
            {
              id: 'autre',
              label: 'Autre projet'
            }
          ],
          id: 'projet_acquereur',
          label: "Projet de l'acquéreur",
          type: 'SELECT'
        },
        {
          id: 'projet_acquereur_autre',
          label: "Autre projet de l'acquéreur",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'pp_fiche_renseignement_acquereur',
          label: 'La fiche de renseignement est-elle signée de manière électronique ?',
          type: 'SELECT-BINARY'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'offre_achat',
          label: "Offre d'achat",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['IMMOBILIER_VENTE_ANCIEN_FICHE_CLOTURE']
              }
            ]
          ],
          id: 'facture_honoraire',
          label: "Facture d'honoraires",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['I_PARTICULIERS_IMMOBILIER_HABITATION_CONTROLE_QUALITE']
              }
            ]
          ],
          id: 'tracfin_mandat',
          label: 'Tracfin',
          type: 'UPLOAD'
        }
      ],
      id: 'c877ad27_7b8a_4d49_b7cf_1b8b5f0ed168',
      label: 'Informations générales sur la vente',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'servitudes',
          label: 'Servitudes',
          type: 'SELECT-BINARY'
        },
        {
          id: 'servitudes_liste',
          label: 'Liste des servitudes',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'viager_indice',
          label: "La rente/mensualité est-elle révisée selon l'indice du cout de la construction ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'viager_reserve_duh',
          label: "Le Mandant conserve-t-il un droit d'usage et d'habitation sur le bien ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'viager_reserve_duh_deces',
          label: "Ce droit d'usage s'éteint-il au décès du Mandant ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'viager_reserve_duh_deces_disposition',
          label: 'Le Vendeur garde la disposition du bien pendant un certain temps seulement?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'viager_reserve_duh_deces_disposition_montant',
          label: "Délai de l'occupation du bien par le Vendeur",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'viager_usage',
              label: 'Vente en viager'
            },
            {
              id: 'viager_terme',
              label: 'Vente à terme'
            },
            {
              id: 'viager_usufruit',
              label: 'Vente de la nue-propriété'
            }
          ],
          id: 'viager_statut',
          label: 'Caractéristiques de la vente',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'usufruit',
              label: "Réserve d'un droit d'usufruit"
            },
            {
              id: 'habitation',
              label: "Réserve d'un droit d'habitation"
            }
          ],
          id: 'viager_statut_simple',
          label: 'Caractéristiques de la vente',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'viager_occupation_libre',
              label: 'Libre'
            },
            {
              id: 'viager_occupation_occupe',
              label: 'Occupé'
            }
          ],
          id: 'viager_occupation',
          label: 'La vente est consentie',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'viager_terme_occupation',
          label: 'La vente à terme est-elle consentie libre ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'temporaire',
              label: 'Temporaire'
            },
            {
              id: 'perpetuelle',
              label: 'Perpetuelle'
            }
          ],
          id: 'viager_reserve_usage',
          label: "Le Vendeur se réserve le droit d'usage de manière",
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'viager_occupation_libre',
          label: "S'agit-il d'une vente à terme ?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'viager_occupation_temporaire',
          label: "Durée du droit d'usage réservé (en années)",
          type: 'NUMBER'
        },
        {
          id: 'viager_terme_delai',
          label: 'Durée totale des mensualités',
          suffix: 'ans',
          type: 'NUMBER'
        },
        {
          description: 'Pourcentage ou montant libre',
          id: 'viager_abandon_rente_pourcentage',
          label: "En cas d'abandon de Jouissance, la rente/mensualité du Vendeur augmentera de :",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description: "Répondre non si le droit est reservé au profit d'un tiers",
          id: 'viager_vendeur_beneficiaire',
          label: 'Ce droit est-il reservé par tous les vendeurs ?',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'viager_beneficiaire_liste_viager_beneficiaire_nom',
              label: 'Nom et prénom du Bénéficiaire',
              type: 'TEXT'
            },
            {
              id: 'viager_beneficiaire_liste_viager_beneficiaire_date_naissance',
              label: 'Date de naissance du Bénéficiaire',
              type: 'DATE'
            }
          ],
          id: 'viager_beneficiaire_liste',
          label: 'Bénéficiaire du droit réservé',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'viager_beneficiaire_liste_viager_beneficiaire_nom'
              }
            ],
            max: 1000,
            min: 0
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'viager_reversion',
          label: 'La rente est-elle réversible au profit du conjoint du vendeur ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'vendeur',
              label: 'du Vendeur'
            },
            {
              id: 'acquereur',
              label: "de l'Acquéreur"
            }
          ],
          id: 'viager_charge_travaux',
          label: 'La répartition des charges, travaux, sont à la charge',
          multiple: true,
          type: 'SELECT'
        },
        {
          id: 'viager_charge_travaux_vendeur_liste',
          label: 'Liste des charges, travaux à la charge du Vendeur',
          type: 'TEXTAREA'
        },
        {
          id: 'viager_charge_travaux_acquereur_liste',
          label: "Liste des charges, travaux à la charge de l'Acquéreur",
          type: 'TEXTAREA'
        }
      ],
      id: '1e747393_b16e_44fb_bd59_cb2f40a7b1a8',
      label: 'Information sur les conditions de la vente',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              id: 'vc_valeur_venale',
              label: 'Valeur vénale du bien',
              type: 'PRICE'
            },
            {
              id: 'vc_valeur_venale_libre',
              label: 'Valeur vénale de la partie libre du bien',
              type: 'PRICE'
            },
            {
              id: 'vc_bouquet',
              label: 'Montant du bouquet',
              type: 'PRICE'
            },
            {
              id: 'vc_usure',
              label: "Ajustement d'usure",
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'vc_esperance',
              label: 'Espérance de vie',
              suffix: 'ans',
              type: 'NUMBER'
            },
            {
              id: 'vc_valeur_usufruit',
              label: "Valeur du droit d'usufruit",
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'vc_valeur_duh',
              label: "Valeur du droit d'usage et d'habitation",
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'vc_taux_rente',
              label: 'Taux de rente',
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'vc_taux_rente_libre',
              label: 'Taux de rente concernant la partie libre du bien',
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'vc_rente_annuite',
              label: "Nombre d'annuités de la rente",
              suffix: 'ans',
              type: 'NUMBER'
            }
          ],
          id: '7599a55f_e160_42c9_af5b_51c93403a983',
          label: 'CONDITION_BLOCK_Viager consulting',
          type: 'CONDITION_BLOCK'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT']
                  }
                ]
              ],
              title: 'Valeur vénale initiale'
            }
          ],
          id: 'viager_valeur_venale',
          label: 'Valeur vénale du bien libre',
          type: 'PRICE'
        },
        {
          id: 'viager_valeur_venale_avenant',
          label: 'Nouvelle valeur vénale du bien libre',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT']
                  }
                ]
              ],
              title: 'Valeur initiale du DUH'
            }
          ],
          id: 'viager_valeur_duh',
          label: "Valeur du droit d'usage et d'habitation",
          type: 'PRICE'
        },
        {
          id: 'viager_valeur_duh_avenant',
          label: "Nouvelle valeur du droit d'usage et d'habitation",
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT']
                  }
                ]
              ],
              title: 'Valeur économique occupée initiale'
            }
          ],
          id: 'viager_valeur_occupe',
          label: 'Valeur économique occupée du bien',
          type: 'PRICE'
        },
        {
          id: 'viager_valeur_occupe_avenant',
          label: 'Nouvelle valeur économique occupée du bien',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_SUCCESS_VIAGER',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_SUCCESS_VIAGER_A_TERME',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_BON_VISITE',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_OFFRE_ACHAT',
                      'KELLER_WILLIAMS_IMMOBILIER_VIAGER_OFFRE_ACHAT_TERME'
                    ]
                  }
                ]
              ],
              title: 'Montant du bouquet net vendeur payé comptant'
            }
          ],
          id: 'viager_bouquet_montant',
          label: 'Montant du bouquet/capital ou prix payé comptant',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT']
                  }
                ]
              ],
              title: 'Valeur bouquet initial'
            }
          ],
          id: 'viager_bouquet_montant_kw',
          label: 'Montant du bouquet net vendeur payé comptant',
          type: 'PRICE'
        },
        {
          id: 'viager_bouquet_montant_kw_avenant',
          label: 'Nouveau montant du bouquet net vendeur payé comptant',
          type: 'PRICE'
        },
        {
          id: 'valeur_usufruit',
          label: "Valeur du droit d'usufruit",
          suffix: '%',
          type: 'NUMBER'
        },
        {
          id: 'valeur_duh',
          label: "Valeur du droit d'usage et d'habitation",
          suffix: '%',
          type: 'NUMBER'
        },
        {
          id: 'taux_rente',
          label: 'Taux de rente',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT']
                  }
                ]
              ],
              title: 'Valeur de la rente/mensualité initiale'
            }
          ],
          id: 'viager_rente_montant',
          label: 'Montant de la rente/mensualité',
          type: 'PRICE'
        },
        {
          id: 'viager_rente_montant_avenant',
          label: 'Nouveau montant de la rente/mensualité',
          type: 'PRICE'
        },
        {
          id: 'viager_rente_duree',
          label: 'Durée des rentes / mensualités',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'consommation',
              label: 'Prix à la consommation'
            },
            {
              id: 'construction',
              label: 'Coût de la construction'
            }
          ],
          id: 'viager_indice_conso_contruction',
          label: 'Indice de révision de la rente/mensualité',
          type: 'SELECT-BINARY'
        },
        {
          id: 'viager_indice_autre',
          label: 'Indice de révision de la rente/mensualité',
          type: 'TEXT'
        },
        {
          id: 'viager_indice_icc_valeur',
          label: "Valeur de l'indice du coût de la construction",
          type: 'TEXT'
        },
        {
          id: 'viager_indice_icc_trimestre',
          label: "Trimestre de l'indice",
          type: 'TEXT'
        },
        {
          id: 'viager_indice_icc_annee',
          label: "Année de l'indice",
          type: 'TEXT'
        },
        {
          id: 'viager_indice_prix_conso_valeur',
          label: "Valeur de l'indice des prix à la consommation",
          type: 'TEXT'
        },
        {
          id: 'viager_indice_prix_conso_trimestre',
          label: "Trimestre de l'indice",
          type: 'TEXT'
        },
        {
          id: 'viager_indice_prix_conso_annee',
          label: "Année de l'indice",
          type: 'TEXT'
        },
        {
          id: 'viager_augmentation_rente',
          label: "En cas de renonciation au droit d'usage, pourcentage d'augmentation de la valeur de la rente",
          type: 'TEXT'
        },
        {
          id: 'viager_mensualite_reevaluee',
          label:
            "Montant de la nouvelle mensualité en cas de renonciation au droit d'usage après la dernière mensualité",
          type: 'TEXT'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_CONTRAT_PRELIMINAIRE_ACHAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Prix de vente initial'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_avenant_signature_electronique',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'EMPTY'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS'
                ]
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER']
              }
            ],
            [
              {
                id: 'mandat_avenant_signature_electronique',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'NOT_EMPTY'
              },
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
              },
              {
                type: 'EMPTY',
                id: 'nouveau_prix_de_vente'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE'
                ]
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER']
              }
            ],
            [
              {
                id: 'mandat_avenant_type',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'CONTAINS',
                value: 'avenant_prix'
              },
              {
                type: 'EMPTY',
                id: 'nouveau_prix_de_vente'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'NOT_CONTAINS',
                value: 'avenant_prix'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: [
                  'OPERATION__EFFICITY__IMMOBILIER',
                  'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE',
                  'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION'
                ]
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
                  'AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'CONTAINS',
                value: 'avenant_prix'
              },
              {
                type: 'EMPTY',
                id: 'nouveau_prix_de_vente'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'NOT_CONTAINS',
                value: 'avenant_prix'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER', 'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION']
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'NOT_CONTAINS',
                value: 'avenant_prix'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION']
              }
            ],
            [
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
                  'AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS'
                ]
              }
            ],
            [
              {
                type: 'EMPTY',
                id: 'nouveau_prix_de_vente'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE'
                ]
              }
            ],
            [
              {
                type: 'EMPTY',
                id: 'nouveau_prix_de_vente'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE'
                ]
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__IMOCONSEIL__IMMOBILIER__VENTE']
              }
            ]
          ],
          description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
          id: 'prix_vente_total',
          label: 'Prix de vente',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_CONTRAT_PRELIMINAIRE_ACHAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Prix de vente initial'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_avenant_signature_electronique',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'NOT_EMPTY'
              },
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
              },
              {
                type: 'EMPTY',
                id: 'nouveau_prix_de_vente'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE'
                ]
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER']
              }
            ],
            [
              {
                id: 'mandat_avenant_type',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'CONTAINS',
                value: 'avenant_prix'
              },
              {
                type: 'EMPTY',
                id: 'nouveau_prix_de_vente'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'NOT_CONTAINS',
                value: 'avenant_prix'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: [
                  'OPERATION__EFFICITY__IMMOBILIER',
                  'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE',
                  'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION'
                ]
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
                  'AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'CONTAINS',
                value: 'avenant_prix'
              },
              {
                type: 'EMPTY',
                id: 'nouveau_prix_de_vente'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'NOT_CONTAINS',
                value: 'avenant_prix'
              },
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER', 'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION']
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE'
                ]
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'NOT_CONTAINS',
                value: 'avenant_prix'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION']
              }
            ],
            [
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
                  'AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS'
                ]
              }
            ],
            [
              {
                type: 'EMPTY',
                id: 'nouveau_prix_de_vente'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE'
                ]
              }
            ],
            [
              {
                type: 'EMPTY',
                id: 'nouveau_prix_de_vente'
              },
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: [
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
                  'BEAUX_VILLAGES__IMMOBILIER__COMPROMIS',
                  'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS',
                  'IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER',
                  'IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE',
                  'PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT',
                  'IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT',
                  'IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE',
                  'IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS',
                  'GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT',
                  'GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE',
                  'GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS',
                  'EFFICITY_IMMOBILIER_BON_VISITE',
                  'EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS',
                  'AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE'
                ]
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__IMOCONSEIL__IMMOBILIER__VENTE']
              }
            ]
          ],
          description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
          id: 'prix_vente_total_cfp',
          label: 'Prix de vente',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_CONTRAT_PRELIMINAIRE_ACHAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Nouveau prix de vente'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_avenant_signature_electronique',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'NOT_EMPTY'
              },
              {
                type: 'DIFFERENT_CONTRACT_MODELS',
                model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__EFFICITY__IMMOBILIER']
              }
            ],
            [
              {
                id: 'mandat_avenant_type',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'CONTAINS',
                value: 'avenant_prix'
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'CONTAINS',
                value: 'avenant_prix'
              }
            ],
            [
              {
                type: 'EQUALS_CONTRACT_MODELS',
                model: ['EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY']
              }
            ],
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__IMOCONSEIL__IMMOBILIER__VENTE']
              }
            ]
          ],
          description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
          id: 'nouveau_prix_de_vente',
          label: 'Prix de vente',
          type: 'PRICE'
        },
        {
          conditionalTitles: [
            {
              conditions: [
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: [
                      'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
                      'TRANSACTION__AVENANT_MANDAT_ANGLAIS',
                      'BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT',
                      'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT',
                      'KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER',
                      'KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT',
                      'PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE',
                      'PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT',
                      'PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT',
                      'PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT',
                      'EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY',
                      'I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT',
                      'IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP',
                      'GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT',
                      'IMOCONSEIL_IMMOBILIER_VENTE_CONTRAT_PRELIMINAIRE_ACHAT',
                      'AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT'
                    ]
                  }
                ]
              ],
              title: 'Nouveau prix de vente'
            }
          ],
          conditions: [
            [
              {
                id: 'mandat_avenant_type',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'CONTAINS',
                value: 'avenant_prix'
              }
            ],
            [
              {
                id: 'mandat_avenant_type_pp',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'CONTAINS',
                value: 'avenant_prix'
              }
            ]
          ],
          description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
          id: 'nouveau_prix_de_vente_cfp',
          label: 'Prix de vente',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'ttc',
              label: 'Toutes Taxes Comprises'
            },
            {
              id: 'ht',
              label: 'Hors Taxes'
            }
          ],
          id: 'prix_vente_tva',
          label: 'Ce prix est exprimé',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'prix_vente_tva_statut',
          label: 'Le prix de vente est soumis à TVA',
          type: 'SELECT-BINARY'
        },
        {
          id: 'ad_prix_vente',
          label: 'Prix de vente',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'prix_vente_ventilation',
          label: 'Ajouter une ventilation du prix de vente',
          type: 'SELECT-BINARY'
        },
        {
          id: 'prix_vente_ventilation_repartition',
          label: 'Ventilation du prix de vente',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'prix_vente_conversion_livres_sterling',
          label: 'Convertir le prix en Livres Sterling',
          type: 'SELECT-BINARY'
        },
        {
          id: 'prix_vente_conversion_livres_sterling_montant',
          label: 'Prix de vente en Livres Sterling',
          suffix: '£',
          type: 'NUMBER'
        },
        {
          id: 'prix_vente_conversion_livres_sterling_taux_change',
          label: 'Taux de change en Euros',
          type: 'PRICE'
        },
        {
          id: 'prix_vente_conversion_livres_sterling_solicitor_nom',
          label: 'Nom du Solicitor dépositaire du prix',
          type: 'TEXT'
        },
        {
          id: 'prix_vente_conversion_livres_sterling_solicitor_adresse',
          label: 'Adresse du Solicitor',
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'presence_meubles',
          label: 'Présence de mobilier',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'meubles_valorisation',
          label: 'Mobilier valorisé au sein du compromis',
          type: 'SELECT-BINARY'
        },
        {
          id: 'meubles_liste_libre',
          label: 'Liste du mobilier',
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                conditions: [
                  {
                    id: 'presence_meubles',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
              }
            ],
            [
              {
                id: 'meubles_valorisation',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'liste_mobilier_annexe',
          label: 'Liste du mobilier',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'meubles_valorisation',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'meubles_montant_libre',
          label: 'Montant du mobilier',
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                conditions: [
                  {
                    id: 'meubles_valorisation',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__AGENCE_DIRECTE__IMMOBILIER']
              }
            ],
            [
              {
                conditions: [
                  {
                    id: 'presence_meubles',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                type: 'DIFFERENT_TEMPLATES',
                templates: [
                  'OPERATION__AGENCE_DIRECTE__IMMOBILIER',
                  'OPERATION__GIBOIRE__IMMOBILIER',
                  'OPERATION__KEREDES__IMMOBILIER_BRS'
                ]
              }
            ]
          ],
          id: 'meubles_montant',
          label: 'Montant du mobilier',
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                conditions: [
                  {
                    id: 'meubles_valorisation',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__AGENCE_DIRECTE__IMMOBILIER']
              }
            ],
            [
              {
                conditions: [
                  {
                    id: 'presence_meubles',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                type: 'DIFFERENT_TEMPLATES',
                templates: [
                  'OPERATION__AGENCE_DIRECTE__IMMOBILIER',
                  'OPERATION__GIBOIRE__IMMOBILIER',
                  'OPERATION__KEREDES__IMMOBILIER_BRS'
                ]
              }
            ]
          ],
          description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
          id: 'meubles_montant_cfp',
          label: 'Montant du mobilier',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'liste_annexe',
                  label: 'annexée au contrat'
                },
                {
                  id: 'liste_compromis',
                  label: 'incluse dans le contrat'
                }
              ],
              id: 'presence_meubles_liste_statut',
              label: 'Liste du mobilier',
              type: 'SELECT'
            },
            {
              conditions: [
                [
                  {
                    id: 'presence_meubles',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'presence_meubles_liste_statut',
                    type: 'EQUALS',
                    value: 'liste_annexe'
                  }
                ]
              ],
              id: 'liste_meuble_annexe_montant',
              label: 'Montant du mobilier',
              type: 'PRICE'
            },
            {
              conditions: [
                [
                  {
                    id: 'presence_meubles',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'presence_meubles_liste_statut',
                    type: 'EQUALS',
                    value: 'liste_annexe'
                  }
                ]
              ],
              description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
              id: 'liste_meuble_annexe_montant_cfp',
              label: 'Montant du mobilier',
              suffix: 'CFP',
              type: 'PRICE'
            },
            {
              conditions: [
                [
                  {
                    id: 'presence_meubles',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'presence_meubles_liste_statut',
                    type: 'EQUALS',
                    value: 'liste_annexe'
                  }
                ]
              ],
              id: 'liste_meuble',
              label: 'Liste du mobilier',
              type: 'UPLOAD'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'cuisine',
                      label: 'Cuisine'
                    },
                    {
                      id: 'salon',
                      label: 'Salon'
                    },
                    {
                      id: 'salle_manger',
                      label: 'Salle à manger'
                    },
                    {
                      id: 'bureau',
                      label: 'Bureau'
                    },
                    {
                      id: 'salle_eau',
                      label: "Salle d'eau"
                    },
                    {
                      id: 'salle_bains',
                      label: 'Salle de bains'
                    },
                    {
                      id: 'chambre',
                      label: 'Chambre'
                    },
                    {
                      id: 'wc',
                      label: 'WC'
                    },
                    {
                      id: 'entrée',
                      label: 'Entrée'
                    },
                    {
                      id: 'piece_autre',
                      label: 'Autre'
                    }
                  ],
                  id: 'liste_meuble_compromis_liste_meuble_piece',
                  label: 'Pièce',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        conditions: [
                          {
                            id: 'presence_meubles',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        type: 'EQUALS_TEMPLATES',
                        templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
                      },
                      {
                        id: 'liste_meuble_compromis_liste_meuble_piece',
                        value: 'piece_autre',
                        type: 'EQUALS'
                      },
                      {
                        id: 'presence_meubles',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'presence_meubles_liste_statut',
                        type: 'EQUALS',
                        value: 'liste_compromis'
                      },
                      {
                        type: 'DIFFERENT_TEMPLATES',
                        templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
                      }
                    ],
                    [
                      {
                        conditions: [
                          {
                            id: 'presence_meubles',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        type: 'EQUALS_TEMPLATES',
                        templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
                      },
                      {
                        id: 'liste_meuble_compromis_liste_meuble_piece',
                        value: 'piece_autre',
                        type: 'EQUALS'
                      },
                      {
                        id: 'presence_meubles',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'liste_meuble_compromis_liste_meuble_piece',
                        value: 'piece_autre',
                        type: 'EQUALS'
                      },
                      {
                        id: 'presence_meubles',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'presence_meubles_liste_statut',
                        type: 'EQUALS',
                        value: 'liste_compromis'
                      },
                      {
                        type: 'DIFFERENT_TEMPLATES',
                        templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
                      }
                    ]
                  ],
                  id: 'liste_meuble_compromis_liste_meuble_piece_autre',
                  label: 'Autre Pièce',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'liste_meuble_chaises',
                      label: 'Chaises'
                    },
                    {
                      id: 'liste_meuble_canape',
                      label: 'Canapé'
                    },
                    {
                      id: 'liste_meuble_congelateur',
                      label: 'Congélateur'
                    },
                    {
                      id: 'liste_meuble_fauteuil',
                      label: 'Fauteuil'
                    },
                    {
                      id: 'liste_meuble_four',
                      label: 'Four'
                    },
                    {
                      id: 'liste_meuble_hotte',
                      label: 'Hotte'
                    },
                    {
                      id: 'liste_meuble_lave_vaisselle',
                      label: 'Lave-Vaisselle'
                    },
                    {
                      id: 'liste_meuble_luminaire',
                      label: 'Luminaires'
                    },
                    {
                      id: 'liste_meuble_machine',
                      label: 'Machine à laver'
                    },
                    {
                      id: 'liste_meuble_meuble_bas',
                      label: 'Meuble Bas'
                    },
                    {
                      id: 'liste_meuble_haut',
                      label: 'Meuble Haut'
                    },
                    {
                      id: 'liste_meuble_micro_onde',
                      label: 'Micro-onde'
                    },
                    {
                      id: 'liste_meuble_miroir',
                      label: 'Miroir'
                    },
                    {
                      id: 'liste_meuble_plaques_induction',
                      label: 'Plaques Induction'
                    },
                    {
                      id: 'liste_meuble_plaques_vitro',
                      label: 'Plaques Vitrocéramique'
                    },
                    {
                      id: 'liste_meuble_plauqes_gaz',
                      label: 'Plaques Gaz'
                    },
                    {
                      id: 'liste_meuble_refrigerateur',
                      label: 'Réfrigérateur'
                    },
                    {
                      id: 'liste_meuble_seche',
                      label: 'Sèche-Linge'
                    },
                    {
                      id: 'liste_meuble_table',
                      label: 'Table'
                    },
                    {
                      id: 'liste_meuble_autre',
                      label: 'Autre'
                    }
                  ],
                  id: 'liste_meuble_compromis_liste_meuble_designation',
                  label: 'Meuble',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        conditions: [
                          {
                            id: 'presence_meubles',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        type: 'EQUALS_TEMPLATES',
                        templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
                      },
                      {
                        id: 'liste_meuble_compromis_liste_meuble_designation',
                        value: 'liste_meuble_autre',
                        type: 'EQUALS'
                      },
                      {
                        id: 'presence_meubles',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'presence_meubles_liste_statut',
                        type: 'EQUALS',
                        value: 'liste_compromis'
                      },
                      {
                        type: 'DIFFERENT_TEMPLATES',
                        templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
                      }
                    ],
                    [
                      {
                        conditions: [
                          {
                            id: 'presence_meubles',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        type: 'EQUALS_TEMPLATES',
                        templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
                      },
                      {
                        id: 'liste_meuble_compromis_liste_meuble_designation',
                        value: 'liste_meuble_autre',
                        type: 'EQUALS'
                      },
                      {
                        id: 'presence_meubles',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'liste_meuble_compromis_liste_meuble_designation',
                        value: 'liste_meuble_autre',
                        type: 'EQUALS'
                      },
                      {
                        id: 'presence_meubles',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'presence_meubles_liste_statut',
                        type: 'EQUALS',
                        value: 'liste_compromis'
                      },
                      {
                        type: 'DIFFERENT_TEMPLATES',
                        templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
                      }
                    ]
                  ],
                  id: 'liste_meuble_compromis_liste_meuble_autre_designation',
                  label: 'Autre meuble',
                  type: 'TEXT'
                },
                {
                  id: 'liste_meuble_compromis_liste_meuble_valeur',
                  label: 'Valeur',
                  type: 'PRICE'
                },
                {
                  description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
                  id: 'liste_meuble_compromis_liste_meuble_valeur_cfp',
                  label: 'Valeur',
                  suffix: 'CFP',
                  type: 'PRICE'
                }
              ],
              conditions: [
                [
                  {
                    conditions: [
                      {
                        id: 'presence_meubles',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
                  },
                  {
                    id: 'presence_meubles',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'presence_meubles',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'presence_meubles_liste_statut',
                    type: 'EQUALS',
                    value: 'liste_compromis'
                  },
                  {
                    type: 'DIFFERENT_TEMPLATES',
                    templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
                  }
                ]
              ],
              id: 'liste_meuble_compromis',
              label: 'Liste du mobilier',
              repetition: {
                label: [
                  {
                    type: 'VARIABLE',
                    value: 'liste_meuble_compromis_liste_meuble_designation'
                  },
                  {
                    type: 'TEXT',
                    value: ' - '
                  },
                  {
                    type: 'VARIABLE',
                    value: 'liste_meuble_compromis_liste_meuble_valeur'
                  },
                  {
                    type: 'TEXT',
                    value: '€'
                  }
                ],
                max: 1000,
                min: 0
              },
              type: 'REPEAT'
            }
          ],
          conditions: [
            [
              {
                id: 'presence_meubles',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: '5fabc87d_4874_402d_8091_d24d3b64a0a7',
          label: 'Mobilier',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'depot_garantie_statut',
              label: 'Dépôt de garantie',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              description:
                "Attention ! L'article 1589-1 du code civil interdit en principe le versement d'un dépôt de garantie par le Promettant",
              id: 'depot_garantie_pua_statut',
              label: 'Un dépôt de garantie est-il versé par le Promettant ?',
              type: 'SELECT-BINARY'
            },
            {
              id: 'autre_depot_garantie_montant',
              label: 'Montant du dépôt de garantie',
              type: 'PRICE'
            },
            {
              description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
              id: 'autre_depot_garantie_montant_cfp',
              label: 'Montant du dépôt de garantie',
              suffix: 'CFP',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'efficity',
                  label: 'A Efficity'
                },
                {
                  id: 'notaire',
                  label: 'Au notaire'
                }
              ],
              id: 'depot_garantie_efficity_versement',
              label: 'Le dépôt de garantie est versé :',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'agence',
                  label: "A l'Agence"
                },
                {
                  id: 'notaire',
                  label: 'Au notaire'
                }
              ],
              id: 'depot_garantie_versement_notaire_agence',
              label: 'Le dépôt de garantie est versé :',
              type: 'SELECT'
            },
            {
              id: 'autre_depot_garantie_pourcentage',
              label: 'Pourcentage du dépôt de garantie',
              suffix: '%',
              type: 'NUMBER'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'dix',
                      label: '10 % du prix de vente'
                    },
                    {
                      id: 'cinq',
                      label: '5 % du prix de vente'
                    },
                    {
                      id: 'depot_autre',
                      label: 'Autre montant'
                    }
                  ],
                  id: 'depot_garantie_montant',
                  label: 'Montant du dépôt de garantie',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'depot_garantie_montant',
                        type: 'EQUALS',
                        value: 'depot_autre'
                      }
                    ]
                  ],
                  id: 'depot_garantie_autre',
                  label: 'Autre montant',
                  type: 'PRICE'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'depot_garantie_montant',
                        type: 'EQUALS',
                        value: 'depot_autre'
                      }
                    ]
                  ],
                  description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
                  id: 'depot_garantie_autre_cfp',
                  label: 'Autre montant',
                  suffix: 'CFP',
                  type: 'PRICE'
                },
                {
                  choices: [
                    {
                      id: 'depot_garantie_agence',
                      label: "A l'Agence"
                    },
                    {
                      id: 'depot_garantie_notaire',
                      label: 'Au Notaire'
                    }
                  ],
                  id: 'depot_garantie_versement',
                  label: 'Le montant du dépôt de garantie est versé',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'depot_garantie_compromis',
                      label: 'Le jour du compromis de vente'
                    },
                    {
                      id: 'depot_garantie_quinze',
                      label: 'Dans les quinze jours de la signature du compromis'
                    },
                    {
                      id: 'depot_garantie_retractation',
                      label: 'A la fin du délai de rétractation'
                    },
                    {
                      id: 'depot_garantie_huit',
                      label: 'Dans les huit jours à compter de la fin du délai de rétractation'
                    },
                    {
                      id: 'depot_garantie_autre_date',
                      label: 'Un autre délai'
                    }
                  ],
                  id: 'depot_garantie_date',
                  label: 'Délai de versement du dépôt de garantie',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'depot_garantie_date',
                        type: 'EQUALS',
                        value: 'depot_garantie_autre_date'
                      }
                    ]
                  ],
                  id: 'depot_garantie_date_autre',
                  label: "Délai de versement du dépôt de garantie, à compter de la signature de l'acte",
                  suffix: 'jours',
                  type: 'NUMBER'
                }
              ],
              id: 'be7ddeff_6586_42d6_a729_f33656be5fc0',
              label: 'CONDITION_BLOCK_Dépôt de garantie',
              type: 'CONDITION_BLOCK'
            }
          ],
          id: 'bce46901_2f59_4802_911e_d6e6c9315893',
          label: 'Dépôt de garantie',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'prorata_foncier_statut',
              label: 'Prorata de taxe foncière',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'tf_annexe',
              label: 'La taxe-foncière est-elle annexée au compromis ?',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'provision_frais',
              label: "Provision sur frais d'acte",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'provision_frais',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'provision_frais_montant',
              label: 'Montant de la provision',
              type: 'PRICE'
            },
            {
              conditions: [
                [
                  {
                    id: 'provision_frais',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
              id: 'provision_frais_montant_cfp',
              label: 'Montant de la provision',
              suffix: 'CFP',
              type: 'PRICE'
            }
          ],
          id: '275f5748_836b_4586_82c3_df513e46d0ce',
          label: 'Autres modalités financières',
          type: 'CATEGORY'
        }
      ],
      id: 'fb6b2c48_5676_4d9f_a9d0_2d05938872aa',
      label: 'Informations financières sur la vente',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'giboire_dossier_alur_complet',
              label: "Tous les documents de la loi ALUR ont été remis à l'acquéreur (dossier complet)",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'compromis',
                  label:
                    "Travaux votés avant le compromis sont à la charge du Vendeur + pouvoir donné à l'Acquéreur pour assister à l'AG"
                },
                {
                  id: 'vente',
                  label: "Travaux votés avant l'acte de vente sont à la charge du Vendeur"
                }
              ],
              description:
                "Prévilégiez la première hypothèse, qui permet à l'acquéreur de voter à une éventuelle AG entre le compromis et la vente. Si le pouvoir n'est pas donné par le Vendeur, il conservera la charge des travaux éventuellement votés.",
              id: 'giboire_charge_copro_derogation',
              label: 'Répartition des charges hors budget prévisionnel',
              type: 'SELECT'
            }
          ],
          id: 'f24808e0_875a_4962_b485_430c473c0320',
          label: 'Informations sur la vente en Copropriété',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'giboire_dispositif_fiscal',
              label: 'Le Vendeur est soumis à un dispositif de défiscalisation',
              type: 'SELECT-BINARY'
            }
          ],
          id: '305fd0d1_a521_4b45_a382_dee2b66a52f1',
          label: 'Informations sur un dispositif fiscal',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'cheque',
                  label: 'Chèque'
                },
                {
                  id: 'virement',
                  label: 'Virement'
                }
              ],
              id: 'giboire_depot_garantie_versement',
              label: 'Le dépôt de garantie est versé par',
              type: 'SELECT-BINARY'
            },
            {
              id: 'giboire_depot_garantie_montant',
              label: 'Montant du dépôt de garantie',
              type: 'PRICE'
            },
            {
              id: 'giboire_banque_cheque',
              label: 'Banque émettrice du chèque',
              type: 'TEXT'
            },
            {
              id: 'giboire_banque_cheque_numero',
              label: 'Numéro de chèque',
              type: 'TEXT'
            },
            {
              id: 'giboire_banque_cheque_date',
              label: 'Date du chèque',
              type: 'DATE'
            },
            {
              conditions: [
                [
                  {
                    id: 'compromis_signature_electronique',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'emprunt_statut',
                    recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'FINANCEMENT_ACQUEREUR', '0'],
                    type: 'EQUALS',
                    value: 'non'
                  }
                ]
              ],
              id: 'mention_manuscrite_giboire',
              label: 'Déclaration de non-recours à un prêt',
              templateId: 'renonciationFinancementGiboire.docx',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'compromis_signature_electronique',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'emprunt_statut',
                    recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'FINANCEMENT_ACQUEREUR', '0'],
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mention_manuscrite_procuration_giboire_30',
                    recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'FINANCEMENT_ACQUEREUR', '0'],
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'mention_manuscrite_giboire_30_document',
              label: 'Déclaration de non-recours à un prêt - apport',
              templateId: 'renonciationFinancementGiboireApport.docx',
              type: 'UPLOAD'
            }
          ],
          id: 'd13e7684_7e9c_4fbe_8528_d4274c1b08b2',
          label: 'Informations sur le dépôt de garantie',
          type: 'CATEGORY'
        }
      ],
      id: '5e044a19_936b_404c_bfb5_b411e29670ae',
      label: 'Informations sur les modalités de la vente',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'offre_brs_utilisateur_document',
          label: 'BRS Utilisateur',
          type: 'UPLOAD'
        },
        {
          id: 'offre_cession_date',
          label: "Date de l'offre de cession à l'acquéreur",
          type: 'DATE'
        },
        {
          children: [
            {
              id: 'offre_brs_notaire_nom',
              label: 'Nom du notaire ayant reçu le BRS initial',
              type: 'TEXT'
            },
            {
              id: 'offre_brs_notaire_ville',
              label: 'Ville du notaire',
              type: 'TEXT'
            },
            {
              id: 'offre_brs_notaire_date',
              label: "Date de l'acte",
              type: 'DATE'
            },
            {
              id: 'offre_brs_societe_operateur',
              label: 'Nom de la société Opérateur - Preneur',
              type: 'TEXT'
            }
          ],
          id: '11186712_e98d_4f69_91ca_27e6f306380b',
          label: 'BRS Initial - OFS',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'acquisition_brs_notaire_nom',
              label: "Nom du notaire ayant reçu l'acte",
              type: 'TEXT'
            },
            {
              id: 'acquisition_brs_notaire_ville',
              label: 'Ville du notaire',
              type: 'TEXT'
            },
            {
              id: 'acquisition_brs_notaire_date',
              label: "Date de l'acte",
              type: 'DATE'
            }
          ],
          id: '10de89ce_11be_48dd_9e96_6f1723a627f1',
          label: 'Acquisition par le vendeur',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'offre_ofs_premiere_cession_prix',
              label: 'Prix de la première cession de droits réels immobilier',
              type: 'PRICE'
            },
            {
              id: 'offre_ofs_premiere_cession_indice',
              label: "Montant de l'IRL applicable lors de la cession",
              type: 'TEXT'
            },
            {
              id: 'offre_ofs_premiere_cession_indice_trimestre',
              label: "Trimestre de l'IRL",
              type: 'TEXT'
            },
            {
              id: 'offre_ofs_premiere_cession_indice_actuel',
              label: "Montant de l'IRL applicable au jour de l'offre de cession",
              type: 'TEXT'
            }
          ],
          id: '43a38280_baca_455a_9ff1_e170d369c2c2',
          label: 'Calcul du prix',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'offre_signature_electronique',
              label: 'Signature électronique',
              type: 'SELECT-BINARY'
            }
          ],
          id: 'a0d836bf_9f59_4b76_a859_9bcd37382a16',
          label: 'Signature',
          type: 'CATEGORY'
        }
      ],
      id: 'fcef2ebb_2c76_45c9_92ee_a4f2394fb348',
      label: 'Informations sur la règlementation sociale',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'sinistre_en_cours',
          label: 'Sinistre en cours sur le bien',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'sinistre_assurance_nom',
              label: "Nom de l'assurance",
              type: 'TEXT'
            },
            {
              id: 'sinistre_declaration_date',
              label: 'Date de la déclaration',
              type: 'DATE'
            },
            {
              id: 'sinistre_declaration_numero',
              label: 'Numéro de la déclaration',
              type: 'TEXT'
            },
            {
              id: 'sinistre_declaration_date_courrier',
              label: "Date de courrier de l'assurance",
              type: 'DATE'
            },
            {
              id: 'sinistre_declaration_montant',
              label: 'Montant de la prise en charge des travaux',
              type: 'PRICE'
            },
            {
              id: 'sinistre_declaration_franchise',
              label: 'Montant de la franchise',
              type: 'PRICE'
            },
            {
              id: 'sinistre_declaration_devis_entreprise',
              label: "Nom de l'entreprise établissant le devis",
              type: 'TEXT'
            },
            {
              id: 'sinistre_declaration_devis_entreprise_adresse',
              label: "Adresse de l'entreprise",
              type: 'ADDRESS'
            }
          ],
          id: '31e72a80_1ab2_4932_b0f0_d21e31a0e230',
          label: 'Information sur le sinistre',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'parcelle_enclavee',
          label: 'Parcelle enclavée suite à la vente',
          type: 'SELECT-BINARY'
        },
        {
          id: 'parcelle_enclavee_section',
          label: 'Parcelle concernée',
          type: 'TEXT'
        },
        {
          id: 'parcelle_enclavee_proprietaire',
          label: 'Désignation du propriétaire',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'visite_virtuelle',
          label: 'Acquéreur ayant procédé à une visite virtuelle',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'convention_anah',
          label: "Convention conclue avec l'ANAH",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'clause_tontine',
          label: "Application d'une tontine",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'immeuble_inhabitable',
          label: 'Immeuble inhabitable',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'preemption_fermier',
          label: 'Droit de préemption du fermier',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'zone_safer',
          label: 'Bien situé dans une zone de préemption de la SAFER',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'autorisation_urbanisme',
          label: "Condition suspensive d'obtention d'une autorisation d'urbanisme",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [
                    {
                      id: 'acquereur',
                      label: 'Acquéreur'
                    },
                    {
                      id: 'vendeur',
                      label: 'Vendeur'
                    }
                  ],
                  id: 'condition_suspensive_urbanisme_liste_condition_suspensive_urbanisme_demandeur',
                  label: "Demandeur de l'autorisation",
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'urbanisme_permis_construire',
                      label: 'Permis de construire'
                    },
                    {
                      id: 'urbanisme_permis_amenager',
                      label: "Permis d'aménager"
                    },
                    {
                      id: 'urbanisme_declaration',
                      label: 'Déclaration préalable'
                    },
                    {
                      id: 'urbanisme_certificat_information',
                      label: "Un certificat d'urbanisme d'information"
                    },
                    {
                      id: 'urbanisme_certificat_preoperationnel',
                      label: "Un certificat d'urbanisme préopérationnel"
                    }
                  ],
                  id: 'condition_suspensive_urbanisme_liste_condition_suspensive_urbanisme_type',
                  label: "Type d'autorisation",
                  type: 'SELECT'
                },
                {
                  id: 'condition_suspensive_urbanisme_liste_condition_suspensive_urbanisme_delai_depot',
                  label: "Délai pour déposer l'autorisation",
                  suffix: 'mois',
                  type: 'NUMBER'
                },
                {
                  id: 'condition_suspensive_urbanisme_liste_condition_suspensive_urbanisme_delai_depot_date',
                  label: "Date limite pour déposer l'autorisation",
                  type: 'DATE'
                },
                {
                  id: 'condition_suspensive_urbanisme_liste_condition_suspensive_urbanisme_delai_obtention',
                  label: "Délai d'obtention de l'autorisation",
                  suffix: 'mois',
                  type: 'NUMBER'
                },
                {
                  id: 'condition_suspensive_urbanisme_liste_condition_suspensive_urbanisme_obtention_date',
                  label: "Date limite pour obtenir l'autorisation",
                  type: 'DATE'
                },
                {
                  id: 'condition_suspensive_urbanisme_liste_condition_suspensive_urbanisme_descriptif',
                  label: "Description du projet de l'acquéreur",
                  type: 'TEXTAREA'
                }
              ],
              id: 'condition_suspensive_urbanisme_liste',
              label: 'Ajouter une condition suspensive',
              repetition: {
                label: [
                  {
                    type: 'TEXT',
                    value: '-Condition Suspensive'
                  }
                ],
                max: 1000,
                min: 1
              },
              type: 'REPEAT'
            }
          ],
          conditions: [
            [
              {
                id: 'autorisation_urbanisme',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: '63fd580d_b69a_48fa_b88a_f5ead5e95051',
          label: 'Condition suspensive',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'condition_suspensive_droit_preference',
          label: 'Droit de préférence sur le bien',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'condition_suspensive_ajout',
          label: 'Ajouter une autre condition suspensive',
          type: 'SELECT-BINARY'
        },
        {
          id: 'condition_suspensive_ajout_texte',
          label: 'Autre condition suspensive',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'faculte_substitution',
          label: 'Faculté de substitution',
          type: 'SELECT-BINARY'
        }
      ],
      id: '19ecf713_5e4a_434a_a9b8_736d72b490b4',
      label: 'Informations particulières sur les modalités de la vente',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER']
              }
            ]
          ],
          description: 'Détection automatique de la zone tendue ou non',
          id: 'indication_zone_location',
          label: 'Afficher la zone de situation du Bien en cas de location',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostic_resultats_statut',
          label: "Affichage des résultats des diagnostics et de l'état des risques",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'georisques_statut',
          label:
            "Les documents d'urbanisme relatifs à l'environnement (BASOL,BASIAS,ICPE, Géorisques) doivent-il être annexés au compromis ?",
          type: 'SELECT-BINARY'
        }
      ],
      id: '69cab445_f8cb_4192_b733_94eea7f5e481',
      label: 'Règlementation spéciale',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'copropriete_vente_stationnement',
          label: "Droit de priorité des copropriétaires en cas de vente d'un stationnement",
          type: 'SELECT-BINARY'
        }
      ],
      id: '924ffp21_pck4_09ix_fp22_03odpog3pol5',
      label: 'Copropriété',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'promesse_vente_date',
          label: "Date d'expiration de la promesse de vente",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            'Attention une promesse de vente signée de manière électronique ne peut pas être enregistrée aux impôts',
          id: 'promesse_signature_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '4b65ccae_019c_446f_a639_d630a600c5ee',
      label: 'Promesse de vente',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'promesse_achat_date',
          label: "Date d'expiration de la promesse d'achat",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'promesse_achat_signature_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '4092b4aa_30a9_4b6d_bea0_63d1366b5e03',
      label: "Promesse d'achat",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          conditions: [
            [
              {
                id: 'compromis_acquereur_pro',
                value: 'personnelles',
                type: 'EQUALS'
              },
              {
                id: 'compromis_purge',
                value: 'purge_main_propre',
                type: 'EQUALS'
              },
              {
                id: 'compromis_signature_electronique',
                value: 'oui',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'compromis_acquereur_pro',
                value: 'personnelles',
                type: 'EQUALS'
              },
              {
                id: 'compromis_purge',
                value: 'purge_main_propre',
                type: 'EQUALS'
              },
              {
                id: 'promesse_signature_electronique',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'purge_sru_manuscrite',
          label: 'Mention manuscrite - Purge SRU',
          templateId: 'remiseMainPropreSru.pdf',
          type: 'UPLOAD'
        }
      ],
      id: '2760c581_dc79_4595_a625_4cb41466352d',
      label: 'Mention manuscrite - Purge SRU',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          conditions: [
            [
              {
                id: 'compromis_acquereur_pro',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'compromis_signature_electronique',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'emprunt_statut',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'FINANCEMENT_ACQUEREUR', '0'],
                type: 'EQUALS',
                value: 'non'
              }
            ],
            [
              {
                id: 'compromis_acquereur_pro',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'emprunt_statut',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'FINANCEMENT_ACQUEREUR', '0'],
                type: 'EQUALS',
                value: 'non'
              },
              {
                id: 'promesse_signature_electronique',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'compromis_signature_electronique',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'pp_emprunt_statut',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'FINANCEMENT_ACQUEREUR', '0'],
                type: 'EQUALS',
                value: 'non'
              }
            ]
          ],
          description: 'Un exemplaire par Acquéreur',
          id: 'mention_manuscrite',
          label: 'Déclaration de non-recours à un prêt',
          templateId: 'renonciationFinancement.pdf',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'compromis_acquereur_pro',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'compromis_signature_electronique',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'pret_partiel',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'FINANCEMENT_ACQUEREUR', '0'],
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'mention_manuscrite_partiel',
          label: 'Renonciation - financement partiel',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'compromis_acquereur_pro',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'compromis_signature_electronique',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'emprunt_statut',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'FINANCEMENT_ACQUEREUR', '0'],
                type: 'EQUALS',
                value: 'non'
              }
            ],
            [
              {
                id: 'compromis_acquereur_pro',
                type: 'EQUALS',
                value: 'personnelles'
              },
              {
                id: 'emprunt_statut',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'FINANCEMENT_ACQUEREUR', '0'],
                type: 'EQUALS',
                value: 'non'
              },
              {
                id: 'promesse_signature_electronique',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'compromis_signature_electronique',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'pp_emprunt_statut',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'FINANCEMENT_ACQUEREUR', '0'],
                type: 'EQUALS',
                value: 'non'
              }
            ]
          ],
          description: 'Un exemplaire par Acquéreur',
          id: 'mention_manuscrite_anglais',
          label: 'Déclaration de non-recours à un prêt',
          templateId: 'renonciationFinancementAnglais.pdf',
          type: 'UPLOAD'
        },
        {
          description: 'Un exemplaire par Acquéreur',
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mention_manuscrite_vc',
          label: 'Déclaration de non-recours à un prêt',
          templateId: 'renonciationFinancement.pdf',
          type: 'UPLOAD'
        }
      ],
      id: '2689cda6_348d_43e0_b64c_0f284ff661cf',
      label: 'Financement',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avenant_libre_statut',
          label: 'Avenant libre',
          type: 'SELECT-BINARY'
        },
        {
          id: 'avenant_libre_texte',
          label: "Texte de l'avenant libre",
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avenant_pret_statut',
          label: 'Avenant prorogation prêt',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'taux_fixe',
                  label: 'Prêt à taux fixe'
                },
                {
                  id: 'taux_variable',
                  label: 'Prêt à taux variable'
                },
                {
                  id: 'in_fine',
                  label: 'Prêt in fine'
                },
                {
                  id: 'pel',
                  label: 'Prêt Plan épargne logement'
                },
                {
                  id: 'employeur',
                  label: 'Prêt 1% employeur'
                },
                {
                  id: 'taux_zero',
                  label: 'Prêt à taux zéro'
                },
                {
                  id: 'taux_zero_plus',
                  label: 'Prêt à taux zéro plus ou PTZ+'
                },
                {
                  id: 'accession_sociale',
                  label: "Prêt à l'accession sociale"
                },
                {
                  id: 'conventionne',
                  label: 'Prêt conventionné'
                },
                {
                  id: 'relais',
                  label: 'Prêt relais'
                },
                {
                  id: 'paris_logement_0',
                  label: 'Prêt Paris logement 0%'
                },
                {
                  id: 'travaux',
                  label: 'Prêt travaux'
                },
                {
                  id: 'autre',
                  label: 'Autre'
                }
              ],
              id: 'avenant_emprunt_list_avenant_emprunt_type',
              label: "Nature de l'emprunt",
              type: 'SELECT'
            },
            {
              id: 'avenant_emprunt_list_avenant_emprunt_type_autre',
              label: "Type d'emprunt",
              type: 'TEXT'
            },
            {
              id: 'avenant_emprunt_list_avenant_emprunt_montant_maximum',
              label: 'Montant maximum à emprunter',
              type: 'PRICE'
            },
            {
              description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
              id: 'avenant_emprunt_list_avenant_emprunt_montant_maximum_cfp',
              label: 'Montant maximum à emprunter',
              suffix: 'CFP',
              type: 'PRICE'
            },
            {
              id: 'avenant_emprunt_list_avenant_emprunt_duree_maximum',
              label: "Durée maximum de l'emprunt",
              suffix: 'années',
              type: 'NUMBER'
            },
            {
              id: 'avenant_emprunt_list_avenant_emprunt_taux_maximum',
              label: "Taux maximum de l'emprunt",
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'avenant_emprunt_list_avenant_emprunt_banque',
              label: 'Etablissement bancaire',
              type: 'TEXT'
            }
          ],
          id: 'avenant_emprunt_list',
          label: "Nouvelle modalité d'emprunt",
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: 'Emprunt '
              },
              {
                type: 'VARIABLE',
                value: 'avenant_emprunt_list_emprunt_montant_maximum'
              },
              {
                type: 'TEXT',
                value: '€'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          id: 'date_financement',
          label: "Date initiale d'obtention du financement",
          type: 'DATE'
        },
        {
          id: 'avenant_date_financement',
          label: "Nouvelle date limite d'obtention du financement",
          type: 'DATE'
        },
        {
          after: {
            type: 'N_DAYS_FROM_NOW',
            value: '0'
          },
          id: 'avenant_date_signature',
          label: 'Nouvelle date butoir de signature de la vente définitive',
          type: 'DATE'
        },
        {
          id: 'avenant_prix_vente',
          label: 'Prix de vente modifié',
          type: 'PRICE'
        },
        {
          id: 'avenant_honoraires',
          label: 'Nouveau montant des honoraires',
          type: 'PRICE'
        },
        {
          description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
          id: 'avenant_prix_vente_cfp',
          label: 'Prix de vente modifié',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
          id: 'avenant_honoraires_cfp',
          label: 'Nouveau montant des honoraires',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avenant_purge',
          label: 'Une nouvelle purge SRU doit-elle être effectuée ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avenant_clause_particuliere',
          label: 'Clause particulière supplémentaire',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'avenant_clause_particuliere_liste_avenant_clause_particuliere_liste_titre',
              label: 'Titre de la clause',
              type: 'TEXT'
            },
            {
              id: 'avenant_clause_particuliere_liste_avenant_clause_particuliere_liste_contenu',
              label: 'Contenu de la clause',
              type: 'TEXTAREA'
            }
          ],
          id: 'avenant_clause_particuliere_liste',
          label: 'Clause particulière',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'avenant_clause_particuliere_liste_avenant_clause_particuliere_liste_titre'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avenant_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '121af53f_6167_4666_b6d1_a3c67b9739fd',
      label: 'Avenant',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'reconnaissance_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: 'b3427781_7615_4f54_a2ed_d148769f6cc6',
      label: "Reconnaissance d'honoraires",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          defaultAnswer: {
            resourceId: 'STATIC_FILES_PIERRE_INVESTISSEMENT'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'pierre_investissement',
          label: "Etapes d'un investissement immobilier",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'presence_meubles',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'pierre_mobilier',
          label: 'Liste du mobilier',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pierre_situation_locative',
                value: 'pierre_situation_locative_2',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'pierre_situation_locative',
                value: 'pierre_situation_locative_3',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'pierre_bail_avenant',
          label: 'Bail + avenant',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pierre_situation_locative',
                value: 'pierre_situation_locative_2',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'pierre_situation_locative',
                value: 'pierre_situation_locative_3',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'pierre_quittance',
          label: 'Dernière quittance de loyer',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pierre_situation_locative',
                value: 'pierre_situation_locative_4',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'pierre_mandat_gestion',
          label: 'Mandat de gestion',
          type: 'UPLOAD'
        }
      ],
      id: '60ec9bc8_55ef_4f82_a0c3_a457593dda99',
      label: 'Informations générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'clause_particuliere',
          label: 'Clause particulière supplémentaire',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'clause_particuliere_liste_clause_particuliere_liste_titre',
              label: 'Titre de la clause',
              type: 'TEXT'
            },
            {
              id: 'clause_particuliere_liste_clause_particuliere_liste_contenu',
              label: 'Contenu de la clause',
              type: 'TEXTAREA'
            }
          ],
          conditions: [
            [
              {
                id: 'clause_particuliere',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'clause_particuliere_liste',
          label: 'Clause particulière',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'clause_particuliere_liste_clause_particuliere_liste_titre'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        }
      ],
      id: '813e707a_a85e_4563_a446_5e52a2c53c4d',
      label: 'Clause particulière',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'ad_plan_sonore',
              label: 'Souhaitez vous insérer une clause relative à un plan de gêne sonore ?',
              type: 'SELECT-BINARY'
            },
            {
              id: 'ad_date_reiteration',
              label: 'Date butoir de réitération authentique',
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              description: 'A défaut les frais seront calculés de manière automatique',
              id: 'ad_frais_acte_libre',
              label: "Les frais d'acte sont-ils indiqués manuellement ?",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'ad_frais_acte_libre',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'ad_frais_acte_montant',
              label: "Montant des frais d'acte",
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'ad_financement_travaux',
              label: 'Une provision de travaux doit-elle être prévue au plan de financement ?',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'ad_financement_travaux',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'ad_provision_travaux',
              label: 'Montant de la provision pour travaux',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'ad_clause_particuliere',
              label: 'Le contrat comporte-t-il une clause particulière supplémentaire ?',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'ad_clause_particuliere_liste_ad_clause_particuliere_liste_titre',
                  label: 'Titre de la clause',
                  type: 'TEXT'
                },
                {
                  id: 'ad_clause_particuliere_liste_ad_clause_particuliere_liste_contenu',
                  label: 'Contenu de la clause',
                  type: 'TEXTAREA'
                }
              ],
              conditions: [
                [
                  {
                    id: 'ad_clause_particuliere',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'ad_clause_particuliere_liste',
              label: 'Clause particulière',
              repetition: {
                label: [
                  {
                    type: 'VARIABLE',
                    value: 'ad_clause_particuliere_liste_ad_clause_particuliere_liste_titre'
                  }
                ],
                max: 1000,
                min: 1
              },
              type: 'REPEAT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'ad_autorisation_urbanisme',
              label:
                "Le compromis est-il soumis à la condition suspensive d'obtention d'une autorisation d'urbanisme ?",
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'urbanisme_permis_construire',
                      label: 'Permis de construire'
                    },
                    {
                      id: 'urbanisme_permis_amenager',
                      label: "Permis d'aménager"
                    },
                    {
                      id: 'urbanisme_declaration',
                      label: 'Déclaration préalable'
                    },
                    {
                      id: 'urbanisme_certificat_information',
                      label: "Un certificat d'urbanisme d'information"
                    },
                    {
                      id: 'urbanisme_certificat_preoperationnel',
                      label: "Un certificat d'urbanisme préopérationnel"
                    }
                  ],
                  id: 'ad_condition_suspensive_urbanisme_liste_ad_condition_suspensive_urbanisme_type',
                  label: "Type d'autorisation :",
                  type: 'SELECT'
                },
                {
                  id: 'ad_condition_suspensive_urbanisme_liste_ad_condition_suspensive_urbanisme_delai_depot',
                  label: "Délai pour déposer l'autorisation :",
                  suffix: 'mois',
                  type: 'NUMBER'
                },
                {
                  id: 'ad_condition_suspensive_urbanisme_liste_ad_condition_suspensive_urbanisme_delai_depot_date',
                  label: "Date limite pour déposer l'autorisation :",
                  type: 'DATE'
                },
                {
                  id: 'ad_condition_suspensive_urbanisme_liste_ad_condition_suspensive_urbanisme_delai_obtention',
                  label: "Délai d'obtention de l'autorisation :",
                  suffix: 'mois',
                  type: 'NUMBER'
                },
                {
                  id: 'ad_condition_suspensive_urbanisme_liste_ad_condition_suspensive_urbanisme_obtention_date',
                  label: "Date limite pour obtenir l'autorisation :",
                  type: 'DATE'
                },
                {
                  id: 'ad_condition_suspensive_urbanisme_liste_ad_condition_suspensive_urbanisme_descriptif',
                  label: "Description du projet de l'acquéreur :",
                  type: 'TEXTAREA'
                }
              ],
              id: 'ad_condition_suspensive_urbanisme_liste',
              label: 'Ajouter une condition suspensive',
              repetition: {
                label: [
                  {
                    type: 'TEXT',
                    value: '-Condition Suspensive'
                  }
                ],
                max: 1000,
                min: 1
              },
              type: 'REPEAT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'ad_logement_famille',
              label: "L'intervention du conjoint est-elle nécessaire au titre de l'article 215 du code civil ?",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'ad_intervention_donateur',
              label: "L'intervention du Donateur est-elle prévue ?",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'ad_copropriete_derogation',
              label:
                'Les parties souhaitent-elles déroger au principe légal de répartition des charges et travaux de copropriété?',
              type: 'SELECT-BINARY'
            },
            {
              id: 'ad_copropriete_fonds_alur',
              label: 'Montant du fonds ALUR',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'ad_copropriete_reserves',
              label: 'Des avances ou réserves sont-elles dues au niveau de la copropriété ?',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'ad_copropriete_reserves',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'ad_copropriete_reserves_montant',
              label: 'Montant des avances ou réserves',
              type: 'PRICE'
            },
            {
              id: 'ad_copropriete_date_pre_etat',
              label: 'Date du pré-étatdaté',
              type: 'DATE'
            },
            {
              id: 'ad_montant_sequestre',
              label: "Montant du séquestre à déposer par l'Acquéreur",
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'ad_signature_electronique',
              label: 'Le compromis est-il signé de manière électronique ?',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'ad_signature_electronique',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'emprunt_statut',
                    recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'FINANCEMENT_ACQUEREUR', '0'],
                    type: 'EQUALS',
                    value: 'non'
                  }
                ]
              ],
              id: 'ad_mention_manuscrite',
              label: 'Déclaration de non-recours à un prêt',
              templateId: 'renonciationFinancement.pdf',
              type: 'UPLOAD'
            }
          ],
          id: '033f482d_1bbb_43ba_9c00_e6f39b952e61',
          label: 'CONDITION_BLOCK_Agence Directe',
          type: 'CONDITION_BLOCK'
        },
        {
          children: [
            {
              description: 'En ce compris les éventuels meubles et hors honoraires acquéreur',
              id: 'blot_prix_vente',
              label: 'Prix de vente',
              type: 'PRICE'
            },
            {
              id: 'blot_frais_acte',
              label: "Montant des frais d'acte",
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'blot_presence_meubles',
              label: 'Des meubles sont-ils vendus avec le bien ?',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'blot_presence_meubles',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'blot_meubles_valorisation',
              label: 'Les meubles sont-ils valorisés au sein du compromis ?',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'blot_meubles_valorisation',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'blot_presence_meubles',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'blot_presence_meubles_montant',
              label: 'Montant des meubles',
              type: 'PRICE'
            },
            {
              conditions: [
                [
                  {
                    id: 'blot_presence_meubles',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'liste_meuble_obligatoire',
              label: 'Liste du mobilier',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'blot_logement_famille',
              label: 'La vente porte-t-elle sur le logement de la famille ?',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'blot_acquereur_professionnel',
              label: "L'Acquéreur est-il un professionnel de l'immobilier ?",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'blot_tf_annexe',
              label: 'La taxe foncière est-elle annexée au compromis ?',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'blot_cs',
              label: "Le compromis est-il soumis à une condition suspensive d'obtention du permis de construire ?",
              type: 'SELECT-BINARY'
            },
            {
              id: 'blot_cs_permis_description',
              label: 'Description du projet objet du permis',
              type: 'TEXT'
            },
            {
              id: 'blot_cs_permis_ville',
              label: 'Ville de dépôt du permis',
              type: 'TEXT'
            },
            {
              id: 'blot_cs_permis_zone',
              label: "Zone d'urbanisme",
              type: 'TEXT'
            },
            {
              id: 'blot_cs_permis_date_depot',
              label: 'Date limite de dépôt du permis',
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'blot_icpe',
              label:
                "Le compromis est-il soumis à une condition suspensive d'obtention d'une confirmation que le bien n’a jamais supporté une installation classée ?",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'cheque',
                  label: 'Le dépôt de garantie est versé par chèque lors de la signature'
                },
                {
                  id: 'cheque_posterieur',
                  label: 'Le dépôt de garantie est versé par chèque après la signature'
                },
                {
                  id: 'virement',
                  label: 'Le dépôt de garantie est versé par virement après la signature'
                },
                {
                  id: 'absence',
                  label: 'Absence de dépôt de garantie'
                }
              ],
              id: 'blot_depot_garantie_statut',
              label: 'Concernant le dépôt de garantie',
              type: 'SELECT'
            },
            {
              id: 'blot_depot_garantie_montant',
              label: 'Montant du dépot de garantie',
              type: 'PRICE'
            },
            {
              id: 'blot_depot_garantie_cheque_numero',
              label: 'Numéro du chèque',
              type: 'TEXT'
            },
            {
              id: 'blot_depot_garantie_cheque_banque',
              label: 'Banque émettrice du chèque',
              type: 'TEXT'
            },
            {
              id: 'blot_depot_garantie_date',
              label: 'Date de versement du dépôt de garantie',
              type: 'DATE'
            },
            {
              id: 'blot_date_reiteration',
              label: 'Date de réitération de la vente',
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'blot_clause_particuliere',
              label: 'Le contrat comporte-t-il une clause particulière supplémentaire ?',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'blot_clause_particuliere_liste_titre',
                  label: 'Titre de la clause',
                  type: 'TEXT'
                },
                {
                  id: 'blot_clause_particuliere_liste_contenu',
                  label: 'Contenu de la clause',
                  type: 'TEXTAREA'
                }
              ],
              id: 'blot_clause_particuliere_liste',
              label: 'Clause particulière',
              repetition: {
                label: [
                  {
                    type: 'VARIABLE',
                    value: 'blot_clause_particuliere_liste_blot_clause_particuliere_liste_titre'
                  }
                ],
                max: 1000,
                min: 1
              },
              type: 'REPEAT'
            },
            {
              choices: [
                {
                  id: 'papier',
                  label: 'Par recommandé papier'
                },
                {
                  id: 'papier_adresse',
                  label: 'Par recommandé papier à une autre adresse que celle de son état civil'
                },
                {
                  id: 'electronique',
                  label: 'Par recommandé électronique'
                }
              ],
              id: 'blot_compromis_purge',
              label: "La purge du délai de rétractation de l'Acquéreur s'effectue",
              type: 'SELECT'
            },
            {
              id: 'blot_recommande_adresse',
              label: "Adresse d'envoi du recommandé pour la purge",
              type: 'ADDRESS'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'blot_compromis_signature_electronique',
              label: 'Le compromis est-il signé de manière électronique ?',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'blot_compromis_signature_electronique',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'blot_logement_famille',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'blot_attestation_conjoint',
              label: 'Attestation du conjoint non propriétaire',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'blot_compromis_signature_electronique',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'emprunt_statut',
                    recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'FINANCEMENT_ACQUEREUR', '0'],
                    type: 'EQUALS',
                    value: 'non'
                  }
                ]
              ],
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'blot_renonciation_scrivener',
              label: "Mention manuscrite - renonciation à l'emprunt",
              templateId: 'renonciationFinancement.pdf',
              type: 'UPLOAD'
            }
          ],
          id: 'e4ace383_5993_4d76_9806_049573bd79e3',
          label: 'CONDITION_BLOCK_Blot',
          type: 'CONDITION_BLOCK'
        }
      ],
      id: 'deb5e310_abfa_4bf5_b18a_d281376c43e1',
      label: 'Conditions de la vente',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'apporteur_nom_proprietaire',
          label: 'Nom du propriétaire des Biens',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'fixe',
              label: 'Selon un montant fixe forfaitaire'
            },
            {
              id: 'pourcentage',
              label: 'Selon un pourcentage'
            }
          ],
          id: 'apporteur_modalites',
          label: "Rémunération de l'apporteur d'affaires",
          type: 'SELECT'
        },
        {
          id: 'apporteur_montant_fixe',
          label: 'Montant de la rémunération',
          type: 'PRICE'
        },
        {
          description: 'Meubles compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur',
          id: 'apporteur_montant_fixe_cfp',
          label: 'Montant de la rémunération',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'apporteur_pourcentage',
          label: 'Pourcentage de rémunération sur le prix du bien',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          id: 'apporteur_pourcentage_facture',
          label: 'Pourcentage de rémunération sur le montant des travaux facturés',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'apporteur_signature_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: 'da3e7e60_8a97_490f_98c8_a93a2c829ace',
      label: "Apport d'affaire",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'attestation_carrez_signature',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '20d18280_8f8f_4186_ab6c_30e3b7afd6d8',
      label: 'Attestation Carrez',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'meuble',
              label: 'Location en meublée'
            },
            {
              id: 'premier',
              label: 'Premier congé'
            },
            {
              id: 'second',
              label: 'Congé secondaire'
            }
          ],
          description:
            'Attention le locataire doit avoir accepté les recommandés électroniques au préalable pour une notification par AR24',
          id: 'conge_type',
          label: 'Type de congé',
          type: 'SELECT'
        },
        {
          id: 'conge_initial_date',
          label: 'Date du premier congé',
          type: 'DATE'
        }
      ],
      id: 'dd7f0030_f6ca_4f2d_a722_af3f73411f6f',
      label: 'Congé',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'convention_honoraires_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '0f2d1828_0780_46cf_9e69_58733dc303a5',
      label: "Convention d'honoraires",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'realise',
              label: 'Réalisé'
            },
            {
              id: 'a_faire',
              label: 'A faire'
            },
            {
              id: 'aucun',
              label: 'Aucun état des lieux'
            }
          ],
          id: 'anticipee_etat_lieux',
          label: 'Etat des lieux',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'anticipee_etat_lieux',
                value: 'realise',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'anticipee_etat_lieux_document',
          label: 'Etat des lieux',
          type: 'UPLOAD'
        }
      ],
      id: 'dd7f93iz_ncp4_pfd2_pch4_af3f7341rt22',
      label: 'Jouissance anticipée',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'taylor_offshore_leaks',
          label: 'Offshore Leaks Database - Acquéreur',
          type: 'UPLOAD'
        },
        {
          id: 'taylor_offshore_leaks_vendeur',
          label: 'Offshore Leaks Database - Vendeur',
          type: 'UPLOAD'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_vendeur_mandat',
              label: 'Mandat de vente',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_vendeur_mandat',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_vendeur_mandat_document',
              label: 'Mandat de vente',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_vendeur_taxe',
              label: 'Dernière taxe foncière',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_vendeur_taxe',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_vendeur_taxe_document',
              label: 'Dernière taxe foncière',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_vendeur_cni',
              label: 'Pièces Identité Vendeur - KBIS Statuts (si société)',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_vendeur_cni',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_vendeur_cni_document',
              label: 'Pièces Identité Vendeur - KBIS Statuts (si société)',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_vendeur_titre',
              label: 'Titre Propriété',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_vendeur_titre',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_vendeur_titre_document',
              label: 'Titre Propriété',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_vendeur_kyc',
              label: 'KYC Propriétaire',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_vendeur_kyc',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_vendeur_kyc_document',
              label: 'KYC Propriétaire',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_vendeur_open',
              label: 'Site "Open Sanctions" Propriétaires (+Sté)',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_vendeur_open',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_vendeur_open_document',
              label: 'Site "Open Sanctions" Propriétaires (+Sté)',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_vendeur_gel',
              label: 'Site "Gel des Avoirs" Propriétaires (+Sté)',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_vendeur_gel',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_vendeur_gel_document',
              label: 'Site "Gel des Avoirs" Propriétaires (+Sté)',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_vendeur_google',
              label: 'Recherche Google Vendeur(s)',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_vendeur_google',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_vendeur_google_document',
              label: 'Recherche Google Vendeur(s)',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_vendeur_tracfin',
              label: 'Fiche Tracfin Transaction Vendeur',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_vendeur_tracfin',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_vendeur_tracfin_document',
              label: 'Fiche Tracfin Transaction Vendeur',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_vendeur_dossier',
              label: 'Dossier de Vente',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_vendeur_dossier',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_vendeur_dossier_document',
              label: 'Dossier de Vente',
              type: 'UPLOAD'
            }
          ],
          id: 'f6f74ae8_f7a7_4802_8c4e_1c6916a4c285',
          label: 'Documents Vendeur Obtenus',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_acquereur_offre',
              label: "Offre d'achat contresignée",
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_acquereur_offre',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_acquereur_offre_document',
              label: "Offre d'achat contresignée",
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_acquereur_cni',
              label: 'Pièces Identité Acquéreur - KBIS Statuts (si société)',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_acquereur_cni',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_acquereur_cni_document',
              label: 'Pièces Identité Acquéreur - KBIS Statuts (si société)',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_acquereur_open',
              label: 'Site "Open Sanctions" Acquéreur (+Sté)',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_acquereur_open',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_acquereur_open_document',
              label: 'Site "Open Sanctions" Acquéreur (+Sté)',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_acquereur_gel',
              label: 'Site "Gel des Avoirs" Acquéreur (+Sté)',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_acquereur_gel',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_acquereur_gel_document',
              label: 'Site "Gel des Avoirs" Acquéreur (+Sté)',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_acquereur_google',
              label: 'Recherche Google Acquéreur(s)',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_acquereur_google',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_acquereur_google_document',
              label: 'Recherche Google Acquéreur(s)',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_acquereur_tracfin_partie_un',
              label: 'Fiche Tracfin Acquéreur (Partie 1)',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_acquereur_tracfin_partie_un',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_acquereur_tracfin_partie_un_document',
              label: 'Fiche Tracfin Acquéreur (Partie 1)',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_acquereur_promesse',
              label: 'Promesse de vente',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_acquereur_promesse',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_acquereur_promesse_document',
              label: 'Promesse de vente',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_acquereur_transaction',
              label: 'Fiche de Transaction',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_acquereur_transaction',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_acquereur_transaction_document',
              label: 'Fiche de Transaction',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_acquereur_facture',
              label: 'Facture',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_acquereur_facture',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_acquereur_facture_document',
              label: 'Facture',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_acquereur_tracfin_partie_deux',
              label: 'Fiche Tracfin Acquéreur (Partie 2)',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_acquereur_tracfin_partie_deux',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_acquereur_tracfin_partie_deux_document',
              label: 'Fiche Tracfin Acquéreur (Partie 2)',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'obtenu',
                  label: 'Obtenu'
                },
                {
                  id: 'non_obtenu',
                  label: 'Non Obtenu'
                },
                {
                  id: 'non_concerne',
                  label: 'Non concerné'
                }
              ],
              id: 'taylor_sharepoint_acquereur_vente',
              label: 'Acte de Vente',
              type: 'PICK_LIST'
            },
            {
              conditions: [
                [
                  {
                    id: 'taylor_sharepoint_acquereur_vente',
                    value: 'obtenu',
                    type: 'CONTAINS'
                  },
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
                  }
                ]
              ],
              id: 'taylor_sharepoint_acquereur_vente_document',
              label: 'Acte de Vente',
              type: 'UPLOAD'
            }
          ],
          id: '1051b81d_c615_4d08_b33c_500ed702c696',
          label: 'Documents Acquéreur Obtenus',
          type: 'CATEGORY'
        }
      ],
      conditions: [
        [
          {
            type: 'EQUALS_TEMPLATES',
            templates: ['OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION']
          }
        ]
      ],
      id: 'ff35ffa4_cd4f_48e4_8c81_258aebe6948d',
      label: 'Récapitulatif documents',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              id: 'belgique_prix_vente',
              label: 'Prix de vente (honoraires inclus)',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_presence_meubles',
              label: 'Présence de mobilier',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  conditions: [
                    [
                      {
                        id: 'presence_meubles',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'presence_meubles_liste_statut',
                        type: 'EQUALS',
                        value: 'liste_annexe'
                      }
                    ]
                  ],
                  id: 'belgique_liste_meuble_annexe_montant',
                  label: 'Montant du mobilier',
                  type: 'PRICE'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'presence_meubles',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'presence_meubles_liste_statut',
                        type: 'EQUALS',
                        value: 'liste_annexe'
                      }
                    ]
                  ],
                  id: 'belgique_liste_meuble',
                  label: 'Liste du mobilier',
                  type: 'UPLOAD'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'cuisine',
                          label: 'Cuisine'
                        },
                        {
                          id: 'salon',
                          label: 'Salon'
                        },
                        {
                          id: 'salle_manger',
                          label: 'Salle à manger'
                        },
                        {
                          id: 'bureau',
                          label: 'Bureau'
                        },
                        {
                          id: 'salle_eau',
                          label: "Salle d'eau"
                        },
                        {
                          id: 'salle_bains',
                          label: 'Salle de bains'
                        },
                        {
                          id: 'chambre',
                          label: 'Chambre'
                        },
                        {
                          id: 'wc',
                          label: 'WC'
                        },
                        {
                          id: 'entrée',
                          label: 'Entrée'
                        },
                        {
                          id: 'piece_autre',
                          label: 'Autre'
                        }
                      ],
                      id: 'belgique_liste_meuble_compromis_belgique_liste_meuble_piece',
                      label: 'Pièce',
                      type: 'SELECT'
                    },
                    {
                      conditions: [
                        [
                          {
                            conditions: [
                              {
                                id: 'presence_meubles',
                                type: 'EQUALS',
                                value: 'oui'
                              }
                            ],
                            type: 'EQUALS_TEMPLATES',
                            templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
                          },
                          {
                            id: 'liste_meuble_compromis_liste_meuble_piece',
                            value: 'piece_autre',
                            type: 'EQUALS'
                          },
                          {
                            id: 'presence_meubles',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'liste_meuble_compromis_liste_meuble_piece',
                            value: 'piece_autre',
                            type: 'EQUALS'
                          },
                          {
                            id: 'presence_meubles',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'presence_meubles_liste_statut',
                            type: 'EQUALS',
                            value: 'liste_compromis'
                          },
                          {
                            type: 'DIFFERENT_TEMPLATES',
                            templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
                          }
                        ]
                      ],
                      id: 'belgique_liste_meuble_compromis_belgique_liste_meuble_piece_autre',
                      label: 'Autre Pièce',
                      type: 'TEXT'
                    },
                    {
                      choices: [
                        {
                          id: 'liste_meuble_chaises',
                          label: 'Chaises'
                        },
                        {
                          id: 'liste_meuble_canape',
                          label: 'Canapé'
                        },
                        {
                          id: 'liste_meuble_congelateur',
                          label: 'Congélateur'
                        },
                        {
                          id: 'liste_meuble_fauteuil',
                          label: 'Fauteuil'
                        },
                        {
                          id: 'liste_meuble_four',
                          label: 'Four'
                        },
                        {
                          id: 'liste_meuble_hotte',
                          label: 'Hotte'
                        },
                        {
                          id: 'liste_meuble_lave_vaisselle',
                          label: 'Lave-Vaisselle'
                        },
                        {
                          id: 'liste_meuble_luminaire',
                          label: 'Luminaires'
                        },
                        {
                          id: 'liste_meuble_machine',
                          label: 'Machine à laver'
                        },
                        {
                          id: 'liste_meuble_meuble_bas',
                          label: 'Meuble Bas'
                        },
                        {
                          id: 'liste_meuble_haut',
                          label: 'Meuble Haut'
                        },
                        {
                          id: 'liste_meuble_micro_onde',
                          label: 'Micro-onde'
                        },
                        {
                          id: 'liste_meuble_miroir',
                          label: 'Miroir'
                        },
                        {
                          id: 'liste_meuble_plaques_induction',
                          label: 'Plaques Induction'
                        },
                        {
                          id: 'liste_meuble_plaques_vitro',
                          label: 'Plaques Vitrocéramique'
                        },
                        {
                          id: 'liste_meuble_plauqes_gaz',
                          label: 'Plaques Gaz'
                        },
                        {
                          id: 'liste_meuble_refrigerateur',
                          label: 'Réfrigérateur'
                        },
                        {
                          id: 'liste_meuble_seche',
                          label: 'Sèche-Linge'
                        },
                        {
                          id: 'liste_meuble_table',
                          label: 'Table'
                        },
                        {
                          id: 'liste_meuble_autre',
                          label: 'Autre'
                        }
                      ],
                      id: 'belgique_liste_meuble_compromis_belgique_liste_meuble_designation',
                      label: 'Meuble',
                      type: 'SELECT'
                    },
                    {
                      conditions: [
                        [
                          {
                            conditions: [
                              {
                                id: 'presence_meubles',
                                type: 'EQUALS',
                                value: 'oui'
                              }
                            ],
                            type: 'EQUALS_TEMPLATES',
                            templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
                          },
                          {
                            id: 'liste_meuble_compromis_liste_meuble_designation',
                            value: 'liste_meuble_autre',
                            type: 'EQUALS'
                          },
                          {
                            id: 'presence_meubles',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'liste_meuble_compromis_liste_meuble_designation',
                            value: 'liste_meuble_autre',
                            type: 'EQUALS'
                          },
                          {
                            id: 'presence_meubles',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'presence_meubles_liste_statut',
                            type: 'EQUALS',
                            value: 'liste_compromis'
                          },
                          {
                            type: 'DIFFERENT_TEMPLATES',
                            templates: ['OPERATION__GIBOIRE__IMMOBILIER__VENTE']
                          }
                        ]
                      ],
                      id: 'belgique_liste_meuble_compromis_belgique_liste_meuble_autre_designation',
                      label: 'Autre meuble',
                      type: 'TEXT'
                    },
                    {
                      id: 'belgique_liste_meuble_compromis_belgique_liste_meuble_valeur',
                      label: 'Valeur',
                      type: 'PRICE'
                    }
                  ],
                  id: 'belgique_liste_meuble_compromis',
                  label: 'Ajouter un meuble',
                  repetition: {
                    label: [
                      {
                        type: 'VARIABLE',
                        value: 'belgique_liste_meuble_compromis_belgique_liste_meuble_designation'
                      },
                      {
                        type: 'TEXT',
                        value: ' - '
                      },
                      {
                        type: 'VARIABLE',
                        value: 'belgique_liste_meuble_compromis_belgique_liste_meuble_valeur'
                      },
                      {
                        type: 'TEXT',
                        value: '€'
                      }
                    ],
                    max: 1000,
                    min: 0
                  },
                  type: 'REPEAT'
                }
              ],
              id: 'a9ed9692_5d0f_4887_b568_48e18c863dfc',
              label: 'Mobilier',
              type: 'CATEGORY'
            },
            {
              id: 'belgique_acompte_montant',
              label: "Montant de l'acompte",
              type: 'PRICE'
            },
            {
              id: 'belgique_acompte_compte_numero',
              label: "Numéro de compte de l'acquéreur",
              type: 'TEXT'
            },
            {
              id: 'belgique_acompte_compte_nom',
              label: "Nom du compte de l'acquéreur",
              type: 'TEXT'
            }
          ],
          id: 'fc407932_1153_4562_8348_487df44b30ad',
          label: 'Informations financières',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_frais_acquereur_supplementaire',
              label: "Frais supplémentaires à la charge de l'acheteur",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_frais_acquereur_supplementaire_liste',
              label: 'Liste des frais supplémentaire charge acheteur',
              type: 'TEXTAREA'
            },
            {
              choices: [
                {
                  id: 'mise_en_vente',
                  label: 'Frais de mise en vente (agence immobilière, négociation, publicité)'
                },
                {
                  id: 'diag',
                  label: 'Frais nécessires pour transférer le bien (contrôle, mainlevée...)'
                },
                {
                  id: 'autre',
                  label: 'Autres frais supplémentaires'
                }
              ],
              id: 'belgique_frais_vendeur_supplementaire',
              label: 'Frais supplémentaires à la charge du vendeur',
              multiple: true,
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'agence',
                  label: "Par l'Agence"
                },
                {
                  id: 'notaire',
                  label: 'Par le Notaire'
                }
              ],
              id: 'belgique_frais_vendeur_supplementaire_autorisation',
              label: 'Personne en charge des démarches aux formalités de délivrance',
              type: 'SELECT'
            },
            {
              id: 'belgique_frais_vendeur_supplementaire_liste',
              label: 'Liste des frais supplémentaire charge vendeur',
              type: 'TEXTAREA'
            }
          ],
          id: '807fc011_c708_4038_854d_b31dd1524dab',
          label: 'Frais liés à la vente',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_signature_electronique',
              label: 'Signature électronique',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_date_reiteration',
              label: "Date de signature de l'acte authentique",
              type: 'DATE'
            }
          ],
          id: '4de12d0b_65fe_4a74_9b77_9e7eb832cd44',
          label: 'Information sur la signature',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_vente_logement_familial',
              label: 'Vente du logement familial',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'signature',
                  label: "Intervention par signature à l'acte"
                },
                {
                  id: 'annexe',
                  label: "Intervention par accord annexé à l'acte"
                }
              ],
              description: "En cas de signature de l'acte, pensez à ajouter le signataire lors du paramétrage",
              id: 'belgique_vente_logement_familial_intervention',
              label: 'Intervention du conjoint',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'homme',
                  label: 'Monsieur'
                },
                {
                  id: 'femme',
                  label: 'Madame'
                }
              ],
              id: 'belgique_vente_logement_familial_civilite',
              label: 'Civilite conjoint',
              type: 'SELECT'
            },
            {
              id: 'belgique_vente_logement_familial_prenoms',
              label: 'Prénom du conjoint',
              type: 'TEXT'
            },
            {
              id: 'belgique_vente_logement_familial_nom',
              label: 'Nom du conjoint',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'epoux',
                  label: 'Epoux'
                },
                {
                  id: 'epouse',
                  label: 'Epouse'
                },
                {
                  id: 'cohabitant',
                  label: 'Cohabitant légal'
                }
              ],
              id: 'belgique_vente_logement_familial_statut',
              label: 'Statut du conjoint',
              type: 'SELECT'
            },
            {
              id: 'belgique_vente_logement_familial_date',
              label: "Date de signature de l'accord",
              type: 'DATE'
            },
            {
              conditions: [
                [
                  {
                    id: 'belgique_vente_logement_familial_intervention',
                    value: 'annexe',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'belgique_vente_logement_familial_document',
              label: 'Accord à la vente - Conjoint Vendeur',
              type: 'UPLOAD'
            }
          ],
          id: 'c5ebe36a_1e9b_43b6_84df_159b9ca8acad',
          label: 'Logement familial',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_acquereur_indivision',
              label: 'Indivision entre les acquéreurs',
              type: 'SELECT-BINARY'
            }
          ],
          id: '89d8a3a6_30d1_4b95_8a40_88106c6b6b9f',
          label: 'Indivision',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_vendeur_mandat_hypothecaire',
              label: "Signature d'un mandat hypothécaire",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_vendeur_accord_mainlevee',
              label: "Condition suspensive d'obtention des accords nécessaires à la liberté hypothécaire du bien",
              type: 'SELECT-BINARY'
            }
          ],
          id: 'ca54ce11_a2fd_4b59_84b6_d2dbbbf15862',
          label: 'Situation Hypothécaire',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_bien_occupe_vendu_libre',
              label: 'Bien vendu libre',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_bien_occupe_vendu_libre_statut',
              label: 'Statut du bien',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_bien_occupe_ecrit',
              label: 'Bail écrit',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_bien_occupe_typologie',
              label: 'Type de bail',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_bien_occupe_preuve_paiement',
              label: 'Preuve de paiement des loyers de la dernièe année',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_bien_occupe_permis_location',
              label: 'Permis de location',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_bien_occupe_droit_preemption',
              label: 'Droit de préemption du locataire',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_bien_occupe_droit_litige',
              label: 'Litige avec le locataire',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'libre',
                  label: 'Bien vendu libre'
                },
                {
                  id: 'vendeur_libre',
                  label: 'Bien occupé par le vendeur mais libre à la vente'
                },
                {
                  id: 'locataire',
                  label: 'Bien vendu au Locataire'
                },
                {
                  id: 'differee',
                  label: 'Jouissance à une date ultérieure'
                },
                {
                  id: 'occupe',
                  label: 'Bien occupé par un tiers'
                }
              ],
              id: 'belgique_occupation_bien',
              label: 'Occupation du bien',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'gratuit',
                  label: 'Gratuitement'
                },
                {
                  id: 'payant',
                  label: 'Moyennant Indemnité'
                }
              ],
              id: 'belgique_occupation_vendeur',
              label: 'Occupation du bien par le Vendeur',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'journalier',
                  label: 'En proportion des jours occupés'
                },
                {
                  id: 'mensuel',
                  label: 'Tout mois entamé est dû'
                }
              ],
              id: 'belgique_occupation_vendeur_indemnite_paiement',
              label: 'Paiement des indemnités',
              type: 'SELECT'
            },
            {
              id: 'belgique_occupation_date_liberation',
              label: 'Date de libération du bien par le Vendeur',
              type: 'DATE'
            },
            {
              id: 'belgique_occupation_valeur_locative',
              label: 'Valeur locative mensuelle estimée du bien',
              type: 'PRICE'
            },
            {
              id: 'belgique_occupation_indemnite_vendeur',
              label: 'Indemnité mensuelle due par le Vendeur',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_occupation_etat_lieux',
              label: 'Etat des lieux réalisés',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_occupation_garantie_locative',
              label: 'Garantie Locative',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_occupation_remise_cles_liste',
              label: 'Elements remis en plus des clés',
              type: 'TEXT'
            }
          ],
          id: '2870dc2f_7f00_4c4e_8601_9f3b31898a76',
          label: 'Occupation - Jouissance',
          type: 'CATEGORY'
        }
      ],
      id: '08621036_00ac_4e17_b474_4bdd10c04ac5',
      label: 'Informations générales sur la vente',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_compromis_sans_urbanisme',
              label: 'Compromis établi sans attendre les renseignements urbanistiques',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_compromis_sans_urbanisme_zone',
              label: 'Zone du plan de secteur',
              type: 'TEXT'
            },
            {
              id: 'belgique_compromis_sans_urbanisme_certificats',
              label: 'Permis et certificats portant sur le bien',
              type: 'TEXT'
            },
            {
              id: 'belgique_compromis_sans_urbanisme_div97',
              label: 'Zones visées à l’article D.IV.97 CoDT',
              type: 'TEXT'
            },
            {
              id: 'belgique_compromis_lettre_urbanisme_date',
              label: 'Date de la lettre adressée par la commune',
              type: 'DATE'
            },
            {
              id: 'belgique_compromis_lettre_urbanisme_liste',
              label: 'Contenu de la lettre',
              type: 'TEXTAREA'
            }
          ],
          id: 'f6648a69_aa17_4b85_a4e9_c39b49b27d8c',
          label: 'Situation urbanistique',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_preemption',
              label: "Existance d'un droit de préemption / droit de préférence",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_preemption_liste',
              label: 'Liste des droits de préemption / préférence',
              type: 'TEXTAREA'
            }
          ],
          id: '5afb5c3b_d2cc_4bf4_a533_50526700dcef',
          label: 'Droit de préemption - préférence',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'belgique_assainissement_bde_date',
              label: "Date de l'extrait de la Banque de Données de l'Etat des sols",
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_assainissement_obligation_analyse',
              label: "Vendeur a été informé qu'il doit effectuer une analyse ou un assainissement du sol",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_contractualisation_bien',
              label: 'Contractualisation de la destination du bien',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_assainissement_destination_bien_liste',
              label: "Destination du bien par l'Acquéreur",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'total',
                  label: 'Total'
                },
                {
                  id: 'partiel',
                  label: 'En partie'
                }
              ],
              id: 'belgique_assainissement_destination_bien_totale_partielle',
              label: 'Destination totale ou partielle',
              type: 'SELECT'
            },
            {
              id: 'belgique_contractualisation_clause_entiere',
              label: 'Clause de contractualisation de la destination du bien',
              type: 'TEXTAREA'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_contractualisation_analyse_assainissement',
              label: 'Soumission volontaire aux obligations d’analyses voire d’assainissement du sol',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_soumission_analyses_clause_entiere',
              label: "Clause soumission volontaire aux obligations d'analyses",
              type: 'TEXTAREA'
            }
          ],
          id: '7511ea52_6320_4c55_aca3_2e1958b6e097',
          label: 'Gestion et assainissement du sol',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_vendeur_primes',
              label: "Le Vendeur a bénéficié d'une ou plusieurs primes",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgiques_vendeur_primes_date',
              label: 'Date de versement des primes',
              type: 'DATE'
            }
          ],
          id: 'c508ade9_058f_40b0_a9ac_3ac50b044b5a',
          label: 'Primes',
          type: 'CATEGORY'
        }
      ],
      id: '96875bf7_1a35_472b_9a5d_d137253931e4',
      label: 'Conditions administratives de la vente',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'tva',
              label: 'à la TVA'
            },
            {
              id: 'droit_enregistrement',
              label: "aux droits d'enregistrement"
            },
            {
              id: 'double',
              label: "à la TVA et aux droits d'enregistrement"
            }
          ],
          id: 'belgique_fiscalite_soumission',
          label: 'Vente soumise',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_fiscalite_cs_reduction',
          label: "Condition suspensive d'application du tarif réduit",
          type: 'SELECT-BINARY'
        }
      ],
      id: 'aaa56fa2_8bf5_4460_9ccd_cb52f99183fd',
      label: 'Fiscalité',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_abattement_personne_physique',
          label: 'Acquisition en tant que personne physique et en pleine propriete',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_abattement_habitation_construction',
          label:
            "Bien est une habitation ou un terrain sur lequel les acquéreurs ont l'intention de construire une habitation",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_abattement_proprietaire',
          label: "Un des acquéreurs est déjà seul propriétaire d'une habitation (en Belgique ou à l'étranger)",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_abattement_proprietaire_totalite',
          label:
            "Les acquéreurs sont déjà ensemble propriétaires de la totalité d’une habitation (en Belgique ou à l'étranger)",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_abattement_engagement',
          label:
            "Engagement de résidence principale dans les 3 ans de l'acte ou en cas de terrain, de construction d'une habitation dans les 5 ans",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_abattement_engagement_domiciliation',
          label: 'Engagement de domiciliation pendant 3 ans ininterrompus',
          type: 'SELECT-BINARY'
        }
      ],
      id: '5fe89bc2_53fd_417e_b6a8_aff3fce9696e',
      label: 'Annexe 1 - Abattement',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_reduction_personne_physique',
          label: 'Acquisition en tant que personne physique',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_reduction_habitation',
          label: 'Bien actuellement une habitation',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_reduction_revenu_cadastral',
          label: 'Revenu cadastral du bien inférieur ou égal à 745€',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_reduction_domiciliation',
          label: 'Engagement de domiciliation dans les 3 ans ET y rester minimum 3 ans ininterrompus',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_reduction_proprietaire',
          label:
            'Un des acquéreurs ou son conjoint/cohabitant légal est déjà propriétaire en tout ou en partie d’un immeuble (en Belgique ou à l’étranger)',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_reduction_emprunt',
          label: 'Un des acquéreurs emprunte auprès d’un guichet de crédit social ou du Fonds du Logement',
          type: 'SELECT-BINARY'
        }
      ],
      id: '7970c4ce_b0ab_4111_b58b_ff3dd946535b',
      label: 'Annexe 2 - Réduction',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__GENERAL',
  label: 'Conditions générales',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__GENERAL',
  specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_ANCIEN', 'GENERAL'],
  type: 'RECORD'
};
