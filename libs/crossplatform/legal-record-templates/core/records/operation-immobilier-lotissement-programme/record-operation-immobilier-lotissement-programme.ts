// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordOperationImmobilierLotissementProgramme: LegalRecordTemplate = {
  config: {},
  form: [
    {
      children: [
        {
          id: 'programme_commune',
          label: 'Commune du lotissement',
          type: 'TEXT'
        },
        {
          id: 'programme_lotissement_nom',
          label: 'Nom du lotissement',
          type: 'TEXT'
        },
        {
          id: 'programme_lotissement_arrete_numero',
          label: "Numéro de l'arrêté municipal autorisation le lotissement",
          type: 'TEXT'
        },
        {
          id: 'programme_lotissement_arrete_date',
          label: "Date de l'arrêté municipal",
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'permis',
              label: "Dans le permis d'aménager"
            },
            {
              id: 'certificat',
              label: 'Dans un certificat de surface de plancher'
            }
          ],
          id: 'surface_lot_document',
          label: 'La surface des lots est indiquée',
          type: 'SELECT'
        },
        {
          id: 'geometre_expert_nom',
          label: 'Nom du Géomètre Expert',
          type: 'TEXT'
        },
        {
          id: 'geometre_expert_adresse',
          label: 'Adresse du Géomètre Expert',
          type: 'ADDRESS'
        },
        {
          description:
            'La livraison d’un immeuble s’opère lorsque le vendeur remet à l’acheteur les titres de propriété (art. 1605 du Code civil). De plus, la Cour de Cassation juge que l’obligation de délivrance du lotisseur ne se limite pas à la simple remise des titres de propriété et du plan de lotissement, mais implique l’obligation de fixer nettement sur le terrain les limites des lots vendus (bornage). Cela dit, le bénéficiaire a besoin d’être informé de la date à partir de laquelle il pourra disposer du terrain pour engager ses travaux de construction. C’est la raison pour laquelle la présente promesse le précise',
          id: 'date_mise_disposition',
          label: 'Date indicative de mise à disposition du terrain pour construire',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'autorisation_vente_anticipee',
          label: 'Autorisation de vente anticipée obtenue',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            ' La création d’une association syndicale libre des colotis n’est plus du tout obligatoire quelque soit le nombre de lots et si le lotisseur s’engage à ce que les équipements soient attribués en propriété divise ou indivise aux acquéreurs de lots. Il en va de même, lorsqu’il conclut une convention de cession des équipements à la collectivité publique',
          id: 'creation_asl',
          label: "Création d'une ASL",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cession_commun_communes',
          label: 'Cession des équipements communs à la commune',
          type: 'SELECT-BINARY'
        }
      ],
      id: 'f7e72886_21ae_47aa_ab5f_8b78ad08001d',
      label: 'Informations générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cs_terrain_acquisition',
          label: "Condition suspensive d'acquisition des terrains par le Promettant",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'achevement',
              label: 'Après achèvement des travaux'
            },
            {
              id: 'travaux',
              label: "Après obtention de l'autorisation de vente avant travaux de finition"
            },
            {
              id: 'total',
              label: "Après obtention de l'autorisation de vente avant tout travaux"
            }
          ],
          description: '',
          id: 'cs_cessibilite_administrative',
          label: 'Cessibilité des lots',
          type: 'SELECT'
        }
      ],
      id: '0e2c4f4e_25d8_4faf_8413_6a27af926336',
      label: 'Conditions suspensives',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'financier_etude_restitution',
          label:
            "L'indemnité d'immobilisation est restituée en cas d'étude de sol faisant apparaître un coût trop important",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            'La clause concernant le changement de TVA est facultative. Elle permet simplement d’éviter un aléa qui peut jouer en faveur, comme en défaveur de chaque partie. Pour rappel, en cas d’acquisition d’un terrain soumis à TVA sur la marge, le promettant, en cas de réalisation de la vente, sera soumis au paiement des droits d’enregistrement de droit commun. Ces droits d’enregistrement doivent être en principe calculés sur le prix HT. Vous pouvez toutefois prévoir un prix net vendeur TVA  incluse, dans l’hypothèse où vous ne souhaitez pas dévoiler votre marge. Dans cette hypothèse, les droits d’enregistrement du bénéficiaire seront calculés sur le prix net vendeur TVA incluse',
          id: 'financier_changement_tva',
          label: 'Un changement du taux de TVA engendre une réévaluation du prix',
          type: 'SELECT-BINARY'
        }
      ],
      id: '65ee463f_578c_41c4_b6d8_b0d1c68cd7b5',
      label: 'Elements financiers',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'termites',
          label: 'La commune est concernée par une zone termite',
          type: 'SELECT-BINARY'
        }
      ],
      id: '03da729a_484b_46bb_a78c_2c0242c7e85e',
      label: 'Environnement et état du terrain',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'engagement_constitution_asl_piece_numero',
          label: "N° de pièce du document à ajouter au permis d'aménager",
          type: 'TEXT'
        }
      ],
      id: '07b88a35_199b_4bd2_a43d_33ea39bf3571',
      label: "Engagement de constitution d'une ASL",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'permis_amenager_modificatif',
          label: "Le permis d'aménager a fait l'objet d'un modificatif",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'reglement_lotissement',
          label: "Le lotissement fait l'objet d'un règlement",
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'reglement_lotissement',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'reglement_lotissement_doc',
          label: 'Règlement de lotissement',
          type: 'UPLOAD'
        },
        {
          id: 'permis_amenager',
          label: "Permis d'aménager",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'permis_amenager_modificatif',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'permis_amenager_modificatif_doc',
          label: "Permis d'aménager modificatif",
          type: 'UPLOAD'
        },
        {
          id: 'plan_bornage',
          label: 'Plan de bornage',
          type: 'UPLOAD'
        },
        {
          id: 'etude_geotechnique',
          label: 'Etude géotechnique préalable',
          type: 'UPLOAD'
        },
        {
          id: 'note_descriptive_geotechnique',
          label: 'Note descriptive de renseignements géotechniques',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'autorisation_vente_anticipee',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'vente_anticipation_doc',
          label: 'Autorisation de vente par anticipation',
          type: 'UPLOAD'
        },
        {
          id: 'cahiers_charge',
          label: 'Cahiers des charges',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'creation_asl',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'statuts_asl',
          label: "Statuts de l'ASL",
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'cession_commun_communes',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'convention_cession_commune',
          label: 'Convention de cession des équipements à la commune',
          type: 'UPLOAD'
        },
        {
          id: 'programme_travaux',
          label: 'Programme des travaux',
          type: 'UPLOAD'
        },
        {
          id: 'plan_branchements',
          label: 'Plan des branchements',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'termites',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'diagnostic_termites',
          label: 'Diagnostic termites',
          type: 'UPLOAD'
        }
      ],
      id: '68ee28c0_011b_430e_9b0e_e441ae3281ad',
      label: 'Documents',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__OPERATION__IMMOBILIER__LOTISSEMENT__PROGRAMME',
  label: 'Fiche Programme',
  mynotaryTemplate: false,
  originTemplate: null,
  specificTypes: ['OPERATION', 'IMMOBILIER', 'LOTISSEMENT', 'PROGRAMME'],
  type: 'RECORD'
};
