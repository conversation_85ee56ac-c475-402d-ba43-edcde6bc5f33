// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationAquitanisVenteAcquereur: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Acquéreur',
      labelPlural: 'Acquéreurs',
      labelWithArticle: 'un acquéreur',
      branches: {
        ACQUEREUR: {
          label: 'Acquéreur',
          labelWithArticle: 'un acquéreur'
        }
      }
    },
    branches: {
      ACQUEREUR: {
        type: 'ACQUEREUR',
        reverseType: 'ACQUEREUR',
        to: {
          type: 'RECORD',
          specificTypes: [
            ['PERSONNE', 'PHYSIQUE'],
            ['PERSONNE', 'MORALE']
          ]
        },
        constraints: {
          min: 1
        },
        links: [
          {
            specificTypes: ['SITUATION_MARITALE', 'MARIAGE']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'DIVORC<PERSON>']
          },
          {
            specificTypes: ['REPRESENTATION', 'PERSONNE_MORALE']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'PACS']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'VEUVAGE']
          },
          {
            specificTypes: ['CAPACITE', 'SAUVEGARDE_JUSTICE']
          },
          {
            specificTypes: ['CAPACITE', 'CURATELLE']
          },
          {
            specificTypes: ['CAPACITE', 'EMANCIPATION']
          },
          {
            specificTypes: ['CAPACITE', 'HABILITATION_FAMILIALE']
          },
          {
            specificTypes: ['CAPACITE', 'TUTELLE']
          },
          {
            specificTypes: ['CAPACITE', 'MINORITE']
          },
          {
            specificTypes: ['CAPACITE', 'MANDAT_PROTECTION_FUTURE']
          },
          {
            specificTypes: ['PROCURATION', 'PROCURATION']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'AUCUN']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'NON_DEFINI']
          },
          {
            specificTypes: ['CAPACITE', 'AUCUN']
          },
          {
            specificTypes: ['CAPACITE', 'NON_DEFINI']
          },
          {
            specificTypes: ['PROCURATION', 'AUCUN']
          }
        ],
        linkMatches: [
          ['SITUATION_MARITALE', '*'],
          ['REPRESENTATION', '*'],
          ['CAPACITE', '*'],
          ['PROCURATION', '*']
        ]
      }
    }
  },
  id: 'LINK__OPERATION__AQUITANIS__VENTE__ACQUEREUR',
  label: 'Acquéreur',
  mynotaryTemplate: false,
  originTemplate: null,
  specificTypes: ['OPERATION', 'AQUITANIS', 'VENTE', 'ACQUEREUR'],
  type: 'LINK'
};
