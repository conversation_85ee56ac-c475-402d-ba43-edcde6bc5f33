// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationCdcHabitatImmobilierVenteIntervenants: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Intervenant',
      labelPlural: 'Intervenants',
      labelWithArticle: 'un Intervenant',
      branches: {
        INTERVENANT: {
          label: 'Intervenant',
          labelWithArticle: 'un Intervenant'
        }
      }
    },
    branches: {
      INTERVENANT: {
        type: 'INTERVENANT',
        reverseType: 'INTERVENANT',
        to: {
          type: 'RECORD',
          specificTypes: [
            ['PERSONNE', 'PHYSIQUE'],
            ['PERSONNE', 'MORALE']
          ]
        },
        constraints: {
          min: 1
        },
        links: [
          {
            specificTypes: ['SITUATION_MARITALE', 'MARIAGE']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'DIVORC<PERSON>']
          },
          {
            specificTypes: ['REPRESENTATION', 'PERSONNE_MORALE']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'PACS']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'VEUVAGE']
          },
          {
            specificTypes: ['CAPACITE', 'SAUVEGARDE_JUSTICE']
          },
          {
            specificTypes: ['CAPACITE', 'CURATELLE']
          },
          {
            specificTypes: ['CAPACITE', 'EMANCIPATION']
          },
          {
            specificTypes: ['CAPACITE', 'HABILITATION_FAMILIALE']
          },
          {
            specificTypes: ['CAPACITE', 'TUTELLE']
          },
          {
            specificTypes: ['CAPACITE', 'MINORITE']
          },
          {
            specificTypes: ['CAPACITE', 'MANDAT_PROTECTION_FUTURE']
          },
          {
            specificTypes: ['PROCURATION', 'PROCURATION']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'AUCUN']
          },
          {
            specificTypes: ['SITUATION_MARITALE', 'NON_DEFINI']
          },
          {
            specificTypes: ['CAPACITE', 'AUCUN']
          },
          {
            specificTypes: ['CAPACITE', 'NON_DEFINI']
          },
          {
            specificTypes: ['PROCURATION', 'AUCUN']
          }
        ],
        linkMatches: [
          ['SITUATION_MARITALE', '*'],
          ['REPRESENTATION', '*'],
          ['CAPACITE', '*'],
          ['PROCURATION', '*']
        ]
      }
    }
  },
  id: 'LINK__OPERATION__CDC_HABITAT__IMMOBILIER__VENTE__INTERVENANTS',
  label: 'INTERVENANTS',
  mynotaryTemplate: false,
  originTemplate: null,
  specificTypes: ['OPERATION', 'CDC_HABITAT', 'IMMOBILIER', 'VENTE', 'INTERVENANTS'],
  type: 'LINK'
};
