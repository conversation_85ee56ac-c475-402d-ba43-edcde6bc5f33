/**
 * BFF Portalys
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


export interface RequisitionBffDto { 
    /**
     * Indicates if AOP upload is allowed
     */
    allowAopUpload?: boolean;
    /**
     * requisition cost
     */
    cout?: number;
    /**
     * On create/update only. Indicates if it should go to the next step in the workflow
     */
    gotoNextStep?: boolean;
    /**
     * Unique identifier of the document
     */
    id?: string;
    immeubleIds?: Array<string>;
    label?: string;
    memoVirement?: string;
    /**
     * Label of the next step in the workflow
     */
    nextStepLabel?: string;
    /**
     * Operation (dossier) ID the demandeCopieDocument is attached to
     */
    operationId?: string;
    personneIds?: Array<string>;
    /**
     * Record ID the requisition is attached to
     */
    recordId?: string;
    /**
     * Code SAGES du SPF concerné
     */
    sagesEnvoi?: string;
    /**
     * can access to ANF stock for this document ?
     */
    showAnfStock?: boolean;
    /**
     * can proceed to the next step ?
     */
    showNextStep?: boolean;
    /**
     * Indicates if the user can ask Planete to re-release the responses
     */
    showReSendResponses?: boolean;
    termeLabel?: string;
    /**
     * Type of the requisition (\'INITIALE\', \'COMPLEMENTAIRE\')
     */
    type?: string;
    /**
     * Indicates if the document is updatable
     */
    updatable?: boolean;
    /**
     * If not updatable, can it be forced ?
     */
    updatableStatusForcible?: boolean;
    /**
     * Update was forced
     */
    updatableStatusForced?: boolean;
    /**
     * User ID associated with the document
     */
    userId?: string;
}

