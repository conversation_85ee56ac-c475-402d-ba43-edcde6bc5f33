import { LegalLinkTemplateId, LegalRecordTemplateId } from '@mynotary/crossplatform/legal-templates/api';
import { isLegalRecordAssociationAllowed, isLegalOperationAssociationAllowed } from './is-legal-association-allowed';
import { getLegalLinkTemplate } from '@mynotary/crossplatform/legal-link-templates/api';
import { getLegalRecordTemplate } from '@mynotary/crossplatform/legal-record-templates/api';

type TestCase = {
  fromLegalTemplateId: LegalRecordTemplateId;
  legalLinkTemplateId: LegalLinkTemplateId;
  result: boolean;
  toLegalTemplateId: LegalRecordTemplateId;
};

const cases: TestCase[] = [
  {
    fromLegalTemplateId: 'RECORD__BIEN__LOT_HABITATION',
    legalLinkTemplateId: 'LINK__COMPOSITION__COPROPRIETE',
    result: true,
    toLegalTemplateId: 'RECORD__STRUCTURE__COPROPRIETE'
  },
  {
    fromLegalTemplateId: 'RECORD__BIEN__INDIVIDUEL_HABITATION',
    legalLinkTemplateId: 'LINK__COMPOSITION__COPROPRIETE',
    result: false,
    toLegalTemplateId: 'RECORD__STRUCTURE__COPROPRIETE'
  },
  {
    fromLegalTemplateId: 'RECORD__PERSONNE__MORALE__AGENT_IMMOBILIER',
    legalLinkTemplateId: 'LINK__REPRESENTATION__PERSONNE_MORALE',
    result: true,
    toLegalTemplateId: 'RECORD__PERSONNE__PHYSIQUE'
  }
];

describe(isLegalRecordAssociationAllowed.name, () => {
  it.each(cases)(
    'should check if clause $fromLegalTemplateId and $toLegalTemplateId are allowed for $legalLinkTemplateId',
    ({ fromLegalTemplateId, legalLinkTemplateId, result, toLegalTemplateId }) => {
      expect(
        isLegalRecordAssociationAllowed({
          fromLegalRecordTemplate: getLegalRecordTemplate(fromLegalTemplateId),
          legalLinkTemplate: getLegalLinkTemplate(legalLinkTemplateId),
          toLegalRecordTemplate: getLegalRecordTemplate(toLegalTemplateId)
        })
      ).toEqual(result);
    }
  );
});

type OperationTestCase = {
  legalLinkTemplateId: LegalLinkTemplateId;
  result: boolean;
  toLegalTemplateId: LegalRecordTemplateId;
};

const operationCases: OperationTestCase[] = [
  {
    legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES',
    result: false,
    toLegalTemplateId: 'RECORD__PERSONNE__PHYSIQUE__AGENT_IMMOBILIER'
  },
  {
    legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES',
    result: true,
    toLegalTemplateId: 'RECORD__PERSONNE__PHYSIQUE__INTERMEDIAIRE_IMMOBILIER'
  }
];

describe(isLegalOperationAssociationAllowed.name, () => {
  it.each(operationCases)(
    'should check if clause $toLegalTemplateId is allowed for $legalLinkTemplateId',
    ({ legalLinkTemplateId, result, toLegalTemplateId }) => {
      expect(
        isLegalOperationAssociationAllowed({
          legalLinkTemplate: getLegalLinkTemplate(legalLinkTemplateId),
          toLegalRecordTemplate: getLegalRecordTemplate(toLegalTemplateId)
        })
      ).toEqual(result);
    }
  );
});
