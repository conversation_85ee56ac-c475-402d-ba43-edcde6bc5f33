import './record-repeat-popin-content.scss';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import React, { ReactElement, useMemo } from 'react';
import { useSelector } from 'react-redux';

import {
  createValidatedRepeatAnswer,
  generateRepeatFormNode,
  RepeatPopinDefault,
  selectContractRecordQuestionsForm,
  selectFormNavigation,
  selectLegalsContext,
  selectRecord,
  selectRecordAnswer,
  selectRecordRawForm,
  usePanelWidthRedactionClass
} from '@mynotary/frontend/legals/api';

import { selectCurrentContractId, selectCurrentOperationId } from '@mynotary/frontend/current-operation/api';
import { FormNode } from '@mynotary/crossplatform/shared/forms-util';
import { SheetPopin } from '@mynotary/frontend/shared/ui';
import { classNames } from '@mynotary/frontend/shared/util';

import { ReferenceCadastralCreate } from '../../reference-cadastrales/referenceCadastralCreate';
import { RecordFormNodes } from '../recordFormNodes';

interface RepeatPopinContentProps {
  debounce: boolean;
  handleClosePopin: () => void;
  isOpen: boolean;
  onChange: (value: AnswerDict) => void;
  onValidation: (value: AnswerDict) => void;
  question: FormNode;
  recordId: number;
}

export const RecordRepeatPopinContent = ({
  debounce,
  handleClosePopin,
  isOpen,
  onChange,
  onValidation,
  question,
  recordId
}: RepeatPopinContentProps): ReactElement | null => {
  const record = useSelector(selectRecord(recordId));
  const form = useSelector(selectRecordRawForm(record?.template.id));
  const operationId = useSelector(selectCurrentOperationId);
  const contractId = useSelector(selectCurrentContractId);
  const contractRecordQuestionIds = useSelector(selectContractRecordQuestionsForm(recordId, operationId, contractId));
  const formContext = useSelector(selectLegalsContext(operationId, contractId));
  const answer = useSelector(selectRecordAnswer(recordId));
  const formNavigation = useSelector(selectFormNavigation);
  const isFormNavigationStarted = formNavigation != null;
  const panelWidth = usePanelWidthRedactionClass();
  const repeatFormNode = useMemo(
    () => generateRepeatFormNode(question.id, answer, form, contractRecordQuestionIds, formContext, recordId),
    [answer, contractRecordQuestionIds, form, formContext, question.id, recordId]
  );

  const handleValidation = () => {
    if (repeatFormNode?.key) {
      const a = createValidatedRepeatAnswer({
        questionId: question.id,
        repeatKey: repeatFormNode.key,
        value: answer[question.id]?.value
      });
      onValidation(a);
    }
  };

  const getRepeatCreationPopinTemplate = (): ReactElement => {
    switch (question.repetition?.type) {
      case 'REF_CADASTRALES':
        return (
          <ReferenceCadastralCreate
            answer={answer}
            debounce={debounce}
            onChange={onChange}
            onClosePopin={handleClosePopin}
            onValidation={handleValidation}
            question={question}
            recordId={recordId}
            repeatFormNode={repeatFormNode}
          />
        );
      default:
        return (
          <RepeatPopinDefault label={question.label} onClosePopin={handleClosePopin} onValidation={handleValidation}>
            {repeatFormNode?.children && (
              <RecordFormNodes
                debounce={debounce}
                editable={true}
                forms={repeatFormNode.children}
                onChange={onChange}
                recordId={recordId}
              />
            )}
          </RepeatPopinDefault>
        );
    }
  };

  return (
    <SheetPopin
      cancelAnimation={isFormNavigationStarted}
      className={classNames('repeat-popin-content', panelWidth, {
        'form-navigation': isFormNavigationStarted
      })}
      direction={'left'}
      isOpened={isOpen}
      onClose={handleClosePopin}
    >
      {getRepeatCreationPopinTemplate()}
    </SheetPopin>
  );
};
