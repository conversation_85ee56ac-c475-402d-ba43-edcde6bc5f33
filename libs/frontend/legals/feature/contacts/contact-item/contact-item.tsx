import './contact-item.scss';
import { Contact } from '@mynotary/frontend/legals/core';
import { ContactAvatar } from '@mynotary/frontend/shared/ui';
import { MnProps, classNames } from '@mynotary/frontend/shared/util';

interface ContactItemProps extends MnProps {
  className?: string;
  contact: Contact;
  onClick: (contact: Contact) => void;
}

export const ContactItem = ({ className, contact, onClick }: ContactItemProps) => {
  return (
    <div className={classNames('contact-item', className)} data-testid={contact.email} onClick={() => onClick(contact)}>
      <div>
        <ContactAvatar firstname={contact.prenoms} lastname={contact.nom} />
      </div>
      <div className={'ci-info'}>
        <div className={'ci-name'}>
          {contact.nom} {contact.prenoms}
        </div>
        <div className={'ci-email'}>{contact.email}</div>
      </div>
    </div>
  );
};
