import { AnswerDict } from '@mynotary/crossplatform/records/api';
import {
  Dictionary,
  formatPrice,
  formatPriceToNumberAndLetter,
  JSONValue,
  MnAddress,
  pays
} from '@mynotary/crossplatform/shared/util';
import {
  ADDRESS_TRACFIN_LABEL,
  ADDRESS_TRACFIN_LABEL_PP,
  COUNTRY_BLACK_LIST,
  COUNTRY_GREY_LIST,
  COUNTRY_LISTE_UE,
  COUNTRY_FR,
  COUNTRY_TRACFIN,
  COUNTRY_UE,
  formeSociale,
  INSEE_PLAFOND_LOYER,
  INSEE_ZONE_TENDUE,
  INSEE_ZONE_TRES_TENDUE,
  INSEE_ZONE_TRES_TENDUE_SIMPLE,
  LISTE_DEPARTEMENT_HAUSSE,
  LISTE_DEPARTEMENT_HAUSSE_MAI,
  NATIONALITY_TRACFIN_LABEL,
  NATIONALITY_TRACFIN_LABEL_PP,
  regimeMatrimonial,
  ZIP_ASSINISSEMENT_JO,
  ZIP_PLAFOND_LOYER,
  ZIP_ZONE_TENDUE,
  ZIP_ZONE_TRES_TENDUE,
  ZIP_ZONE_TRES_TENDUE_SIMPLE,
  ZIP_ZONE_MIXTE,
  checkBoxUnchecked,
  checkBoxChecked
} from '@mynotary/frontend/shared/static-data-util';
import { allFormatters, formatDate, NumericDictionary } from '@mynotary/frontend/shared/util';
import { containsSpecificTypes, ContractMappingData, ExtensionRecord, RecordsByLinks } from '../index';
import {
  concat,
  drop,
  every,
  filter,
  find,
  flatten,
  forEach,
  get,
  includes,
  isEmpty,
  isEqual,
  isObject,
  join,
  keyBy,
  map,
  mapValues,
  orderBy,
  reduce,
  size,
  some,
  sumBy,
  uniqBy
} from 'lodash';
import { fns } from '@mynotary/crossplatform/shared/dates-util';
import { ContractLegacy } from '.';
import { Record } from '../records';
import { Operation } from '../operations';
import { MnLink } from '../legal-links';
import { Answer } from '@mynotary/crossplatform/shared/forms-util';

import { getPublicFile } from '@mynotary/frontend/files/api';
import {
  isProgramConfig,
  LegalOperationTemplate,
  LegalRecordTemplate,
  LegalTemplate
} from '@mynotary/crossplatform/legal-templates/api';
import { getFormattedAddress } from '@mynotary/crossplatform/shared/util';

/* export const formatPrice = (price: number): string => {
  const res = !isNil(price) ? priceFormat.format(price) : '';

  // Replace narrow no breaking space by classic no breaking space
  return res.replace(/\u202f/g, '\u00a0');
};
const priceFormat = new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }); */

type AnswerArrayItem = {
  questionId: string;
  value: number;
};
const getLinkById = (activeLinks: NumericDictionary<NumericDictionary<MnLink>>, linkId: number): MnLink | null => {
  let link: MnLink | null = null;
  forEach(activeLinks, (activeLinks) => {
    if (activeLinks[linkId]) {
      link = activeLinks[linkId];
    }
  });
  return link;
};
const getRepeatValues = (answer: AnswerDict, questionId: string): AnswerDict[] => {
  const res: AnswerDict[] = [];

  forEach(answer?.[questionId]?.value, (order, key) => {
    if (order == null) {
      return;
    }
    const repeatValue: AnswerDict = {
      $key: { value: key },
      $order: { value: order }
    };
    const prefix = `${questionId}_${key}_`;
    forEach(answer, (answerValue, answerId) => {
      if (answerId.startsWith(prefix)) {
        repeatValue[answerId.substr(prefix.length)] = answerValue;
      }
    });
    res.push(repeatValue);
  });
  return orderBy(res, (value) => value?.['$order'].value);
};
const filterRecords = (
  records: Record[],
  specificTypes: string[][],
  templates: Dictionary<LegalTemplate>
): Record[] => {
  return filter(records, (record) => {
    const recordTemplate = templates[record.template.id] as LegalRecordTemplate;
    return some(
      specificTypes,
      (specificTypes) => recordTemplate && containsSpecificTypes(specificTypes, recordTemplate.specificTypes)
    );
  });
};
export const createMappingSource = (
  contractMappingData: ContractMappingData,
  brandingLogo: string,
  records: RecordsByLinks,
  operation: Operation,
  activeLinks: NumericDictionary<NumericDictionary<MnLink>>,
  templates: Dictionary<LegalTemplate>,
  contractsById: NumericDictionary<ContractLegacy>,
  extensions?: NumericDictionary<ExtensionRecord>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
): any => {
  const {
    answers,
    branchesRecordsByRecordIdAndBranchType,
    linksByLinkIdAndBranchType,
    linksRecordsByLinkIdAndBranchType
  } = contractMappingData;
  const operationId = operation?.id;
  return {
    _: {
      debug: (value: unknown): unknown => {
        console.log('[DEBUG] Value => ', value);
        return value;
      },
      ...allFormatters,
      add: (collection: number[] | Dictionary<number>, add: number) => {
        if (isObject(collection)) {
          return mapValues(collection, (v: number) => v + add);
        } else {
          return map(collection, (v: number) => v + add);
        }
      },

      addDays: (dateInput: number, days: number) => {
        const result = new Date(Number(dateInput));
        result.setDate(result.getDate() + days);
        return formatDate(result);
      },

      addHausseDmto: (price: number, frais: number, form: Record): number => {
        const HAUSSE = price * 0.005;
        const PRIMO_ACCEDANT_COMPROMIS = answers[form.id]?.['compromis_acquereur_primo_accedant']?.value === 'oui';
        if (PRIMO_ACCEDANT_COMPROMIS) return frais;
        else return frais + HAUSSE;
      },

      addMonths: (dateInput: number, months: number) => {
        const result = new Date(Number(dateInput));
        result.setMonth(result.getMonth() + months);
        return formatDate(result);
      },

      addToCurrentYear: (years: number) => {
        const current = new Date();
        current.setFullYear(current.getFullYear() + years);
        return current.getFullYear();
      },

      addYears: (dateInput: number, years: number) => {
        const result = new Date(Number(dateInput));
        result.setFullYear(result.getFullYear() + years);
        return formatDate(result);
      },

      agenceDirecteFees: (price: number): number => {
        return Math.round(Math.max(5850, price * 0.039));
      },

      agenceDirecteFees2: (price = 0, records: Record[]): number => {
        const HORS_HABITATION =
          !isEmpty(records) &&
          every(records, (record) => {
            return (
              answers[record.id]['nature_bien']?.value === 'nature_garage' ||
              answers[record.id]['nature_bien']?.value === 'nature_bien_cave' ||
              answers[record.id]['nature_bien']?.value === 'nature_bien_cellier' ||
              answers[record.id]['nature_bien']?.value === 'nature_bien_parking'
            );
          });
        if (HORS_HABITATION) {
          return 2900;
        }
        if (price < 100000) {
          return 5900;
        }
        if (price < 175000) {
          return 6825;
        }
        if (price < 370000) {
          return Math.round(price * 0.039);
        }
        if (price < 500000) {
          return 14500;
        }
        if (price < 1000000) {
          return Math.round(price * 0.029);
        }
        return Math.round(price * 0.039);
      },

      checkAlsaceMoselle: (records: Record[]) => {
        return some(records, (record) => {
          return (
            answers[record.id]['adresse']?.value?.zip.startsWith('67') ||
            answers[record.id]['adresse']?.value?.zip.startsWith('68') ||
            answers[record.id]['adresse']?.value?.zip.startsWith('57')
          );
        });
      },

      checkAssainissementJo: (records: Record[]) => {
        return some(records, (record) => {
          return (
            answers[record.id]['adresse']?.value?.zip.startsWith('75') ||
            answers[record.id]['adresse']?.value?.zip.startsWith('9117') ||
            answers[record.id]['adresse']?.value?.zip.startsWith('949') ||
            answers[record.id]['adresse']?.value?.zip.startsWith('940') ||
            ZIP_ASSINISSEMENT_JO.includes(answers[record.id]['adresse']?.value?.zip)
          );
        });
      },
      checkDepartement56: (records: Record[]) => {
        return some(records, (record) => {
          return answers[record.id]['adresse']?.value?.zip.startsWith('56');
        });
      },
      checkDepartementHausse: (records: Record[]) => {
        const includedZipCodes = LISTE_DEPARTEMENT_HAUSSE;
        return some(records, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip?.slice(0, 2);
          return includedZipCodes.includes(zip);
        });
      },
      checkDepartementHausseMai: (records: Record[]) => {
        const includedZipCodes = LISTE_DEPARTEMENT_HAUSSE_MAI;
        return some(records, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip?.slice(0, 2);
          return includedZipCodes.includes(zip);
        });
      },
      checkEuropeCountry: (pays: string) => {
        console.log('pays', pays);
        const pays_ue = COUNTRY_UE;
        console.log('pays_ue', pays_ue);
        return pays_ue.includes(pays);
      },
      checkFirstDay: (dateInput: number) => {
        const result = new Date(dateInput);
        return result.getDate() === 1;
      },

      checkIncompleteZip: (records: Record[]) => {
        return some(records, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip;
          return zip === undefined || zip.length !== 5;
        });
      },
      checkLotAndCoproAdress: (copro: AnswerDict, lot: AnswerDict) => {
        const adresse_copro = copro?.['adresse']?.value?.formattedAddress;
        const adresse_lot = lot?.['adresse']?.value?.formattedAddress;
        return adresse_copro === adresse_lot;
      },

      checkMonth: (date_fin_bail: number, duree_conge: number) => {
        const date = new Date(date_fin_bail);
        return formatDate(date.setMonth(date.getMonth() - duree_conge));
      },

      checkMonthDifferential: (date_debut: number, date_fin: number) => {
        const debut = new Date(date_debut);
        const fin = new Date(date_fin);
        let annees = fin.getFullYear() - debut.getFullYear();
        let mois = fin.getMonth() - debut.getMonth();
        let jours = fin.getDate() - debut.getDate();
        if (jours < 0) {
          mois--;
          jours += new Date(debut.getFullYear(), debut.getMonth() + 1, 0).getDate();
        }
        if (mois < 0) {
          annees--;
          mois += 12;
        }
        const moisEcoulés = annees * 12 + mois;
        let result = '';
        if (moisEcoulés > 0) {
          result += `${moisEcoulés} mois`;
          if (jours > 0) {
            result += ` ${jours} jours`;
          }
        } else {
          result = `${jours} jours`;
        }
        return result;
      },

      checkMonthInterval: (date_bail: number, date_conge: number, duree_conge: number) => {
        const date_fin_bail = new Date(date_bail);
        const date_delivrance_conge = new Date(date_conge);
        const date_duree_conge = date_fin_bail.setMonth(date_fin_bail.getMonth() - duree_conge);
        const date_validation = new Date(date_duree_conge);
        if (date_validation > date_delivrance_conge) return true;
        return false;
      },

      checkNumberInterval: (X: number, A: number, B: number) => {
        if (X >= A && X <= B) return true;
        return false;
      },

      checkSomeAddressIncludedInRecords: (records: Record[], questionId: string, value: string) => {
        return some(records, (record) => {
          return answers[record.id][questionId]?.value.formattedAddress.includes(value);
        });
      },

      // Vérifie si au moins un record par type contient une adresse, du genre code postal
      checkSomeAdressIncludedInRecordsByTypes: (
        linkType: string,
        branchType: string,
        questionId: string,
        value: string
      ) => {
        const targetRecords = records[linkType]?.[branchType];
        return some(targetRecords, (record) => {
          return answers[record.id][questionId]?.value.formattedAddress.includes(value);
        });
      },

      checkSomeAgeIsAfterValue: (linkType: string, branchType: string, questionId: string, maxAge: number) => {
        const targetRecords = records[linkType]?.[branchType];
        return some(targetRecords, (record) => {
          const today = new Date();
          const birthDate = new Date(answers[record.id][questionId]?.value);
          const m = today.getMonth() - birthDate.getMonth();
          let age = today.getFullYear() - birthDate.getFullYear();
          if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
            age--;
          }
          return age >= maxAge;
        });
      },

      // Vérifie si au moins une date dans un record par type est postérieure à la value
      checkSomeDateIsAfterValueInRecordsByTypes: (
        linkType: string,
        branchType: string,
        questionId: string,
        value: string
      ) => {
        const targetRecords = records[linkType]?.[branchType];
        return some(targetRecords, (record) => {
          return fns.isAfter(answers[record.id][questionId]?.value, new Date(value));
        });
      },

      // Vérifie si au moins un link d'un record par type est présent
      checkSomeLinkInRecordsByTypes: (linkType: string, branchType: string, recordLinkType: string) => {
        const targetRecords = records[linkType]?.[branchType];

        return some(targetRecords, (record) => {
          return !!linksRecordsByLinkIdAndBranchType[record.id]?.[recordLinkType]?.[0];
        });
      },

      checkSomeValueExistsInRecords: (records: Record[], questionId: string, repeatQuestionId?: string) => {
        return some(records, (record) => {
          if (repeatQuestionId) {
            const repeatItems = getRepeatValues(answers[record.id], questionId);
            return some(repeatItems, (item) => !!item[repeatQuestionId]?.value);
          } else {
            return !!answers[record.id][questionId]?.value;
          }
        });
      },

      checkSomeValueInEveryRecords: (records: Record[], questionId: string, value: unknown) => {
        return every(records, (record) => {
          return answers[record.id][questionId]?.value === value;
        });
      },

      // Vérifie si TOUS les records par types contiennent une valeur donnée
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      checkSomeValueInEveryRecordsByTypes: (
        linkType: string,
        branchType: string,
        questionId: string,
        value: unknown
      ) => {
        const targetRecords = records[linkType]?.[branchType];

        return every(targetRecords, (record) => {
          return answers[record.id][questionId]?.value === value;
        });
      },

      // Vérifie si au moins un link d'un record par type contient une value
      checkSomeValueInLinkRecordsByTypes: (
        linkType: string,
        branchType: string,
        recordLinkType: string,
        questionId: string,
        value: Answer
      ) => {
        const targetRecords = records[linkType]?.[branchType];

        return some(targetRecords, (record) => {
          const recordLink = linksRecordsByLinkIdAndBranchType[record.id]?.[recordLinkType]?.[0];
          return answers[recordLink.id][questionId]?.value === value;
        });
      },

      checkSomeValueInRecords: (records: Record[], questionId: string, value: Answer, repeatQuestionId?: string) => {
        return some(records, (record) => {
          if (repeatQuestionId) {
            const repeatItems = getRepeatValues(answers[record.id], questionId);
            return some(repeatItems, (item) => item[repeatQuestionId]?.value === value);
          } else {
            return answers[record.id][questionId]?.value === value;
          }
        });
      },

      // Vérifie si au moins un record par type contient une valeur donnée
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      checkSomeValueInRecordsByTypes: (linkType: string, branchType: string, questionId: string, values: string[]) => {
        const targetRecords = records[linkType]?.[branchType];

        return some(targetRecords, (record) => {
          return includes(values, answers[record.id][questionId]?.value);
        });
      },

      // Vérifie si au moins un extension record par type contient une value
      checkSomeValueInRecordsExtensionsByTypes: (
        linkType: string,
        branchType: string,
        questionId: string,
        value: Answer
      ) => {
        const targetRecords = records[linkType]?.[branchType];

        return some(targetRecords, (record) => {
          const extensionId = extensions?.[record.id]?.extensionId;
          if (extensionId) {
            return answers[extensionId][questionId]?.value === value;
          } else {
            return false;
          }
        });
      },
      checkSomeValueStartsWithInRecordsByTypes: (
        linkType: string,
        branchType: string,
        questionId: string,
        value: string
      ) => {
        const targetRecords = records[linkType]?.[branchType];
        return some(targetRecords, (record) => {
          return answers[record.id][questionId]?.value.startsWith(value);
        });
      },

      checkSubOperation: (): boolean => {
        return !!operation.parentOperationId;
      },

      checkTracfinAddress: (personne: AnswerDict) => getTracfinAddressLabel(personne),
      checkTracfinAddressPP: (personne: AnswerDict) => {
        const country = getAddressCountry(personne);
        if (country == null) {
          return 'Risque Modéré (2) (Pays tiers non listé par une institution internationale)';
        }
        return (
          ADDRESS_TRACFIN_LABEL_PP[COUNTRY_TRACFIN[country]] ??
          'Risque Modéré (2) (Pays tiers non listé par une institution internationale)'
        );
      },
      checkTracfinBlackList: (personne: AnswerDict, questionID: string) => {
        const pays: string = personne?.[questionID]?.value;
        const pays_liste_noire = COUNTRY_BLACK_LIST;
        return includes(pays_liste_noire, pays);
      },
      checkTracfinBlackListInRecords: (
        linkType: string,
        branchType: string,
        domicile: string,
        nationalite: string,
        pays: string
      ) => {
        const targetRecords = records[linkType]?.[branchType];
        const pays_liste_noire = COUNTRY_BLACK_LIST;
        return some(targetRecords, (record) => {
          const address = answers[record.id][domicile]?.value?.address;
          const segments = address?.split(',');
          const country = segments[segments.length - 1].trim();
          const manualCountry = answers[record.id][domicile]?.value?.country;
          return (
            includes(pays_liste_noire, answers[record.id][nationalite]?.value) ||
            includes(pays_liste_noire, answers[record.id][pays]?.value) ||
            includes(pays_liste_noire, country) ||
            includes(pays_liste_noire, manualCountry)
          );
        });
      },
      checkTracfinEurope: (personne: AnswerDict, questionID: string) => {
        const pays: string = personne?.[questionID]?.value;
        const pays_europe = COUNTRY_UE;
        return includes(pays_europe, pays);
      },

      checkTracfinEuropeInRecords: (
        linkType: string,
        branchType: string,
        domicile: string,
        nationalite: string,
        pays: string
      ) => {
        const targetRecords = records[linkType]?.[branchType];
        const pays_europe = COUNTRY_UE;
        return some(targetRecords, (record) => {
          const address = answers[record.id][domicile]?.value?.address;
          const segments = address?.split(',');
          const country = segments[segments.length - 1].trim();
          const manualCountry = answers[record.id][domicile]?.value?.country;
          return (
            includes(pays_europe, answers[record.id][nationalite]?.value) ||
            includes(pays_europe, answers[record.id][pays]?.value) ||
            includes(pays_europe, country) ||
            includes(pays_europe, manualCountry)
          );
        });
      },
      checkTracfinEuropeList: (personne: AnswerDict, questionID: string) => {
        const pays: string = personne?.[questionID]?.value;
        const pays_liste_ue = COUNTRY_LISTE_UE;
        return includes(pays_liste_ue, pays);
      },
      checkTracfinGreyList: (personne: AnswerDict, questionID: string) => {
        const pays: string = personne?.[questionID]?.value;
        const pays_liste_grise = COUNTRY_GREY_LIST;
        return includes(pays_liste_grise, pays);
      },
      checkTracfinGreyListInRecords: (
        linkType: string,
        branchType: string,
        domicile: string,
        nationalite: string,
        pays: string
      ) => {
        const targetRecords = records[linkType]?.[branchType];
        const pays_liste_grise = COUNTRY_GREY_LIST;
        return some(targetRecords, (record) => {
          const address = answers[record.id][domicile]?.value?.address;
          const segments = address?.split(',');
          const country = segments[segments.length - 1].trim();
          const manualCountry = answers[record.id][domicile]?.value?.country;
          return (
            includes(pays_liste_grise, answers[record.id][nationalite]?.value) ||
            includes(pays_liste_grise, answers[record.id][pays]?.value) ||
            includes(pays_liste_grise, country) ||
            includes(pays_liste_grise, manualCountry)
          );
        });
      },
      checkTracfinListeEuropeInRecords: (
        linkType: string,
        branchType: string,
        domicile: string,
        nationalite: string,
        pays: string
      ) => {
        const targetRecords = records[linkType]?.[branchType];
        const pays_liste_ue = COUNTRY_LISTE_UE;
        return some(targetRecords, (record) => {
          const address = answers[record.id][domicile]?.value?.address;
          const segments = address?.split(',');
          const country = segments[segments.length - 1].trim();
          const manualCountry = answers[record.id][domicile]?.value?.country;
          return (
            includes(pays_liste_ue, answers[record.id][nationalite]?.value) ||
            includes(pays_liste_ue, answers[record.id][pays]?.value) ||
            includes(pays_liste_ue, country) ||
            includes(pays_liste_ue, manualCountry)
          );
        });
      },
      checkTracfinNationality: (personne: AnswerDict) => {
        const nationality: string = personne?.['informations_personnelles_nationalite']?.value;
        return (
          NATIONALITY_TRACFIN_LABEL[COUNTRY_TRACFIN[nationality]] ?? "Nationalité d'un Pays TIERS : VIGILANCE RENFORCÉE"
        );
      },
      checkTracfinNationalityPP: (personne: AnswerDict) => {
        const nationality: string = personne?.['informations_personnelles_nationalite']?.value;
        return (
          NATIONALITY_TRACFIN_LABEL_PP[COUNTRY_TRACFIN[nationality]] ??
          'Risque Modéré (2) (Pays tiers non listé par une institution internationale)'
        );
      },
      checkValueIsNotUndefinedInRecords: (records: Record[], questionId: string) => {
        return some(records, (record) => {
          const value = answers[record.id][questionId]?.value;
          return value !== undefined && value !== null && value !== 0;
        });
      },
      checkZoneCorse: (records: Record[]) => {
        return some(records, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip?.slice(0, 2);
          return zip === '20';
        });
      },
      checkZoneDomTom: (records: Record[]) => {
        return some(records, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip?.slice(0, 3);
          return zip === '971' || zip === '972' || zip === '974';
        });
      },
      checkZoneMixte: (records: Record[]) => {
        return some(records, (record) => {
          const cityCode = answers[record.id]['adresse']?.value.cityCode;
          if (cityCode === undefined || cityCode === null) {
            return ZIP_ZONE_MIXTE.includes(answers[record.id]['adresse']?.value?.zip);
          }
          return false;
        });
      },
      checkZonePlafonnementLoyer: (records: Record[]) => {
        return some(records, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip;
          const insee = answers[record.id]['adresse'].value.cityCode;
          if (INSEE_PLAFOND_LOYER.includes(insee)) {
            return true;
          }
          return ZIP_PLAFOND_LOYER.includes(zip);
        });
      },

      checkZoneTendue: (records: Record[]) => {
        return some(records, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip;
          const insee = answers[record.id]['adresse'].value.cityCode;
          if (INSEE_ZONE_TENDUE.includes(insee)) {
            return true;
          }
          return ZIP_ZONE_TENDUE.includes(zip);
        });
      },
      checkZoneTresTendue: (records: Record[]) => {
        return some(records, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip;
          const insee = answers[record.id]['adresse'].value.cityCode;
          if (INSEE_ZONE_TRES_TENDUE.includes(insee)) {
            return true;
          }
          return ZIP_ZONE_TRES_TENDUE.includes(zip);
        });
      },
      checkZoneTresTendueSimple: (records: Record[]) => {
        return some(records, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip;
          const insee = answers[record.id]['adresse'].value.cityCode;
          if (INSEE_ZONE_TRES_TENDUE_SIMPLE.includes(insee)) {
            return true;
          }
          return ZIP_ZONE_TRES_TENDUE_SIMPLE.includes(zip);
        });
      },

      computeTracfin: (nombre_tracfin_fort: number): JSONValue => {
        const tracfinScale: Dictionary<number> = {
          tracfin_critique: 4,
          tracfin_faible: 1,
          tracfin_fort: 3,
          tracfin_modere: 2
        };

        const structure = [
          {
            id: 'client',
            questions: [
              'tracfin_pays',
              'tracfin_nationalite',
              'tracfin_profession',
              'tracfin_age',
              'tracfin_revenus',
              'tracfin_coherence',
              'tracfin_politique',
              'tracfin_physique'
            ]
          },
          {
            id: 'bien',
            questions: ['tracfin_localisation', 'tracfin_prix']
          },
          {
            id: 'operation',
            questions: ['tracfin_financement', 'tracfin_origine', 'tracfin_montage', 'tracfin_tiers']
          }
        ];

        const answer = answers[records['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES']['TRACFIN'][0].id];
        const tracfinAnswerArray: AnswerArrayItem[] = filter(
          map(answer, (formAnswer, questionId) => {
            if (questionId.startsWith('tracfin_')) {
              return { questionId, value: tracfinScale[formAnswer.value] };
            }
            return undefined;
          }),
          (item) => !!item
        ) as AnswerArrayItem[];
        const tracfinAnswerMap = mapValues(
          keyBy(tracfinAnswerArray, (item) => item.questionId),
          (item) => {
            return item.value;
          }
        );
        const tracfinAnswer: Dictionary<Dictionary<number>> = reduce(
          structure,
          (res, structureItem) => {
            res[structureItem.id] = {};

            forEach(structureItem.questions, (questionId) => {
              res[structureItem.id][questionId] = tracfinAnswerMap[questionId] ?? 0;
            });

            res[structureItem.id]['average'] = Math.round(
              sumBy(structureItem.questions, (questionId) => tracfinAnswerMap[questionId] ?? 0) /
                structureItem.questions.length
            );

            return res;
          },
          {} as Dictionary<Dictionary<number>>
        );
        return {
          ...tracfinAnswer,
          tracfinAverage: Math.round(sumBy(tracfinAnswerArray, (item) => item.value) / tracfinAnswerArray.length),
          tracfinCount: {
            critique: reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 4 ? 1 : 0), 0),
            faible: reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 1 ? 1 : 0), 0),
            fort: reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 3 ? 1 : 0), 0),
            moyen: reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 2 ? 1 : 0), 0)
          },
          tracfinCritique: some(tracfinAnswerArray, (answer) => answer.value === 4),
          tracfinFort:
            reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 3 ? 1 : 0), 0) >= nombre_tracfin_fort
        };
      },

      computeTracfinPPClient: (
        linkType: string,
        branchType: string,
        nationalite: string,
        domicile: string
      ): JSONValue => {
        const tracfinScale: Dictionary<number> = {
          tracfin_eleve: 3,
          tracfin_eleve_1: 3,
          tracfin_eleve_2: 3,
          tracfin_eleve_3: 3,
          tracfin_eleve_4: 3,
          tracfin_faible: 1,
          tracfin_faible_1: 1,
          tracfin_faible_2: 1,
          tracfin_modere: 2
        };
        const targetRecords = records[linkType]?.[branchType];
        const pays_liste_ue = COUNTRY_UE;
        const pays_liste_fr = COUNTRY_FR;
        const pays_liste_grise = COUNTRY_GREY_LIST;
        const pays_liste_noire = COUNTRY_BLACK_LIST;
        const pays_liste_noire_ue = COUNTRY_LISTE_UE;
        const nationality_undefined = every(targetRecords, (record) => {
          return answers[record.id][nationalite]?.value === undefined;
        });
        const nationality_fr_ue = some(targetRecords, (record) => {
          return (
            includes(pays_liste_ue, answers[record.id][nationalite]?.value) ||
            includes(pays_liste_fr, answers[record.id][nationalite]?.value)
          );
        });
        const nationality_grise_noire = some(targetRecords, (record) => {
          return (
            includes(pays_liste_grise, answers[record.id][nationalite]?.value) ||
            includes(pays_liste_noire, answers[record.id][nationalite]?.value) ||
            includes(pays_liste_noire_ue, answers[record.id][nationalite]?.value)
          );
        });
        let nationality_result;
        if (nationality_grise_noire) {
          nationality_result = 'tracfin_eleve';
        } else if (!nationality_fr_ue && !nationality_undefined) {
          nationality_result = 'tracfin_modere';
        } else if (nationality_undefined) {
          nationality_result = '1';
        } else {
          nationality_result = 'tracfin_faible';
        }

        const adresse_black_grey = some(targetRecords, (record) => {
          const address = answers[record.id][domicile]?.value?.address;
          const segments = address?.split(',');
          const country = segments[segments.length - 1].trim();
          const manualCountry = answers[record.id][domicile]?.value?.country;
          return (
            includes(pays_liste_grise, country) ||
            includes(pays_liste_grise, manualCountry) ||
            includes(pays_liste_noire, country) ||
            includes(pays_liste_noire, manualCountry) ||
            includes(pays_liste_noire_ue, country) ||
            includes(pays_liste_noire_ue, manualCountry)
          );
        });
        const adresse_fr_ue = some(targetRecords, (record) => {
          const address = answers[record.id][domicile]?.value?.address;
          const segments = address?.split(',');
          const country = segments[segments.length - 1].trim();
          const manualCountry = answers[record.id][domicile]?.value?.country;
          return (
            includes(pays_liste_ue, country) ||
            includes(pays_liste_ue, manualCountry) ||
            includes(pays_liste_fr, country) ||
            includes(pays_liste_fr, manualCountry)
          );
        });
        let adress_result;
        if (adresse_black_grey) {
          adress_result = 'tracfin_eleve';
        } else if (!adresse_fr_ue) {
          adress_result = 'tracfin_modere';
        } else {
          adress_result = 'tracfin_faible';
        }

        const answer = answers[records['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES']['TRACFIN'][0].id];
        const tracfinAnswerArray: number[] = filter(
          map(answer, (formAnswer, questionId) => {
            if (questionId.startsWith('pp_tracfin_client_')) {
              return tracfinScale[formAnswer.value];
            }
            return undefined;
          }),
          (item) => !!item
        ) as number[];
        tracfinAnswerArray.push(tracfinScale[nationality_result], tracfinScale[adress_result]);
        return {
          tracfinCount: {
            faible: reduce(tracfinAnswerArray, (res, value) => (res += value === 1 ? 1 : 0), 0),
            fort: reduce(tracfinAnswerArray, (res, value) => (res += value === 3 ? 1 : 0), 0),
            moyen: reduce(tracfinAnswerArray, (res, value) => (res += value === 2 ? 1 : 0), 0)
          }
        };
      },

      computeTracfinPPOperation: (): JSONValue => {
        const tracfinScale: Dictionary<number> = {
          tracfin_eleve: 3,
          tracfin_eleve_1: 3,
          tracfin_eleve_2: 3,
          tracfin_eleve_3: 3,
          tracfin_eleve_4: 3,
          tracfin_faible: 1,
          tracfin_faible_1: 1,
          tracfin_faible_2: 1,
          tracfin_modere: 2
        };
        const answer = answers[records['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES']['TRACFIN'][0].id];
        const tracfinAnswerArray: AnswerArrayItem[] = filter(
          map(answer, (formAnswer, questionId) => {
            if (questionId.startsWith('pp_tracfin_operation_')) {
              return { questionId, value: tracfinScale[formAnswer.value] };
            }
            return undefined;
          }),
          (item) => !!item
        ) as AnswerArrayItem[];
        return {
          tracfinCount: {
            faible: reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 1 ? 1 : 0), 0),
            fort: reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 3 ? 1 : 0), 0),
            moyen: reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 2 ? 1 : 0), 0)
          }
        };
      },

      computeTracfinSextant: (): JSONValue => {
        const tracfinScale: Dictionary<number> = {
          tracfin_faible: 1,
          tracfin_fort: 3,
          tracfin_modere: 2
        };
        const answer = answers[records['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES']['TRACFIN'][0].id];
        const tracfinAnswerArray: AnswerArrayItem[] = filter(
          map(answer, (formAnswer, questionId) => {
            if (questionId.startsWith('sextant_')) {
              return { questionId, value: tracfinScale[formAnswer.value] };
            }
            return undefined;
          }),
          (item) => !!item
        ) as AnswerArrayItem[];
        return {
          tracfinCount: {
            faible: reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 1 ? 1 : 0), 0),
            fort: reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 3 ? 1 : 0), 0),
            moyen: reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 2 ? 1 : 0), 0)
          },
          tracfinCritique: some(tracfinAnswerArray, (answer) => answer.value === 4),
          tracfinFort: reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 3 ? 1 : 0), 0) >= 5
        };
      },

      computeTracfinSimpleAcquereur: (trigger_number: number, trigger_number_critique: number) => {
        const tracfinScale: Dictionary<number> = {
          non: 1,
          oui: 2
        };
        const answer = answers[records['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES']['TRACFIN'][0].id];
        const tracfinAnswerArray: AnswerArrayItem[] = filter(
          map(answer, (formAnswer, questionId) => {
            if (questionId.endsWith('acquereur_simple')) {
              return { questionId, value: tracfinScale[formAnswer.value] };
            }
            return undefined;
          }),
          (item) => !!item
        ) as AnswerArrayItem[];
        return {
          tracfinCount: reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 2 ? 1 : 0), 0),
          tracfinCritique:
            reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 2 ? 1 : 0), 0) >=
            trigger_number_critique,
          tracfinEleve:
            reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 2 ? 1 : 0), 0) >= trigger_number,
          tracfinFaible: some(tracfinAnswerArray, (answer) => answer.value === 2)
        };
      },

      computeTracfinSimpleVendeur: (trigger_number: number, trigger_number_critique: number) => {
        const tracfinScale: Dictionary<number> = {
          non: 1,
          oui: 2
        };
        const answer = answers[records['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES']['TRACFIN'][0].id];
        const tracfinAnswerArray: AnswerArrayItem[] = filter(
          map(answer, (formAnswer, questionId) => {
            if (questionId.endsWith('vendeur_simple')) {
              return { questionId, value: tracfinScale[formAnswer.value] };
            }
            return undefined;
          }),
          (item) => !!item
        ) as AnswerArrayItem[];
        return {
          tracfinCount: reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 2 ? 1 : 0), 0),
          tracfinCritique:
            reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 2 ? 1 : 0), 0) >=
            trigger_number_critique,
          tracfinEleve:
            reduce(tracfinAnswerArray, (res, answer) => (res += answer.value === 2 ? 1 : 0), 0) >= trigger_number,
          tracfinFaible: some(tracfinAnswerArray, (answer) => answer.value === 2)
        };
      },

      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      concat: (array: any, ...values: any[]) => {
        return concat(array ?? [], ...values.map((value) => value ?? []));
      },

      drop: drop,

      feesSelectionHabitat: (price = 0) => {
        const isOperationTemplate = (...operationSpecificTypes: string[][]) => {
          const specificTypes = operation.template.specificTypes;
          if (specificTypes) {
            return operationSpecificTypes.some((types) => isEqual(specificTypes, types));
          }
          return false;
        };
        const MANDAT_EXCLUSIF =
          answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id]?.['mandat_type']?.value ===
            'exclusif' ||
          answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id]?.['mandat_type']?.value === 'semi';
        const MANDAT_SIMPLE =
          answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id]?.['mandat_type']?.value ===
          'simple';
        if (
          isOperationTemplate(
            ['SELECTION_HABITAT', 'DOSSIER_DE_VENTE_SELECTION_HABITAT'],
            ['SELECTION_HABITAT', 'DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO'],
            ['SELECTION_HABITAT', 'DOSSIER_DE_VENTE_SELECTION_OCEAN'],
            ['SELECTION_HABITAT', 'DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO']
          )
        ) {
          if (price <= 29999) return 5000;
          if (price <= 39999) return 5500;
          if (price <= 44999) return 6000;
          if (price <= 49999) return 6500;
          if (price <= 54999) return 7000;
          if (price <= 59999) return 7500;
          if (price <= 69999) return 8000;
          if (price <= 79999) return 8500;
          if (price <= 89999) return 9000;
          if (price <= 99999) return 10000;
          if (price <= 109999) return 10500;
          if (price <= 114999) return 11000;
          if (price <= 119999) return 11500;
          if (price <= 129999) return 12000;
          if (price <= 139999) return 12500;
          if (price <= 149999) return 13000;
          if (price <= 159999) return 14000;
          if (price <= 169999) return 14500;
          if (price <= 179999) return 15000;
          if (price <= 189999) return 16000;
          if (price <= 199999) return 16500;
          if (price <= 209999) return 17000;
          if (price <= 219999) return 18000;
          if (price <= 229999) return 19000;
          if (price <= 239999) return 19500;
          if (price <= 249999) return 20000;
          if (price <= 259999) return 21000;
          if (price <= 269999) return 21500;
          if (price <= 279999) return 22000;
          if (price <= 289999) return 22500;
          if (price <= 299999) return 23000;
          if (price <= 309999) return 24000;
          if (price <= 319999) return 24500;
          if (price <= 329999) return 25000;
          if (price <= 339999) return 26000;
          if (price <= 349999) return 27000;
          if (price <= 359999) return 27500;
          if (price <= 379999) return 28000;
          if (price <= 399999) return 29000;
          if (price <= 419999) return 30000;
          if (price <= 439999) return 32000;
          if (price <= 459999) return 33000;
          if (price <= 479999) return 34000;
          if (price <= 499999) return 35000;
          if (price <= 539999) return 36000;
          if (price <= 559999) return 37000;
          if (price <= 579999) return 38000;
          if (price <= 599999) return 39000;
          if (price <= 659999) return 40000;
          return Math.round(price * 0.06);
        }
        if (
          isOperationTemplate(
            ['SELECTION_HABITAT', 'DOSSIER_DE_VENTE_ARIEG_IMMO'],
            ['SELECTION_HABITAT', 'DOSSIER_DE_VENTE_ARIEG_IMMO_PRO']
          )
        ) {
          if (MANDAT_EXCLUSIF) {
            if (price <= 29999) return 5000;
            if (price <= 39999) return 5500;
            if (price <= 44999) return 6000;
            if (price <= 49999) return 6500;
            if (price <= 54999) return 7000;
            if (price <= 59999) return 7500;
            if (price <= 69999) return 8000;
            if (price <= 79999) return 8500;
            if (price <= 89999) return 9000;
            if (price <= 99999) return 10000;
            if (price <= 109999) return 10500;
            if (price <= 114999) return 11000;
            if (price <= 119999) return 11500;
            if (price <= 129999) return 12000;
            if (price <= 139999) return 12500;
            if (price <= 149999) return 13000;
            if (price <= 159999) return 14000;
            if (price <= 169999) return 14500;
            if (price <= 179999) return 15000;
            if (price <= 189999) return 16000;
            if (price <= 199999) return 16500;
            if (price <= 209999) return 17000;
            if (price <= 219999) return 18000;
            if (price <= 229999) return 19000;
            if (price <= 239999) return 19500;
            if (price <= 249999) return 20000;
            if (price <= 259999) return 21000;
            if (price <= 269999) return 21500;
            if (price <= 279999) return 22000;
            if (price <= 289999) return 22500;
            if (price <= 299999) return 23000;
            if (price <= 309999) return 24000;
            if (price <= 319999) return 24500;
            if (price <= 329999) return 25000;
            if (price <= 339999) return 26000;
            if (price <= 349999) return 27000;
            if (price <= 359999) return 27500;
            if (price <= 379999) return 28000;
            if (price <= 399999) return 29000;
            if (price <= 419999) return 30000;
            if (price <= 439999) return 32000;
            if (price <= 459999) return 33000;
            if (price <= 479999) return 34000;
            if (price <= 499999) return 35000;
            if (price <= 539999) return 36000;
            if (price <= 559999) return 37000;
            if (price <= 579999) return 38000;
            if (price <= 599999) return 39000;
            if (price <= 659999) return 40000;
            return Math.round(price * 0.06);
          }
          if (MANDAT_SIMPLE) {
            if (price < 50000) return 5000;
            if (price < 100000) return 6000;
            if (price < 200000) return Math.round(price * 0.07);
            return Math.round(price * 0.06);
          }
          return 'SELECTIONNER LE TYPE DE MANDAT';
        }
        if (
          isOperationTemplate(
            ['SELECTION_HABITAT', 'DOSSIER_DE_VENTE_SELECTION_IMMOBILIER'],
            ['SELECTION_HABITAT', 'DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO']
          )
        ) {
          if (price <= 49999) return 5000;
          if (price <= 54999) return 5500;
          if (price <= 59999) return 6000;
          if (price <= 69999) return 6500;
          if (price <= 79999) return 7000;
          if (price <= 89999) return 8000;
          if (price <= 99999) return 8500;
          if (price <= 109999) return 9000;
          if (price <= 119999) return 10000;
          if (price <= 129999) return 11000;
          if (price <= 139999) return 12000;
          if (price <= 149999) return 12500;
          if (price <= 159999) return 13000;
          if (price <= 169999) return 13500;
          if (price <= 179999) return 14000;
          if (price <= 189999) return 15000;
          if (price <= 199999) return 15500;
          if (price <= 209999) return 16000;
          if (price <= 219999) return 16500;
          if (price <= 229999) return 17000;
          if (price <= 239999) return 17500;
          if (price <= 249999) return 18000;
          if (price <= 259999) return 18500;
          if (price <= 269999) return 19000;
          if (price <= 279999) return 19500;
          if (price <= 319999) return 20000;
          if (price <= 339999) return 21000;
          if (price <= 349999) return 22000;
          if (price <= 359999) return 22500;
          if (price <= 389999) return 23000;
          return Math.round(price * 0.06);
        }
        if (
          isOperationTemplate(
            ['SELECTION_HABITAT', 'DOSSIER_DE_VENTE_AGENCE_HAMILTON'],
            ['SELECTION_HABITAT', 'DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO']
          )
        ) {
          if (price < 250000) return Math.round(price * 0.1);
          if (price < 350000) return Math.round(price * 0.09);
          if (price < 500000) return Math.round(price * 0.08);
          if (price < 800000) return Math.round(price * 0.07);
          return Math.round(price * 0.06);
        }
        return '0';
      },

      filterRecords: (records: Record[], specificTypes: string[][]) => filterRecords(records, specificTypes, templates),

      // Filtre le record en fonction d'une value sur une answer et export une liste
      filterRecordsByAnswer: (records: Record[], questionId: string, value: unknown) => {
        return filter(records, (record) => {
          return answers[record.id][questionId]?.value === value;
        });
      },

      getAge: (dateInput: number) => {
        const today = new Date();
        const birthDate = new Date(dateInput);
        const m = today.getMonth() - birthDate.getMonth();
        let age = today.getFullYear() - birthDate.getFullYear();
        if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
          age--;
        }
        return age;
      },

      getAgeAtDate: (dateNaissance: number, dateContrat: number) => {
        const contractDate = new Date(dateContrat);
        const birthDate = new Date(dateNaissance);
        const m = contractDate.getMonth() - birthDate.getMonth();
        let age = contractDate.getFullYear() - birthDate.getFullYear();
        if (m < 0 || (m === 0 && contractDate.getDate() < birthDate.getDate())) {
          age--;
        }
        return age;
      },

      getCivility: (value: string, masculine: string, feminine: string) => {
        return value === 'homme' ? masculine : feminine;
      },

      getCombinedAddressValues: (linkType: string, branchType: string, questionId: string) => {
        const targetRecords = records[linkType]?.[branchType];
        const uniqueTextValuesSet = new Set();

        targetRecords.forEach((record) => {
          const address = answers[record.id][questionId]?.value as MnAddress | undefined;
          const addressText = getFormattedAddress(address);
          uniqueTextValuesSet.add(addressText);
        });

        return Array.from(uniqueTextValuesSet).join('- ');
      },

      getCombinedCityValues: (linkType: string, branchType: string, questionId: string) => {
        const targetRecords = records[linkType]?.[branchType];
        const uniqueTextValuesSet = new Set();
        targetRecords.forEach((record) => {
          const city = answers[record.id][questionId]?.value?.city ?? ' ';
          const textValue = `${city}`;
          uniqueTextValuesSet.add(textValue);
        });
        const result = Array.from(uniqueTextValuesSet).join('- ');
        return result;
      },

      getCombinedDepartementValues: (linkType: string, branchType: string, questionId: string) => {
        const targetRecords = records[linkType]?.[branchType];
        const uniqueTextValuesSet = new Set();
        targetRecords.forEach((record) => {
          const departement = answers[record.id][questionId]?.value?.zip.slice(0, 2) ?? ' ';
          const textValue = `${departement}`;
          uniqueTextValuesSet.add(textValue);
        });
        const result = Array.from(uniqueTextValuesSet).join('- ');
        return result;
      },

      getCombinedLotNumber: (linkType: string, branchType: string, questionId: string) => {
        const isTemplateOfType = (templateId: string, targetSpecificTypes: string[]) => {
          const specificTypes = templates[templateId]?.specificTypes;
          if (specificTypes) {
            return isEqual(specificTypes, targetSpecificTypes);
          }
          return false;
        };
        const targetRecords = records[linkType]?.[branchType];
        return targetRecords
          .filter((record) => {
            const templateId = record.template?.id;
            if (isTemplateOfType(templateId, ['BIEN', 'LOT_HABITATION'])) {
              return true;
            }
            if (isTemplateOfType(templateId, ['BIEN', 'LOT_HORS_HABITATION'])) {
              const prixVenteOccupe = answers[record.id]?.programme_prix_vente_occupe?.value;
              const prixVenteLibre = answers[record.id]?.programme_prix_vente_libre?.value;
              return (
                (prixVenteOccupe == null || prixVenteOccupe === 0) && (prixVenteLibre == null || prixVenteLibre === 0)
              );
            }
            return false;
          })
          .map((record) => `n°${answers[record.id]?.[questionId]?.value ?? ' '}`)
          .join(', le lot ');
      },

      getCombinedTextValues: (linkType: string, branchType: string, questionIds: string[]) => {
        const targetRecords = records[linkType]?.[branchType];
        const combinedValues = targetRecords.map((record) => {
          // eslint-disable-next-line array-callback-return
          const textValues = questionIds.map((questionId) => {
            if (questionId === 'personne_morale_forme_sociale') {
              const answerValue = answers[record.id][questionId]?.value;
              if (answerValue === 'societe_sci') {
                return 'SCI';
              }
              if (answerValue === 'societe_sas') {
                return 'SAS';
              }
              if (answerValue === 'societe_sasu') {
                return 'SASU';
              }
              if (answerValue === 'societe_sarl') {
                return 'SARL';
              }
              if (answerValue === 'societe_sa') {
                return 'SA';
              }
              if (answerValue === 'commune') {
                return 'Commune de';
              }
              if (answerValue === 'association') {
                return 'Association';
              }
              if (answerValue === 'autre') {
                return 'Société';
              }
            } else {
              return answers[record.id][questionId]?.value ?? ' ';
            }
          });
          return textValues.join(' ');
        });

        const result = combinedValues.join(', ');
        return result;
      },

      getCombinedTextValuesInRecords: (records: Record[], questionIds: string[], repeatQuestionId?: string) => {
        const combinedValues = records.map((record) => {
          const textValues = questionIds.map((questionId) => {
            if (repeatQuestionId) {
              const repeatItems = getRepeatValues(answers[record.id], questionId);
              return repeatItems.map((item) => item[repeatQuestionId]?.value ?? ' ').join(' ');
            } else {
              return answers[record.id][questionId]?.value ?? ' ';
            }
          });
          return textValues.join(' ');
        });
        const result = combinedValues.join(', ');
        return result;
      },
      getCopros: (records: Record[]): Record[] => {
        const operationTemplate = templates[operation.template.id] as LegalOperationTemplate;

        /**
         * If the operation is a program, we don't want to return "Coproprietes" linked to the "lots", we want the
         * "Coproprietes" directly linked to the program.
         */
        if (isProgramConfig(operationTemplate?.config)) {
          return [];
        }

        const lotsWithoutCopro: Record[] = [];
        const COPROPRIETES_CONTENT = flatten(
          map(records, (record) => {
            const copros = branchesRecordsByRecordIdAndBranchType[record.id]?.['CONTENU'];
            const template = templates[record.template.id];
            const isTargetTemplate =
              isEqual(template.specificTypes, ['BIEN', 'LOT_HABITATION']) ||
              isEqual(template.specificTypes, ['BIEN', 'LOT_HORS_HABITATION']);
            if (!copros && isTargetTemplate) {
              lotsWithoutCopro.push(record);
            }

            return copros ?? [];
          })
        );

        const COPROPRIETES = uniqBy(
          filterRecords(COPROPRIETES_CONTENT, [['STRUCTURE', 'COPROPRIETE']], templates),
          'id'
        );

        if (!isEmpty(lotsWithoutCopro)) {
          branchesRecordsByRecordIdAndBranchType[-1] = {
            IMMEUBLES: uniqBy(lotsWithoutCopro, (record) => record?.id)
          };
          return [...COPROPRIETES, { id: -1 } as Record];
        }
        return COPROPRIETES;
      },

      getDays: (date_fin_bail: number, duree_conge: number) => {
        const today = new Date();
        const date = new Date(date_fin_bail);
        const date_check = new Date(date.setMonth(date.getMonth() - duree_conge));
        const difference = date_check.getTime() - today.getTime();
        const TotalDays = Math.ceil(difference / (1000 * 3600 * 24));
        return TotalDays;
      },
      getDepotGarantie: (form: Record) => {
        const depotGarantieAutomatiqueChoix = answers[form.id]?.['depot_garantie_automatique']?.value === 'oui';
        const depotGarantieChoixManuel = answers[form.id]?.['depot_garantie_montant']?.value || 0;
        const bailMeuble = answers[form.id]?.['meublee_statut']?.value === 'oui';
        const bailNonMeuble = answers[form.id]?.['meublee_statut']?.value === 'non';
        const loyer = answers[form.id]?.['loyer_initial']?.value;
        const complementLoyer = answers[form.id]?.['loyer_complement_montant']?.value || 0;

        if (depotGarantieAutomatiqueChoix && bailMeuble) {
          return (loyer + complementLoyer) * 2;
        }
        if (depotGarantieAutomatiqueChoix && bailNonMeuble) {
          return loyer + complementLoyer;
        }
        return depotGarantieChoixManuel;
      },
      // Permet d'additionner une valeur sur toutes les fiches présentes, même dans un repeat
      getGenericTotalPrice: (linkType: string, branchType: string, questionId: string, repeatQuestionId?: string) => {
        const targetRecords = records[linkType]?.[branchType];
        const total = sumBy(targetRecords, (record) => {
          if (repeatQuestionId) {
            const repeatItems = getRepeatValues(answers[record.id], questionId);
            return sumBy(repeatItems, (item) => item[repeatQuestionId]?.value ?? 0);
          } else {
            return answers[record.id][questionId]?.value ?? 0;
          }
        });
        return total;
      },
      getGenericTotalSocialPrice: (
        linkType: string,
        branchType: string,
        priceOccupe: string,
        priceVacant: string,
        form: Record
      ) => {
        const targetRecords = records[linkType]?.[branchType];

        const totalOccupe = sumBy(targetRecords, (record) => {
          return answers[record.id][priceOccupe]?.value ?? 0;
        });
        const totalVacant = sumBy(targetRecords, (record) => {
          return answers[record.id][priceVacant]?.value ?? 0;
        });
        const venteOccupee = answers[form.id]?.['occupation_sociale']?.value === 'oui';
        if (venteOccupee) {
          return totalOccupe;
        } else {
          return totalVacant;
        }
      },
      getHonorairesRedaction: (honoraires: number, form: Record) => {
        const honorairesRedaction = answers[form.id]?.['mandat_honoraires_redaction_charge']?.value;
        if (honorairesRedaction === 'acquereur') {
          return honoraires;
        }
        return 0;
      },
      getLinkBranchesFrom: (linkId: number, branchType: string) => {
        const link = getLinkById(activeLinks, linkId);
        return filter(link?.branches, (branch) => branch.type === branchType).map((branch) => branch.from);
      },

      getLinkBranchesTo: (linkId: number, branchType: string) => {
        const link = getLinkById(activeLinks, linkId);
        return filter(link?.branches, (branch) => branch.type === branchType).map((branch) => branch.to);
      },
      getLotAdress: (copro: AnswerDict, lot: AnswerDict) => {
        return copro?.['adresse']?.value?.formattedAddress ?? lot?.['adresse']?.value?.formattedAddress;
      },
      getLots: (branchWithRecords: Dictionary<Record[]>): Record[] => {
        const operationTemplate = templates[operation.template.id] as LegalOperationTemplate;

        if (!isProgramConfig(operationTemplate?.config)) {
          return branchWithRecords['IMMEUBLES'];
        }
        return [];
      },
      getMonopropriete: (records: Record[]): Record[] => {
        const operationTemplate = templates[operation.template.id] as LegalOperationTemplate;
        if (isProgramConfig(operationTemplate?.config)) {
          return [];
        }

        const lotsWithoutMonopropriete: Record[] = [];
        const PROPRIETE_CONTENT = flatten(
          map(records, (record) => {
            const monopropriete = branchesRecordsByRecordIdAndBranchType[record.id]?.['CONTENU'];
            const template = templates[record.template.id];
            const isTargetTemplate =
              isEqual(template.specificTypes, ['BIEN', 'MONOPROPRIETE_HABITATION']) ||
              isEqual(template.specificTypes, ['BIEN', 'MONOPROPRIETE_HORS_HABITATION']);
            if (!monopropriete && isTargetTemplate) {
              lotsWithoutMonopropriete.push(record);
            }

            return monopropriete ?? [];
          })
        );

        const MONOPROPRIETES = uniqBy(
          filterRecords(PROPRIETE_CONTENT, [['STRUCTURE', 'ENSEMBLE_IMMOBILIER']], templates),
          'id'
        );

        if (!isEmpty(lotsWithoutMonopropriete)) {
          branchesRecordsByRecordIdAndBranchType[-1] = {
            IMMEUBLES: uniqBy(lotsWithoutMonopropriete, (record) => record?.id)
          };
          return [...MONOPROPRIETES, { id: -1 } as Record];
        }
        return MONOPROPRIETES;
      },
      getMonthlyProrata: (dateInput: number, price: number) => {
        const result = new Date(dateInput);
        const year = result.getFullYear();
        const daysInMonth = new Date(year, result.getMonth() + 1, 0).getDate();
        const daysLeftInMonth = daysInMonth + 1 - result.getDate();
        return (price * daysLeftInMonth) / daysInMonth;
      },
      getOneOrNone: (size: number, none: string, one: string) => {
        return size > 0 ? one : none;
      },

      getPlural: (size: number, singular: string, plural: string) => {
        return size > 1 ? plural : singular;
      },
      getRecordsBranches: (records: Record[], branchType: string) => {
        return flatten(map(records, (record) => branchesRecordsByRecordIdAndBranchType[record.id]?.[branchType] ?? []));
      },
      getRecordsGenericTotalPrice: (records: Record[], questionId: string) => {
        const total = sumBy(records, (record) => {
          return answers[record.id][questionId]?.value ?? 0;
        });
        return total;
      },

      getRepeatValues,
      getTotalHonorairesLocation: (surface: number, form: Record, properties: Record[], output: string) => {
        const zoneTendue = some(properties, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip;
          const insee = answers[record.id]['adresse'].value.cityCode;
          if (INSEE_ZONE_TENDUE.includes(insee)) {
            return true;
          }
          return ZIP_ZONE_TENDUE.includes(zip);
        });
        const zoneTresTendue = some(properties, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip;
          const insee = answers[record.id]['adresse'].value.cityCode;
          if (INSEE_ZONE_TRES_TENDUE.includes(insee)) {
            return true;
          }
          return ZIP_ZONE_TRES_TENDUE.includes(zip);
        });
        const zoneMixte = some(properties, (record) => {
          const cityCode = answers[record.id]['adresse']?.value?.cityCode;
          if (cityCode === undefined || cityCode === null) {
            return ZIP_ZONE_MIXTE.includes(answers[record.id]['adresse']?.value?.zip);
          }
          return false;
        });

        const zoneMixteTresTendue = some(properties, (record) => {
          return answers[record.id]['zone_tendue']?.value === 'tres_tendue';
        });
        const zoneMixteTendue = some(properties, (record) => {
          return answers[record.id]['zone_tendue']?.value === 'oui';
        });
        const zoneMixteNonTendue = some(properties, (record) => {
          return (
            answers[record.id]['zone_tendue']?.value !== 'oui' &&
            answers[record.id]['zone_tendue']?.value !== 'tres_tendue'
          );
        });
        const honorairesNegociationBailleur = answers[form.id]?.['honoraires_location_bailleur']?.value;

        const honorairesEdlBailleurAutomatiqueChoix =
          answers[form.id]?.['honoraires_prestation_etat_des_lieux_bailleur_automatique']?.value === 'oui';
        const honorairesEdlBailleurMontantManuel =
          answers[form.id]?.['honoraires_prestation_etat_des_lieux_bailleur']?.value || 0;

        const honorairesVisiteBailleurAutomatiqueChoix =
          answers[form.id]?.['honoraires_prestation_redaction_bailleur_automatique']?.value === 'oui';

        const honorairesVisiteBailleurMontantManuel =
          answers[form.id]?.['honoraires_prestation_redaction_bailleur']?.value;

        let honorairesVisiteBailleurMontant;

        if (honorairesVisiteBailleurAutomatiqueChoix) {
          if (zoneTendue) {
            honorairesVisiteBailleurMontant = surface * 10;
          } else if (zoneTresTendue) {
            honorairesVisiteBailleurMontant = surface * 12;
          } else if (zoneMixte) {
            if (zoneMixteTresTendue) {
              honorairesVisiteBailleurMontant = surface * 12;
            } else if (zoneMixteTendue) {
              honorairesVisiteBailleurMontant = surface * 10;
            } else if (zoneMixteNonTendue) {
              honorairesVisiteBailleurMontant = surface * 8;
            }
          } else {
            honorairesVisiteBailleurMontant = surface * 8;
          }
        } else {
          honorairesVisiteBailleurMontant = honorairesVisiteBailleurMontantManuel;
        }

        let honorairesEdlBailleurMontant;

        if (honorairesEdlBailleurAutomatiqueChoix) {
          honorairesEdlBailleurMontant = surface * 3;
        } else {
          honorairesEdlBailleurMontant = honorairesEdlBailleurMontantManuel;
        }

        const totalHonorairesBailleur =
          honorairesNegociationBailleur + honorairesVisiteBailleurMontant + honorairesEdlBailleurMontant;

        const honorairesEdlLocataireAutomatiqueChoix =
          answers[form.id]?.['honoraires_prestation_etat_des_lieux_locataire_automatique']?.value === 'oui';
        const honorairesEdlLocataireMontantManuel =
          answers[form.id]?.['honoraires_prestation_etat_des_lieux_locataire']?.value || 0;

        const honorairesVisiteLocataireAutomatiqueChoix =
          answers[form.id]?.['honoraires_prestation_redaction_locataire_automatique']?.value === 'oui';

        const honorairesVisiteLocataireMontantManuel =
          answers[form.id]?.['honoraires_prestation_redaction_locataire']?.value || 0;
        let honorairesVisiteLocataireMontant;

        if (honorairesVisiteLocataireAutomatiqueChoix) {
          if (zoneTendue) {
            honorairesVisiteLocataireMontant = surface * 10;
          } else if (zoneTresTendue) {
            honorairesVisiteLocataireMontant = surface * 12;
          } else if (zoneMixte) {
            if (zoneMixteTresTendue) {
              honorairesVisiteLocataireMontant = surface * 12;
            } else if (zoneMixteTendue) {
              honorairesVisiteLocataireMontant = surface * 10;
            } else if (zoneMixteNonTendue) {
              honorairesVisiteLocataireMontant = surface * 8;
            }
          } else {
            honorairesVisiteLocataireMontant = surface * 8;
          }
        } else {
          honorairesVisiteLocataireMontant = honorairesVisiteLocataireMontantManuel;
        }

        let honorairesEdlLocataireMontant;

        if (honorairesEdlLocataireAutomatiqueChoix) {
          honorairesEdlLocataireMontant = surface * 3;
        } else {
          honorairesEdlLocataireMontant = honorairesEdlLocataireMontantManuel;
        }

        const totalHonorairesLocataire = honorairesVisiteLocataireMontant + honorairesEdlLocataireMontant;
        switch (output) {
          case 'TOTAL_HONORAIRES_BAILLEUR':
            return totalHonorairesBailleur;
          case 'HONORAIRES_VISITE_BAILLEUR':
            return honorairesVisiteBailleurMontant;
          case 'HONORAIRES_EDL_BAILLEUR':
            return honorairesEdlBailleurMontant;
          case 'TOTAL_HONORAIRES_LOCATAIRE':
            return totalHonorairesLocataire;
          case 'HONORAIRES_VISITE_LOCATAIRE':
            return honorairesVisiteLocataireMontant;
          case 'HONORAIRES_EDL_LOCATAIRE':
            return honorairesEdlLocataireMontant;
        }
      },
      getTotalPrice: (linkType: string, branchType: string) => {
        const targetRecords = records[linkType]?.[branchType];

        const total = sumBy(targetRecords, (record) => {
          return answers[record.id]['programme_prix_vente']?.value ?? 0;
        });
        return total;
      },

      getTotalSuperficie: (total: number) => {
        const value = total.toString();
        const hectare = value.substring(0, value.length - 4).padStart(2, '0');
        const are = value.substring(value.length - 4, value.length - 2);
        const ca = value.substring(value.length - 2, value.length);
        return `${hectare ? hectare : 0} h ${are ? are : 0} a ${ca ? ca : 0} ca`;
      },
      getTva: (price: number, module: string, format: 'PRICE' | 'LETTER') => {
        const TTC_TO_HT_20 = price / 1.2;
        const TTC_TO_TVA_20 = price - TTC_TO_HT_20;
        const HT_TO_TTC_20 = price * 1.2;
        const HT_TO_TVA_20 = HT_TO_TTC_20 - price;
        const TTC_TO_HT_10 = price / 1.1;
        const TTC_TO_TVA_10 = price - TTC_TO_HT_10;
        const HT_TO_TTC_10 = price * 1.1;
        const HT_TO_TVA_10 = HT_TO_TTC_10 - price;
        const TTC_TO_HT_5 = price / 1.055;
        const TTC_TO_TVA_5 = price - TTC_TO_HT_5;
        const HT_TO_TTC_5 = price * 1.055;
        const HT_TO_TVA_5 = HT_TO_TTC_5 - price;
        let FINAL_TVA = 0;

        if (module === 'TTC_TO_TVA_20') FINAL_TVA = TTC_TO_TVA_20;
        if (module === 'TTC_TO_HT_20') FINAL_TVA = TTC_TO_HT_20;
        if (module === 'HT_TO_TTC_20') FINAL_TVA = HT_TO_TTC_20;
        if (module === 'HT_TO_TVA_20') FINAL_TVA = HT_TO_TVA_20;
        if (module === 'TTC_TO_TVA_10') FINAL_TVA = TTC_TO_TVA_10;
        if (module === 'TTC_TO_HT_10') FINAL_TVA = TTC_TO_HT_10;
        if (module === 'HT_TO_TTC_10') FINAL_TVA = HT_TO_TTC_10;
        if (module === 'HT_TO_TVA_10') FINAL_TVA = HT_TO_TVA_10;
        if (module === 'TTC_TO_TVA_5') FINAL_TVA = TTC_TO_TVA_5;
        if (module === 'TTC_TO_HT_5') FINAL_TVA = TTC_TO_HT_5;
        if (module === 'HT_TO_TTC_5') FINAL_TVA = HT_TO_TTC_5;
        if (module === 'HT_TO_TVA_5') FINAL_TVA = HT_TO_TVA_5;

        return format === 'LETTER' ? formatPriceToNumberAndLetter(FINAL_TVA) : formatPrice(FINAL_TVA);
      },
      getTvaDom: (price: number, estate: Record[], format: 'PRICE' | 'LETTER') => {
        const ZIP_DOM = some(estate, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip?.slice(0, 3);
          return zip === '971' || zip === '972' || zip === '974';
        });
        const TVA_HORS_DOM = price / 1.2;
        const TVA_DOM = price / 1.085;
        const FINAL_TVA = ZIP_DOM ? TVA_DOM : TVA_HORS_DOM;
        return format === 'LETTER' ? formatPriceToNumberAndLetter(FINAL_TVA) : formatPrice(FINAL_TVA);
      },
      getTvaDomInForm: (price: number, adresse: Answer, format: 'PRICE' | 'LETTER') => {
        const zip = adresse.value?.zip?.slice(0, 3);
        const ZIP_DOM = zip === '971' || zip === '972' || zip === '974';
        const TVA_HORS_DOM = price / 1.2;
        const TVA_DOM = price / 1.085;
        const FINAL_TVA = ZIP_DOM ? TVA_DOM : TVA_HORS_DOM;
        return format === 'LETTER' ? formatPriceToNumberAndLetter(FINAL_TVA) : formatPrice(FINAL_TVA);
      },
      getTvaDomRate: (estate: Record[]) => {
        const ZIP_DOM = some(estate, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip?.slice(0, 3);
          return zip === '971' || zip === '972' || zip === '974';
        });
        return ZIP_DOM ? '8,5' : '20';
      },
      getTvaDomRateInForm: (adresse: Answer) => {
        const zip = adresse.value?.zip?.slice(0, 3);
        const ZIP_DOM = zip === '971' || zip === '972' || zip === '974';
        return ZIP_DOM ? '8,5' : '20';
      },
      getUndefinedDefaultValue: (value: string, defaultValue: string) => {
        return value ? value : defaultValue;
      },
      getVisualCheckBox: (value: boolean): string => {
        return `${value ? checkBoxChecked : checkBoxUnchecked}`;
      },

      getVisualCheckBoxChecked: (): string => {
        return checkBoxChecked;
      },
      getVisualCheckBoxUnchecked: (): string => {
        return checkBoxUnchecked;
      },
      getYearlyProrata: (dateInput: number, price: number) => {
        const result = new Date(dateInput);
        const finalDay = new Date(result.getFullYear(), 0, 0);
        const diff = result.getTime() - finalDay.getTime();
        const oneDay = 1000 * 60 * 60 * 24;
        const days = Math.floor(diff / oneDay);
        const currentYear = result.getFullYear();
        let endYear;
        if (currentYear % 4 === 0) {
          if (currentYear % 100 === 0 && currentYear % 400 !== 0) {
            endYear = 366;
          } else {
            endYear = 366;
          }
        } else {
          endYear = 365;
        }
        const daysLeftInYear = endYear - days;
        return formatPrice(Math.round((price * daysLeftInYear) / endYear));
      },
      getYesOrNo: (value: string, positive: string, negative: string) => {
        return value === 'oui' ? positive : negative;
      },
      hasContractOfType: (modelId: string): boolean => {
        return !!find(
          contractsById,
          ({ legalContractTemplateId, operation }) =>
            operation?.id === operationId && legalContractTemplateId === modelId
        );
      },
      isDateAfter: fns.isAfter,
      isEqual: isEqual,
      isOperationTemplate: (...operationSpecificTypes: string[][]) => {
        const specificTypes = operation.template.specificTypes;
        if (specificTypes) {
          return operationSpecificTypes.some((types) => isEqual(specificTypes, types));
        }
        return false;
      },
      isTemplateOfType: (templateId: number, targetSpecificTypes: string[]) => {
        const specificTypes = templates[templateId]?.specificTypes;
        if (specificTypes) {
          return isEqual(specificTypes, targetSpecificTypes);
        }
        return false;
      },
      join: join,
      map: map,
      mapIdx: (list: unknown[], path: string[]) => {
        return reduce(
          list,
          (acc, value, key) => {
            return { ...acc, [get(value, path)]: key };
          },
          {}
        );
      },
      mapObject: (list: unknown[], path: string[], object: Dictionary<unknown>) => {
        return map(list, (item) => object?.[get(item, path)]);
      },
      max: Math.max,
      min: Math.min,
      notOlderThanNMonths: (date: number | Date, months: number): boolean => {
        return fns.isAfter(date, fns.subMonths(new Date(), months));
      },
      notaryFees: (price: number, furnitures: number): number => {
        return Math.round(
          (price - furnitures) * 0.045 +
            (price - furnitures) * 0.012 +
            (price - furnitures) * 0.045 * 0.0237 +
            Math.min(price, 6500) * 0.0387 +
            Math.max(Math.min(price, 17000) - 6500, 0) * 0.01596 +
            Math.max(Math.min(price, 60000) - 17000, 0) * 0.01064 +
            Math.max(price - 60000, 0) * 0.00799 +
            (Math.min(price, 6500) * 0.0387 +
              Math.max(Math.min(price, 17000) - 6500, 0) * 0.01596 +
              Math.max(Math.min(price, 60000) - 17000, 0) * 0.01064 +
              Math.max(price - 60000, 0) * 0.00799) *
              0.2 +
            850 +
            400 +
            170 +
            Math.max(15, ((price - furnitures) * 0.001) | 0)
        );
      },
      notaryFeesDiminution: (
        price: number,
        furnitures: number,
        form: Record,
        properties: Record[],
        formOffre?: Record
      ): number => {
        const DEPARTEMENT_REDUIT = some(properties, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip?.slice(0, 2);
          return zip === '36';
        });
        const DEPARTEMENT_56 = some(properties, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip?.slice(0, 2);
          return zip === '56';
        });
        const DEPARTEMENT_HAUSSE = some(properties, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip?.slice(0, 2);
          return LISTE_DEPARTEMENT_HAUSSE.includes(zip);
        });
        const DEPARTEMENT_SANS_CSI = some(properties, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip?.slice(0, 2);
          return zip === '67' || zip === '68' || zip === '57';
        });
        const DEPARTEMENT_MARTINIQUE_GUADELOUPE = some(properties, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip?.slice(0, 3);
          return zip === '971' || zip === '972';
        });
        const DEPARTEMENT_GUYANE = some(properties, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip?.slice(0, 3);
          return zip === '973';
        });
        const DEPARTEMENT_MAYOTTE = some(properties, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip?.slice(0, 3);
          return zip === '976';
        });
        const DEPARTEMENT_REUNION = some(properties, (record) => {
          const zip = answers[record.id]['adresse']?.value?.zip?.slice(0, 3);
          return zip === '974';
        });
        const PRIX_TAXABLE = price - furnitures;
        let DROIT_DEPARTEMENTAL = 0.045;
        let DROIT_COMMUNAL = 0.012;
        let FRAIS_ASSIETTE = DROIT_DEPARTEMENTAL * 0.0237;
        let FRAIS_NOTAIRE =
          Math.min(price, 6500) * 0.0387 +
          Math.max(Math.min(price, 17000) - 6500, 0) * 0.01596 +
          Math.max(Math.min(price, 60000) - 17000, 0) * 0.01064 +
          Math.max(price - 60000, 0) * 0.00799;
        const FRAIS_NOTAIRE_MARTINIQUE_GUADELOUPE = FRAIS_NOTAIRE + FRAIS_NOTAIRE * 0.25;
        const FRAIS_NOTAIRE_REUNION_MAYOTTE = FRAIS_NOTAIRE + FRAIS_NOTAIRE * 0.4;
        const FRAIS_NOTAIRE_GUYANE = FRAIS_NOTAIRE + FRAIS_NOTAIRE * 0.23;
        let TVA_NOTAIRE = FRAIS_NOTAIRE * 0.2;
        const TVA_NOTAIRE_MARTINIQUE_GUADELOUPE = FRAIS_NOTAIRE_MARTINIQUE_GUADELOUPE * 0.085;
        const TVA_NOTAIRE_REUNION = FRAIS_NOTAIRE_REUNION_MAYOTTE * 0.085;
        let CSI = Math.max(15, PRIX_TAXABLE * 0.001 || 0);
        const DEBOURS = 400;
        let FORMALITES = 850;
        let FRAIS_ANNEXES = 170;
        const FRAIS_NOTAIRE_MAX = Math.max(0.1 * price, 90);
        const NON_PRIMO_ACCEDANT_COMPROMIS = answers[form.id]?.['compromis_acquereur_primo_accedant']?.value === 'non';
        const NON_PRIMO_ACCEDANT_OFFRE = formOffre
          ? answers[formOffre.id]?.['offre_acquereur_primo_accedant']?.value === 'non'
          : false;
        const PRIMO_ACCEDANT_TOTAL = NON_PRIMO_ACCEDANT_COMPROMIS || NON_PRIMO_ACCEDANT_OFFRE;
        if (FRAIS_NOTAIRE + FORMALITES + FRAIS_ANNEXES >= FRAIS_NOTAIRE_MAX) {
          FRAIS_NOTAIRE = FRAIS_NOTAIRE_MAX;
          FORMALITES = 0;
          FRAIS_ANNEXES = 0;
        }
        if (answers[form.id]?.['compromis_acquereur_pro_frais_reduits']?.value === 'oui') {
          switch (answers[form.id]?.['compromis_acquereur_pro_frais_reduits_type']?.value) {
            case 'construction':
              DROIT_DEPARTEMENTAL = 0;
              FRAIS_ASSIETTE = 0;
              DROIT_COMMUNAL = 0;
              FRAIS_ANNEXES = 320;
              break;
            case 'revente':
              DROIT_DEPARTEMENTAL = 0.007;
              DROIT_COMMUNAL = 0;
              FRAIS_ASSIETTE = DROIT_DEPARTEMENTAL * 0.0214;
              FRAIS_ANNEXES = 250;
              break;
            default:
              break;
          }
        } else if (DEPARTEMENT_MARTINIQUE_GUADELOUPE) {
          FRAIS_NOTAIRE = FRAIS_NOTAIRE_MARTINIQUE_GUADELOUPE;
          TVA_NOTAIRE = TVA_NOTAIRE_MARTINIQUE_GUADELOUPE;
          FORMALITES = 1100;
        } else if (DEPARTEMENT_GUYANE) {
          FRAIS_NOTAIRE = FRAIS_NOTAIRE_GUYANE;
          TVA_NOTAIRE = 0;
          FORMALITES = 1050;
        } else if (DEPARTEMENT_MAYOTTE) {
          FRAIS_NOTAIRE = FRAIS_NOTAIRE_REUNION_MAYOTTE;
          TVA_NOTAIRE = 0;
          DROIT_DEPARTEMENTAL = 0.038;
        } else if (DEPARTEMENT_REUNION) {
          FRAIS_NOTAIRE = FRAIS_NOTAIRE_REUNION_MAYOTTE;
          TVA_NOTAIRE = TVA_NOTAIRE_REUNION;
          FORMALITES = 1190;
        } else if (DEPARTEMENT_REDUIT) {
          DROIT_DEPARTEMENTAL = 0.038;
        } else if (DEPARTEMENT_HAUSSE && PRIMO_ACCEDANT_TOTAL) {
          DROIT_DEPARTEMENTAL = 0.05;
        } else if (DEPARTEMENT_56 && PRIMO_ACCEDANT_TOTAL) {
          DROIT_DEPARTEMENTAL = 0.05;
        } else if (DEPARTEMENT_56 && !PRIMO_ACCEDANT_TOTAL) {
          DROIT_DEPARTEMENTAL = 0.038;
        } else if (DEPARTEMENT_SANS_CSI) {
          CSI = 0;
        }

        return Math.round(
          PRIX_TAXABLE * DROIT_DEPARTEMENTAL +
            PRIX_TAXABLE * DROIT_COMMUNAL +
            PRIX_TAXABLE * FRAIS_ASSIETTE +
            FRAIS_NOTAIRE +
            TVA_NOTAIRE +
            CSI +
            DEBOURS +
            FORMALITES +
            FRAIS_ANNEXES
        );
      },
      notaryFeesSocial: (price: number, furnitures: number): number => {
        return Math.round(
          Math.min(price, 6500) * 0.0387 +
            Math.max(Math.min(price, 17000) - 6500, 0) * 0.01596 +
            Math.max(Math.min(price, 60000) - 17000, 0) * 0.01064 +
            Math.max(price - 60000, 0) * 0.00799 +
            (Math.min(price, 6500) * 0.0387 +
              Math.max(Math.min(price, 17000) - 6500, 0) * 0.01596 +
              Math.max(Math.min(price, 60000) - 17000, 0) * 0.01064 +
              Math.max(price - 60000, 0) * 0.00799) *
              0.2 +
            850 +
            400 +
            170 +
            Math.max(15, ((price - furnitures) * 0.001) | 0)
        );
      },
      notaryFeesViager: (
        price_venal: number,
        furnitures: number,
        montant_usufruit: number,
        montant_duh: number,
        form: Record
      ): number => {
        let PRIX_TAXABLE_VENAL = price_venal - furnitures;
        const VALEUR_USUFRUIT = (PRIX_TAXABLE_VENAL * montant_usufruit) / 100;
        const VALEUR_DUH = (PRIX_TAXABLE_VENAL * montant_duh) / 100;
        const VALEUR_ECONOMIC_USUFRUIT = PRIX_TAXABLE_VENAL - VALEUR_USUFRUIT;
        const VALEUR_ECONOMIC_DUH = PRIX_TAXABLE_VENAL - VALEUR_DUH;
        const DROIT_DEPARTEMENTAL = 0.045;
        const DROIT_COMMUNAL = 0.012;
        const FRAIS_ASSIETTE = DROIT_DEPARTEMENTAL * 0.0237;
        let FRAIS_NOTAIRE =
          Math.min(price_venal, 6500) * 0.0387 +
          Math.max(Math.min(price_venal, 17000) - 6500, 0) * 0.01596 +
          Math.max(Math.min(price_venal, 60000) - 17000, 0) * 0.01064 +
          Math.max(price_venal - 60000, 0) * 0.00799;
        const FRAIS_NOTAIRE_USUFRUIT =
          Math.min(VALEUR_ECONOMIC_USUFRUIT, 6500) * 0.0387 +
          Math.max(Math.min(VALEUR_ECONOMIC_USUFRUIT, 17000) - 6500, 0) * 0.01596 +
          Math.max(Math.min(VALEUR_ECONOMIC_USUFRUIT, 60000) - 17000, 0) * 0.01064 +
          Math.max(VALEUR_ECONOMIC_USUFRUIT - 60000, 0) * 0.00799;
        const FRAIS_NOTAIRE_DUH =
          Math.min(VALEUR_ECONOMIC_DUH, 6500) * 0.0387 +
          Math.max(Math.min(VALEUR_ECONOMIC_DUH, 17000) - 6500, 0) * 0.01596 +
          Math.max(Math.min(VALEUR_ECONOMIC_DUH, 60000) - 17000, 0) * 0.01064 +
          Math.max(VALEUR_ECONOMIC_DUH - 60000, 0) * 0.00799;
        const TVA_NOTAIRE = FRAIS_NOTAIRE * 0.2;
        const CSI_ECONOMIC_USUFRUIT = Math.max(15, VALEUR_ECONOMIC_USUFRUIT * 0.001 || 0);
        const CSI_ECONOMIC_DUH = Math.max(15, VALEUR_ECONOMIC_DUH * 0.001 || 0);
        let CSI_VENAL = Math.max(15, PRIX_TAXABLE_VENAL * 0.001 || 0);
        const DEBOURS = 200;
        const FORMALITES = 850;
        const FRAIS_ANNEXES = 170;
        const USUFRUIT = answers[form.id]?.['viager_statut']?.value === 'viager_usufruit';
        const OCCUPEE =
          answers[form.id]?.['viager_statut']?.value !== 'viager_usufruit' &&
          answers[form.id]?.['viager_occupation']?.value === 'viager_occupation_occupe';
        if (USUFRUIT) {
          PRIX_TAXABLE_VENAL = VALEUR_ECONOMIC_USUFRUIT;
          CSI_VENAL = CSI_ECONOMIC_USUFRUIT;
          FRAIS_NOTAIRE = FRAIS_NOTAIRE_USUFRUIT;
        } else if (OCCUPEE) {
          PRIX_TAXABLE_VENAL = VALEUR_ECONOMIC_DUH;
          CSI_VENAL = CSI_ECONOMIC_DUH;
          FRAIS_NOTAIRE = FRAIS_NOTAIRE_DUH;
        }
        return Math.round(
          PRIX_TAXABLE_VENAL * DROIT_DEPARTEMENTAL +
            PRIX_TAXABLE_VENAL * DROIT_COMMUNAL +
            PRIX_TAXABLE_VENAL * FRAIS_ASSIETTE +
            FRAIS_NOTAIRE +
            TVA_NOTAIRE +
            CSI_VENAL +
            DEBOURS +
            FORMALITES +
            FRAIS_ANNEXES
        );
      },
      notaryFeesViagerVc: (
        price_venal: number,
        price_venal_mixte: number,
        furnitures: number,
        montant_usufruit: number,
        montant_duh: number,
        form: Record
      ): number => {
        let PRIX_TAXABLE_VENAL = price_venal - furnitures;
        const VALEUR_USUFRUIT = (PRIX_TAXABLE_VENAL * montant_usufruit) / 100;
        const VALEUR_DUH = (PRIX_TAXABLE_VENAL * montant_duh) / 100;
        const VALEUR_ECONOMIC_USUFRUIT = PRIX_TAXABLE_VENAL - VALEUR_USUFRUIT;
        const VALEUR_ECONOMIC_DUH = PRIX_TAXABLE_VENAL - VALEUR_DUH;
        const VALEUR_VIAGER_MIXTE = VALEUR_ECONOMIC_DUH + price_venal_mixte;
        const DROIT_DEPARTEMENTAL = 0.045;
        const DROIT_COMMUNAL = 0.012;
        const FRAIS_ASSIETTE = DROIT_DEPARTEMENTAL * 0.0237;
        let FRAIS_NOTAIRE =
          Math.min(price_venal, 6500) * 0.0387 +
          Math.max(Math.min(price_venal, 17000) - 6500, 0) * 0.01596 +
          Math.max(Math.min(price_venal, 60000) - 17000, 0) * 0.01064 +
          Math.max(price_venal - 60000, 0) * 0.00799;
        const FRAIS_NOTAIRE_USUFRUIT =
          Math.min(VALEUR_ECONOMIC_USUFRUIT, 6500) * 0.0387 +
          Math.max(Math.min(VALEUR_ECONOMIC_USUFRUIT, 17000) - 6500, 0) * 0.01596 +
          Math.max(Math.min(VALEUR_ECONOMIC_USUFRUIT, 60000) - 17000, 0) * 0.01064 +
          Math.max(VALEUR_ECONOMIC_USUFRUIT - 60000, 0) * 0.00799;
        const FRAIS_NOTAIRE_DUH =
          Math.min(VALEUR_ECONOMIC_DUH, 6500) * 0.0387 +
          Math.max(Math.min(VALEUR_ECONOMIC_DUH, 17000) - 6500, 0) * 0.01596 +
          Math.max(Math.min(VALEUR_ECONOMIC_DUH, 60000) - 17000, 0) * 0.01064 +
          Math.max(VALEUR_ECONOMIC_DUH - 60000, 0) * 0.00799;
        const FRAIS_NOTAIRE_MIXTE =
          Math.min(VALEUR_VIAGER_MIXTE, 6500) * 0.0387 +
          Math.max(Math.min(VALEUR_VIAGER_MIXTE, 17000) - 6500, 0) * 0.01596 +
          Math.max(Math.min(VALEUR_VIAGER_MIXTE, 60000) - 17000, 0) * 0.01064 +
          Math.max(VALEUR_VIAGER_MIXTE - 60000, 0) * 0.00799;
        const TVA_NOTAIRE = FRAIS_NOTAIRE * 0.2;
        const CSI_ECONOMIC_USUFRUIT = Math.max(15, VALEUR_ECONOMIC_USUFRUIT * 0.001 || 0);
        const CSI_ECONOMIC_DUH = Math.max(15, VALEUR_ECONOMIC_DUH * 0.001 || 0);
        let CSI_VENAL = Math.max(15, PRIX_TAXABLE_VENAL * 0.001 || 0);
        let DEBOURS = 200;
        const FORMALITES = 850;
        const FRAIS_ANNEXES = 170;
        const USUFRUIT = answers[form.id]?.['vc_type_vente']?.value === 'usufruit';
        const OCCUPEE =
          answers[form.id]?.['vc_type_vente']?.value !== 'usufruit' &&
          answers[form.id]?.['vc_type_occupee']?.value === 'oui' &&
          answers[form.id]?.['vc_viager_mixte']?.value !== 'oui';
        const MIXTE =
          answers[form.id]?.['vc_type_vente']?.value === 'viager' &&
          answers[form.id]?.['vc_type_occupee']?.value === 'oui' &&
          answers[form.id]?.['vc_viager_mixte']?.value === 'oui';
        if (USUFRUIT) {
          PRIX_TAXABLE_VENAL = VALEUR_ECONOMIC_USUFRUIT;
          CSI_VENAL = CSI_ECONOMIC_USUFRUIT;
          FRAIS_NOTAIRE = FRAIS_NOTAIRE_USUFRUIT;
        } else if (OCCUPEE) {
          PRIX_TAXABLE_VENAL = VALEUR_ECONOMIC_DUH;
          CSI_VENAL = CSI_ECONOMIC_DUH;
          FRAIS_NOTAIRE = FRAIS_NOTAIRE_DUH;
        } else if (MIXTE) {
          PRIX_TAXABLE_VENAL = VALEUR_VIAGER_MIXTE;
          FRAIS_NOTAIRE = FRAIS_NOTAIRE_MIXTE;
          DEBOURS = 500;
        }
        return Math.round(
          PRIX_TAXABLE_VENAL * DROIT_DEPARTEMENTAL +
            PRIX_TAXABLE_VENAL * DROIT_COMMUNAL +
            PRIX_TAXABLE_VENAL * FRAIS_ASSIETTE +
            FRAIS_NOTAIRE +
            TVA_NOTAIRE +
            CSI_VENAL +
            DEBOURS +
            FORMALITES +
            FRAIS_ANNEXES
        );
      },
      orderBy: orderBy,
      round: Math.round,
      size: size,
      sumBy: sumBy,
      uniqBy: uniqBy
    },
    answers,
    branches: branchesRecordsByRecordIdAndBranchType,
    branding: {
      logo: getPublicFile(brandingLogo)
    },
    extensions: mapValues(extensions, (extension) => ({
      id: extension.extensionId
    })),
    links: linksRecordsByLinkIdAndBranchType,
    operation,
    rawLinks: linksByLinkIdAndBranchType,
    records,
    staticMetadata: {
      formeSociale,
      pays,
      regimeMatrimonial
    }
  };
};

export function getTracfinAddressLabel(personne: AnswerDict) {
  const country = getAddressCountry(personne);
  if (country == null) {
    return 'Domicile dans un Pays Tiers ou non identifié sur une liste : VIGILANCE RENFORCÉE';
  }
  return (
    ADDRESS_TRACFIN_LABEL[COUNTRY_TRACFIN[country]] ??
    'Domicile dans un Pays Tiers ou non identifié sur une liste : VIGILANCE RENFORCÉE'
  );
}

function getAddressCountry(personne: AnswerDict) {
  const manualCountry: string = personne?.['adresse']?.value?.country;
  if (manualCountry != null) {
    return manualCountry;
  }

  const countries = getMatchingCountries(personne);

  if (countries.length === 1) {
    return countries[0];
  }

  /**
   * In some cases, the country is not defined even for a Google address.
   * In this case, we extract the country from the address.
   */
  const address: string | undefined = personne?.['adresse']?.value?.address;
  if (address != null) {
    return extractCountry(address);
  }

  /**
   * In some cases, such as custom interconnections, the address is not defined.
   * In this case, we extract the country from the formatted address.
   */
  const formattedAddress: string = personne?.['adresse']?.value?.formattedAddress;
  if (formattedAddress != null) {
    return extractCountry(formattedAddress);
  }

  return undefined;
}

function extractCountry(address: string): string {
  const segments = address.split(',');
  const country = segments[segments.length - 1].trim();

  return country.replace(/\(\d+\)$/, '').trim();
}

function getMatchingCountries(personne: AnswerDict): string[] {
  const formattedAddress: string | undefined = personne?.['adresse']?.value?.formattedAddress;
  if (formattedAddress != null) {
    return detectCountries(formattedAddress);
  }

  return [];
}

function detectCountries(address: string): string[] {
  const countryNames = Object.values(pays).map((country) => country.nom);

  const countryMap = new Map(countryNames.map((name) => [name.toLowerCase(), name]));

  const regex = new RegExp(`\\b(${countryNames.join('|')})\\b`, 'gi');

  const matches = address.match(regex);

  return matches
    ? [...new Set(matches.map((match) => countryMap.get(match.toLowerCase())))].filter((c) => c != null)
    : [];
}
