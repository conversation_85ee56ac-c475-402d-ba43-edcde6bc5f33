import { AppAsyncThunk } from '@mynotary/frontend/shared/redux-util';
import {
  setIsValidating,
  setRedactionTab,
  changeRedactionView,
  updateContractValidationFiles,
  generateContractPdf
} from '../index';
import { RedactionTab, RedactionView } from '@mynotary/frontend/legals/core';

export const validateContract =
  (operationId: number, contractId: number): AppAsyncThunk<void> =>
  async (dispatch) => {
    dispatch(setIsValidating(true));
    const contractFile = await dispatch(generateContractPdf(contractId, false));
    dispatch(updateContractValidationFiles(operationId, contractId, contractFile.id)).catch(console.error);
    dispatch(setRedactionTab(RedactionTab.INFORMATION));
    /** Force to switch display mode to preview and form since we can't display validation + other mode in other contract view   */
    dispatch(changeRedactionView(RedactionView.CONTRACT_VALIDATED));
    dispatch(setIsValidating(false));
  };
