import './operation-contract-file-popin.scss';
import { useEffect, useState } from 'react';
import {
  getShareFileConfig,
  OperationFileEmptyState,
  OperationShareFilesCount,
  OperationShareFilePopinHeader
} from '../operation-file-ui/operation-file-ui';
import { CategorySeparator, FooterLayout, FullSizePopin, MnButton } from '@mynotary/frontend/shared/ui';
import { useToggle } from '@mynotary/frontend/shared/util';
import {
  recordFileLabelsToSearch,
  handleFileSelectionProps,
  OperationAnnexeFolderInfo,
  OperationFileType,
  OperationFileInfo,
  OperationFileToggleType,
  operationFilesLabels,
  FileCategoryEnum
} from '@mynotary/frontend/operation-files/core';
import { groupBy, isBoolean, isEmpty } from 'lodash';
import { useSelector } from 'react-redux';
import {
  AnnexedDocument,
  AnnexedDocumentState,
  generateContractPdf,
  selectAnnexedDocumentByContract,
  selectContract,
  selectContractRecordDocuments,
  selectInterpolatedContractFrame
} from '@mynotary/frontend/legals/api';
import { selectAnnexedOperationFiles, selectDraftContractFile } from '@mynotary/frontend/operation-files/store';
import { OperationFolder } from '../operation-file-tiles/operation-folder';
import { OperationFileTiles } from '../operation-file-tiles/operation-file-tiles';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { findFiles } from '@mynotary/frontend/files/api';
import { ContractFrame } from '@mynotary/frontend/legals/api';
import { filterFilesBySearch } from '@mynotary/frontend/drives/api';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';
import { Folder, FolderFileInfo } from '@mynotary/crossplatform/files/api';

const VARIANT = 'SHARE';

const info = getShareFileConfig(VARIANT);

interface OperationFilesPopinProps {
  contractId: number;
  isLoading?: boolean;
  onClose?: () => void;
  onShareProject?: (args: Folder) => void;
}

export const OperationContractFilePopin = ({
  contractId,
  isLoading,
  onClose,
  onShareProject
}: OperationFilesPopinProps) => {
  const {
    annexeFolders,
    areAllFilesSelected,
    areItemsSelected,
    changeSearch,
    contractFile,
    handleFileSelection,
    search,
    selectedAnnexes,
    selectedCount,
    toggleSelection
  } = useContractProjetFiles(contractId);
  const dispatch = useAsyncDispatch();

  const handleShare = async () => {
    const annexesfolder: Folder[] = annexeFolders.map((folder) => {
      const files = folder.files
        .filter((file) => file.fileId && selectedAnnexes.find((i) => i.fileId === file.fileId))
        .map((file) => {
          assertNotNull(file.fileId, 'fileId should not be null');
          return {
            id: file.fileId,
            name: file.fileName
          };
        });

      return {
        files,
        name: folder.folder.label
      };
    });

    const filteredFolders = annexesfolder.filter((folder) => folder?.files && folder?.files.length > 0);

    let contractFolderFileInfo: FolderFileInfo | undefined = undefined;

    const currentContract = selectedAnnexes.find(
      (annexe) =>
        annexe.type === OperationFileType.CONTRACT_FILE || annexe.type === OperationFileType.REDACTION_CONTRACT_FILE
    );

    if (currentContract != null) {
      let fileId = currentContract.fileId;

      if (currentContract.type === OperationFileType.REDACTION_CONTRACT_FILE) {
        const contractFileInfo = await dispatch(generateContractPdf(contractId, true));
        fileId = contractFileInfo.id;
      }

      assertNotNull(fileId, 'fileId should not be null');
      contractFolderFileInfo = {
        id: fileId,
        name: currentContract.fileName
      };
    }

    onShareProject?.({
      files: contractFolderFileInfo ? [contractFolderFileInfo] : [],
      folders: filteredFolders,
      name: 'Projet de contrat'
    } satisfies Folder);
  };

  const hasFiles = !isEmpty(annexeFolders) || contractFile != null;

  return (
    <FullSizePopin onClose={onClose}>
      <div className='operation-contract-file-popin'>
        <OperationShareFilePopinHeader
          areAllFilesSelected={areAllFilesSelected}
          hasFiles={hasFiles}
          onChangeSearch={changeSearch}
          onToggleSelection={toggleSelection}
          search={search}
          title={'les fichiers à partager'}
        />
        <div className='ocfp-container'>
          {!hasFiles && <OperationFileEmptyState variant={VARIANT} />}
          {annexeFolders.length > 0 && (
            <>
              <CategorySeparator label={operationFilesLabels[FileCategoryEnum.RECORD]} />
              <div className='ocfp-wrapper'>
                {annexeFolders.map((item) => (
                  <OperationFolder
                    areItemsSelected={areItemsSelected}
                    handleFileSelection={handleFileSelection}
                    key={item.folder.id}
                    operationFolder={item}
                  >
                    <>
                      {item.files.map((operationFile) => (
                        <OperationFileTiles
                          areItemsSelected={areItemsSelected}
                          handleFileSelection={handleFileSelection}
                          key={operationFile.id}
                          operationFile={operationFile}
                        />
                      ))}
                    </>
                  </OperationFolder>
                ))}
              </div>
            </>
          )}
          {contractFile != null && (
            <>
              <CategorySeparator label={operationFilesLabels[FileCategoryEnum.CONTRACTS]} />
              <div className='ocfp-wrapper'>
                <OperationFileTiles
                  areItemsSelected={areItemsSelected}
                  handleFileSelection={handleFileSelection}
                  key={contractFile.contractId}
                  operationFile={contractFile}
                />
              </div>
            </>
          )}
        </div>
        <FooterLayout.Container>
          <FooterLayout.RightSide>
            <OperationShareFilesCount count={selectedCount} />
            <MnButton
              disabled={isLoading || selectedCount === 0}
              icon={info.icon}
              label={info.label}
              onClick={handleShare}
              size='medium'
              testId='operation-contract-file-popin-share'
            />
          </FooterLayout.RightSide>
        </FooterLayout.Container>
      </div>
    </FullSizePopin>
  );
};

/**
 * Hook to manage the selection of the files to share on the contract
 * We need to get the current contract with the status REDACTION or VALIDATION_PENDING or VALIDATED
 * and also the current records
 */
function useContractProjetFiles(contractId: number) {
  const dispatch = useAsyncDispatch();
  const [search, setSearch] = useState<string>('');

  const [hasFetchFiles, setHasFetchFiles] = useState(false);

  const { areItemsSelected, handleToggleItems, selectedItems, selectedItemsTotal } =
    useToggle<OperationFileToggleType>();

  const annexedDocuments = useSelector(selectAnnexedDocumentByContract(contractId));

  const contract = useSelector(selectContract(contractId));

  const contractFrame = useSelector(selectInterpolatedContractFrame(contract.operation.id, contractId));

  const contractRecordDocuments = useSelector(selectContractRecordDocuments(contract.operation.id, contractId));

  const sortedAnnexeFolders = useSelector(selectAnnexedOperationFiles(contract.operation.id, contract.id));
  const contractFile = useSelector(selectDraftContractFile(contractId));

  useEffect(() => {
    dispatch(findFiles(contractRecordDocuments.fileIds)).then(() => {
      setHasFetchFiles(true);
    });
  }, [dispatch, contractRecordDocuments]);

  /**
   * By default, the current contract and the annexed documents are selected
   */
  useEffect(() => {
    if (!hasFetchFiles) {
      return;
    }
    const defaultSelections = getDefaultSelection({ annexedDocuments, contractFrame, sortedAnnexeFolders });

    if (contractFile != null) {
      defaultSelections.push(convertToSelection(contractFile));
    }

    handleToggleItems(defaultSelections);
  }, [annexedDocuments, handleToggleItems, sortedAnnexeFolders, contractFrame, contractFile, hasFetchFiles]);

  const handleFileSelection = ({ defaultSelection, files }: handleFileSelectionProps) => {
    const selections = files.map((file) => ({
      ...convertToSelection(file),
      selected: isBoolean(defaultSelection) ? defaultSelection : !areItemsSelected([file.id])
    }));
    handleToggleItems(selections);
  };

  const filteredContractFile =
    contractFile != null && filterFilesBySearch({ labelsToSearch: [contractFile.fileName], search })
      ? contractFile
      : null;

  const allOperationFiles: OperationFileInfo[] = [
    ...sortedAnnexeFolders.flatMap((folder) => folder.files),
    ...(filteredContractFile != null ? [filteredContractFile] : [])
  ];

  const areAllFilesSelected = selectedItemsTotal === allOperationFiles.length && allOperationFiles.length > 0;

  const toggleSelection = () => {
    handleFileSelection({ defaultSelection: !areAllFilesSelected, files: allOperationFiles });
  };

  return {
    annexeFolders: filterOperationAnnexeFolderBySearch({ folders: sortedAnnexeFolders, search }),
    areAllFilesSelected,
    areItemsSelected,
    changeSearch: (value?: string) => setSearch(value ?? ''),
    contractFile: filteredContractFile,
    handleFileSelection,
    search,
    selectedAnnexes: selectedItems,
    selectedCount: selectedItemsTotal,
    toggleSelection
  };
}

function getDefaultSelection({
  annexedDocuments,
  contractFrame,
  sortedAnnexeFolders
}: {
  annexedDocuments: AnnexedDocument[];
  contractFrame: ContractFrame | null;
  sortedAnnexeFolders: OperationAnnexeFolderInfo[];
}) {
  const annexedDocumentByRecordId = groupBy(annexedDocuments, (recordDocument) => recordDocument.recordId);

  return sortedAnnexeFolders.flatMap((record) =>
    (record.files ?? [])
      .filter(
        (operationFileTileType) =>
          operationFileTileType.type === OperationFileType.RECORD_FILE &&
          (annexedDocumentByRecordId[record.folder.recordId]?.find(
            (doc) => doc.documentId === operationFileTileType.documentId && doc.state === AnnexedDocumentState.ADD
          ) != null ||
            contractFrame?.questions[record.folder.recordId]?.[operationFileTileType.documentId])
      )
      .map((file) => convertToSelection(file))
  );
}

function convertToSelection(file: OperationFileInfo): OperationFileToggleType {
  if (file.type === OperationFileType.REDACTION_CONTRACT_FILE) {
    return {
      contractId: file.contractId,
      fileId: file.fileId,
      fileName: file.fileName,
      id: file.id,
      selected: true,
      type: file.type
    };
  }

  return {
    fileId: file.fileId,
    fileName: file.fileName,
    id: file.id,
    selected: true,
    type: file.type
  };
}

function filterOperationAnnexeFolderBySearch({
  folders,
  search
}: {
  folders: OperationAnnexeFolderInfo[];
  search: string;
}): OperationAnnexeFolderInfo[] {
  if (!search) {
    return folders;
  }

  return folders
    .map((folder) => {
      const matchedFiles = folder.files.filter(
        (operationFileInfo) =>
          operationFileInfo.type === OperationFileType.RECORD_FILE &&
          filterFilesBySearch({
            labelsToSearch: recordFileLabelsToSearch({
              folderLabel: folder.folder.label,
              recordFile: operationFileInfo
            }),
            search
          })
      );
      if (matchedFiles.length > 0) {
        return { ...folder, files: matchedFiles };
      }
      return null;
    })
    .filter((folder) => folder !== null);
}
