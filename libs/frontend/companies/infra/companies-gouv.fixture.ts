export const apiCompanyListResponse = {
  page: 1,
  per_page: 10,
  results: [
    {
      activite_principale: '66.30Z',
      categorie_entreprise: 'PME',
      complements: {
        collectivite_territoriale: null,
        convention_collective_renseignee: false,
        est_entrepreneur_individuel: false,
        est_entrepreneur_spectacle: false,
        est_ess: false,
        est_finess: false,
        est_rge: false,
        est_uai: false,
        identifiant_association: null
      },
      date_creation: '2017-12-31',
      date_mise_a_jour: null,
      dirigeants: [
        {
          annee_de_naissance: '1989',
          nom: 'boyer',
          prenoms: 'sacha',
          qualite: 'Président',
          type_dirigeant: 'personne physique'
        }
      ],
      etat_administratif: 'A',
      nature_juridique: '5710',
      nom_complet: 'mynotary investissement',
      nom_raison_sociale: 'MYNOTARY INVESTISSEMENT',
      nombre_etablissements: 4,
      nombre_etablissements_ouverts: 1,
      section_activite_principale: 'K',
      siege: {
        activite_principale: '66.30Z',
        activite_principale_registre_metier: null,
        adresse_complete: '49 RUE PRESIDENT EDOUARD HERRIOT 69382 LYON 2EME',
        adresse_complete_secondaire: null,
        cedex: null,
        code_pays_etranger: null,
        code_postal: '69002',
        commune: '69382',
        complement_adresse: null,
        date_creation: '2022-02-01',
        date_debut_activite: '2022-02-01',
        departement: '69',
        distribution_speciale: null,
        etat_administratif: 'A',
        geo_id: '69382_5720_00049',
        indice_repetition: null,
        latitude: '45.762814',
        libelle_cedex: null,
        libelle_commune: 'LYON 2EME',
        libelle_commune_etranger: null,
        libelle_pays_etranger: null,
        libelle_voie: 'PRESIDENT EDOUARD HERRIOT',
        longitude: '4.834263',
        numero_voie: '49',
        siret: '*********00044',
        tranche_effectif_salarie: null,
        type_voie: 'RUE'
      },
      siren: '*********',
      tranche_effectif_salarie: null
    }
  ],
  total_pages: 1,
  total_results: 1
};

export const apiForeignCompanyResponse = {
  page: 1,
  per_page: 10,
  results: [
    {
      activite_principale: '82.99Z',
      categorie_entreprise: 'PME',
      complements: {
        collectivite_territoriale: null,
        convention_collective_renseignee: false,
        est_entrepreneur_individuel: false,
        est_entrepreneur_spectacle: false,
        est_ess: false,
        est_finess: false,
        est_rge: false,
        est_uai: false,
        identifiant_association: null
      },
      date_creation: '2008-04-01',
      date_mise_a_jour: '2015-09-09T08:25:59',
      dirigeants: [],
      etat_administratif: 'A',
      matching_etablissements: [],
      nature_juridique: '3220',
      nom_complet: 'ing belgique sa',
      nom_raison_sociale: 'ING BELGIQUE SA',
      nombre_etablissements: 1,
      nombre_etablissements_ouverts: 1,
      section_activite_principale: 'N',
      siege: {
        activite_principale: '82.99Z',
        activite_principale_registre_metier: null,
        adresse: '24 AV MARNIX B 1000 BRUXELLES BELGIQUE',
        cedex: null,
        code_pays_etranger: '99131',
        code_postal: null,
        commune: null,
        complement_adresse: null,
        coordonnees: null,
        date_creation: '2008-04-01',
        date_debut_activite: '2014-11-01',
        departement: null,
        distribution_speciale: null,
        est_siege: true,
        etat_administratif: 'A',
        geo_adresse: null,
        geo_id: null,
        indice_repetition: null,
        latitude: null,
        libelle_cedex: null,
        libelle_commune: null,
        libelle_commune_etranger: 'B 1000 BRUXELLES',
        libelle_pays_etranger: 'BELGIQUE',
        libelle_voie: 'MARNIX',
        liste_enseignes: null,
        liste_finess: null,
        liste_idcc: null,
        liste_rge: null,
        liste_uai: null,
        longitude: null,
        nom_commercial: null,
        numero_voie: '24',
        siret: '*********00018',
        tranche_effectif_salarie: null,
        type_voie: 'AV'
      },
      siren: '*********',
      tranche_effectif_salarie: null
    }
  ],
  total_pages: 1,
  total_results: 1
};

export const apiCompanyEmptyResponse = {
  page: 1,
  per_page: 10,
  results: [],
  total_pages: 0,
  total_results: 0
};
