import './operation-link-creation.scss';
import { useSelector } from 'react-redux';
import {
  getBranchTemplate,
  isLegacyNewRecord,
  NewMnBranch,
  NewRecord,
  postOperationLinkBranch,
  Record,
  selectDefaultBranch,
  selectLegalComponentTemplate,
  selectOperation,
  selectRecordsIdsFromLinkId,
  selectTemplatesFeatures
} from '@mynotary/frontend/legals/api';
import { classNames, MnProps } from '@mynotary/frontend/shared/util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { LegalLinkBranchRecordConfig, LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';
import { RecordCreateOrSearch } from '@mynotary/frontend/legal-record-creations/api';

interface OperationLinkCreationProps extends MnProps {
  contractId?: number;
  defaultAuthorizedTypes?: string[][];
  linkId: number;
  onValidate?: (record: Record) => void;
  operationId: number;
}

export const MnOperationLinkCreation = ({
  className,
  contractId,
  defaultAuthorizedTypes,
  linkId,
  onValidate,
  operationId
}: OperationLinkCreationProps) => {
  const dispatch = useAsyncDispatch();
  const templates = useSelector(selectTemplatesFeatures);
  const operation = useSelector(selectOperation(operationId));
  const linkTemplate = useSelector(selectLegalComponentTemplate<LegalLinkTemplate>(linkId));
  const branchTemplate = getBranchTemplate(templates[operation.template.id], linkTemplate);
  const defaultBranch = useSelector(selectDefaultBranch(operation.id, linkId, contractId));
  const recordIds = useSelector(selectRecordsIdsFromLinkId(linkId, operation.id, contractId));

  const handleBranchCreation = (record: Record | NewRecord): void => {
    const newBranch = { ...defaultBranch } as NewMnBranch;
    if (isLegacyNewRecord(record)) {
      newBranch.to = {
        answer: record.answer,
        template: {
          id: record.template.id
        },
        type: 'RECORD'
      };
    } else {
      newBranch.to = {
        id: record.id,
        type: 'RECORD'
      };
    }

    dispatch(postOperationLinkBranch(operation.id, linkId, newBranch)).then((branch) => {
      onValidate?.(branch.to as Record);
    });
  };

  if (!branchTemplate) {
    return null;
  }

  return (
    <div className={classNames(className, 'mn-operation-link-creation')}>
      <RecordCreateOrSearch
        authorizedTypes={defaultAuthorizedTypes ?? (branchTemplate.to as LegalLinkBranchRecordConfig).specificTypes}
        filteredRecordIds={recordIds}
        label={`Ajouter ${linkTemplate?.config.display.branches[branchTemplate.type].labelWithArticle ?? 'une fiche'}`}
        onValidateCreation={handleBranchCreation}
        onValidateSearch={handleBranchCreation}
      />
    </div>
  );
};
