import { memoize } from 'lodash';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { createSelector } from '@reduxjs/toolkit';
import { hasPermission } from '@mynotary/frontend/roles/core';
import { selectConnectedUserRole } from './select-connected-user-role';

export const selectPermission = memoize(
  (type: PermissionType, entityType: string, subEntityType?: string) =>
    createSelector(selectConnectedUserRole, (role) => {
      return hasPermission(type, role, entityType, subEntityType);
    }),
  (type: string, entityType: string, subEntityType?: string) => `${type}${entityType}${subEntityType}`
);
