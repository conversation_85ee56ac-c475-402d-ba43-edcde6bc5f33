import styles from './requisition-merge-popin.module.scss';
import { Mn<PERSON><PERSON><PERSON>, Mn<PERSON><PERSON><PERSON>, MnPopinFooter } from '@mynotary/frontend/shared/ui';
import { Form, FormMode, mergeAndCopyAnswer } from '@mynotary/frontend/legals/api';
import { FormChoice, FormQuestion, SelectFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { useState } from 'react';
import { RequisitionBff } from '@mynotary/crossplatform/bff-portalys/api';
import { setInfoMessage } from '@mynotary/frontend/snackbars/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { useMutationRequisitionMerge, useQueryRequisitionComplements } from '@mynotary/frontend/teleactes/core';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';
import { selectActiveTokenInfo } from '@mynotary/frontend/auth/api';
import { useSelector } from 'react-redux';

type RequisitionMergePopinProps = {
  onClose: () => void;
  opened: boolean;
  requisition: RequisitionBff;
};

export function RequisitionMergePopin({ onClose, opened, requisition }: RequisitionMergePopinProps) {
  const dispatch = useAsyncDispatch();
  const auth = useSelector(selectActiveTokenInfo);
  const [mergeAnfAnswers, setMergeAnfAnswers] = useState<AnswerDict>({});
  assertNotNull(requisition.id, 'requisitionId');
  const queryComplements = useQueryRequisitionComplements(requisition.id);
  const mutationMerge = useMutationRequisitionMerge();

  function handleMerge() {
    assertNotNull(auth, 'auth');
    assertNotNull(requisition.id, 'requisitionId');

    mutationMerge.mutate({
      complementFileIds: mergeAnfAnswers.complements.value,
      planeteDossierId: requisition.id,
      userId: auth.userId
    });
    dispatch(setInfoMessage("Fusion en cours, l'état-réponse complété sera disponible dans quelques instants."));
    onClose();
  }

  function getForm(choices: FormChoice[] = []): Array<FormQuestion | SelectFormQuestion> {
    return [
      {
        choices,
        id: 'complements',
        label: 'Compléments de réquisition',
        multiple: true,
        type: 'SELECT'
      } as SelectFormQuestion
    ];
  }

  function handleChange(newAnswers: AnswerDict) {
    setMergeAnfAnswers((oldAnswers) => {
      return mergeAndCopyAnswer(oldAnswers, newAnswers);
    });
  }

  return (
    <MnPopin buttonToClose={true} clickOutsideToClose={true} closable={true} onClose={onClose} opened={opened}>
      <div className={styles.content}>
        <div className={styles.title}>Fusionner des réquisitions</div>
        <div className={styles.text}>
          Voici les compléments issus de vos recherches dans ANF Stock.
          <br />
          Sélectionnez celui/ceux à prendre en compte pour créer un état-réponse complété.
        </div>
        <Form
          answer={mergeAnfAnswers}
          debounce={false}
          formMode={FormMode.SMALL}
          forms={getForm(queryComplements.data)}
          onChange={handleChange}
        />
      </div>
      <MnPopinFooter>
        <MnButton label='Créer état-réponse complété' onClick={() => handleMerge()} />
      </MnPopinFooter>
    </MnPopin>
  );
}
