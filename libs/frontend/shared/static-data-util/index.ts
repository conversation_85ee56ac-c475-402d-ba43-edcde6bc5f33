export { regimeMatrimonial } from './regime-matrimonial';
export { formeSociale } from './forme-sociale';
export { WEEKDAYS_SHORT, WEEKDAYS_LONG, MONTHS } from './date';
export * from './special-character';
export * from './wordings';

export const ZIP_ASSINISSEMENT_JO = [
  '91200',
  '91210',
  '91260',
  '91230',
  '91420',
  '91550',
  '91600',
  '91270',
  '92160',
  '92340',
  '92290',
  '93470',
  '93220',
  '93460',
  '93340',
  '93370',
  '93100',
  '93360',
  '93330',
  '93160',
  '93110',
  '93250',
  '94480',
  '94140',
  '94110',
  '94470',
  '94380',
  '94230',
  '94500',
  '94220',
  '94430',
  '94550',
  '94600',
  '94120',
  '94260',
  '94250',
  '94200',
  '94340',
  '94240',
  '94510',
  '94270',
  '94170',
  '94420',
  '94450',
  '94700',
  '94440',
  '94130',
  '94880',
  '94310',
  '94490',
  '94520',
  '94150',
  '94160',
  '94100',
  '94210',
  '94410',
  '94370',
  '94320',
  '94460',
  '94800',
  '94290',
  '94190',
  '94350',
  '94300',
  '94400'
];

export const LISTE_DEPARTEMENT_HAUSSE = [
  '02',
  '04',
  '08',
  '09',
  '11',
  '12',
  '13',
  '14',
  '15',
  '17',
  '18',
  '19',
  '20',
  '21',
  '22',
  '23',
  '24',
  '25',
  '28',
  '29',
  '30',
  '31',
  '32',
  '33',
  '34',
  '35',
  '39',
  '41',
  '42',
  '43',
  '44',
  '45',
  '47',
  '49',
  '50',
  '51',
  '52',
  '53',
  '54',
  '55',
  '56',
  '57',
  '58',
  '59',
  '62',
  '63',
  '64',
  '66',
  '67',
  '68',
  '69',
  '70',
  '74',
  '72',
  '75',
  '76',
  '77',
  '78',
  '80',
  '81',
  '83',
  '84',
  '85',
  '86',
  '87',
  '88',
  '89',
  '91',
  '92',
  '93',
  '94'
];
export const LISTE_DEPARTEMENT_HAUSSE_MAI = ['99'];
export const TVAS = {
  HT_TO_TTC_20: 'HT_TO_TTC_20',
  HT_TO_TTC_5: 'HT_TO_TTC_5',
  HT_TO_TVA_20: 'HT_TO_TVA_20',
  HT_TO_TVA_5: 'HT_TO_TVA_5',
  TTC_TO_HT_20: 'TTC_TO_HT_20',
  TTC_TO_HT_5: 'TTC_TO_HT_5',
  TTC_TO_TVA_20: (price: number) => {
    const TTC_TO_HT_20 = price / 1.2;
    return price - TTC_TO_HT_20;
  },
  TTC_TO_TVA_5: 'TTC_TO_TVA_5'
};
export const COUNTRY_UE = [
  'DE',
  'Allemagne',
  'BE',
  'Belgique',
  'AT',
  'Autriche',
  'BG',
  'Bulgarie',
  'CY',
  'Chypre',
  'DK',
  'Danemark',
  'ES',
  'Espagne',
  'EE',
  'Estonie',
  'FI',
  'Finlande',
  'GR',
  'Grèce',
  'HU',
  'Hongrie',
  'IE',
  'Irlande',
  'IT',
  'Italie',
  'LV',
  'Lituanie',
  'LT',
  'Lettonie',
  'LU',
  'Luxembourg',
  'MT',
  'Malte',
  'NL',
  'Pays-Bas',
  'PL',
  'Pologne',
  'PT',
  'Portugal',
  'CZ',
  'Tchéquie',
  'RO',
  'Roumanie',
  'SK',
  'Slovaquie',
  'SE',
  'Suède'
];
export const COUNTRY_FR = ['FR', 'France'];
export const COUNTRY_LISTE_UE = [
  'Anguilla',
  'AI',
  'Fidji',
  'FJ',
  'GU',
  'Guam',
  'JO',
  'Jordanie',
  'UG',
  'Ouganda',
  'PA',
  'Panama',
  'PW',
  'Palaos',
  'RU',
  'Russie',
  'AS',
  'Samoa américaines',
  'WS',
  'Samoa',
  'TT',
  'Trinité-et-Tobago',
  'Îles Vierges des États-Unis',
  'VI',
  'VU',
  'Vanuatu'
];
export const COUNTRY_BLACK_LIST = ['KP', 'Corée du nord', 'MM', 'Myanmar', 'Birmanie', 'IR', 'Iran'];
export const COUNTRY_GREY_LIST = [
  'DZ',
  'Algérie',
  'AO',
  'Angola',
  'ZA',
  'Afrique Du Sud',
  'BF',
  'Burkina Faso',
  'CM',
  'Cameroun',
  'CI',
  'Côte Ivoire',
  'HR',
  'Croatie',
  'CD',
  'Congo',
  'HT',
  'Haïti',
  'KE',
  'Kenya',
  'LA',
  'Laos',
  'LB',
  'Liban',
  'MC',
  'Monaco',
  'ML',
  'Mali',
  'MZ',
  'Mozambique',
  'NA',
  'Namibie',
  'NG',
  'Nigeria',
  'NP',
  'Népal',
  'SN',
  'Sénégal',
  'SS',
  'Soudan Du Sud',
  'SY',
  'Syrie',
  'TZ',
  'Tanzanie',
  'VE',
  'Venezuela',
  'VN',
  'Vietnam',
  'YE',
  'Yémen'
];
export const COUNTRY_TRACFIN: Record<string, string> = {
  'AE': 'COUNTRY_LISTE_UE',
  'AF': 'COUNTRY_LISTE_UE',
  'AI': 'COUNTRY_LISTE_UE',
  'AO': 'COUNTRY_LISTE_UE',
  'AS': 'COUNTRY_LISTE_UE',
  'AT': 'COUNTRY_UE',
  'Afghanistan': 'COUNTRY_LISTE_UE',
  'Afrique Du Sud': 'COUNTRY_LISTE_GRISE',
  'Algérie': 'COUNTRY_LISTE_GRISE',
  'Allemagne': 'COUNTRY_UE',
  'Angola': 'COUNTRY_LISTE_UE',
  'Anguilla': 'COUNTRY_LISTE_UE',
  'Autriche': 'COUNTRY_UE',
  'BB': 'COUNTRY_LISTE_UE',
  'BE': 'COUNTRY_UE',
  'BF': 'COUNTRY_LISTE_GRISE',
  'BG': 'COUNTRY_LISTE_GRISE',
  'Barbade': 'COUNTRY_LISTE_UE',
  'Belgique': 'COUNTRY_UE',
  'Burkina Faso': 'COUNTRY_LISTE_GRISE',
  'CD': 'COUNTRY_LISTE_GRISE',
  'CI': 'COUNTRY_UE',
  'CM': 'COUNTRY_LISTE_GRISE',
  'CY': 'COUNTRY_UE',
  'CZ': 'COUNTRY_UE',
  'Cameroun': 'COUNTRY_LISTE_GRISE',
  'Chypre': 'COUNTRY_UE',
  'Congo, La République Démocratique Du': 'COUNTRY_LISTE_GRISE',
  'Corée du Nord': 'COUNTRY_LISTE_NOIRE',
  'Croatie': 'COUNTRY_LISTE_GRISE',
  'Côte Ivoire': 'COUNTRY_UE',
  'DE': 'COUNTRY_UE',
  'DK': 'COUNTRY_UE',
  'DZ': 'COUNTRY_LISTE_GRISE',
  'Danemark': 'COUNTRY_UE',
  'EE': 'COUNTRY_UE',
  'ES': 'COUNTRY_UE',
  'Espagne': 'COUNTRY_UE',
  'Estonie': 'COUNTRY_UE',
  'FI': 'COUNTRY_UE',
  'FJ': 'COUNTRY_LISTE_UE',
  'FR': 'COUNTRY_FR',
  'Fidji': 'COUNTRY_LISTE_UE',
  'Finlande': 'COUNTRY_UE',
  'France': 'COUNTRY_FR',
  'GI': 'COUNTRY_LISTE_UE',
  'GR': 'COUNTRY_UE',
  'GU': 'COUNTRY_LISTE_UE',
  'Gibraltar': 'COUNTRY_LISTE_UE',
  'Grèce': 'COUNTRY_UE',
  'Guam': 'COUNTRY_LISTE_UE',
  'HR': 'COUNTRY_LISTE_GRISE',
  'HT': 'COUNTRY_LISTE_GRISE',
  'HU': 'COUNTRY_UE',
  'Haïti': 'COUNTRY_LISTE_GRISE',
  'Hongrie': 'COUNTRY_UE',
  'IE': 'COUNTRY_UE',
  'IR': 'COUNTRY_LISTE_NOIRE',
  'IT': 'COUNTRY_UE',
  'Ile Caïmans': 'COUNTRY_LISTE_UE',
  'Iran': 'COUNTRY_LISTE_NOIRE',
  'Irlande': 'COUNTRY_UE',
  'Italie': 'COUNTRY_UE',
  'JM': 'COUNTRY_LISTE_GRISE',
  'JO': 'COUNTRY_LISTE_UE',
  'Jamaïque': 'COUNTRY_LISTE_GRISE',
  'Jordanie': 'COUNTRY_LISTE_UE',
  'KE': 'COUNTRY_LISTE_GRISE',
  'KP': 'COUNTRY_LISTE_NOIRE',
  'KY': 'COUNTRY_LISTE_UE',
  'Kenya': 'COUNTRY_LISTE_GRISE',
  'LA': 'COUNTRY_LISTE_GRISE',
  'LB': 'COUNTRY_UE',
  'LT': 'COUNTRY_UE',
  'LU': 'COUNTRY_UE',
  'LV': 'COUNTRY_UE',
  'Laos': 'COUNTRY_LISTE_GRISE',
  'Lettonie': 'COUNTRY_UE',
  'Liban': 'COUNTRY_UE',
  'Lituanie': 'COUNTRY_UE',
  'Luxembourg': 'COUNTRY_UE',
  'ML': 'COUNTRY_LISTE_GRISE',
  'MM': 'COUNTRY_LISTE_NOIRE',
  'MT': 'COUNTRY_UE',
  'MZ': 'COUNTRY_LISTE_GRISE',
  'Mali': 'COUNTRY_LISTE_GRISE',
  'Malte': 'COUNTRY_UE',
  'Mozambique': 'COUNTRY_LISTE_GRISE',
  'Myanmar': 'COUNTRY_LISTE_NOIRE',
  'NA': 'COUNTRY_LISTE_GRISE',
  'NG': 'COUNTRY_LISTE_GRISE',
  'NL': 'COUNTRY_UE',
  'NP': 'COUNTRY_LISTE_GRISE',
  'Namibie': 'COUNTRY_LISTE_GRISE',
  'Nigeria': 'COUNTRY_LISTE_GRISE',
  'Népal': 'COUNTRY_LISTE_GRISE',
  'Ouganda': 'COUNTRY_LISTE_UE',
  'PA': 'COUNTRY_LISTE_UE',
  'PH': 'COUNTRY_LISTE_GRISE',
  'PL': 'COUNTRY_UE',
  'PT': 'COUNTRY_UE',
  'PW': 'COUNTRY_LISTE_UE',
  'Palaos': 'COUNTRY_LISTE_UE',
  'Panama': 'COUNTRY_LISTE_UE',
  'Pays-Bas': 'COUNTRY_UE',
  'Philippines': 'COUNTRY_LISTE_GRISE',
  'Pologne': 'COUNTRY_UE',
  'Portugal': 'COUNTRY_UE',
  'RO': 'COUNTRY_UE',
  'RU': 'COUNTRY_LISTE_UE',
  'Roumanie': 'COUNTRY_UE',
  'Russie': 'COUNTRY_LISTE_UE',
  'SE': 'COUNTRY_UE',
  'SK': 'COUNTRY_UE',
  'SN': 'COUNTRY_LISTE_GRISE',
  'SS': 'COUNTRY_LISTE_GRISE',
  'SY': 'COUNTRY_LISTE_GRISE',
  'Samoa': 'COUNTRY_LISTE_UE',
  'Samoa américaines': 'COUNTRY_LISTE_UE',
  'Slovaquie': 'COUNTRY_UE',
  'Soudan Du Sud': 'COUNTRY_LISTE_GRISE',
  'Suède': 'COUNTRY_UE',
  'Syrie': 'COUNTRY_LISTE_GRISE',
  'Sénégal': 'COUNTRY_LISTE_GRISE',
  'TR': 'COUNTRY_LISTE_GRISE',
  'TT': 'COUNTRY_LISTE_UE',
  'TZ': 'COUNTRY_LISTE_GRISE',
  'Tanzanie': 'COUNTRY_LISTE_GRISE',
  'Tchéquie': 'COUNTRY_UE',
  'Trinité-et-Tobago': 'COUNTRY_LISTE_UE',
  'Turquie': 'COUNTRY_LISTE_GRISE',
  'UG': 'COUNTRY_LISTE_UE',
  'VI': 'COUNTRY_LISTE_UE',
  'VN': 'COUNTRY_LISTE_GRISE',
  'VU': 'COUNTRY_LISTE_UE',
  'Vanuatu': 'COUNTRY_LISTE_UE',
  'Vietnam': 'COUNTRY_LISTE_GRISE',
  'WS': 'COUNTRY_LISTE_UE',
  'YE': 'COUNTRY_LISTE_GRISE',
  'Yémen': 'COUNTRY_LISTE_GRISE',
  'ZA': 'COUNTRY_LISTE_GRISE',
  'Émirats Arabes Unis': 'COUNTRY_LISTE_UE',
  'Îles Vierges des États-Unis': 'COUNTRY_LISTE_UE'
};
export const NATIONALITY_TRACFIN_LABEL: Record<string, string> = {
  COUNTRY_FR: 'Nationalité Française : Vigilance simple pour ce critère',
  COUNTRY_LISTE_GRISE: "Nationalité d'un Pays figurant sur liste grise du GAFI : DECLARATION TRACFIN RECOMMANDEÉ",
  COUNTRY_LISTE_NOIRE: "Nationalité d'un Pays figurant sur liste noire du GAFI : DECLARATION TRACFIN OBLIGATOIRE",
  COUNTRY_LISTE_UE:
    "Nationalité d'un Pays listé par l'Union Européenne comme étant à risque élevé : DECLARATION TRACFIN RECOMMANDEÉ",
  COUNTRY_UE: "Nationalité d'un Pays de l'Union Européenne : Vigilance classique pour ce critère"
};
export const ADDRESS_TRACFIN_LABEL: Record<string, string> = {
  COUNTRY_FR: 'Domicile en France : Vigilance simple pour ce critère',
  COUNTRY_LISTE_GRISE: 'Domicile dans un Pays figurant sur liste grise du GAFI : DECLARATION TRACFIN RECOMMANDEÉ',
  COUNTRY_LISTE_NOIRE: 'Domicile dans un Pays figurant sur liste noire du GAFI : DECLARATION TRACFIN OBLIGATOIRE',
  COUNTRY_LISTE_UE:
    "Domicile dans un Pays listé par l'Union Européenne comme étant à risque élevé : DECLARATION TRACFIN RECOMMANDEÉ",
  COUNTRY_UE: "Domicile dans un Pays de l'Union Européenne : Vigilance classique pour ce critère"
};

export const NATIONALITY_TRACFIN_LABEL_PP: Record<string, string> = {
  COUNTRY_FR: 'Risque faible (1) (France)',
  COUNTRY_LISTE_GRISE: 'Risque élevé (3) (Pays figurant sur la liste du GAFI)',
  COUNTRY_LISTE_NOIRE: 'Risque élevé (3) (Pays figurant sur la liste du GAFI)',
  COUNTRY_LISTE_UE: 'Risque élevé (3) (Pays figurant sur la liste du GAFI)',
  COUNTRY_UE: "Risque faible (1) (Pays de l'UE)"
};
export const ADDRESS_TRACFIN_LABEL_PP: Record<string, string> = {
  COUNTRY_FR: 'Risque faible (1) (France)',
  COUNTRY_LISTE_GRISE: 'Risque élevé (3) (Pays figurant sur la liste du GAFI)',
  COUNTRY_LISTE_NOIRE: 'Risque élevé (3) (Pays figurant sur la liste du GAFI)',
  COUNTRY_LISTE_UE: 'Risque élevé (3) (Pays figurant sur la liste du GAFI)',
  COUNTRY_UE: "Risque faible (1) (Pays de l'UE)"
};

export { QUERY_PARAMS_OPEN_TASKS, QUERY_PARAMS_OPEN_MYNOTARY_CONTRACT_SELECTION } from './constants';

export const ZIP_ZONE_TENDUE = [
  '77090',
  '91420',
  '91300',
  '13170',
  '83500',
  '13112',
  '69760',
  '66700',
  '83700',
  '35170',
  '06690',
  '59152',
  '77400',
  '95250',
  '31820',
  '97200',
  '69600',
  '93600',
  '95740',
  '59155',
  '91210',
  '77144',
  '20600',
  '59650',
  '95870',
  '13080',
  '95480',
  '13105',
  '34430',
  '38130',
  '44000',
  '95390',
  '44480',
  '44117',
  '33560',
  '59113',
  '94510',
  '06950',
  '91700',
  '67203',
  '83400',
  '34170',
  '38330',
  '84000',
  '13122',
  '35131',
  '59350',
  '69190',
  '78780',
  '94380',
  '78640',
  '94460',
  '97142',
  '95280',
  '64210',
  '83430',
  '83150',
  '06570',
  '44120',
  '78180',
  '84440',
  '59790',
  '91240',
  '13950',
  '44610',
  '95460',
  '83720',
  '33185',
  '84810',
  '78330',
  '94440',
  '94490',
  '31240',
  '91320',
  '95400',
  '84580',
  '34770',
  '93440',
  '97280',
  '97129',
  '59420',
  '69660',
  '60340',
  '77310',
  '84250',
  '38113',
  '97122',
  '13850',
  '13160',
  '77186',
  '69520',
  '59840',
  '91360',
  '59491',
  '97111',
  '69200',
  '33870',
  '91390',
  '91200',
  '97414',
  '69570',
  '59910',
  '93370',
  '14650',
  '84800',
  '95580',
  '95290',
  '60940',
  '69480',
  '34990',
  '78300',
  '69390',
  '95360',
  '97110',
  '38610',
  '67800',
  '06150',
  '13100',
  '14840',
  '78700',
  '13700',
  '34970',
  '83530',
  '13130',
  '74460',
  '77500',
  '44600',
  '77340',
  '45650',
  '66690',
  '06340',
  '91600',
  '67540',
  '34690',
  '78310',
  '95610',
  '95680',
  '97290',
  '01600',
  '44510',
  '31830',
  '34590',
  '97450',
  '13630',
  '95600',
  '06210',
  '78260',
  '95430',
  '59810',
  '59237',
  '78711',
  '95130',
  '97227',
  '34740',
  '84860',
  '06810',
  '97224',
  '59250',
  '33130',
  '84350',
  '91000',
  '93190',
  '31770',
  '84320',
  '91450',
  '67300',
  '97211',
  '45000',
  '06730',
  '06270',
  '97420',
  '91220',
  '74240',
  '95110',
  '13200',
  '94190',
  '69450',
  '69370',
  '91160',
  '67206',
  '67202',
  '44340',
  '59115',
  '13360',
  '77330',
  '44840',
  '94000',
  '67116',
  '13090',
  '31860',
  '44220',
  '13340',
  '95100',
  '13910',
  '13390',
  '67205',
  '06580',
  '34540',
  '35740',
  '06480',
  '84660',
  '69410',
  '13380',
  '69890',
  '34880',
  '14123',
  '33400',
  '84270',
  '01700',
  '13190',
  '13790',
  '93470',
  '83160',
  '35000',
  '31100',
  '13180',
  '91350',
  '91540',
  '06400',
  '94550',
  '97410',
  '83210',
  '83000',
  '33150',
  '73490',
  '06000',
  '59211',
  '33170',
  '95370',
  '67400',
  '83480',
  '95170',
  '33450',
  '69110',
  '77127',
  '97300',
  '91380',
  '35235',
  '13770',
  '69740',
  '44550',
  '44730',
  '33600',
  '78360',
  '95140',
  '17440',
  '69140',
  '97180',
  '69270',
  '83320',
  '13800',
  '77100',
  '78740',
  '91230',
  '38100',
  '78670',
  '13670',
  '91460',
  '44800',
  '94400',
  '95550',
  '33470',
  '78570',
  '44500',
  '59117',
  '94600',
  '30670',
  '74970',
  '31150',
  '13120',
  '17180',
  '94310',
  '93460',
  '84460',
  '59150',
  '13940',
  '69150',
  '06220',
  '60460',
  '33530',
  '59120',
  '84700',
  '59960',
  '95240',
  '59100',
  '83130',
  '78370',
  '74380',
  '31490',
  '93390',
  '95220',
  '69970',
  '95120',
  '30400',
  '31200',
  '69130',
  '67000',
  '83780',
  '97215',
  '69350',
  '94480',
  '69230',
  '31270',
  '30660',
  '06650',
  '91620',
  '13590',
  '93330',
  '06610',
  '13550',
  '34750',
  '31400',
  '91270',
  '35700',
  '44980',
  '14200',
  '66660',
  '13270',
  '69120',
  '95500',
  '83330',
  '69260',
  '06160',
  '69126',
  '95440',
  '45590',
  '95350',
  '95630',
  '91170',
  '97233',
  '83490',
  '13710',
  '06520',
  '44620',
  '78210',
  '33360',
  '59510',
  '44570',
  '69800',
  '78820',
  '59290',
  '69530',
  '93270',
  '38400',
  '91650',
  '91100',
  '31790',
  '06790',
  '59139',
  '97270',
  '13320',
  '77177',
  '14790',
  '69500',
  '33120',
  '83370',
  '91130',
  '78340',
  '91440',
  '45560',
  '60740',
  '95230',
  '38670',
  '33880',
  '38600',
  '59520',
  '78960',
  '95150',
  '44740',
  '97228',
  '95800',
  '77183',
  '34790',
  '91330',
  '77200',
  '14730',
  '95320',
  '67380',
  '31780',
  '69340',
  '20620',
  '95330',
  '83190',
  '78510',
  '33700',
  '91550',
  '34200',
  '40220',
  '93350',
  '30540',
  '06700',
  '91290',
  '20200',
  '33800',
  '84130',
  '77700',
  '14460',
  '97354',
  '77185',
  '83110',
  '06100',
  '95200',
  '06200',
  '95660',
  '13500',
  '97131',
  '69160',
  '97190',
  '35135',
  '84450',
  '38640',
  '13600',
  '13109',
  '59700',
  '13290',
  '33260',
  '30132',
  '93220',
  '31300',
  '95160',
  '84870',
  '13570',
  '93150',
  '13127',
  '69540',
  '95700',
  '84300',
  '44250',
  '67200',
  '30900',
  '60160',
  '69730',
  '20222',
  '17140',
  '45770',
  '30000',
  '13920',
  '13750',
  '33310',
  '06590',
  '78955',
  '69320',
  '64500',
  '64600',
  '13810',
  '83920',
  '84470',
  '45100',
  '59166',
  '38180',
  '44860',
  '13510',
  '35200',
  '64100',
  '13440',
  '91070',
  '94880',
  '97430',
  '59493',
  '73000',
  '31840',
  '91520',
  '44380',
  '67460',
  '67100',
  '93420',
  '97460',
  '06130',
  '06250',
  '13140',
  '91190',
  '77860',
  '59170',
  '34730',
  '97170',
  '13730',
  '84370',
  '93320',
  '31750',
  '94420',
  '83740',
  '44115',
  '83100',
  '30820',
  '69630',
  '31140',
  '60550',
  '13119',
  '84560',
  '60180',
  '14000',
  '91180',
  '93700',
  '97118',
  '91090',
  '97419',
  '06110',
  '83220',
  '77000',
  '13480',
  '78114',
  '91280',
  '59390',
  '94470',
  '78540',
  '06740',
  '83200',
  '33270',
  '33140',
  '06300',
  '67207',
  '78190',
  '67115',
  '95540',
  '14120',
  '84200',
  '78280',
  '91120',
  '84170',
  '13124',
  '44700',
  '59118',
  '13530',
  '97234',
  '66750',
  '77420',
  '97139',
  '91480',
  '31170',
  '78480',
  '97240',
  '13420',
  '78990',
  '94350',
  '01280',
  '13220',
  '44400',
  '17690',
  '78760',
  '64700',
  '33610',
  '91340',
  '06550',
  '91560',
  '95490',
  '94450',
  '59175',
  '64990',
  '97212',
  '33520',
  '93290',
  '44720',
  '35136',
  '06370',
  '13870',
  '38560',
  '78500',
  '17000',
  '44300',
  '95520',
  '35830',
  '83270',
  '31850',
  '31520',
  '44230',
  '91310',
  '91860',
  '45800',
  '78390',
  '94430',
  '35132',
  '67204',
  '13400',
  '73420',
  '91800',
  '31180',
  '31500',
  '13300',
  '64480',
  '44770',
  '94520',
  '35760',
  '97438',
  '59560',
  '20000',
  '20090',
  '44880',
  '69280',
  '94320',
  '77184',
  '77680',
  '93160',
  '97160',
  '83140',
  '13450',
  '34830',
  '94150',
  '17139',
  '95620',
  '77176',
  '38170',
  '31700',
  '31670',
  '66190',
  '64340',
  '59126',
  '69300',
  '31880',
  '59130',
  '33110',
  '38240',
  '13110',
  '13880',
  '01710',
  '94260',
  '13690',
  '95760',
  '69310',
  '44490',
  '84260',
  '95530',
  '97400',
  '60100',
  '13821',
  '31000',
  '91140',
  '95410',
  '69580',
  '92360',
  '13240',
  '97213',
  '74650',
  '69960',
  '13540',
  '95210',
  '67201',
  '44200',
  '67450',
  '77270',
  '91260',
  '67114',
  '94500',
  '33850',
  '94290',
  '13330',
  '78130',
  '77380',
  '34920',
  '33127',
  '38000',
  '95310',
  '78560',
  '14760',
  '14980',
  '94370',
  '83260',
  '60870',
  '59110',
  '97351',
  '84380',
  '44100',
  '06640',
  '84740',
  '30133',
  '59370',
  '84310',
  '35510',
  '77360',
  '64200',
  '93410',
  '06600',
  '74000',
  '45750',
  '97231',
  '59200',
  '59223',
  '33320',
  '06800',
  '69680',
  '34470',
  '77164',
  '64122',
  '93110',
  '73290',
  '13000',
  '13001',
  '13002',
  '13003',
  '13004',
  '13005',
  '13006',
  '13007',
  '13008',
  '13009',
  '13010',
  '13011',
  '13012',
  '13013',
  '13014',
  '13015',
  '13016',
  '13348',
  '33000',
  '33100',
  '33200',
  '33300',
  '33800',
  '34000',
  '34070',
  '34080',
  '34090',
  '59000',
  '59160',
  '59260',
  '59777',
  '59800',
  '69001',
  '69000',
  '69002',
  '69003',
  '69004',
  '69005',
  '69006',
  '69007',
  '69008',
  '69009',
  '69100',
  '93800',
  '93450',
  '93120',
  '93380',
  '93240',
  '93430',
  '93000',
  '93140',
  '93130'
];
export const ZIP_PLAFOND_LOYER = [
  '33000',
  '33100',
  '33200',
  '33300',
  '33800',
  '34000',
  '34070',
  '34080',
  '34090',
  '59000',
  '59160',
  '59260',
  '59777',
  '59800',
  '69001',
  '69000',
  '69002',
  '69003',
  '69004',
  '69005',
  '69006',
  '69007',
  '69008',
  '69009',
  '69100',
  '75000',
  '75001',
  '75002',
  '75003',
  '75004',
  '75005',
  '75006',
  '75007',
  '75008',
  '75009',
  '75010',
  '75011',
  '75012',
  '75013',
  '75014',
  '75015',
  '75016',
  '75017',
  '75018',
  '75019',
  '75020',
  '75270',
  '75680',
  '75116',
  '93000',
  '93300',
  '93800',
  '93450',
  '93120',
  '93200',
  '93210',
  '93240',
  '93430',
  '93380',
  '93400',
  '93170',
  '93140',
  '93310',
  '93260',
  '93100',
  '93130',
  '93500',
  '93230',
  '64210',
  '64600',
  '64200',
  '64700',
  '64340',
  '64500',
  '64480',
  '64990',
  '64122'
];
export const INSEE_ZONE_TENDUE = [
  '01030',
  '01043',
  '01049',
  '01142',
  '01157',
  '01166',
  '01194',
  '01238',
  '01249',
  '01250',
  '01262',
  '01275',
  '01285',
  '01322',
  '01339',
  '01347',
  '01353',
  '01376',
  '01401',
  '01419',
  '01423',
  '01427',
  '06004',
  '06006',
  '06007',
  '06010',
  '06015',
  '06026',
  '06027',
  '06029',
  '06030',
  '06031',
  '06033',
  '06034',
  '06035',
  '06038',
  '06039',
  '06044',
  '06046',
  '06048',
  '06054',
  '06060',
  '06064',
  '06065',
  '06067',
  '06068',
  '06069',
  '06079',
  '06083',
  '06084',
  '06085',
  '06088',
  '06089',
  '06090',
  '06095',
  '06108',
  '06112',
  '06113',
  '06114',
  '06122',
  '06123',
  '06128',
  '06137',
  '06138',
  '06140',
  '06147',
  '06148',
  '06149',
  '06155',
  '06157',
  '06161',
  '13001',
  '13002',
  '13004',
  '13005',
  '13007',
  '13009',
  '13010',
  '13012',
  '13014',
  '13015',
  '13016',
  '13018',
  '13019',
  '13020',
  '13023',
  '13025',
  '13026',
  '13027',
  '13028',
  '13031',
  '13032',
  '13034',
  '13036',
  '13039',
  '13040',
  '13041',
  '13042',
  '13043',
  '13044',
  '13045',
  '13046',
  '13047',
  '13052',
  '13054',
  '13055',
  '13056',
  '13060',
  '13062',
  '13063',
  '13064',
  '13066',
  '13069',
  '13070',
  '13071',
  '13072',
  '13073',
  '13075',
  '13076',
  '13077',
  '13081',
  '13083',
  '13086',
  '13087',
  '13089',
  '13092',
  '13095',
  '13098',
  '13101',
  '13102',
  '13103',
  '13106',
  '13107',
  '13109',
  '13110',
  '13112',
  '13113',
  '13114',
  '13116',
  '13117',
  '14042',
  '14068',
  '14101',
  '14118',
  '14137',
  '14167',
  '14181',
  '14215',
  '14221',
  '14242',
  '14271',
  '14274',
  '14301',
  '14311',
  '14319',
  '14327',
  '14341',
  '14437',
  '14438',
  '14454',
  '14543',
  '14587',
  '14707',
  '14738',
  '17010',
  '17028',
  '17094',
  '17142',
  '17190',
  '17200',
  '17264',
  '17274',
  '17291',
  '17300',
  '17420',
  '2A001',
  '2A004',
  '2A006',
  '2A017',
  '2A271',
  '2B033',
  '2B037',
  '2B043',
  '2B120',
  '2B305',
  '2B309',
  '2B353',
  '30004',
  '30011',
  '30019',
  '30036',
  '30060',
  '30075',
  '30117',
  '30123',
  '30156',
  '30169',
  '30189',
  '30333',
  '30347',
  '30351',
  '30356',
  '31022',
  '31032',
  '31035',
  '31044',
  '31053',
  '31056',
  '31057',
  '31069',
  '31079',
  '31088',
  '31091',
  '31113',
  '31116',
  '31117',
  '31118',
  '31136',
  '31149',
  '31150',
  '31157',
  '31160',
  '31161',
  '31165',
  '31169',
  '31182',
  '31186',
  '31187',
  '31188',
  '31203',
  '31205',
  '31230',
  '31248',
  '31252',
  '31254',
  '31259',
  '31273',
  '31282',
  '31291',
  '31293',
  '31340',
  '31351',
  '31352',
  '31364',
  '31389',
  '31395',
  '31409',
  '31410',
  '31411',
  '31417',
  '31420',
  '31421',
  '31424',
  '31429',
  '31433',
  '31445',
  '31446',
  '31458',
  '31460',
  '31462',
  '31467',
  '31484',
  '31488',
  '31490',
  '31497',
  '31506',
  '31515',
  '31516',
  '31526',
  '31533',
  '31541',
  '31547',
  '31555',
  '31557',
  '31561',
  '31563',
  '31575',
  '31578',
  '31580',
  '31587',
  '31588',
  '33003',
  '33009',
  '33013',
  '33015',
  '33018',
  '33032',
  '33033',
  '33039',
  '33049',
  '33056',
  '33061',
  '33063',
  '33065',
  '33069',
  '33075',
  '33079',
  '33080',
  '33084',
  '33085',
  '33090',
  '33096',
  '33099',
  '33118',
  '33119',
  '33122',
  '33143',
  '33162',
  '33165',
  '33167',
  '33192',
  '33199',
  '33200',
  '33207',
  '33226',
  '33234',
  '33238',
  '33241',
  '33245',
  '33249',
  '33273',
  '33274',
  '33281',
  '33293',
  '33303',
  '33311',
  '33312',
  '33318',
  '33322',
  '33330',
  '33339',
  '33349',
  '33363',
  '33366',
  '33376',
  '33381',
  '33397',
  '33415',
  '33422',
  '33425',
  '33433',
  '33448',
  '33449',
  '33466',
  '33483',
  '33487',
  '33496',
  '33518',
  '33519',
  '33522',
  '33527',
  '33529',
  '33534',
  '33535',
  '33539',
  '33550',
  '33553',
  '33554',
  '34014',
  '34023',
  '34024',
  '34057',
  '34077',
  '34090',
  '34095',
  '34108',
  '34113',
  '34116',
  '34120',
  '34123',
  '34129',
  '34134',
  '34145',
  '34146',
  '34151',
  '34165',
  '34169',
  '34172',
  '34198',
  '34213',
  '34217',
  '34247',
  '34255',
  '34270',
  '34272',
  '34280',
  '34290',
  '34295',
  '34301',
  '34309',
  '34327',
  '34337',
  '34340',
  '35024',
  '35047',
  '35051',
  '35055',
  '35059',
  '35066',
  '35173',
  '35189',
  '35206',
  '35210',
  '35238',
  '35278',
  '35281',
  '35334',
  '35353',
  '35363',
  '38045',
  '38057',
  '38070',
  '38071',
  '38087',
  '38111',
  '38126',
  '38150',
  '38151',
  '38158',
  '38169',
  '38170',
  '38175',
  '38179',
  '38185',
  '38200',
  '38229',
  '38249',
  '38271',
  '38281',
  '38303',
  '38309',
  '38317',
  '38382',
  '38397',
  '38421',
  '38423',
  '38431',
  '38474',
  '38485',
  '38486',
  '38501',
  '38516',
  '38524',
  '38533',
  '38538',
  '38540',
  '38547',
  '38565',
  '40248',
  '40273',
  '40312',
  '44009',
  '44010',
  '44018',
  '44020',
  '44026',
  '44035',
  '44047',
  '44049',
  '44052',
  '44055',
  '44069',
  '44071',
  '44074',
  '44101',
  '44103',
  '44109',
  '44114',
  '44126',
  '44130',
  '44132',
  '44135',
  '44136',
  '44143',
  '44150',
  '44151',
  '44154',
  '44162',
  '44166',
  '44168',
  '44172',
  '44176',
  '44182',
  '44184',
  '44190',
  '44194',
  '44198',
  '44204',
  '44210',
  '44215',
  '45034',
  '45075',
  '45089',
  '45100',
  '45147',
  '45169',
  '45194',
  '45232',
  '45234',
  '45235',
  '45272',
  '45274',
  '45282',
  '45284',
  '45285',
  '45286',
  '45298',
  '45302',
  '45308',
  '59009',
  '59013',
  '59034',
  '59044',
  '59090',
  '59098',
  '59128',
  '59146',
  '59152',
  '59163',
  '59193',
  '59195',
  '59220',
  '59247',
  '59275',
  '59278',
  '59279',
  '59286',
  '59299',
  '59328',
  '59332',
  '59339',
  '59343',
  '59346',
  '59350',
  '59352',
  '59356',
  '59360',
  '59367',
  '59368',
  '59378',
  '59386',
  '59410',
  '59421',
  '59426',
  '59437',
  '59457',
  '59470',
  '59482',
  '59507',
  '59508',
  '59512',
  '59522',
  '59527',
  '59553',
  '59560',
  '59566',
  '59585',
  '59598',
  '59599',
  '59602',
  '59609',
  '59611',
  '59636',
  '59643',
  '59646',
  '59648',
  '59650',
  '59656',
  '59660',
  '60013',
  '60074',
  '60102',
  '60134',
  '60154',
  '60173',
  '60175',
  '60342',
  '60360',
  '60404',
  '60406',
  '60409',
  '60414',
  '60463',
  '60513',
  '60524',
  '60539',
  '60584',
  '60589',
  '60635',
  '60670',
  '60684',
  '60686',
  '64009',
  '64024',
  '64035',
  '64038',
  '64065',
  '64100',
  '64102',
  '64122',
  '64125',
  '64130',
  '64140',
  '64160',
  '64189',
  '64213',
  '64249',
  '64255',
  '64260',
  '64282',
  '64304',
  '64317',
  '64407',
  '64483',
  '64496',
  '64540',
  '64545',
  '64547',
  '64558',
  '66002',
  '66008',
  '66053',
  '66059',
  '66065',
  '66093',
  '66094',
  '66133',
  '66148',
  '66168',
  '66171',
  '66196',
  '66208',
  '66225',
  '67001',
  '67043',
  '67118',
  '67131',
  '67137',
  '67204',
  '67218',
  '67256',
  '67267',
  '67268',
  '67296',
  '67309',
  '67326',
  '67343',
  '67350',
  '67365',
  '67378',
  '67389',
  '67447',
  '67471',
  '67482',
  '67506',
  '67551',
  '69003',
  '69005',
  '69009',
  '69013',
  '69020',
  '69027',
  '69028',
  '69029',
  '69033',
  '69034',
  '69040',
  '69043',
  '69044',
  '69046',
  '69047',
  '69049',
  '69052',
  '69055',
  '69059',
  '69061',
  '69063',
  '69068',
  '69069',
  '69071',
  '69072',
  '69074',
  '69076',
  '69081',
  '69085',
  '69087',
  '69088',
  '69089',
  '69091',
  '69092',
  '69094',
  '69096',
  '69100',
  '69105',
  '69106',
  '69112',
  '69115',
  '69116',
  '69117',
  '69118',
  '69121',
  '69122',
  '69123',
  '69125',
  '69126',
  '69127',
  '69131',
  '69133',
  '69136',
  '69140',
  '69142',
  '69143',
  '69148',
  '69149',
  '69156',
  '69159',
  '69168',
  '69190',
  '69191',
  '69194',
  '69199',
  '69202',
  '69204',
  '69205',
  '69212',
  '69233',
  '69241',
  '69244',
  '69249',
  '69250',
  '69255',
  '69256',
  '69259',
  '69260',
  '69264',
  '69266',
  '69268',
  '69270',
  '69271',
  '69272',
  '69273',
  '69275',
  '69276',
  '69277',
  '69278',
  '69281',
  '69282',
  '69283',
  '69284',
  '69286',
  '69290',
  '69291',
  '69292',
  '69293',
  '69294',
  '69296',
  '69297',
  '69298',
  '73008',
  '73017',
  '73029',
  '73030',
  '73031',
  '73050',
  '73051',
  '73059',
  '73064',
  '73065',
  '73084',
  '73087',
  '73103',
  '73128',
  '73137',
  '73151',
  '73155',
  '73160',
  '73179',
  '73182',
  '73183',
  '73208',
  '73213',
  '73222',
  '73225',
  '73228',
  '73243',
  '73249',
  '73288',
  '73300',
  '73301',
  '73310',
  '73326',
  '73328',
  '73329',
  '74005',
  '74007',
  '74008',
  '74010',
  '74012',
  '74013',
  '74016',
  '74018',
  '74019',
  '74020',
  '74021',
  '74024',
  '74026',
  '74037',
  '74040',
  '74042',
  '74044',
  '74064',
  '74067',
  '74072',
  '74081',
  '74087',
  '74090',
  '74094',
  '74104',
  '74108',
  '74112',
  '74116',
  '74118',
  '74119',
  '74121',
  '74122',
  '74128',
  '74133',
  '74147',
  '74152',
  '74153',
  '74154',
  '74162',
  '74163',
  '74164',
  '74166',
  '74169',
  '74172',
  '74185',
  '74197',
  '74200',
  '74201',
  '74209',
  '74211',
  '74213',
  '74218',
  '74220',
  '74224',
  '74226',
  '74240',
  '74242',
  '74244',
  '74250',
  '74253',
  '74262',
  '74263',
  '74264',
  '74267',
  '74272',
  '74278',
  '74281',
  '74298',
  '74304',
  '74305',
  '74311',
  '74312',
  '77018',
  '77039',
  '77040',
  '77055',
  '77058',
  '77059',
  '77062',
  '77067',
  '77075',
  '77083',
  '77085',
  '77108',
  '77111',
  '77121',
  '77122',
  '77124',
  '77125',
  '77128',
  '77132',
  '77139',
  '77141',
  '77142',
  '77143',
  '77146',
  '77152',
  '77155',
  '77169',
  '77171',
  '77181',
  '77209',
  '77221',
  '77232',
  '77243',
  '77249',
  '77251',
  '77255',
  '77258',
  '77268',
  '77276',
  '77284',
  '77285',
  '77288',
  '77291',
  '77294',
  '77296',
  '77307',
  '77315',
  '77326',
  '77330',
  '77337',
  '77350',
  '77369',
  '77372',
  '77373',
  '77378',
  '77382',
  '77389',
  '77390',
  '77394',
  '77407',
  '77413',
  '77438',
  '77445',
  '77449',
  '77450',
  '77464',
  '77468',
  '77475',
  '77479',
  '77487',
  '77495',
  '77513',
  '77514',
  '77521',
  '77529',
  '78005',
  '78015',
  '78031',
  '78050',
  '78073',
  '78118',
  '78123',
  '78138',
  '78140',
  '78160',
  '78165',
  '78168',
  '78172',
  '78208',
  '78227',
  '78239',
  '78242',
  '78261',
  '78267',
  '78297',
  '78299',
  '78314',
  '78321',
  '78327',
  '78335',
  '78343',
  '78354',
  '78356',
  '78361',
  '78362',
  '78382',
  '78383',
  '78384',
  '78397',
  '78401',
  '78403',
  '78418',
  '78423',
  '78440',
  '78442',
  '78443',
  '78466',
  '78490',
  '78498',
  '78501',
  '78502',
  '78545',
  '78550',
  '78575',
  '78576',
  '78586',
  '78609',
  '78621',
  '78623',
  '78624',
  '78638',
  '78642',
  '78643',
  '78644',
  '78647',
  '78672',
  '78674',
  '78683',
  '78688',
  '82075',
  '82142',
  '83004',
  '83009',
  '83016',
  '83017',
  '83027',
  '83034',
  '83035',
  '83047',
  '83049',
  '83050',
  '83053',
  '83054',
  '83058',
  '83061',
  '83062',
  '83069',
  '83085',
  '83086',
  '83090',
  '83098',
  '83099',
  '83103',
  '83112',
  '83118',
  '83120',
  '83123',
  '83126',
  '83129',
  '83130',
  '83131',
  '83132',
  '83137',
  '83141',
  '83144',
  '83153',
  '84001',
  '84004',
  '84007',
  '84012',
  '84013',
  '84016',
  '84025',
  '84027',
  '84030',
  '84031',
  '84035',
  '84036',
  '84038',
  '84039',
  '84043',
  '84050',
  '84051',
  '84054',
  '84055',
  '84056',
  '84062',
  '84067',
  '84071',
  '84072',
  '84073',
  '84077',
  '84080',
  '84081',
  '84086',
  '84087',
  '84088',
  '84092',
  '84099',
  '84108',
  '84114',
  '84119',
  '84122',
  '84124',
  '84129',
  '84131',
  '84132',
  '84139',
  '84141',
  '84142',
  '91021',
  '91027',
  '91044',
  '91086',
  '91097',
  '91103',
  '91105',
  '91106',
  '91114',
  '91115',
  '91122',
  '91136',
  '91161',
  '91174',
  '91179',
  '91191',
  '91201',
  '91204',
  '91207',
  '91215',
  '91216',
  '91225',
  '91228',
  '91235',
  '91244',
  '91272',
  '91275',
  '91286',
  '91312',
  '91326',
  '91333',
  '91339',
  '91340',
  '91345',
  '91347',
  '91363',
  '91377',
  '91386',
  '91421',
  '91425',
  '91432',
  '91434',
  '91435',
  '91457',
  '91458',
  '91461',
  '91468',
  '91471',
  '91477',
  '91479',
  '91494',
  '91514',
  '91521',
  '91534',
  '91538',
  '91549',
  '91552',
  '91553',
  '91570',
  '91573',
  '91577',
  '91581',
  '91587',
  '91589',
  '91600',
  '91631',
  '91657',
  '91659',
  '91661',
  '91665',
  '91666',
  '91667',
  '91679',
  '91685',
  '91687',
  '91689',
  '91691',
  '91692',
  '93005',
  '93007',
  '93008',
  '93010',
  '93013',
  '93014',
  '93015',
  '93027',
  '93029',
  '93030',
  '93031',
  '93032',
  '93033',
  '93039',
  '93046',
  '93047',
  '93050',
  '93051',
  '93053',
  '93057',
  '93059',
  '93064',
  '93071',
  '93072',
  '93073',
  '93074',
  '93078',
  '93079',
  '94001',
  '94004',
  '94011',
  '94017',
  '94019',
  '94021',
  '94022',
  '94028',
  '94034',
  '94044',
  '94047',
  '94048',
  '94053',
  '94054',
  '94055',
  '94056',
  '94059',
  '94060',
  '94065',
  '94070',
  '94071',
  '94073',
  '94074',
  '94075',
  '94077',
  '94078',
  '94079',
  '94081',
  '95014',
  '95018',
  '95019',
  '95039',
  '95051',
  '95060',
  '95063',
  '95088',
  '95091',
  '95120',
  '95127',
  '95134',
  '95176',
  '95183',
  '95197',
  '95199',
  '95203',
  '95205',
  '95211',
  '95212',
  '95218',
  '95219',
  '95229',
  '95252',
  '95256',
  '95257',
  '95268',
  '95277',
  '95280',
  '95288',
  '95306',
  '95313',
  '95323',
  '95369',
  '95392',
  '95394',
  '95424',
  '95426',
  '95427',
  '95428',
  '95446',
  '95450',
  '95476',
  '95480',
  '95488',
  '95489',
  '95491',
  '95500',
  '95510',
  '95527',
  '95539',
  '95555',
  '95563',
  '95572',
  '95574',
  '95582',
  '95585',
  '95598',
  '95607',
  '95612',
  '95628',
  '95633',
  '95637',
  '95678',
  '95680',
  '97101',
  '97103',
  '97113',
  '97115',
  '97116',
  '97117',
  '97118',
  '97119',
  '97120',
  '97125',
  '97128',
  '97205',
  '97207',
  '97209',
  '97210',
  '97212',
  '97217',
  '97220',
  '97221',
  '97222',
  '97223',
  '97224',
  '97226',
  '97227',
  '97229',
  '97232',
  '97302',
  '97307',
  '97309',
  '97403',
  '97407',
  '97408',
  '97411',
  '97414',
  '97415',
  '97416',
  '97418',
  '97422'
];
export const INSEE_ZONE_TRES_TENDUE = [
  '01143',
  '01160',
  '01281',
  '01313',
  '01354',
  '01397',
  '01399',
  '01435',
  '06011',
  '06012',
  '06018',
  '06032',
  '06059',
  '06104',
  '06105',
  '06121',
  '06150',
  '06152',
  '06159',
  '13022',
  '60141',
  '74056',
  '74082',
  '74243',
  '75056',
  '78007',
  '78043',
  '78092',
  '78117',
  '78124',
  '78126',
  '78133',
  '78146',
  '78158',
  '78190',
  '78224',
  '78311',
  '78322',
  '78350',
  '78358',
  '78367',
  '78372',
  '78396',
  '78455',
  '78481',
  '78518',
  '78551',
  '78640',
  '78646',
  '78650',
  '78686',
  '83065',
  '83101',
  '91064',
  '91635',
  '91645',
  '92002',
  '92004',
  '92007',
  '92009',
  '92012',
  '92014',
  '92019',
  '92020',
  '92022',
  '92023',
  '92024',
  '92025',
  '92026',
  '92032',
  '92033',
  '92035',
  '92036',
  '92040',
  '92044',
  '92046',
  '92047',
  '92048',
  '92049',
  '92050',
  '92051',
  '92060',
  '92062',
  '92063',
  '92064',
  '92071',
  '92072',
  '92073',
  '92075',
  '92076',
  '92077',
  '92078',
  '93001',
  '93006',
  '93045',
  '93048',
  '93049',
  '93055',
  '93061',
  '93062',
  '93063',
  '93066',
  '93070',
  '93077',
  '94002',
  '94003',
  '94015',
  '94016',
  '94018',
  '94033',
  '94037',
  '94038',
  '94041',
  '94042',
  '94043',
  '94046',
  '94052',
  '94058',
  '94067',
  '94068',
  '94069',
  '94076',
  '94080',
  '95210'
];
export const INSEE_ZONE_TRES_TENDUE_SIMPLE = ['01143', '01397', '13022', '74056', '83065', '83101'];
export const INSEE_PLAFOND_LOYER = [
  '75056',
  '33063',
  '93006',
  '93008',
  '93010',
  '93061',
  '93045',
  '93048',
  '93053',
  '93055',
  '93063',
  '59355',
  '59350',
  '59298',
  '69123',
  '69266',
  '34172',
  '93001',
  '93031',
  '93039',
  '93027',
  '93059',
  '93066',
  '93070',
  '93072',
  '93079',
  '64009',
  '64024',
  '64035',
  '64038',
  '64065',
  '64100',
  '64102',
  '64122',
  '64125',
  '64130',
  '64140',
  '64189',
  '64249',
  '64260',
  '64282',
  '64304',
  '64317',
  '64407',
  '64545',
  '64483',
  '64496',
  '64540',
  '64547',
  '64558'
];

export const ZIP_ZONE_TRES_TENDUE = [
  '91570',
  '93230',
  '92390',
  '92230',
  '94140',
  '83580',
  '83350',
  '78530',
  '78420',
  '78170',
  '78620',
  '78800',
  '78430',
  '78750',
  '78590',
  '78380',
  '78240',
  '78870',
  '06310',
  '06240',
  '06410',
  '06320',
  '06360',
  '06190',
  '06330',
  '06230',
  '06560',
  '01210',
  '01280',
  '13260',
  '74400',
  '75000',
  '75001',
  '75002',
  '75003',
  '75004',
  '75005',
  '75006',
  '75007',
  '75008',
  '75009',
  '75010',
  '75011',
  '75012',
  '75013',
  '75014',
  '75015',
  '75016',
  '75017',
  '75018',
  '75019',
  '75020',
  '75270',
  '75680',
  '75116',
  '78400',
  '78290',
  '78230',
  '78140',
  '78110',
  '78112',
  '78600',
  '78160',
  '78150',
  '78100',
  '78000',
  '78220',
  '92160',
  '92600',
  '92220',
  '92270',
  '92100',
  '92340',
  '92290',
  '92320',
  '92370',
  '92140',
  '92110',
  '92700',
  '92400',
  '92260',
  '92380',
  '92130',
  '92250',
  '92350',
  '92300',
  '92240',
  '92430',
  '92190',
  '92120',
  '92000',
  '92200',
  '92800',
  '92500',
  '92210',
  '92330',
  '92310',
  '92150',
  '92170',
  '92420',
  '92410',
  '93360',
  '93250',
  '93300',
  '93200',
  '93210',
  '93340',
  '93400',
  '93170',
  '93260',
  '93100',
  '93500',
  '93310',
  '94110',
  '94360',
  '94230',
  '94220',
  '94120',
  '94250',
  '94200',
  '94340',
  '94270',
  '94170',
  '94240',
  '94700',
  '94130',
  '94160',
  '94100',
  '94210',
  '94410',
  '94800',
  '94300',
  '95880'
];
export const ZIP_ZONE_TRES_TENDUE_SIMPLE = ['01220', '01210', '13260', '74400', '83580', '83350'];
export const ZIP_ZONE_MIXTE = [
  '78350',
  '91430',
  '91370',
  '60500',
  '01170',
  '00000',
  '59780',
  '01120',
  '01210',
  '01220',
  '01480',
  '33710',
  '01630',
  '06140',
  '06390',
  '06500',
  '06510',
  '06530',
  '06620',
  '06670',
  '13250',
  '13720',
  '14112',
  '14210',
  '14280',
  '14540',
  '14610',
  '17137',
  '17138',
  '17220',
  '17340',
  '20167',
  '30250',
  '30300',
  '30320',
  '30230',
  '30600',
  '30620',
  '31120',
  '31130',
  '31320',
  '31340',
  '31450',
  '31470',
  '31600',
  '31620',
  '31650',
  '33160',
  '33240',
  '33290',
  '33370',
  '33440',
  '33500',
  '33550',
  '33650',
  '33670',
  '33750',
  '34110',
  '34400',
  '34560',
  '34570',
  '34820',
  '34980',
  '35230',
  '35520',
  '38120',
  '38190',
  '38320',
  '38340',
  '38360',
  '38420',
  '38570',
  '38700',
  '38760',
  '38800',
  '38950',
  '40390',
  '44240',
  '44350',
  '44470',
  '44640',
  '44830',
  '45140',
  '45160',
  '45380',
  '45400',
  '45430',
  '45760',
  '59320',
  '59710',
  '59890',
  '60140',
  '60290',
  '60660',
  '64250',
  '64310',
  '66200',
  '66740',
  '67550',
  '67640',
  '69210',
  '69250',
  '69290',
  '69330',
  '69360',
  '69380',
  '69400',
  '69440',
  '69510',
  '69640',
  '69670',
  '69700',
  '69780',
  '73100',
  '73160',
  '73190',
  '73230',
  '73370',
  '73800',
  '74100',
  '74130',
  '74140',
  '74160',
  '74200',
  '74210',
  '74250',
  '74300',
  '74320',
  '74330',
  '74370',
  '74410',
  '74420',
  '74500',
  '74560',
  '74800',
  '74930',
  '74950',
  '77124',
  '77150',
  '77170',
  '77181',
  '77190',
  '77240',
  '77290',
  '77350',
  '77450',
  '77470',
  '77550',
  '77580',
  '77600',
  '77950',
  '77990',
  '78200',
  '78250',
  '78320',
  '78440',
  '78450',
  '78460',
  '78470',
  '78490',
  '78520',
  '78630',
  '78690',
  '78930',
  '82170',
  '83300',
  '83390',
  '83460',
  '83600',
  '83640',
  '84100',
  '84150',
  '84190',
  '84210',
  '84220',
  '84330',
  '91250',
  '91400',
  '91680',
  '91830',
  '91940',
  '95000',
  '95190',
  '95300',
  '95380',
  '95570',
  '95650',
  '95690',
  '95840',
  '97222'
];
