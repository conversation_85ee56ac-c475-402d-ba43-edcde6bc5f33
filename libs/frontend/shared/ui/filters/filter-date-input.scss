@use 'style/variables/colors' as *;
@use 'style/mixins/typography' as *;

.filter-date-input {
  position: relative;

  .fdi-input {
    @include small-font($bold);

    cursor: pointer;

    width: 140px;
    padding: 5px 10px;
    padding-left: 5px;
    border: 1px solid $gray150;
    border-radius: 5px;

    color: $black;

    &.selected:not(.error) {
      border-color: var(--primary);
      background-color: var(--primary-10);
    }

    &.error {
      border-color: $red300;
      background-color: $red50;
    }

    &.disabled {
      cursor: not-allowed;
      border-color: $gray150;
      color: $gray300;
      background-color: $gray50;
    }
  }

  .fdi-image {
    cursor: pointer;

    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(-50%, -50%);

    width: 10px;
    height: 10px;

    stroke-width: 2px;
  }
}

.filter-date-input + .filter-date-input {
  margin-left: 5px;
}
