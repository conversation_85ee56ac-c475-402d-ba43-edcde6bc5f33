import { AnyFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { Answers } from '@mynotary/crossplatform/records/api';
import {
  SendCodeType,
  SignatureAssociationType,
  SignatureMethod,
  SignaturePlace,
  SignatureProviderType
} from '@mynotary/crossplatform/signatures/api';
import { NewPartialSignatory } from '@mynotary/frontend/signatures/core';

export enum NewSignatoryQuestionId {
  email = 'email',
  nom = 'nom',
  prenoms = 'prenoms',
  send_code = 'send_code',
  send_code_advanced = 'send_code_advanced',
  signatory_mention = 'signatory_mention',
  signatory_mention_choice = 'signatory_mention_choice',
  signatory_place = 'signatory_place',
  telephone = 'telephone'
}

const questions: Record<NewSignatoryQuestionId, AnyFormQuestion> = {
  [NewSignatoryQuestionId.email]: {
    id: NewSignatoryQuestionId.email,
    label: 'Email',
    type: 'EMAIL'
  },

  [NewSignatoryQuestionId.nom]: {
    id: NewSignatoryQuestionId.nom,
    label: 'Nom de naissance',
    required: true,
    type: 'TEXT',
    uppercase: 'UPPERCASE'
  },
  [NewSignatoryQuestionId.prenoms]: {
    id: NewSignatoryQuestionId.prenoms,
    label: 'Prénoms(s)',
    required: true,
    type: 'TEXT',
    uppercase: 'WORD'
  },
  [NewSignatoryQuestionId.send_code]: {
    choices: [
      { id: 'SMS', label: 'Par SMS' },
      {
        id: 'MAIL',
        label: 'Par e-mail',
        tooltip: `Pour une signature plus sécurisée, privilégiez l'envoi du code par sms.`
      }
    ],
    id: NewSignatoryQuestionId.send_code,
    label: "Envoyer le code d'authentification",
    type: 'SELECT-BINARY'
  },
  [NewSignatoryQuestionId.send_code_advanced]: {
    choices: [
      { id: 'SMS', label: 'Par SMS' },
      {
        id: 'MAIL',
        label: 'Par e-mail',
        tooltip: `Pour une signature plus sécurisée, privilégiez l'envoi du code par sms.`
      }
    ],
    description: "Le code d'authentification par SMS est obligatoire pour une signature électronique avancée.",
    disabled: true,
    id: NewSignatoryQuestionId.send_code,
    label: "Envoyer le code d'authentification",
    type: 'SELECT-BINARY'
  },
  [NewSignatoryQuestionId.signatory_mention]: {
    conditions: [[{ id: NewSignatoryQuestionId.signatory_mention_choice, type: 'EQUALS', value: 'oui' }]],
    id: NewSignatoryQuestionId.signatory_mention,
    label: 'Mention',
    type: 'TEXTAREA'
  },
  [NewSignatoryQuestionId.signatory_mention_choice]: {
    choices: [
      { id: 'non', label: 'Non' },
      { id: 'oui', label: 'Oui' }
    ],
    id: NewSignatoryQuestionId.signatory_mention_choice,
    label: 'Faire recopier une mention lors de la signature ?',
    type: 'SELECT-BINARY'
  },
  [NewSignatoryQuestionId.signatory_place]: {
    choices: [
      { id: 'PHYSICAL', label: 'Sur place' },
      {
        id: 'ONLINE',
        label: 'À distance',
        tooltip:
          'Un e-mail sera automatiquement envoyé au client (ou son représentant) pour signer le contrat avec le lien de la signature'
      }
    ],
    id: NewSignatoryQuestionId.signatory_place,
    label: 'Faire signer le client (ou son représentant)',
    type: 'SELECT-BINARY'
  },
  [NewSignatoryQuestionId.telephone]: {
    description: 'Un téléphone portable est préférable pour la signature électronique.',
    id: NewSignatoryQuestionId.telephone,
    label: 'Téléphone',
    type: 'PHONE'
  }
};

const electronicQuestions: (level: SignatureMethod) => Array<AnyFormQuestion> = (level: SignatureMethod) => {
  return [
    questions.nom,
    questions.prenoms,
    questions.email,
    questions.telephone,
    questions.signatory_place,
    level === SignatureMethod.ADVANCED ? questions.send_code_advanced : questions.send_code,
    questions.signatory_mention_choice,
    questions.signatory_mention
  ];
};

const uncertifiedQuestions: Array<AnyFormQuestion> = [
  questions.nom,
  questions.prenoms,
  questions.email,
  questions.telephone
];

const paperQuestions: Array<AnyFormQuestion> = [
  questions.nom,
  questions.prenoms,
  questions.signatory_mention_choice,
  questions.signatory_mention
];

export const signatoryCreationForm = ({
  method,
  providerType
}: {
  method: SignatureMethod;
  providerType: SignatureProviderType;
}) => {
  switch (providerType) {
    case SignatureProviderType.YOUSIGN_V2:
    case SignatureProviderType.YOUSIGN_V3:
      return electronicQuestions(method);
    case SignatureProviderType.UNCERTIFIED:
      return uncertifiedQuestions;
    case SignatureProviderType.MYNOTARY_PAPER:
      return paperQuestions;
  }
};

export const signatoryActiveUpdateForm = [questions.nom, questions.prenoms, questions.email, questions.telephone];

export const createPartialSignatory = ({
  answer,
  recordId,
  type
}: {
  answer: Answers<NewSignatoryQuestionId>;
  recordId?: number;
  type: SignatureProviderType;
}): NewPartialSignatory => {
  switch (type) {
    case SignatureProviderType.YOUSIGN_V2:
    case SignatureProviderType.YOUSIGN_V3:
      return createPartialSignatoryElectronic({ answer, recordId });
    case SignatureProviderType.MYNOTARY_PAPER:
      return createPartialSignatoryPaper({ answer, recordId });
    case SignatureProviderType.UNCERTIFIED:
      return createPartialSignatoryUncertified({ answer, recordId });
  }
};

const createPartialSignatoryElectronic = ({ answer, recordId }: CreatePartialSignatoryArgs) => {
  return {
    association: createSignatoryAssociation(recordId),
    email: answer[NewSignatoryQuestionId.email]?.value,
    firstname: answer[NewSignatoryQuestionId.prenoms]?.value,
    lastname: answer[NewSignatoryQuestionId.nom]?.value,
    mention:
      answer[NewSignatoryQuestionId.signatory_mention_choice]?.value === 'oui'
        ? answer[NewSignatoryQuestionId.signatory_mention]?.value
        : null,
    phone: answer[NewSignatoryQuestionId.telephone]?.value,
    sendCode: answer[NewSignatoryQuestionId.send_code]?.value ?? SendCodeType.SMS,
    signaturePlace: answer[NewSignatoryQuestionId.signatory_place]?.value ?? SignaturePlace.ONLINE
  };
};

const createPartialSignatoryPaper = ({ answer, recordId }: CreatePartialSignatoryArgs) => {
  return {
    association: createSignatoryAssociation(recordId),
    firstname: answer[NewSignatoryQuestionId.prenoms]?.value,
    lastname: answer[NewSignatoryQuestionId.nom]?.value,
    mention:
      answer[NewSignatoryQuestionId.signatory_mention_choice]?.value === 'oui'
        ? answer[NewSignatoryQuestionId.signatory_mention]?.value
        : undefined,
    signaturePlace: SignaturePlace.PHYSICAL
  };
};
const createPartialSignatoryUncertified = ({ answer, recordId }: CreatePartialSignatoryArgs) => {
  return {
    association: createSignatoryAssociation(recordId),
    email: answer[NewSignatoryQuestionId.email]?.value,
    firstname: answer[NewSignatoryQuestionId.prenoms]?.value,
    lastname: answer[NewSignatoryQuestionId.nom]?.value,
    phone: answer[NewSignatoryQuestionId.telephone]?.value,
    signaturePlace: SignaturePlace.ONLINE
  };
};

/**
 * Create a signatory association between a record and a signatory.
 * It can be used to update the record answer when the signatory is updated.
 * It's currently not used by the new workflow of signature.
 * Also note that the association is not part of the SignatoryActive model, it exists only in the draft creation context
 */
const createSignatoryAssociation = (recordId?: number) => {
  if (!recordId) {
    return undefined;
  }

  return {
    legalComponent: { recordId },
    type: SignatureAssociationType.LEGAL_COMPONENT
  };
};

export const getNewSignatoryDefaultAnswer = (signatureType: SignatureProviderType): Answers<NewSignatoryQuestionId> => {
  switch (signatureType) {
    case SignatureProviderType.MYNOTARY_PAPER:
      return {
        [NewSignatoryQuestionId.signatory_place]: { value: 'PHYSICAL' }
      } as Answers<NewSignatoryQuestionId>;
    case SignatureProviderType.UNCERTIFIED:
      return {
        [NewSignatoryQuestionId.signatory_place]: { value: 'ONLINE' },
        [NewSignatoryQuestionId.send_code]: { value: 'SMS' },
        [NewSignatoryQuestionId.signatory_mention_choice]: { value: 'non' }
      } as Answers<NewSignatoryQuestionId>;
    case SignatureProviderType.YOUSIGN_V2:
    case SignatureProviderType.YOUSIGN_V3:
      return {
        [NewSignatoryQuestionId.signatory_place]: { value: 'ONLINE' },
        [NewSignatoryQuestionId.send_code]: { value: 'SMS' },
        [NewSignatoryQuestionId.signatory_mention_choice]: { value: 'non' }
      } as Answers<NewSignatoryQuestionId>;
  }
};

export const setSignatoryAnswerByProviderType = ({
  method,
  providerType,
  signatory
}: {
  method: SignatureMethod;
  providerType?: SignatureProviderType;
  signatory: NewPartialSignatory;
}): Answers<NewSignatoryQuestionId> => {
  const baseAnswer = {
    [NewSignatoryQuestionId.prenoms]: { value: signatory.firstname },
    [NewSignatoryQuestionId.nom]: { value: signatory.lastname }
  };

  switch (providerType) {
    case SignatureProviderType.YOUSIGN_V2:
    case SignatureProviderType.YOUSIGN_V3: {
      const sendCode =
        method === SignatureMethod.ADVANCED ? SendCodeType.SMS : (signatory.sendCode ?? SendCodeType.SMS);

      return {
        ...baseAnswer,
        [NewSignatoryQuestionId.email]: { value: signatory.email },
        [NewSignatoryQuestionId.telephone]: { value: signatory.phone },
        [NewSignatoryQuestionId.signatory_mention]: { value: signatory.mention || '' },
        [NewSignatoryQuestionId.signatory_mention_choice]: { value: signatory.mention ? 'oui' : 'non' },
        [NewSignatoryQuestionId.send_code]: { value: sendCode },
        [NewSignatoryQuestionId.signatory_place]: { value: signatory.signaturePlace || SignaturePlace.ONLINE }
      } as Answers<NewSignatoryQuestionId>;
    }
    case SignatureProviderType.UNCERTIFIED:
      return {
        ...baseAnswer,
        [NewSignatoryQuestionId.signatory_place]: { value: SignaturePlace.ONLINE },
        [NewSignatoryQuestionId.email]: { value: signatory.email },
        [NewSignatoryQuestionId.telephone]: { value: signatory.phone }
      } as Answers<NewSignatoryQuestionId>;
    case SignatureProviderType.MYNOTARY_PAPER:
      return {
        ...baseAnswer,
        [NewSignatoryQuestionId.signatory_place]: { value: SignaturePlace.PHYSICAL },
        [NewSignatoryQuestionId.signatory_mention_choice]: { value: signatory.mention ? 'oui' : 'non' },
        [NewSignatoryQuestionId.signatory_mention]: { value: signatory.mention || '' }
      } as Answers<NewSignatoryQuestionId>;
    default:
      throw new Error(`Unknown provider type: ${providerType}`);
  }
};

interface CreatePartialSignatoryArgs {
  answer: Answers<NewSignatoryQuestionId>;
  recordId?: number;
}
