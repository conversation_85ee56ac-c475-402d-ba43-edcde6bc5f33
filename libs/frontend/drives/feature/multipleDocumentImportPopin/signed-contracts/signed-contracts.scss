@use 'style/variables/colors' as *;

.signed-contracts {
  overflow: auto;
  display: flex;
  flex: 1;
  flex-direction: column;

  margin-bottom: 80px;

  .sc-search {
    display: flex;
    align-items: center;
    padding: 0 24px;
  }

  .sc-header{
    position: sticky;
    z-index: 1;
    top: 0;

    display: flex;
    flex-direction: column;
    gap: 24px;

    padding: 24px 0;

    background-color: $white;
  }

  .sc-no-files {
    display: flex;
    flex-direction: column;
    gap: 16px;
    align-items: center;
    justify-content: center;

    padding: 24px;
  }

  .sc-title {
    display: flex;
    justify-content: center;

    span {
      color: var(--primary);
    }
  }

  .sc-list {
    overflow: auto;
    display: flex;
    flex-direction: column;
  }
}
