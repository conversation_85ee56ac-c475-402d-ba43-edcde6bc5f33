import { generatePath, matchPath } from 'react-router';
import { ContractPath, MnAppRoute, MnAppRoutes, PublicPagesUrl, routePaths, SignaturePath } from './index';
import { get } from 'lodash';
import { openInNewTab } from '@mynotary/frontend/shared/util';

import { ContractStatus } from '@mynotary/crossplatform/legals/api';

export const findMatchingRoute = (routes: MnAppRoutes, targetPath: string): MnAppRoute | null => {
  for (const key in routes) {
    const route = routes[key];

    if (typeof route === 'object') {
      const res = findMatchingRoute(route, targetPath);

      if (res) {
        return res;
      }
    }

    // TODO: à vérifier
    if (route?.path == null) {
      continue;
    }

    const match = matchPath({ end: true, path: route?.path }, targetPath);

    if (match) {
      return route;
    }
  }
  return null;
};

export const isPublicPage = (): boolean => {
  return PublicPagesUrl.some((page) => {
    return (
      window.location.pathname === page ||
      window.location.hash === '#' + page ||
      !!matchPath({ end: true, path: page }, window.location.pathname)
    );
  });
};

export const reverse = (
  id: string,
  params?: { [paramName: string]: string | number | boolean | undefined }
): string => {
  return generatePath(id, params);
};

export const baseUrl = window.location.protocol + '//' + window.location.host;

export const isSubOperationHomePage = (path: string, isResponsive = false) => {
  if (isResponsive) {
    return (
      matchPath(
        {
          end: true,
          path: routePaths.operation.operations.subOperations.path
        },
        path
      ) != null
    );
  }
  return (
    matchPath(
      {
        end: true,
        path: routePaths.operation.operations.subOperations.contrats.path
      },
      path
    ) != null ||
    matchPath(
      {
        end: true,
        path: routePaths.operation.operations.subOperations.drive.path
      },
      path
    ) != null
  );
};

export const getOperationRoute = (
  pathOperation: string,
  url: string,
  params?: { [paramName: string]: string | number | undefined }
): string => {
  const path = isSubOperationRoute(url) ? routePaths.operation.operations.subOperations : routePaths.operation;

  const pathString = get(path, pathOperation);

  return reverse(pathString, { ...params });
};

export const isSubOperationRoute = (path: string): boolean => {
  const subOperation = matchPath(
    {
      caseSensitive: false,
      end: false,
      path: routePaths.operation.operations.subOperations.path
    },
    path
  );
  return !!subOperation;
};

export const getOperationUrl = (
  operation: { id: number; parentOperationId?: number },
  path?: string,
  params?: { [paramName: string]: string | number | boolean | undefined }
): string => {
  if (path) {
    return reverse(path, { ...params, id: operation.id });
  }

  if (operation.parentOperationId) {
    return reverse(routePaths.operation.operations.subOperations.path, {
      ...params,
      id: operation.parentOperationId,
      subOperationId: operation.id
    });
  }
  return reverse(routePaths.operation.path, { ...params, id: operation.id });
};

export const containsUrl = (url: string, toMatch: string): boolean => {
  const split = url.split('?');
  return new RegExp(`^${split[0]}($|/.*$)`).test(toMatch);
};

export const navigateWithEvents = ({
  event,
  navigate,
  url
}: {
  event: { ctrlKey: boolean; metaKey: boolean };
  navigate: (args: { pathname: string }) => void;
  url: string;
}): void => {
  if (event.ctrlKey || event.metaKey) {
    openInNewTab(url);
  } else {
    navigate({ pathname: url });
  }
};

/**
 * Get the route for a signature. Handle the case where the signature is inside a sub-operation.
 * If no path is provided, the default path is 'signatures', this route is used to handle the redirection depending on
 * the contract status.
 */
export const getSignatureRoute = ({
  contractStatus,
  operationId,
  parentOperationId,
  path,
  signatureId
}: {
  contractStatus?: ContractStatus;
  operationId: number;
  parentOperationId?: number;
  path?: SignaturePath;
  signatureId: number;
}) => {
  const url = getOperationUrl({ id: operationId, parentOperationId });
  if (
    contractStatus === ContractStatus.SIGNATURE_COMPLETED ||
    contractStatus === ContractStatus.SIGNATURE_EXPIRED ||
    contractStatus === ContractStatus.SIGNATURE_PENDING
  ) {
    const pathname = 'signaturesActives.path';
    return getOperationRoute(pathname, url, {
      id: parentOperationId ?? operationId,
      signatureId,
      subOperationId: parentOperationId ? operationId : undefined
    });
  }
  const pathname = path ? `signatures.${path}.path` : 'signatures.path';

  return getOperationRoute(pathname, url, {
    id: parentOperationId ?? operationId,
    signatureId,
    subOperationId: parentOperationId ? operationId : undefined
  });
};

export const getContractUrl = ({
  contractId,
  notificationId,
  operationId,
  pagePath,
  parentOperationId,
  signatureId
}: {
  contractId: number;
  notificationId?: number;
  operationId: number;
  pagePath: ContractPath;
  parentOperationId?: number;
  signatureId?: number;
}): string => {
  let operationUrl = routePaths.operation.path;
  if (parentOperationId != null) {
    operationUrl = reverse(routePaths.operation.operations.subOperations.path, {
      id: parentOperationId,
      subOperationId: operationId
    });
  }

  return getOperationRoute(`contrats.${pagePath}.path`, operationUrl, {
    contractId: contractId,
    id: parentOperationId ?? operationId,
    registeredLetterId: notificationId,
    signatureId: signatureId,
    subOperationId: parentOperationId ? operationId : undefined
  });
};

/**
 * Return the correct contract route depending on the contract type and the parent operation id.
 * First we check if we are in a suboperation or not because the bath path is different.
 * Then we check the contract type:
 * - for imported contract we want to redirect to the operation page
 * - for MyNotary contract we want to redirect to the redaction page.
 */
export const getOperationContractRoute = ({
  contractId,
  isImportedContract,
  operationId,
  parentOperationId
}: {
  contractId: number;
  isImportedContract: boolean;
  operationId: number;
  parentOperationId?: number;
}): string => {
  if (parentOperationId != null && isImportedContract) {
    return reverse(routePaths.operation.operations.subOperations.path, {
      id: parentOperationId,
      subOperationId: operationId
    });
  } else if (parentOperationId != null) {
    return reverse(routePaths.operation.operations.subOperations.contrats.redactionFollowUp.path, {
      contractId,
      id: parentOperationId,
      subOperationId: operationId
    });
  } else if (isImportedContract) {
    return reverse(routePaths.operation.path, { id: operationId });
  }
  return reverse(routePaths.operation.contrats.redactionFollowUp.path, { contractId, id: operationId });
};

/**
 * Must return true if the current page is a signature workflow page.
 * eg: /operation/1/signatures/1, /operation/1/signatures/1/signataires...
 */
export const isSignatureWorkflowUrl = () => {
  return window.location.pathname.match('signatures\\/\\d+') != null;
};

export const getRegisteredLetterUrl = (args: {
  contractId: number;
  operationId: number;
  parentOperationId?: number;
}) => {
  const url = getOperationUrl({
    id: args.operationId,
    parentOperationId: args.parentOperationId
  });

  return getOperationRoute('contrats.registeredLetterCreation.path', url, {
    contractId: args.contractId,
    id: args.parentOperationId ?? args.operationId,
    subOperationId: args.parentOperationId ? args.operationId : undefined
  });
};

export const getOperationBaseRoute = () => {
  const subOperation = isSubOperationRoute(window.location.pathname);
  return subOperation ? routePaths.operation.operations.subOperations : routePaths.operation;
};

export const getPreEtatDateUrl = (args: { operationId: number; parentOperationId?: number }) => {
  const url = getOperationUrl({
    id: args.operationId,
    parentOperationId: args.parentOperationId
  });

  return getOperationRoute('preEtatDate.path', url, {
    id: args.parentOperationId ?? args.operationId,
    subOperationId: args.parentOperationId ? args.operationId : undefined
  });
};
