import './operationDocumentCreationForm.scss';
import { AnswerDict, generateNewDocumentAnswer } from '@mynotary/crossplatform/records/api';
import React, { ChangeEvent, ReactElement, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { keys, size } from 'lodash';
import {
  filterForm,
  Form,
  mergeAndCopyAnswer,
  Record,
  selectLegalsContext,
  selectRecord,
  selectRecordForm,
  selectRecordUnfoldForm,
  unfoldForm,
  updateRecordAnswer
} from '@mynotary/frontend/legals/api';
import { MnButton, MnInput, MnTitle } from '@mynotary/frontend/shared/ui';
import { MnProps } from '@mynotary/frontend/shared/util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { FormNode, FormQuestion, UploadFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { selectCurrentContractId, selectCurrentOperationId } from '@mynotary/frontend/current-operation/api';

interface OperationDocumentCreationSelectorProps extends MnProps {
  handleDocumentCreation: (record: Record, document: UploadFormQuestion) => void;
  legalRecordId: number;
}

const OperationDocumentCreationForm = ({
  handleDocumentCreation,
  legalRecordId
}: OperationDocumentCreationSelectorProps): ReactElement => {
  const contractId = useSelector(selectCurrentContractId);
  const operationId = useSelector(selectCurrentOperationId);

  const [documentQuestions, setDocumentQuestions] = useState<FormQuestion[]>([]);
  const [answer, setAnswer] = useState<AnswerDict>({});
  const [documentName, setDocumentName] = useState<string>('');

  const formContext = useSelector(selectLegalsContext(operationId, contractId));

  const dispatch = useAsyncDispatch();
  const recordForm = useSelector(selectRecordForm(legalRecordId));
  const recordUnfoldForm = useSelector(selectRecordUnfoldForm(legalRecordId));
  const record = useSelector(selectRecord(legalRecordId));
  const nodes: FormNode[] = [...documentQuestions].filter((node) => node != null) as FormNode[];

  const documentGroups = useMemo(() => {
    const unfoldedForm = unfoldForm(recordForm, recordUnfoldForm.answer, true);
    const filteredForm = filterForm(
      unfoldedForm,
      recordUnfoldForm.answer,
      {
        CONDITION: {}
      },
      formContext,
      record?.id
    );

    return filteredForm;
  }, [formContext, record?.id, recordForm, recordUnfoldForm.answer]);

  const handleChange = (event: ChangeEvent<HTMLInputElement>): void => {
    setDocumentName(event.target.value);
  };

  const handleAnswerChange = (update: AnswerDict): void => {
    setAnswer((answer) => {
      return mergeAndCopyAnswer(answer, update);
    });
  };

  useEffect(() => {
    const questions: FormQuestion[] = [];

    if (documentGroups.length === 1) {
      setAnswer({ otherDocumentLevel: { value: documentGroups[0].id } });
    }
    setDocumentQuestions(questions);
  }, [documentGroups]);

  const onFinishCreation = async (): Promise<void> => {
    if (record) {
      const newDocumentAnswer = generateNewDocumentAnswer(documentName);

      dispatch(updateRecordAnswer(record.id, newDocumentAnswer));

      const documentId = keys(newDocumentAnswer['divers'].additionalDocuments)[0];

      const uploadForm = {
        created: true,
        deletable: true,
        id: documentId,
        label: documentName,
        optional: true,
        renamable: true
      };
      handleDocumentCreation(record, uploadForm);
    }
  };

  const isButtonDisabled = documentName.length === 0 || size(answer['divers']) > 0;

  return (
    <div className='operation-document-creation-form'>
      <Form answer={answer} editable forms={nodes} onChange={handleAnswerChange} />
      <div className='odcf-container'>
        <MnTitle className='odcf-name-title' label='Nommez votre document' />
        <p className='odcf-label'>Nom du document</p>
        <MnInput
          className='odcf-input'
          onChange={handleChange}
          onEnter={onFinishCreation}
          testId={'annex-name'}
          value={documentName}
        />
      </div>
      <div className='odcf-buttons-container'>
        <MnButton
          className='odcf-button'
          disabled={isButtonDisabled}
          icon='/assets/images/pictos/icon/plus-light.svg'
          label='Créer'
          onClick={onFinishCreation}
          size='medium'
          variant='primary'
        />
      </div>
    </div>
  );
};

export { OperationDocumentCreationForm };
