import './operationGroupRecordMobileContent.scss';
import {
  Record,
  selectContract,
  selectAnnexedDocumentByContract,
  selectInterpolatedContractFrame,
  useDocumentFiles
} from '@mynotary/frontend/legals/api';
import { ReactElement } from 'react';
import { useSelector } from 'react-redux';
import { OperationGroupRecordMobileTile } from './operationGroupRecordMobileTile';
import { MnAnnexSelect } from '../../annexDocuments/annexSelect';
import { MnFloatingPopin } from '../../floatingPopin/floatingPopin';
import { FormQuestion, UploadFormAnswer } from '@mynotary/crossplatform/shared/forms-util';
import { FileViewer } from '@mynotary/frontend/files/api';

interface OperationGroupRecordMobileProps {
  answer: UploadFormAnswer;
  contractId: number;
  document: FormQuestion;
  documentCount: number;
  editable: boolean;
  operationId: number;
  record: Record;
}

const OperationGroupRecordMobileContent = ({
  answer,
  contractId,
  document,
  documentCount,
  editable,
  operationId,
  record
}: OperationGroupRecordMobileProps): ReactElement => {
  const contract = useSelector(selectContract(contractId));
  const annexedDocuments = useSelector(selectAnnexedDocumentByContract(contractId));
  const frame = useSelector(selectInterpolatedContractFrame(operationId, contractId));
  const { handleFileDeletion, handleSelectedFile, orderedFiles, selectedFile } = useDocumentFiles({
    answer,
    documentId: document.id,
    hasDefaultSelectedFile: false
  });

  return (
    <>
      <div className='operation-group-record-mobile-content' key={document.id}>
        <MnAnnexSelect
          annexedDocuments={annexedDocuments}
          canUpdate={editable}
          className='ogrmc-annex-select'
          contract={contract}
          document={document}
          documentCount={documentCount}
          id={operationId}
          questions={frame?.questions?.[record.id]}
          recordId={record.id}
          type='bordered'
        />
        {orderedFiles.map((file) => {
          return (
            <OperationGroupRecordMobileTile
              editable={editable}
              file={file}
              handleFileDeletion={(file) => handleFileDeletion(file, record)}
              handleSelectedFile={handleSelectedFile}
              key={file.id}
            />
          );
        })}
      </div>
      {selectedFile && (
        <MnFloatingPopin isStatic={true} onClose={() => handleSelectedFile(undefined)}>
          <FileViewer fileId={selectedFile.id} />
        </MnFloatingPopin>
      )}
    </>
  );
};

export { OperationGroupRecordMobileContent };
