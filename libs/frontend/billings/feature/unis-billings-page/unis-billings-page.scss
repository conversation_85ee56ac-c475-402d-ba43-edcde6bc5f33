@use 'style/variables/colors' as *;
@use 'style/mixins' as *;

.unis-billings-page {
  height: 100vh;

  .ubp—title {
    @include responsive($tablet-max) {
      display: none;
    }

    margin-bottom: 16px;
    color: $black;
    text-align: center;
  }

  .ubp—alert-box {
    max-width: fit-content;
    margin: 0 auto 24px;
  }

  .ubp—box {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .ubp—manager—button {
    display: flex;
    margin: 24px auto 0;
  }

  .ubp-floating-button {
    position: fixed;
    right: 80px;
    bottom: 16px;
  }

  .ubp-content {
    overflow: auto;
    display: flex;
    flex-direction: column;
    flex-grow: 1;

    height: calc(100vh - 70px);
    padding: 48px;
  }

  .ubp—payment {
    padding-bottom: 80px;
  }
}
