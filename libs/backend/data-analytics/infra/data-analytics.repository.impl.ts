import { Prisma, PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { Injectable } from '@nestjs/common';
import {
  DataAnalyticsRepository,
  GetDocumentRequestsArgs,
  GetDriveImportFilesArgs,
  GetInvoiceArgs,
  GetOperationsArgs,
  GetOrganizationsArgs,
  GetUsersArgs,
  OrganizationDataAnalytic
} from '@mynotary/backend/data-analytics/core';
import { map } from 'lodash';
import { assertNotNull, MnAddress } from '@mynotary/crossplatform/shared/util';

@Injectable()
export class DataAnalyticsRepositoryImpl implements DataAnalyticsRepository {
  constructor(private prisma: PrismaService) {}

  async getDataAnalyticsOperations(args: GetOperationsArgs) {
    const operations = await this.prisma.legal_component.findMany({
      select: {
        creation_time: true,
        creator_user_id: true,
        id: true,
        legal_component_operation: {
          select: {
            parent_operation_id: true
          }
        },
        legal_component_template: {
          select: {
            id_str: true,
            label: true
          }
        },
        organization_id: true,
        user: {
          select: {
            organization_user: {
              select: {
                organization_id: true,
                role: {
                  select: {
                    name: true
                  }
                }
              }
            }
          }
        }
      },
      where: {
        creation_time: {
          gte: args.startDate,
          lte: args.endDate
        },
        creator_user_id: {
          not: null
        },
        deleted: {
          not: true
        },
        legal_component_template: {
          type: 'OPERATION'
        }
      }
    });

    const operationsWithRole = map(operations, (operation) => {
      assertNotNull(operation.organization_id, 'Operation has no organization');
      assertNotNull(operation.creator_user_id, 'Operation has no creator');
      assertNotNull(operation.legal_component_template?.id_str, 'Operation has no template');

      /** Notes : any user invited on operation can create an operation */
      const memberRole = this.findRole({
        organizationId: operation.organization_id,
        userId: operation.creator_user_id,
        userMember: operation.user?.organization_user
      });

      return {
        creationTime: operation.creation_time,
        id: operation.id.toString(),
        isProgram: operation.legal_component_operation?.parent_operation_id !== null,
        label: operation.legal_component_template.label,
        operationType: operation.legal_component_template.id_str,
        organizationId: operation.organization_id.toString(),
        role: memberRole,
        userId: operation.creator_user_id?.toString()
      };
    });

    return operationsWithRole;
  }

  async getDataAnalyticsOrganizations(): Promise<OrganizationDataAnalytic[]> {
    const organizations = await this.prisma.organization.findMany({
      select: {
        address: true,
        creation_time: true,
        id: true,
        name: true,
        subscription: {
          select: {
            id: true,
            plan_type: true,
            status: true
          }
        },
        type: true
      },
      where: {
        deletion_time: null
      }
    });

    const orgaHoldings = await this.prisma.organization_organization.findMany({
      select: {
        parent_organization_id: true,
        sub_organization_id: true
      }
    });

    const organizationMapped = organizations.map(async (organization) => {
      assertNotNull(organization.name, 'Organization has no name');

      const organizationAddress = organization.address as MnAddress;

      const { holdingId, majorHoldingId } = this.findHoldingId({
        orgaHoldings,
        organizationId: organization.id
      });

      const statusList = await this.generateFeatureStatusList(organization.id.toString());

      return {
        address: organizationAddress.address || '',
        city: organizationAddress.city || '',
        creationTime: organization.creation_time.toISOString(),
        features: statusList,
        holdingId,
        id: organization.id.toString(),
        majorHoldingId,
        name: organization.name,
        subscriptionId: organization.subscription?.id.toString(),
        subscriptionPlanType: organization.subscription?.plan_type,
        subscriptionStatus: organization.subscription?.status,
        type: organization.type,
        zipCode: organizationAddress.zip || ''
      };
    });

    return Promise.all(organizationMapped);
  }

  async getDataAnalyticsUsers(args: GetUsersArgs) {
    const users = await this.prisma.user.findMany({
      select: {
        creation_time: true,
        email: true,
        id: true,
        last_authentication_time: true,
        organization_user: {
          select: {
            organization_id: true,
            role: {
              select: {
                name: true
              }
            }
          }
        },
        verified: true
      },
      where: {
        last_authentication_time: {
          gte: args.startDate,
          lte: args.endDate
        }
      }
    });

    assertNotNull(users, 'No users found');

    return users.map((user) => ({
      creationTime: user.creation_time,
      email: user.email,
      lastAuthenticationTime: user.last_authentication_time || undefined,
      organizationIds: user.organization_user.map((orgUser) => orgUser.organization_id.toString()),
      userId: user.id.toString(),
      verified: user.verified
    }));
  }

  async getDataAnalyticsDriveFiles(args: GetDriveImportFilesArgs) {
    const drive = await this.prisma.drive_file.findMany({
      select: {
        creation_time: true,
        id: true,
        legal_component_operation: {
          select: {
            legal_component: {
              select: {
                organization_id: true
              }
            }
          }
        },
        operation_id: true,
        user: USER_ROLE_COLUMNS,
        user_id: true
      },
      where: {
        creation_time: {
          gte: args.startDate,
          lte: args.endDate
        }
      }
    });

    assertNotNull(drive, 'No drive files found');

    const driveWithRole = drive.map((driveFile) => {
      assertNotNull(
        driveFile.legal_component_operation.legal_component.organization_id,
        'Drive file has no organization id'
      );

      const userDriveRole = this.findRole({
        operationId: driveFile.operation_id,
        operationInvitation: driveFile.user?.operation_invitation,
        organizationId: driveFile.legal_component_operation.legal_component.organization_id,
        userId: driveFile.user_id,
        userMember: driveFile.user?.organization_user
      });

      return {
        creationTime: driveFile.creation_time,
        id: driveFile.id.toString(),
        operationId: driveFile.operation_id.toString(),
        role: userDriveRole,
        userId: driveFile.user_id?.toString()
      };
    });

    return driveWithRole;
  }

  async getDataAnalyticsDocumentRequests(args: GetDocumentRequestsArgs) {
    const documentRequests = await this.prisma.task.findMany({
      select: {
        creation_time: true,
        creator_user_id: true,
        id: true,
        legal_component: {
          select: {
            legal_component_operation: {
              select: {
                legal_component_id: true
              }
            },
            organization_id: true
          }
        },
        user: USER_ROLE_COLUMNS
      },
      where: {
        creation_time: {
          gte: args.startDate,
          lte: args.endDate
        },
        type: 'DOCUMENT_REQUEST'
      }
    });

    const docRequestWithRole = documentRequests.map((documentRequest) => {
      assertNotNull(
        documentRequest.legal_component?.legal_component_operation?.legal_component_id,
        `Document request ${documentRequest.id} has no operation id`
      );
      assertNotNull(
        documentRequest.legal_component?.organization_id,
        `Document request ${documentRequest.id} has no organization id associated`
      );

      const documentRequestUserRole = this.findRole({
        operationId: documentRequest.legal_component.legal_component_operation.legal_component_id,
        operationInvitation: documentRequest.user?.operation_invitation,
        organizationId: documentRequest.legal_component.organization_id,
        userId: documentRequest.creator_user_id,
        userMember: documentRequest.user?.organization_user
      });

      return {
        creationTime: documentRequest.creation_time,
        id: documentRequest.id.toString(),
        operationId: documentRequest.legal_component.legal_component_operation.legal_component_id.toString(),
        role: documentRequestUserRole,
        userId: documentRequest.creator_user_id.toString()
      };
    });

    return docRequestWithRole;
  }

  async getDataAnalyticsInvoices(args: GetInvoiceArgs) {
    const invoices = await this.prisma.invoice.findMany({
      select: {
        creation_time: true,
        creator_id: true,
        id: true,
        organization_id: true,
        user: USER_ROLE_COLUMNS
      },
      where: {
        creation_time: {
          gte: args.startDate,
          lte: args.endDate
        }
      }
    });

    const invoicesWithRole = invoices.map((invoice) => {
      assertNotNull(invoice.id, 'Invoice has no id');

      const roleName = this.findRole({
        organizationId: invoice.organization_id,
        userId: invoice.creator_id,
        userMember: invoice.user?.organization_user
      });

      return {
        creationTime: invoice.creation_time,
        id: invoice.id.toString(),
        organizationId: invoice.organization_id.toString(),
        role: roleName,
        userId: invoice.creator_id.toString()
      };
    });

    return invoicesWithRole;
  }

  private findHoldingId({ orgaHoldings, organizationId }: { orgaHoldings: OrgaHolding[]; organizationId: number }) {
    let majorHoldingId: number | null = null;
    let holdingId: number | null = null;

    const holding = orgaHoldings.find((orgaHolding) => orgaHolding.sub_organization_id === organizationId);

    if (holding) {
      holdingId = holding.parent_organization_id;

      const majorHolding = orgaHoldings.find((orgaHolding) => orgaHolding.sub_organization_id === holdingId);

      if (majorHolding) {
        majorHoldingId = majorHolding.parent_organization_id;
      }
    }

    return { holdingId: holdingId?.toString(), majorHoldingId: majorHoldingId?.toString() };
  }

  private async findOrganizationIdsSubscriptionLastUpdateDate(args: GetOrganizationsArgs) {
    const subscriptions = await this.prisma.subscription.findMany({
      select: {
        organization: {
          select: {
            id: true
          }
        }
      },
      where: {
        update_time: {
          gte: args.startDate,
          lte: args.endDate
        }
      }
    });

    return subscriptions.map((subscription) => subscription.organization.map((org) => org.id)).flat();
  }

  private async generateFeatureStatusList(organizationId: string): Promise<string[]> {
    const features = await this.prisma.organization_feature.findMany({
      select: {
        feature_id: true
      },
      where: {
        organization_id: parseInt(organizationId)
      }
    });

    return features.map((feature) => feature.feature_id.toString());
  }

  private findRole({
    operationId,
    operationInvitation,
    organizationId,
    userId,
    userMember
  }: {
    operationId?: number;
    operationInvitation?: { operation_id: number; role?: { name: string } }[];
    organizationId: number;
    userId: number | null;
    userMember?: { organization_id: number; role: { name: string } | null }[];
  }): string | undefined {
    if (!userId) {
      return undefined;
    }

    assertNotNull(operationInvitation || userMember, `No role found for user ${userId}`);

    const memberRole = userMember?.find((member) => member.organization_id === organizationId);

    if (memberRole) {
      assertNotNull(memberRole.role, 'Member has no role');
      return memberRole.role.name;
    }

    const invitationRole = operationInvitation?.find((invitation) => invitation.operation_id === operationId);

    if (invitationRole) {
      assertNotNull(invitationRole.role, 'Member has no role');
      return invitationRole.role.name;
    }

    return undefined;
  }
}

const USER_ROLE_COLUMNS = Prisma.validator<Prisma.userDefaultArgs>()({
  select: {
    operation_invitation: {
      select: {
        operation_id: true,
        role: {
          select: {
            name: true
          }
        }
      }
    },
    organization_user: {
      select: {
        organization_id: true,
        role: {
          select: {
            name: true
          }
        }
      }
    }
  }
});

interface OrgaHolding {
  parent_organization_id: number;
  sub_organization_id: number;
}
