import { Injectable, Type, UnprocessableEntityException } from '@nestjs/common';
import { AuthorizationArgs, bodyResolver } from '@mynotary/backend/shared/auth-util';
import { AuthorizationBase, AuthorizationsApiService } from '@mynotary/backend/authorizations/api';

interface HasInvoiceAccessOption {
  invoiceIdResolver?: (args: AuthorizationArgs) => string | null;
  organizationIdResolver?: (args: AuthorizationArgs) => string | null;
}
export function HasInvoiceAccess(options: HasInvoiceAccessOption = {}): Type<AuthorizationBase> {
  const invoiceIdResolver = options?.invoiceIdResolver ?? bodyResolver('invoiceId');
  const organizationIdResolver = options?.organizationIdResolver ?? bodyResolver('organizationId');

  @Injectable()
  class HasInvoiceAccess extends AuthorizationBase {
    constructor(private authorizationsApiService: AuthorizationsApiService) {
      super();
    }

    async isAuthorized(authorizationRequest: AuthorizationArgs) {
      const userId = authorizationRequest.userInfo?.userId;
      const invoiceId = invoiceIdResolver(authorizationRequest);
      const organizationId = organizationIdResolver(authorizationRequest);

      const isUserVerified = await this.authorizationsApiService.isUserVerified(userId);

      if (!isUserVerified || userId == null) {
        return false;
      }

      if (invoiceId == null) {
        throw new UnprocessableEntityException('Invoice id is required');
      }

      if (organizationId == null) {
        throw new UnprocessableEntityException('Organization id is required');
      }

      return await this.authorizationsApiService.hasInvoiceAccess({ invoiceId, organizationId, userId });
    }
  }

  return HasInvoiceAccess;
}
