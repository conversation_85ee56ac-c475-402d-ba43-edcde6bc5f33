import { Injectable } from '@nestjs/common';
import {
  assertNotNull,
  datePartsToDate,
  Exception,
  InvalidInputError,
  MnDate
} from '@mynotary/crossplatform/shared/util';
import {
  Difference,
  EtatCivilDemandeBff,
  EtatCivilSummary,
  EtatCivilTitulaireActe
} from '@mynotary/crossplatform/bff-portalys/api';
import { EtatcivilClient } from '@mynotary/backend/etatscivils-client/api';
import { etatcivilToBackConverter, etatCivilToFrontConverter } from './demande-bff.converter';
import { buildDemandesBff, buildEtatCivilTitulaireActes } from './titulaire-acte-bff.converter';
import { buildSummary, CommuneResolverArgs } from './summary-bff.converter';
import { AdsnClient } from '@mynotary/backend/adsn-client/api';
import { MnapiClient } from '@mynotary/backend/mnapi-client/api';
import { PlaneteStatus } from '@mynotary/crossplatform/api-adsn/api';
import {
  EtatCivilDemande,
  EtatCivilEvenement,
  EtatCivilTitulaire,
  EtatCivilTypeActe
} from '@mynotary/crossplatform/api-etatscivils/api';
import { AuthorizationsApiService } from '@mynotary/backend/authorizations/api';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { Civility } from '@mynotary/crossplatform/shared/users-core';
import { AnswerDict, LegalRecord } from '@mynotary/crossplatform/records/api';
import { LegalsApiService } from '@mynotary/backend/legals/api';

@Injectable()
export class EtatcivilBffService {
  constructor(
    private etatcivilClient: EtatcivilClient,
    private adsnClient: AdsnClient,
    private mnApiClient: MnapiClient,
    private authorizationsApiService: AuthorizationsApiService,
    private legalsApiService: LegalsApiService
  ) {}

  async setEtatcivilTitulaires(titulaireIds: string[]) {
    const titulaireData = await Promise.all(
      titulaireIds.map(async (id) => {
        const legalData = await this.mnApiClient.getLegalData(id);
        return { id, legalData };
      })
    );
    return buildEtatCivilTitulaireActes(titulaireData);
  }

  async setEtatcivilTitulaireActes(titulaireActes: EtatCivilTitulaireActe[]) {
    // load 'personne' info from id
    const titulaireIds = new Set<string>();
    titulaireActes.forEach((acte) => titulaireIds.add(acte.id));
    const demandes: EtatCivilDemandeBff[] = [];

    // for each 'titulaire', create a 'demande' for the selected 'actes'
    for (const titulaireActe of titulaireActes) {
      const legalData = await this.mnApiClient.getLegalData(titulaireActe.id);
      demandes.push(...buildDemandesBff(titulaireActe, legalData));
    }

    return demandes;
  }

  async sumupDemandes(demandes: EtatCivilDemandeBff[]): Promise<EtatCivilSummary[]> {
    const communeResolver = async (args: CommuneResolverArgs) => {
      let found = await this.adsnClient.getReferentielComedec(args);
      if (found.length > 0) {
        return found[0];
      }
      found = await this.adsnClient.getReferentielComedec({ codeInsee: args.codeInsee });
      if (found.length > 0) {
        return found[0];
      }
      found = await this.adsnClient.getReferentielComedec({ nom: args.nom });
      if (found.length > 0) {
        return found[0];
      }
      return undefined;
    };
    const summaries: EtatCivilSummary[] = [];
    // get all unique 'titulaire' ids
    const titulaireIds = new Set<string>();
    demandes.forEach((demande) => titulaireIds.add(demande.titulaireId));
    for (const id of titulaireIds) {
      const legalData = await this.mnApiClient.getLegalData(id);
      const demandesTitulaire = demandes.filter((demande) => demande.titulaireId === id);
      const summaryTitulaire = await buildSummary({
        communeResolver,
        demandes: demandesTitulaire,
        legalData
      });
      summaries.push(summaryTitulaire);
    }

    return summaries;
  }

  async createDemandes(demandeBff: EtatCivilDemandeBff) {
    try {
      const legalData = await this.mnApiClient.getLegalData(demandeBff.titulaireId);
      return await this.etatcivilClient.createEtatCivil(etatcivilToBackConverter(demandeBff, legalData));
    } catch (error) {
      throw new Exception('Erreur lors de la création de la demande', { cause: error });
    }
  }

  async getDemande({ demandeId, userId }: GetDemandeArgs) {
    const demandeBack = await this.etatcivilClient.getEtatCivilById(demandeId);
    const demande = etatCivilToFrontConverter(demandeBack);

    const organizations = await this.mnApiClient.getOrganizations({ operationId: demandeBack.operationId });
    if (!organizations || organizations.length === 0) {
      throw new Exception(`Cannot find organization for operationId ${demandeBack.operationId}`);
    }
    const canSend = await this.authorizationsApiService.hasPermission({
      organizationId: organizations[0].id,
      permission: {
        entityType: EntityType.PL_ETAT_CIVIL,
        type: PermissionType.PL_ETAT_CIVIL_SEND
      },
      userId
    });
    if (!canSend) {
      demande.showNextStep = false;
    }
    return demande;
  }

  async updateDemande(demandeBff: EtatCivilDemandeBff) {
    assertNotNull(demandeBff.id, 'id');

    let old: EtatCivilDemande;
    let demande: EtatCivilDemande;

    try {
      /* retrieve fields not handled at the BFF/front level, and merge */
      old = await this.etatcivilClient.getEtatCivilById(demandeBff.id);
      const legalData = await this.mnApiClient.getLegalData(demandeBff.titulaireId);

      demande = { ...old, ...etatcivilToBackConverter(demandeBff, legalData) };

      if (demandeBff.updatableStatusForced) {
        demande.status = PlaneteStatus.DRAFT;
      }

      await this.etatcivilClient.updateEtatCivil(demande);
    } catch (error) {
      throw new Exception('Erreur lors de la mise à jour de la demande', { cause: error });
    }

    if (demandeBff.gotoNextStep) {
      /* if this throws, the error is forwarded directly */
      await this.etatcivilClient.planeteGetEtatCivil(demande.id);

      try {
        demande.status = this.nextStep(old.status);
        if (demande.status === PlaneteStatus.CLOSED) {
          this.resetReviewFlags(demande).then(); /* no need to wait */
        }
        await this.etatcivilClient.updateEtatCivil(demande);
      } catch (error) {
        throw new Exception('Erreur lors de la mise à jour de la demande', { cause: error });
      }
    }
  }

  async gotoNextStepEtatCivilById({ id, userId }: { id: string; userId: string }) {
    const demande = await this.etatcivilClient.getEtatCivilById(id);
    if (demande.status === PlaneteStatus.TO_SIGN) {
      await this.etatcivilClient.planeteGetEtatCivil(id);
    }
    demande.status = this.nextStep(demande.status);
    if (demande.status === PlaneteStatus.CLOSED) {
      this.resetReviewFlags(demande).then(); /* no need to wait */
    }
    demande.userId = userId;
    await this.etatcivilClient.updateEtatCivil(demande);
  }

  private nextStep(status: PlaneteStatus) {
    switch (status) {
      case PlaneteStatus.DRAFT:
        return PlaneteStatus.TO_SIGN;
      case PlaneteStatus.SIGNED:
        return PlaneteStatus.TO_SEND;
      case PlaneteStatus.TO_RECOVER:
        return PlaneteStatus.SENT;
      case PlaneteStatus.RECEIVED:
        return PlaneteStatus.CLOSED;
      default:
        return status;
    }
  }

  async getDifferences(demandeId: string) {
    const demande = await this.etatcivilClient.getEtatCivilById(demandeId);
    const response = await this.etatcivilClient.getEtatCivilResponse(demandeId);

    const differences: Difference[] = [];
    if (demande.evenement && response.evenement) {
      const evenementDifferences = await this.getEvenementDifferences(demande, response);
      differences.push(...evenementDifferences);
    }
    for (const titulaire of demande.titulaires) {
      const index = demande.titulaires.indexOf(titulaire);
      const titulaireDifferences = await this.getTitulaireDifferences(titulaire, response.titulaires[index]);
      differences.push(...titulaireDifferences);
    }

    /*
     * special case : the response contains 1 more Titulaire than the request, create a difference to add a new Fiche Personne
     * When this happens, we can assume that:
     * - the type is VAM
     * - 1 Titulaire was sent, 2 were retreived
     * - the first Titulaire in the response is the one we sent, followed by the new one
     * TODO: handle the case (frontend and BFF for records creation)
     */
    if (response.titulaires.length > demande.titulaires.length) {
      differences.push({
        creationKey: 'titulaire-1',
        displayValue: `${response.titulaires.at(-1)?.nom} ${response.titulaires.at(-1)?.prenoms.join(' ')}`,
        label: 'Créer la fiche conjoint',
        questionId: '',
        recordId: demande.id,
        value: undefined
      });
    }

    return differences;
  }

  private async resetReviewFlags(demande: EtatCivilDemande) {
    for (const titulaire of demande.titulaires) {
      const legalData = await this.mnApiClient.getLegalData(titulaire.id);
      const record = legalData.legalRecords.find((personne) => personne.id === titulaire.id);
      assertNotNull(record, 'legal record');
      this.cleanupAnswer(record.answer);
      await this.mnApiClient.updateRecord({ answer: record.answer, id: record.id, templateId: record.templateId });
    }

    const evenement = demande.evenement;
    if (evenement) {
      const legalData = await this.mnApiClient.getLegalData(evenement.id);
      const record = legalData.legalRecords.find((event) => event.id === evenement.id);
      assertNotNull(record, 'legal record');
      this.cleanupAnswer(record.answer);
      await this.mnApiClient.updateRecord({ answer: record.answer, id: record.id, templateId: record.templateId });
    }
  }

  private cleanupAnswer(answer: AnswerDict) {
    for (const questionId of Object.keys(answer)) {
      if (answer[questionId].review) {
        answer[questionId].review = false;
      }
    }
  }

  private async getEvenementDifferences(request: EtatCivilDemande, response: EtatCivilDemande) {
    const requestEvenement = request.evenement;
    const responseEvenement = response.evenement;
    assertNotNull(requestEvenement, 'evenement in request');
    assertNotNull(responseEvenement, 'evenement in response');

    const legalData = await this.mnApiClient.getLegalData(request.titulaires[0].id);
    let record;
    if (request.type === EtatCivilTypeActe.VAD) {
      record = legalData.legalRecords.find((record) => record.id === request.titulaires[0].id);
    } else {
      /* VAM */
      record = legalData.legalRecords.find(
        (record) => record.templateId === 'RECORD__LIEN__SITUATION_MARITALE__MARIAGE'
      );
    }
    assertNotNull(record, 'legal record');

    const differences: Difference[] = [];
    if (
      requestEvenement.annee !== responseEvenement.annee ||
      requestEvenement.jour !== responseEvenement.jour ||
      requestEvenement.mois !== responseEvenement.mois ||
      requestEvenement.heures !== responseEvenement.heures ||
      requestEvenement.minutes !== responseEvenement.minutes
    ) {
      let labelDate: string;
      let questionIdDate: string;
      /* only 2 kind of requests are handled here : VAD / VAM
       * 'evenement' field is unused for VAN
       * other kinds are unused in the context of an ANTS request */
      if (request.type === EtatCivilTypeActe.VAD) {
        labelDate = `Date de décès de ${record.answer.nom?.value} ${record.answer.prenoms?.value}`;
        questionIdDate = 'informations_personnelles_date_deces';
      } else {
        labelDate = 'Date de mariage';
        questionIdDate = 'date_mariage';
      }

      const displayValue = responseEvenement.jour
        ? `${responseEvenement.jour}/${responseEvenement.mois}/${responseEvenement.annee}`
        : responseEvenement.mois
          ? `${responseEvenement.mois}/${responseEvenement.annee}`
          : responseEvenement.annee;

      differences.push({
        displayValue,
        label: labelDate,
        questionId: questionIdDate,
        recordId: record.id,
        value: {
          day: responseEvenement.jour,
          month: responseEvenement.mois,
          timestamp: datePartsToDate(responseEvenement.annee, responseEvenement.mois, responseEvenement.jour).getTime(),
          year: responseEvenement.annee
        }
      });

      this.tagForReview(record, questionIdDate);
    }

    if (requestEvenement.codePays !== responseEvenement.codePays) {
      const label = request.type === EtatCivilTypeActe.VAD ? 'Pays du décès' : 'Pays du mariage';
      const questionId =
        request.type === EtatCivilTypeActe.VAD ? 'informations_personnelles_pays_deces' : 'pays_mariage';
      differences.push({
        displayValue: responseEvenement.codePays,
        label,
        questionId,
        recordId: record.id,
        value: responseEvenement.codePays
      });
      this.tagForReview(record, questionId);
    }

    if (requestEvenement.libelleCommune !== responseEvenement.libelleCommune) {
      const label = request.type === EtatCivilTypeActe.VAD ? 'Commune du décès' : 'Commune du mariage';
      const questionId =
        request.type === EtatCivilTypeActe.VAD ? 'informations_personnelles_commune_deces' : 'ville_mariage';
      differences.push({
        displayValue: responseEvenement.libelleCommune,
        label,
        questionId,
        recordId: record.id,
        value: responseEvenement.libelleCommune
      });
      this.tagForReview(record, questionId);
    }

    if (responseEvenement.codeDepartement && requestEvenement.codeDepartement !== responseEvenement.codeDepartement) {
      const label = request.type === EtatCivilTypeActe.VAD ? 'Département du décès' : 'Département du mariage';
      const questionId =
        request.type === EtatCivilTypeActe.VAD ? 'informations_personnelles_departement_deces' : 'departement_mariage';
      differences.push({
        displayValue: responseEvenement.codeDepartement,
        label,
        questionId,
        recordId: record.id,
        value: responseEvenement.codeDepartement
      });
      this.tagForReview(record, questionId);
    }

    if (
      request.type === EtatCivilTypeActe.VAM &&
      responseEvenement.libelleVoie &&
      requestEvenement.libelleVoie !== responseEvenement.libelleVoie
    ) {
      differences.push({
        displayValue: responseEvenement.libelleVoie,
        label: 'Libellé voie du mariage',
        questionId: 'libelle_voie_mariage',
        recordId: requestEvenement.id,
        value: responseEvenement.libelleVoie
      });
      this.tagForReview(record, 'libelle_voie_mariage');
    }

    if (
      request.type === EtatCivilTypeActe.VAM &&
      responseEvenement.numeroVoie &&
      requestEvenement.numeroVoie !== responseEvenement.numeroVoie
    ) {
      differences.push({
        displayValue: responseEvenement.numeroVoie,
        label: 'Numéro voie du mariage',
        questionId: 'numero_voie_mariage',
        recordId: requestEvenement.id,
        value: responseEvenement.numeroVoie
      });
      this.tagForReview(record, 'numero_voie_mariage');
    }

    if (differences.length > 0) {
      await this.mnApiClient.updateRecord({ answer: record.answer, id: record.id, templateId: record.templateId });
    }

    return differences;
  }

  private async getTitulaireDifferences(request: EtatCivilTitulaire, response: EtatCivilTitulaire) {
    const legalData = await this.mnApiClient.getLegalData(request.id);
    const record = legalData.legalRecords.find((personne) => personne.id === request.id);
    assertNotNull(record, 'legal record');
    const fullName = `${record.answer.nom?.value} ${record.answer.prenoms?.value}`;

    const differences: Difference[] = [];
    if (request.nom !== response.nom) {
      differences.push({
        displayValue: response.nom,
        label: `Nom de ${fullName}`,
        questionId: 'nom',
        recordId: request.id,
        value: response.nom
      });
      this.tagForReview(record, 'nom');
    }
    const responsePrenoms = response.prenoms.join(' ');
    if (request.prenoms.join(' ') !== responsePrenoms) {
      differences.push({
        displayValue: responsePrenoms,
        label: `Prénoms de ${fullName}`,
        questionId: 'prenoms',
        recordId: request.id,
        value: responsePrenoms
      });
      this.tagForReview(record, 'prenoms');
    }
    if (request.sexe !== response.sexe) {
      differences.push({
        displayValue: response.sexe === Civility.MAN ? 'Masculin' : 'Féminin',
        label: `Sexe de ${fullName}`,
        questionId: 'sexe',
        recordId: request.id,
        value: response.sexe === Civility.MAN ? 'homme' : 'femme'
      });
      this.tagForReview(record, 'sexe');
    }

    if (
      request.naissance.annee !== response.naissance.annee ||
      request.naissance.jour !== response.naissance.jour ||
      request.naissance.mois !== response.naissance.mois
    ) {
      const displayValue = response.naissance.jour
        ? `${response.naissance.jour}/${response.naissance.mois}/${response.naissance.annee}`
        : response.naissance.mois
          ? `${response.naissance.mois}/${response.naissance.annee}`
          : response.naissance.annee;

      differences.push({
        displayValue,
        label: `Date de naissance de ${record.answer.nom?.value} ${record.answer.prenoms?.value}`,
        questionId: 'informations_personnelles_date_naissance',
        recordId: request.id,
        value: {
          day: response.naissance.jour,
          month: response.naissance.mois,
          timestamp: datePartsToDate(
            response.naissance.annee,
            response.naissance.mois,
            response.naissance.jour
          ).getTime(),
          year: response.naissance.annee
        }
      });
      this.tagForReview(record, 'informations_personnelles_date_naissance');
    }

    if (request.naissance.codePays !== response.naissance.codePays) {
      differences.push({
        displayValue: response.naissance.codePays,
        label: `Pays de naissance de ${fullName}`,
        questionId: 'informations_personnelles_pays_naissance',
        recordId: request.id,
        value: response.naissance.codePays
      });
      this.tagForReview(record, 'informations_personnelles_pays_naissance');
    }
    if (request.naissance.libelleCommune !== response.naissance.libelleCommune) {
      differences.push({
        displayValue: response.naissance.libelleCommune,
        label: `Commune de naissance de ${fullName}`,
        questionId: 'informations_personnelles_ville_naissance',
        recordId: request.id,
        value: response.naissance.libelleCommune
      });
      this.tagForReview(record, 'informations_personnelles_ville_naissance');
    }
    if (
      response.naissance.codeDepartement &&
      request.naissance.codeDepartement !== response.naissance.codeDepartement
    ) {
      differences.push({
        displayValue: response.naissance.codeDepartement,
        label: `Département de naissance de ${fullName}`,
        questionId: 'informations_personnelles_departement_naissance',
        recordId: request.id,
        value: response.naissance.codeDepartement
      });
      this.tagForReview(record, 'informations_personnelles_departement_naissance');
    }

    if (differences.length > 0) {
      await this.mnApiClient.updateRecord({ answer: record.answer, id: record.id, templateId: record.templateId });
    }
    // TODO : add differences for parents ('parent' link not created yet)

    return differences;
  }

  private tagForReview(record: LegalRecord, questionId: string) {
    const newAnswer = { ...record.answer[questionId], review: true };
    record.answer = { ...record.answer, [questionId]: newAnswer };
  }

  async createNewRecord({ creationKey, demandeId, userId }: CreateNewRecordArgs) {
    const keyRegex = /^titulaire-(\d)(?:-parent-(\d))?$/;
    const match = creationKey.match(keyRegex);
    if (!match) {
      throw new InvalidCreationKeyError(creationKey);
    }
    const [, titulaireIndex, parentIndex] = match;

    const demande = await this.etatcivilClient.getEtatCivilById(demandeId);
    const response = await this.etatcivilClient.getEtatCivilResponse(demandeId);
    const operation = await this.legalsApiService.getOperation(demande.operationId);

    if (!parentIndex) {
      // create a new titulaire
      const titulaireAnswer: AnswerDict = this.createTitulaireAnswer(response.titulaires[Number(titulaireIndex)]);
      const newTitulaireRecord = await this.mnApiClient.createRecord({
        answer: titulaireAnswer,
        creatorId: userId,
        organizationId: operation.organizationId,
        templateId: 'RECORD__PERSONNE__PHYSIQUE'
      });

      // get or create link and branch
      const legalDataOldTitulaire = await this.mnApiClient.getLegalData(demande.titulaires[0].id);
      const branchMariage = legalDataOldTitulaire.legalBranches.find(
        (b) =>
          b.legalLinkTemplateId === 'LINK__SITUATION_MARITALE__MARIAGE' && b.fromLegalId === demande.titulaires[0].id
      );
      if (!branchMariage) {
        // create a new link and branch
        const createdData = await this.mnApiClient.createLegalLink({
          creatorId: userId,
          fromLegalId: demande.titulaires[0].id,
          legalLinkTemplateId: 'LINK__SITUATION_MARITALE__MARIAGE',
          toLegalId: newTitulaireRecord.id
        });
        // store marriage data
        const marriageRecord = createdData.legalRecords[0];
        assertNotNull(response.evenement, 'evenement');
        const marriageAnswer: AnswerDict = this.createMarriageAnswer(response.evenement);
        await this.mnApiClient.updateRecord({
          answer: marriageAnswer,
          id: marriageRecord.id,
          templateId: marriageRecord.templateId
        });
      } else {
        // update the 'toId' to point to the new record on the preexisting branch
        branchMariage.toLegalId = newTitulaireRecord.id;
        await this.mnApiClient.updateLegalBranch({ id: branchMariage.id, toLegalId: newTitulaireRecord.id });
      }
    } else {
      // TODO create a new parent
      throw new Error('not implemented');
    }
  }

  private createTitulaireAnswer(titulaire: EtatCivilTitulaire): AnswerDict {
    return {
      informations_personnelles_date_naissance: {
        value: {
          day: titulaire.naissance.jour,
          month: titulaire.naissance.mois,
          timestamp: datePartsToDate(
            titulaire.naissance.annee,
            titulaire.naissance.mois,
            titulaire.naissance.jour
          ).getTime(),
          year: titulaire.naissance.annee
        } satisfies MnDate
      },
      informations_personnelles_departement_naissance: { value: titulaire.naissance.codeDepartement },
      informations_personnelles_pays_naissance: { value: titulaire.naissance.codePays },
      informations_personnelles_ville_naissance: { value: titulaire.naissance.libelleCommune },
      nom: { value: titulaire.nom },
      prenoms: { value: titulaire.prenoms.join(' ') },
      sexe: { value: titulaire.sexe === Civility.MAN ? 'homme' : 'femme' }
    };
  }

  private createMarriageAnswer(evenement: EtatCivilEvenement): AnswerDict {
    return {
      date_mariage: {
        value: {
          day: evenement.jour,
          month: evenement.mois,
          timestamp: datePartsToDate(
            evenement.annee,
            evenement.mois,
            evenement.jour,
            evenement.heures,
            evenement.minutes
          ).getTime(),
          year: evenement.annee
        } satisfies MnDate
      },
      pays_mariage: { value: evenement.codePays },
      ville_mariage: { value: evenement.libelleCommune }
    };
  }
}

type GetDemandeArgs = {
  demandeId: string;
  userId: string;
};

export type CreateNewRecordArgs = {
  creationKey: string;
  demandeId: string;
  userId: string;
};

class InvalidCreationKeyError extends InvalidInputError {
  constructor(key: string) {
    super({
      displayedMessage: `Impossible de créer cet enregistrement`,
      message: `Invalid creation key ${key}`
    });
  }
}
