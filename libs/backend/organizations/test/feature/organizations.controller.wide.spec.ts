import { GetOrganizationQueryDto, OrganizationsController } from '@mynotary/backend/organizations/feature';

import {
  OrganizationDto,
  OrganizationNewDto,
  OrganizationTypeDto,
  OrganizationUpdateDto,
  PlOrganizationNewDtoInteropVersionEnum,
  PlOrganizationNewDtoNotaryOfficeTypeEnum,
  PlOrganizationNewDtoTypeEnum
} from '@mynotary/crossplatform/api-mynotary/openapi';
import { OrganizationsApiService } from '@mynotary/backend/organizations/api';
import { v4 as uuid } from 'uuid';
import {
  OrganizationDefaultRolesService,
  OrganizationDefaultViewsService,
  TooManyChildrenOrganizations
} from '@mynotary/backend/organizations/core';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { InternalEntityType } from '@mynotary/backend/external-apps/api';
import { randomUUID } from 'crypto';
import { OrganizationType } from '@mynotary/crossplatform/organizations/api';
import { provideOrganizationsTest } from '../index';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';

describe(OrganizationsController.name, () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const organizationNewDto: OrganizationNewDto = {
    address: {
      address: '1 rue de Paris',
      city: 'Paris',
      country: 'France',
      formattedAddress: '1 rue du paradis, Paris (75001), France',
      street: '1 rue de Paris',
      zip: '75001'
    },
    name: 'Agence 1',
    siren: '123 123 123',
    type: OrganizationTypeDto.AGENCY
  };
  const organizationPortalysNewDto: OrganizationNewDto = {
    address: {
      address: '1 rue de Paris',
      city: 'Paris',
      country: 'France',
      formattedAddress: '1 rue du paradis, Paris (75001), France',
      street: '1 rue de Paris',
      zip: '75001'
    },
    crpcen: '99031',
    fax: '0405060708',
    iban: {
      bban: '*************************',
      bic: 'BNPAFRPPXXX',
      cle: '85',
      domiciliation: 'BNP PARIBAS',
      guichet: '30001',
      pays: 'FR',
      titulaire: 'M. Dupont'
    },
    interopAppId: 0,
    interopVersion: PlOrganizationNewDtoInteropVersionEnum.V4,
    name: 'Agence 1',
    notaryOfficeType: PlOrganizationNewDtoNotaryOfficeTypeEnum.SCP,
    phone: '0405060707',
    type: PlOrganizationNewDtoTypeEnum.PORTALYS_NOTARY_OFFICE
  };

  it('should return a list of organizations', async () => {
    const { client, testingRepos } = await setup();

    const organization = await testingRepos.organizations.createOrganization({
      name: 'organization_1',
      subscriptionId: '2000'
    });

    await testingRepos.organizations.createOrganization({
      name: 'organization_2',
      subscriptionId: '2'
    });

    const response = await client.get(`/organizations`).query({ subscriptionId: '2000' }).send();

    expect(response.statusCode).toBe(200);
    expect(response.body.items.length).toBe(1);

    expect(response.body).toMatchObject({
      items: [convertToOrganizationDto(organization)] satisfies OrganizationDto[]
    });
  });

  it('should filter organizations by legal operation template id', async () => {
    const { client, testingRepos } = await setup();

    const organization = await testingRepos.organizations.createOrganization({
      name: 'organization_1',
      subscriptionId: '1'
    });

    await testingRepos.organizations.createOrganization({
      name: 'organization_2',
      subscriptionId: '2'
    });

    await testingRepos.roles.createRoleWithPermissions({
      organizationId: organization.id,
      permissions: [
        { entityType: EntityType.PRELLO__IMMOBILIER__LOCATION, permissionType: PermissionType.CREATE_OPERATION }
      ]
    });

    const response = await client
      .get(`/organizations`)
      .query({ legalOperationTemplateId: 'OPERATION__PRELLO__IMMOBILIER__LOCATION' } satisfies GetOrganizationQueryDto)
      .send();

    expect(response.statusCode).toBe(200);
    expect(response.body.items.length).toBe(1);

    expect(response.body).toMatchObject({
      items: [convertToOrganizationDto(organization)] satisfies OrganizationDto[]
    });
  });

  it('should filter organizations by unique identifier', async () => {
    const { client, testingRepos } = await setup();

    const uniqueIdentifier = randomUUID();

    const organization = await testingRepos.organizations.createOrganization({
      name: 'organization_unique_identifier',
      subscriptionId: '1',
      uniqueIdentifier
    });

    await testingRepos.organizations.createOrganization({ name: 'organization_2' });

    const response = await client
      .get(`/organizations`)
      .query({ uniqueIdentifier } satisfies GetOrganizationQueryDto)
      .send();

    expect(response.statusCode).toBe(200);
    expect(response.body.items.length).toBe(1);

    expect(response.body).toMatchObject({
      items: [convertToOrganizationDto(organization)] satisfies OrganizationDto[]
    });
  });

  it('should return a child organization with hasHoldingSubscription as true when has same subscription as holding', async () => {
    const { client, testingRepos } = await setup();

    const organization = await testingRepos.organizations.createOrganization({
      name: 'organization_1',
      subscriptionId: '1'
    });

    const childOrganization = await testingRepos.organizations.createChildOrganization({
      name: 'organization_2',
      parentId: organization.id,
      subscriptionId: '1'
    });

    const response = await client.get(`/organizations/${childOrganization.id}`).send();

    expect(response.statusCode).toBe(200);

    expect(response.body).toMatchObject(
      convertToOrganizationDto({ ...childOrganization, hasHoldingSubscription: true })
    );
  });

  it('should return a child organization with hasHoldingSubscription as true when has same subscription as major holding', async () => {
    const { client, testingRepos } = await setup();

    const organization = await testingRepos.organizations.createOrganization({
      name: 'organization_1',
      subscriptionId: '1'
    });

    await testingRepos.organizations.createChildOrganization({
      name: 'organization_2',
      parentId: organization.id,
      subscriptionId: '2'
    });

    const grandChildOrganization = await testingRepos.organizations.createChildOrganization({
      name: 'organization_3',
      parentId: organization.id,
      subscriptionId: '1'
    });

    const response = await client.get(`/organizations/${grandChildOrganization.id}`).send();

    expect(response.statusCode).toBe(200);

    expect(response.body).toMatchObject(
      convertToOrganizationDto({ ...grandChildOrganization, hasHoldingSubscription: true })
    );
  });

  it('should update an organization', async () => {
    const { client, organizationsApiService, testingRepos } = await setup();
    const organization = await testingRepos.organizations.createOrganization({
      name: 'organization_1',
      subscriptionId: '1'
    });

    const response = await client.patch(`/organizations/${organization.id}`).send({
      subscriptionId: '2'
    } satisfies OrganizationUpdateDto);

    const updatedOrganization = await organizationsApiService.getOrganization({ organizationId: organization.id });

    expect(response.statusCode).toBe(200);
    expect(response.body).toMatchObject(convertToOrganizationDto({ ...updatedOrganization, subscriptionId: '2' }));
  });

  it('should update a Portalys organization', async () => {
    const { client, organizationsApiService, testingRepos } = await setup();
    const crpcen = uuid().slice(0, 5);
    const user = await testingRepos.users.createUser();

    const organization = (
      await client
        .post(`/organizations`)
        .send({ ...organizationPortalysNewDto, creatorUserId: user.id, crpcen } satisfies OrganizationNewDto)
    ).body;

    const response = await client.patch(`/organizations/${organization.id}`).send({
      interopAppId: 2
    });

    const updatedOrganization = await organizationsApiService.getOrganization({ organizationId: organization.id });

    expect(response.statusCode).toBe(200);
    expect(updatedOrganization.type).toBe(OrganizationType.PORTALYS_NOTARY_OFFICE);
    if (updatedOrganization.type === OrganizationType.PORTALYS_NOTARY_OFFICE) {
      expect(updatedOrganization.interopAppId).toBe(2);
    }
  });

  it('should throw error when there are already 2 children organizations', async () => {
    const { client, testingRepos } = await setup();
    const organization = await testingRepos.organizations.createOrganization({
      name: 'organization_1',
      subscriptionId: '1'
    });

    const child1 = await testingRepos.organizations.createOrganization({
      name: 'organization_1',
      parentId: organization.id,
      subscriptionId: '1'
    });

    const child2 = await testingRepos.organizations.createOrganization({
      name: 'organization_1',
      parentId: child1.id,
      subscriptionId: '1'
    });

    const user = await testingRepos.users.createUser();
    const siren = uuid().slice(0, 9);

    await client.post(`/organizations`).send({
      ...{ ...organizationNewDto, parentOrganizationId: child2.id },
      creatorUserId: user.id,
      siren
    } satisfies OrganizationNewDto);

    const response = await client.post(`/organizations`).send({
      ...{ ...organizationNewDto, parentOrganizationId: child2.id },
      creatorUserId: user.id,
      siren
    } satisfies OrganizationNewDto);
    expect(response.body.type).toBe(TooManyChildrenOrganizations.name);
  });

  it('should create organization', async () => {
    const { client, getService, testingRepos } = await setup();
    const siren = uuid().slice(0, 9);

    const organizationDefaultRolesService = getService(OrganizationDefaultRolesService);
    const organizationDefaultViewService = getService(OrganizationDefaultViewsService);

    const spyOrganizationDefaultRolesService = jest.spyOn(
      organizationDefaultRolesService,
      'createOrganizationDefaultRoles'
    );
    const spyOrganizationDefaultViewService = jest.spyOn(
      organizationDefaultViewService,
      'createOrganizationDefaultViews'
    );

    const user = await testingRepos.users.createUser();

    const response = await client
      .post(`/organizations`)
      .send({ ...organizationNewDto, creatorUserId: user.id, siren } satisfies OrganizationNewDto);

    await testingRepos.organizations.deleteOrganization(response.body.id);

    expect(response.statusCode).toBe(201);
    expect(response.body).toMatchObject({
      id: expect.any(String),
      name: 'Agence 1'
    });
    expect(spyOrganizationDefaultRolesService).toHaveBeenCalledTimes(1);
    expect(spyOrganizationDefaultRolesService).toHaveBeenCalledWith({
      id: response.body.id,
      type: OrganizationTypeDto.AGENCY
    });
    expect(spyOrganizationDefaultViewService).toHaveBeenCalledTimes(1);
    expect(spyOrganizationDefaultViewService).toHaveBeenCalledWith(response.body.id);
    await testingRepos.organizations.deleteOrganization(response.body.id);
  });

  it('should create a Portalys organization', async () => {
    const { client, testingRepos } = await setup();
    const crpcen = uuid().slice(0, 5);

    const user = await testingRepos.users.createUser();

    const response = await client
      .post(`/organizations`)
      .send({ ...organizationPortalysNewDto, creatorUserId: user.id, crpcen } satisfies OrganizationNewDto);

    await testingRepos.organizations.deleteOrganization(response.body.id);

    expect(response.statusCode).toBe(201);
    expect(response.body).toMatchObject({
      crpcen,
      id: expect.any(String),
      name: 'Agence 1'
    });
  });

  it('should prevent creation if organization with same siren exists', async () => {
    const { client } = await setup();
    const siren = uuid().slice(0, 9);

    const response = await client.post(`/organizations`).send({ ...organizationNewDto, siren });
    expect(response.statusCode).toBe(201);
    const response2 = await client.post(`/organizations`).send({ ...organizationNewDto, siren });
    expect(response2.statusCode).toBe(400);
  });

  it('should return 404 if the organization does not exist', async () => {
    const { client } = await setup();
    const nonExistentId = '-1';

    const response = await client.delete(`/organizations/${nonExistentId}`).send();

    expect(response.statusCode).toBe(404);
  });

  it('should delete an organization and its children if it has dependent organizations', async () => {
    const { client, testingRepos } = await setup();
    const parentOrganization = await testingRepos.organizations.createOrganization({
      name: 'Parent Organization',
      subscriptionId: '1'
    });

    const childOrganization = await testingRepos.organizations.createOrganization({
      name: 'Child Organization',
      parentId: parentOrganization.id,
      subscriptionId: '1'
    });

    const application = await testingRepos.externalApps.createApp({
      apiKey: 'apiKey',
      name: 'app',
      organizationId: parentOrganization.id
    });
    await testingRepos.externalApps.createAppClient({
      applicationId: application.id.toString(),
      organizationId: parentOrganization.id
    });
    await testingRepos.externalApps.createAppClient({
      applicationId: application.id.toString(),
      organizationId: childOrganization.id
    });

    await testingRepos.externalApps.createAssociation({
      externalId: 'externalId',
      myNotaryId: 'foo',
      organizationId: parentOrganization.id,
      type: 'RECORD_PERSONNE'
    });
    await testingRepos.externalApps.createAssociation({
      externalId: 'externalId',
      myNotaryId: 'foo',
      organizationId: childOrganization.id,
      type: 'RECORD_PERSONNE'
    });

    const organizationAssociation = await testingRepos.externalApps.createAssociation({
      externalId: 'externalId',
      internalType: InternalEntityType.ORGANIZATION,
      myNotaryId: parentOrganization.id,
      organizationId: null,
      type: 'ORGANIZATION'
    });

    const response = await client.delete(`/organizations/${parentOrganization.id}`).send();

    expect(response.statusCode).toBe(200);

    const deletedParentOrga = await testingRepos.organizations.getOrganizationById(parentOrganization.id);
    const deletedChildrenOrga = await testingRepos.organizations.getOrganizationById(childOrganization.id);

    const parentApplicationClients = await testingRepos.externalApps.getAppClients(parentOrganization.id);
    const childrenApplicationClients = await testingRepos.externalApps.getAppClients(childOrganization.id);

    const parentAssociations = await testingRepos.externalApps.getAssociations({
      organizationId: parentOrganization.id
    });
    const childrenAssociations = await testingRepos.externalApps.getAssociations({
      organizationId: childOrganization.id
    });

    const parentOrganizationAssociation = await testingRepos.externalApps.findAssociation(organizationAssociation.id);

    expect(deletedParentOrga.deletion_time).not.toBeNull();
    expect(deletedChildrenOrga.deletion_time).not.toBeNull();
    expect(deletedParentOrga.unique_identifier).not.toEqual(parentOrganization.uniqueIdentifier);
    expect(deletedChildrenOrga.unique_identifier).not.toEqual(childOrganization.uniqueIdentifier);

    expect(parentApplicationClients.length).toEqual(0);
    expect(childrenApplicationClients.length).toEqual(0);
    expect(parentAssociations.length).toEqual(0);
    expect(childrenAssociations.length).toEqual(0);
    expect(parentOrganizationAssociation).toEqual(null);
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,
      controller: OrganizationsController,
      providers: provideOrganizationsTest()
    });

    const testingRepos = getService(TestingRepositories);

    return {
      client,
      getService,
      organizationsApiService: getService(OrganizationsApiService),
      testingRepos
    };
  }
});

const convertToOrganizationDto = (organization: {
  hasHoldingSubscription?: boolean;
  id: string;
  name: string;
  subscriptionId?: string;
}): OrganizationDto => {
  return {
    address: {
      address: 'address',
      city: 'city',
      country: 'country',
      street: 'street'
    },
    apiKey: expect.any(String),
    creationTime: expect.any(String),
    email: '<EMAIL>',
    hasHoldingSubscription: organization.hasHoldingSubscription ?? false,
    hubspotId: 'hubspotId',
    id: organization.id,
    name: organization.name,
    rib: 'rib',
    subscriptionId: organization.subscriptionId,
    type: OrganizationTypeDto.AGENCY,
    uniqueIdentifier: expect.any(String)
  };
};
