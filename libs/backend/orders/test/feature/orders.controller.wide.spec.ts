import { OrdersController } from '@mynotary/backend/orders/feature';
import {
  InternalExternalOrderPaymentDtoPaymentTypeEnum,
  OrderNewDto,
  OrderNewPaymentCreditDtoPaymentTypeEnum,
  OrderNewPaymentExternalDtoPaymentTypeEnum,
  OrderNewPaymentInternalDtoPaymentTypeEnum,
  OrderPreEtatDateNewDtoTypeEnum
} from '@mynotary/crossplatform/api-mynotary/openapi';
import { LegalsApiService, Task } from '@mynotary/backend/legals/api';
import { TaskType } from '@mynotary/crossplatform/legals/api';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { BillingsApiService, PaymentType } from '@mynotary/backend/billings/api';
import { OrderPaymentType, OrderStatus, OrderType } from '@mynotary/crossplatform/orders/core';
import { AsyncTasksApiService } from '@mynotary/backend/async-tasks/api';
import { provideOrdersTest } from '../index';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import { FeatureType } from '@mynotary/crossplatform/features/api';

describe(OrdersController.name, () => {
  it('should deny order creation when payment type is credit and no credit', async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    const operation = await testingRepos.operations.createVenteAncien({ organizationId, userId });
    const record = await testingRepos.records.createRecord({ organizationId, userId });

    await testingRepos.billings.createCredits({
      featureState: { preEtatDateCredits: { number: 0 } },
      featureType: FeatureType.PRE_ETAT_DATE_CREDITS,
      organizationId
    });

    const postDto = {
      ...orderNewPreEtatDateCommonDate,
      creatorId: userId,
      destinationLegalRecordId: record.id,
      operationId: operation.id,
      organizationId: organizationId,
      payment: {
        paymentType: OrderNewPaymentCreditDtoPaymentTypeEnum.CREDIT
      },
      type: OrderPreEtatDateNewDtoTypeEnum.PRE_ETAT_DATE,
      venteTousLesLotsCopropriete: true
    } satisfies OrderNewDto;

    const response = await client.post('/orders').send(postDto);

    expect(response.status).toBe(400);
  });

  it('should allow order creation when payment type is credit and have credit', async () => {
    const { client, getService, organizationId, testingRepos, userId } = await setup();

    const operation = await testingRepos.operations.createVenteAncien({ organizationId, userId });
    const record = await testingRepos.records.createRecord({ organizationId, userId });

    const asyncTaskApiService = getService(AsyncTasksApiService);
    const createAsyncTaskSpy = jest.spyOn(asyncTaskApiService, 'createAsyncTask');

    const credit = await testingRepos.billings.createCredits({
      featureState: { preEtatDateCredits: { number: 1 } },
      featureType: FeatureType.PRE_ETAT_DATE_CREDITS,
      organizationId
    });

    const postDto = {
      ...orderNewPreEtatDateCommonDate,
      creatorId: userId,
      destinationLegalRecordId: record.id,
      operationId: operation.id,
      organizationId: organizationId,
      payment: {
        paymentType: OrderNewPaymentCreditDtoPaymentTypeEnum.CREDIT
      },
      type: OrderPreEtatDateNewDtoTypeEnum.PRE_ETAT_DATE,
      venteTousLesLotsCopropriete: true
    } satisfies OrderNewDto;

    const response = await client.post('/orders').send(postDto);

    expect(response.status).toBe(201);
    expect(createAsyncTaskSpy).toHaveBeenCalledTimes(1);

    const order = await testingRepos.orders.findOrder(response.body.id);

    const credits = await testingRepos.billings.getCreditNumber({ creditId: credit.id });

    expect(order).toMatchObject({
      creatorUserId: userId,
      operationId: operation.id,
      organizationId: organizationId,
      status: OrderStatus.PENDING_ORDER,
      type: OrderType.PRE_ETAT_DATE
    });
    expect(credits).toBe(0);
  });

  it('should allow order creation when payment type is credit and have credit', async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    const operation = await testingRepos.operations.createVenteAncien({ organizationId, userId });
    const record = await testingRepos.records.createRecord({ organizationId, userId });

    const credit = await testingRepos.billings.createCredits({
      featureState: { preEtatDateCredits: { number: 1 } },
      featureType: FeatureType.PRE_ETAT_DATE_CREDITS,
      organizationId
    });

    const postDto = {
      ...orderNewPreEtatDateCommonDate,
      creatorId: userId,
      destinationLegalRecordId: record.id,
      operationId: operation.id,
      organizationId: organizationId,
      payment: {
        paymentType: OrderNewPaymentCreditDtoPaymentTypeEnum.CREDIT
      },
      type: OrderPreEtatDateNewDtoTypeEnum.PRE_ETAT_DATE,
      venteTousLesLotsCopropriete: true
    } satisfies OrderNewDto;

    const response = await client.post('/orders').send(postDto);

    expect(response.status).toBe(201);

    const order = await testingRepos.orders.findOrder(response.body.id);

    const credits = await testingRepos.billings.getCreditNumber({ creditId: credit.id });

    expect(order).toMatchObject({
      creatorUserId: userId,
      operationId: operation.id,
      organizationId: organizationId,
      status: OrderStatus.PENDING_ORDER,
      type: OrderType.PRE_ETAT_DATE
    });
    expect(credits).toBe(0);
  });

  it('should create pending payment order and task when payment type is external', async () => {
    const { client, getService, organizationId, testingRepos, userId } = await setup();

    const operation = await testingRepos.operations.createVenteAncien({ organizationId, userId });
    const record = await testingRepos.records.createRecord({ organizationId, userId });

    const legalsApiService = getService(LegalsApiService);
    const createTaskSpy = jest
      .spyOn(legalsApiService, 'createTask')
      .mockResolvedValue({ id: 'task-id', uuid: 'uuid' } as Task);

    const billingsApiService = getService(BillingsApiService);
    const createPaymentLinkSpy = jest
      .spyOn(billingsApiService, 'createPaymentLink')
      .mockResolvedValue({ id: 'FAKE_PAYMENT_LINK_ID', url: 'FAKE_PAYMENT_LINK_URL' });

    const postDto = {
      ...orderNewPreEtatDateCommonDate,
      ...orderNewTask,
      creatorId: userId,
      destinationLegalRecordId: record.id,
      operationId: operation.id,
      organizationId: organizationId,
      payment: {
        paymentType: OrderNewPaymentExternalDtoPaymentTypeEnum.EXTERNAL,
        taskDescription: 'Task description',
        taskTitle: 'Task title'
      },

      type: OrderPreEtatDateNewDtoTypeEnum.PRE_ETAT_DATE,
      venteTousLesLotsCopropriete: true
    } satisfies OrderNewDto;

    const response = await client.post('/orders').send(postDto);

    expect(response.status).toBe(201);

    const order = await testingRepos.orders.findOrder(response.body.id);

    expect(order).toMatchObject({
      creatorUserId: userId,
      operationId: operation.id,
      organizationId: organizationId,
      payment: {
        externalPaymentId: 'FAKE_PAYMENT_LINK_ID',
        paymentType: InternalExternalOrderPaymentDtoPaymentTypeEnum.EXTERNAL,
        paymentUrl: 'FAKE_PAYMENT_LINK_URL'
      },
      status: OrderStatus.PENDING_PAYMENT,
      type: OrderType.PRE_ETAT_DATE
    });

    expect(createTaskSpy).toHaveBeenCalledTimes(1);
    expect(createTaskSpy).toHaveBeenCalledWith({
      creatorUserId: userId,
      description: orderNewTask.taskDescription,
      legalComponentId: operation.id,
      orderId: response.body.id,
      organizationId: organizationId,
      title: orderNewTask.taskTitle,
      type: TaskType.PAYMENT_REQUEST
    });

    expect(createPaymentLinkSpy).toHaveBeenCalledTimes(1);
    expect(createPaymentLinkSpy).toHaveBeenCalledWith({
      orderId: response.body.id,
      redirectUrl: 'http://localhost:9002/taches/task-id?taskUuid=uuid',
      taskId: 'task-id',
      type: PaymentType.PRE_ETAT_DATE
    });
  });

  it('should create pending payment order and no task when payment type is internal', async () => {
    const { client, getService, organizationId, testingRepos, userId } = await setup();

    const operation = await testingRepos.operations.createVenteAncien({ organizationId, userId });
    const record = await testingRepos.records.createRecord({ organizationId, userId });

    const legalsApiService = getService(LegalsApiService);
    const createTaskSpy = jest
      .spyOn(legalsApiService, 'createTask')
      .mockResolvedValue({ id: 'task-id', uuid: 'uuid' } as Task);

    const billingsApiService = getService(BillingsApiService);
    const createPaymentLinkSpy = jest
      .spyOn(billingsApiService, 'createPaymentLink')
      .mockResolvedValue({ id: 'FAKE_PAYMENT_LINK_ID', url: 'FAKE_PAYMENT_LINK_URL' });

    const postDto = {
      ...orderNewPreEtatDateCommonDate,
      ...orderNewTask,
      creatorId: userId,
      destinationLegalRecordId: record.id,
      operationId: operation.id,
      organizationId: organizationId,
      payment: {
        paymentType: OrderNewPaymentInternalDtoPaymentTypeEnum.INTERNAL
      },
      type: OrderPreEtatDateNewDtoTypeEnum.PRE_ETAT_DATE,
      venteTousLesLotsCopropriete: true
    } satisfies OrderNewDto;

    const response = await client.post('/orders').send(postDto);

    expect(response.status).toBe(201);

    const order = await testingRepos.orders.findOrder(response.body.id);

    expect(order).toMatchObject({
      creatorUserId: userId,
      operationId: operation.id,
      organizationId: organizationId,
      payment: {
        externalPaymentId: 'FAKE_PAYMENT_LINK_ID',
        paymentType: InternalExternalOrderPaymentDtoPaymentTypeEnum.INTERNAL,
        paymentUrl: 'FAKE_PAYMENT_LINK_URL'
      },
      status: OrderStatus.PENDING_PAYMENT,
      type: OrderType.PRE_ETAT_DATE
    });

    expect(createTaskSpy).toHaveBeenCalledTimes(0);

    expect(createPaymentLinkSpy).toHaveBeenCalledTimes(1);
    expect(createPaymentLinkSpy).toHaveBeenCalledWith({
      orderId: response.body.id,
      redirectUrl: `http://localhost:9002/operation/${operation.id}?preEtatDate=success`,
      type: PaymentType.PRE_ETAT_DATE
    });
  });

  it('should cancel an order', async () => {
    const { client, getService, organizationId, testingRepos, userId } = await setup();

    const operation = await testingRepos.operations.createVenteAncien({ organizationId, userId });
    const order = await testingRepos.orders.createOrder({
      operationId: operation.id,
      organizationId,
      status: OrderStatus.PENDING_PAYMENT,
      type: 'PRE_ETAT_DATE',
      userId
    });

    const paymentService = getService(BillingsApiService);
    const paymentSpy = jest.spyOn(paymentService, 'cancelPayment').mockResolvedValue();

    const response = await client.delete(`/orders/${order.id}`);

    expect(response.status).toBe(200);
    expect(paymentSpy).toHaveBeenCalledTimes(0);

    const orderDeleted = await testingRepos.orders.findOrder(order.id);

    expect(orderDeleted).not.toBeNull();
    expect(orderDeleted?.status).toEqual(OrderStatus.CANCELLED);
  });

  it('should cancel an order and payment link when paymentType is set', async () => {
    const { client, getService, organizationId, testingRepos, userId } = await setup();
    const PAYMENT_ID = '1';

    const operation = await testingRepos.operations.createVenteAncien({ organizationId, userId });
    const order = await testingRepos.orders.createOrder({
      externalPaymentId: PAYMENT_ID,
      operationId: operation.id,
      organizationId,
      paymentType: OrderPaymentType.INTERNAL,
      status: OrderStatus.PENDING_PAYMENT,
      type: 'PRE_ETAT_DATE',
      userId
    });

    const paymentService = getService(BillingsApiService);
    const paymentSpy = jest.spyOn(paymentService, 'cancelPayment').mockResolvedValue();

    const response = await client.delete(`/orders/${order.id}`);

    expect(response.status).toBe(200);
    expect(paymentSpy).toHaveBeenCalledTimes(1);
    expect(paymentSpy).toHaveBeenCalledWith({ paymentId: PAYMENT_ID });

    const orderDeleted = await testingRepos.orders.findOrder(order.id);

    expect(orderDeleted).not.toBeNull();
    expect(orderDeleted?.status).toEqual(OrderStatus.CANCELLED);
  });

  it('should retrieve order by operationId', async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    const operation1 = await testingRepos.operations.createVenteAncien({ organizationId, userId });
    const operation2 = await testingRepos.operations.createVenteAncien({ organizationId, userId });

    const orderOperation1 = await testingRepos.orders.createOrder({
      operationId: operation1.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId
    });
    await testingRepos.orders.createOrder({
      operationId: operation2.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId
    });

    const response = await client.get(`/orders?operationId=${operation1.id}`);

    expect(response.status).toBe(200);

    expect(response.body.items).toHaveLength(1);
    expect(response.body.items[0]).toMatchObject({
      id: orderOperation1.id,
      legalOperationId: operation1.id,
      type: OrderType.PRE_ETAT_DATE
    });
  });

  it('should retrieve order by userId and organizationId when the user has permissions', async () => {
    const { client, organizationId, testingRepos, userId, userWithOrderAccessRole } = await setup();

    const operation = await testingRepos.operations.createVenteAncien({
      organizationId,
      userId: userWithOrderAccessRole.id
    });
    const order = await testingRepos.orders.createOrder({
      operationId: operation.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId: userWithOrderAccessRole.id
    });

    const operation2FromAnotherUser = await testingRepos.operations.createVenteAncien({
      organizationId,
      userId: userId
    });
    const order2FromAnotherUser = await testingRepos.orders.createOrder({
      operationId: operation2FromAnotherUser.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId
    });

    const response = await client.get(`/orders?userId=${userWithOrderAccessRole.id}&organizationId=${organizationId}`);

    expect(response.status).toBe(200);

    expect(response.body.items).toHaveLength(2);
    expect(response.body.items).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          id: order.id,
          legalOperationId: operation.id,
          type: OrderType.PRE_ETAT_DATE
        }),
        expect.objectContaining({
          id: order2FromAnotherUser.id,
          legalOperationId: operation2FromAnotherUser.id,
          type: OrderType.PRE_ETAT_DATE
        })
      ])
    );
  });

  it('should retrieve order by userId and organizationId when the user does not have permissions', async () => {
    const { client, organizationId, testingRepos, userId, userWithOrderAccessRole } = await setup();

    const operation = await testingRepos.operations.createVenteAncien({
      organizationId,
      userId
    });
    const order = await testingRepos.orders.createOrder({
      operationId: operation.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId
    });

    const operation2FromAnotherUser = await testingRepos.operations.createVenteAncien({
      organizationId,
      userId: userWithOrderAccessRole.id
    });
    await testingRepos.orders.createOrder({
      operationId: operation2FromAnotherUser.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId: userWithOrderAccessRole.id
    });

    const response = await client.get(`/orders?userId=${userId}&organizationId=${organizationId}`);

    expect(response.status).toBe(200);

    expect(response.body.items).toHaveLength(1);
    expect(response.body.items).toEqual([
      expect.objectContaining({
        id: order.id,
        legalOperationId: operation.id,
        type: OrderType.PRE_ETAT_DATE
      })
    ]);
  });

  it('should retrieve order with a filter by type', async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    const operation1 = await testingRepos.operations.createVenteAncien({ organizationId, userId });

    const order1Operation1 = await testingRepos.orders.createOrder({
      operationId: operation1.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId
    });

    await testingRepos.orders.createOrder({
      operationId: operation1.id,
      organizationId,
      type: OrderType.ERP,
      userId
    });

    const response = await client.get(
      `/orders?types[]=${OrderType.PRE_ETAT_DATE}&organizationId=${organizationId}&userId=${userId}`
    );

    expect(response.status).toBe(200);
    expect(response.body.items).toHaveLength(1);

    expect(response.body.items[0]).toMatchObject({
      id: order1Operation1.id,
      legalOperationId: operation1.id,
      type: OrderType.PRE_ETAT_DATE
    });
  });

  it('should retrieve order with a filter by search', async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    const operation1 = await testingRepos.operations.createVenteAncien({
      labelOperation: 'Madame DUPONT',
      organizationId,
      userId
    });
    const operation2 = await testingRepos.operations.createVenteAncien({
      labelOperation: 'Madame PIERRE',
      organizationId,
      userId
    });
    const operation3 = await testingRepos.operations.createVenteAncien({
      labelOperation: 'Monsieur DURAND',
      organizationId,
      userId
    });

    await testingRepos.orders.createOrder({
      operationId: operation1.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId
    });

    await testingRepos.orders.createOrder({
      operationId: operation2.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId
    });

    const order3Operation3 = await testingRepos.orders.createOrder({
      operationId: operation3.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId
    });

    const response = await client.get(`/orders?search[]=duran&organizationId=${organizationId}&userId=${userId}`);

    expect(response.status).toBe(200);
    expect(response.body.items).toHaveLength(1);

    expect(response.body.items[0]).toMatchObject({
      id: order3Operation3.id,
      legalOperationId: operation3.id,
      type: OrderType.PRE_ETAT_DATE
    });
  });

  it('should retrieve order with a filter by type and search', async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    const operation1 = await testingRepos.operations.createVenteAncien({
      labelOperation: 'Madame DUPONT',
      organizationId,
      userId
    });

    const operation2 = await testingRepos.operations.createVenteAncien({
      labelOperation: 'Madame PIERRE',
      organizationId,
      userId
    });

    const operation3 = await testingRepos.operations.createVenteAncien({
      labelOperation: 'Madame PIERRE',
      organizationId,
      userId
    });

    await testingRepos.orders.createOrder({
      operationId: operation1.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId
    });

    const order2Operation2 = await testingRepos.orders.createOrder({
      operationId: operation2.id,
      organizationId,
      type: OrderType.PRE_ETAT_DATE,
      userId
    });

    await testingRepos.orders.createOrder({
      operationId: operation3.id,
      organizationId,
      type: OrderType.ERP,
      userId
    });

    const response = await client.get(
      `/orders?search[]=pierre&types[]=${OrderType.PRE_ETAT_DATE}&organizationId=${organizationId}&userId=${userId}`
    );

    expect(response.status).toBe(200);

    expect(response.body.items).toHaveLength(1);

    expect(response.body.items[0]).toMatchObject({
      id: order2Operation2.id,
      legalOperationId: operation2.id,
      type: OrderType.PRE_ETAT_DATE
    });
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,
      controller: OrdersController,
      providers: provideOrdersTest()
    });

    const testingRepos = getService(TestingRepositories);

    const { organizationId, userId } = await testingRepos.createMember({});

    const userWithOrderAccessRole = await testingRepos.users.createUser({});

    const role = await testingRepos.roles.createRoleWithPermissions({
      organizationId,
      permissions: [
        {
          entityType: EntityType.ORGANIZATION,
          permissionType: PermissionType.READ_ORGANIZATION_ORDER
        }
      ]
    });

    await testingRepos.members.createMember({
      organizationId,
      roleId: role.roleId,
      userId: userWithOrderAccessRole.id
    });

    return {
      client,
      getService,
      organizationId,
      testingRepos,
      userId,
      userWithOrderAccessRole
    };
  }
});

const orderNewPreEtatDateCommonDate = {
  contactCivilite: 'M.',
  contactEmail: '<EMAIL>',
  contactFirstname: 'John',
  contactLastname: 'Doe',
  contactPhone: '+33000000000',
  coproprieteAdresse: {
    city: 'Paris',
    country: 'France',
    street: '123 Main Street',
    zip: '75001'
  },
  lots: [
    {
      natureBien: 'Appartement',
      numeroLot: 'A12'
    },
    {
      natureBien: 'Maison',
      numeroLot: 'M34'
    }
  ],
  syndicIdentifiant: 'SYNDIC123',
  syndicName: 'Syndic Company',
  syndicPassword: 'securepassword123'
};

const orderNewTask = {
  taskDescription: 'Task description',
  taskTitle: 'Task title'
};
