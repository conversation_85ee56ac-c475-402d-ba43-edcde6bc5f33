
import { RoleNewDto, RoleWithPermissionsDto, UpdateRoleDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { DeleteDefaultRoleError, RoleIsUsedError } from '@mynotary/backend/roles/core';
import { RolesController } from '@mynotary/backend/roles/feature';
import { provideRolesTest } from '../index';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';

describe(RolesController.name, () => {
  it('should get organization roles', async () => {
    const { client, testingRepository } = await setup();

    const member = await testingRepository.createMember({
      permissions: [{ entityType: EntityType.ORGANIZATION, permissionType: PermissionType.READ_OPERATION_COLLABORATOR }]
    });

    const response = await client.get(`/roles?organizationId=${member.organizationId}`);

    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual(
      expect.objectContaining([
        {
          allowedRoles: [],
          creationTime: expect.any(String),
          id: member.roleId,
          isAdmin: false,
          isDefault: false,
          name: expect.any(String),
          organizationId: member.organizationId,
          permissions: [
            expect.objectContaining({
              entityType: 'ORGANIZATION',
              id: expect.any(String),
              subEntityType: 'NONE',
              type: 'READ_OPERATION_COLLABORATOR'
            })
          ]
        }
      ] satisfies RoleWithPermissionsDto[])
    );
  });

  it('should get roles associated to a roleId', async () => {
    const { client, testingRepository } = await setup();

    const member = await testingRepository.createMember({
      permissions: [{ entityType: EntityType.ORGANIZATION, permissionType: PermissionType.READ_OPERATION_COLLABORATOR }]
    });

    const response = await client.get(`/roles/${member.roleId}`);

    expect(response.statusCode).toBe(200);
    expect(response.body.permissions.length).toEqual(1);
    expect(response.body).toEqual(
      expect.objectContaining({
        allowedRoles: [],
        id: member.roleId,
        organizationId: member.organizationId,
        permissions: [
          expect.objectContaining({
            entityType: 'ORGANIZATION',
            id: expect.any(String),
            subEntityType: 'NONE',
            type: 'READ_OPERATION_COLLABORATOR'
          })
        ]
      } satisfies Partial<RoleWithPermissionsDto>)
    );
  });

  it('should create role with permissions', async () => {
    const { client, collabRole, organizationId, testingRepository } = await setup();

    const permissions = await testingRepository.roles.createPermissions({
      permissions: [{ entityType: EntityType.ORGANIZATION, permissionType: PermissionType.READ_OPERATION_COLLABORATOR }]
    });

    const response = await client.post(`/roles`).send({
      allowedRoles: [collabRole.name, 'Custom Role'],
      name: 'Custom Role',
      organizationId,
      permissions: permissions.map((permission) => permission.id.toString())
    } as RoleNewDto);

    expect(response.statusCode).toBe(201);
    expect(response.body.allowedRoles.length).toBe(2);
    expect(response.body).toEqual(
      expect.objectContaining({
        allowedRoles: expect.arrayContaining([
          { allowed: true, id: response.body.id, name: 'Custom Role' },
          { allowed: true, id: collabRole.id, name: collabRole.name }
        ]),
        creationTime: expect.any(String),
        id: expect.any(String),
        isAdmin: false,
        isDefault: false,
        name: 'Custom Role',
        organizationId,
        permissions: [
          expect.objectContaining({
            entityType: EntityType.ORGANIZATION,
            id: expect.any(String),
            subEntityType: 'NONE',
            type: PermissionType.READ_OPERATION_COLLABORATOR
          })
        ]
      } satisfies RoleWithPermissionsDto)
    );
  });

  it('should update role with permissions if name is already taken ', async () => {
    const { client, collabRole, organizationId, testingRepository } = await setup();

    const role = await testingRepository.roles.createRoleWithPermissions({
      name: 'MyRole',
      organizationId,
      permissions: [
        { entityType: EntityType.ORGANIZATION, permissionType: PermissionType.READ_OPERATION_COLLABORATOR },
        { entityType: EntityType.ORGANIZATION, permissionType: PermissionType.UPDATE_ORGANIZATION_RECORDS }
      ]
    });

    await testingRepository.roles.createInvitationAllowedRole({
      allowedRoleId: collabRole.id,
      roleId: role.roleId
    });

    const newPermission = await testingRepository.roles.createPermissions({
      permissions: [{ entityType: EntityType.ORGANIZATION, permissionType: PermissionType.UPDATE_ORGANIZATION_VIEW }]
    });

    const response = await client.post(`/roles`).send({
      allowedRoles: [collabRole.name, 'MyRole'],
      name: 'MyRole',
      organizationId,
      permissions: [newPermission[0].id.toString()]
    } satisfies RoleNewDto);

    const body: RoleWithPermissionsDto = response.body;

    expect(response.statusCode).toBe(201);
    expect(body).toEqual(
      expect.objectContaining({
        allowedRoles: expect.arrayContaining([
          { allowed: true, id: collabRole.id, name: collabRole.name },
          { allowed: true, id: response.body.id, name: 'MyRole' }
        ]),
        creationTime: expect.any(String),
        id: role.roleId,
        isAdmin: false,
        isDefault: false,
        name: 'MyRole',
        organizationId,
        permissions: [
          expect.objectContaining({
            entityType: EntityType.ORGANIZATION,
            id: expect.any(String),
            subEntityType: 'NONE',
            type: PermissionType.UPDATE_ORGANIZATION_VIEW
          })
        ]
      } satisfies RoleWithPermissionsDto)
    );
  });

  it('should update role with permissions', async () => {
    const { client, organizationId, testingRepository } = await setup();

    const role = await testingRepository.roles.createRoleWithPermissions({
      name: 'MyRole',
      organizationId,
      permissions: [
        { entityType: EntityType.ORGANIZATION, permissionType: PermissionType.READ_OPERATION_COLLABORATOR },
        { entityType: EntityType.ORGANIZATION, permissionType: PermissionType.UPDATE_ORGANIZATION_RECORDS }
      ]
    });

    const newPermission = await testingRepository.roles.createPermissions({
      permissions: [{ entityType: EntityType.ORGANIZATION, permissionType: PermissionType.UPDATE_ORGANIZATION_VIEW }]
    });

    const response = await client.put(`/roles/${role.roleId}`).send({
      allowedRoles: [],
      name: 'MyRoleUpdated',
      permissions: [newPermission[0].id.toString()]
    } satisfies UpdateRoleDto);

    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual(
      expect.objectContaining({
        allowedRoles: [],
        creationTime: expect.any(String),
        id: expect.any(String),
        isAdmin: false,
        isDefault: false,
        name: 'MyRoleUpdated',
        organizationId: organizationId,
        permissions: [
          expect.objectContaining({
            entityType: EntityType.ORGANIZATION,
            id: expect.any(String),
            subEntityType: 'NONE',
            type: PermissionType.UPDATE_ORGANIZATION_VIEW
          })
        ]
      } satisfies RoleWithPermissionsDto)
    );
  });

  it('should not delete if role is default', async () => {
    const { client, organizationId, testingRepository } = await setup();

    const role = await testingRepository.roles.createRole({
      isDefault: true,
      name: 'role-delete',
      organizationId
    });

    const response = await client.delete(`/roles/${role.id}`).send();

    expect(response.statusCode).toBe(400);
    expect(response.body.type).toBe(DeleteDefaultRoleError.name);
  });

  it('should not delete if role is used by a member', async () => {
    const { client, organizationId, testingRepository } = await setup();

    const role = await testingRepository.roles.createRole({
      isDefault: false,
      organizationId
    });

    const user = await testingRepository.users.createUser();

    await testingRepository.members.createMember({
      organizationId,
      roleId: role.id,
      userId: user.id
    });

    const response = await client.delete(`/roles/${role.id}`).send();

    expect(response.statusCode).toBe(400);
    expect(response.body.type).toBe(RoleIsUsedError.name);
  });

  it('should not delete if role is used by an invited user', async () => {
    const { client, organizationId, testingRepository } = await setup();

    const role = await testingRepository.roles.createRole({
      isDefault: false,
      organizationId
    });

    const creatorUser = await testingRepository.users.createUser();

    const operation = await testingRepository.operations.createVenteAncien({
      organizationId
    });

    await testingRepository.operations.createOperationInvitation({
      email: '<EMAIL>',
      operationId: operation.id,
      roleId: role.id,
      userId: creatorUser.id
    });

    const response = await client.delete(`/roles/${role.id}`).send();

    expect(response.statusCode).toBe(400);
    expect(response.body.type).toBe(RoleIsUsedError.name);
  });

  it('should  delete role', async () => {
    const { client, organizationId, testingRepository } = await setup();

    const role = await testingRepository.roles.createRole({
      isDefault: false,
      organizationId
    });

    const otherRole = await testingRepository.roles.createRole({
      isDefault: true,
      name: 'role-delete',
      organizationId
    });

    await testingRepository.roles.createInvitationAllowedRole({
      allowedRoleId: otherRole.id,
      roleId: role.id
    });

    const response = await client.delete(`/roles/${role.id}`).send();

    const allowedRoles = await testingRepository.roles.getInvitationAllowedRole({ roleId: role.id });

    expect(response.statusCode).toBe(200);
    expect(allowedRoles.length).toBe(0);
  });

  it('should update allowed roles', async () => {
    const { client, collabRole, organizationId, responsableRole } = await setup();

    const roleWithAllowedRole = await client.put(`/roles/${collabRole.id}`).send({
      allowedRoles: [responsableRole.name],
      name: collabRole.name,
      permissions: []
    } satisfies UpdateRoleDto);
    const roleWithoutAllowedRole = await client.put(`/roles/${collabRole.id}`).send({
      allowedRoles: [],
      name: collabRole.name,
      permissions: []
    } satisfies UpdateRoleDto);

    expect(roleWithAllowedRole.statusCode).toBe(200);
    expect(roleWithAllowedRole.body).toEqual(
      expect.objectContaining({
        allowedRoles: [{ allowed: true, id: responsableRole.id, name: responsableRole.name }],
        creationTime: expect.any(String),
        id: expect.any(String),
        isAdmin: false,
        isDefault: true,
        name: collabRole.name,
        organizationId: organizationId,
        permissions: []
      } satisfies RoleWithPermissionsDto)
    );
    expect(roleWithoutAllowedRole.statusCode).toBe(200);
    expect(roleWithoutAllowedRole.body.allowedRoles.length).toEqual(0);
    expect(roleWithoutAllowedRole.body).toEqual(
      expect.objectContaining({
        allowedRoles: [],
        creationTime: expect.any(String),
        id: expect.any(String),
        isAdmin: false,
        isDefault: true,
        name: collabRole.name,
        organizationId: organizationId,
        permissions: []
      } satisfies RoleWithPermissionsDto)
    );
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,

      controller: RolesController,
      providers: provideRolesTest()
    });

    const testingRepository = getService(TestingRepositories);
    const organization = await testingRepository.organizations.createOrganization({ name: 'MyNotary' });
    const { collabRole, responsableRole } = await testingRepository.roles.createDefaultRoles(organization.id);

    return { client, collabRole, organizationId: organization.id, responsableRole, testingRepository };
  }
});
