import { Injectable } from '@nestjs/common';
import { SignaturesRepository } from './signatures.repository';
import {
  assertIsSignatureActive,
  Signature,
  SignatureActive,
  SignatureCompleteUpdate,
  SignatureStatus
} from './signatures';

import { SignatureContractStatusError, SignatureStatusNotSupportedError } from './signatures.error';
import { assertNotNull, Exception, isValidEmail } from '@mynotary/crossplatform/shared/util';
import { EmailsApiService, EmailTemplateId, SignatureCompletedEmail } from '@mynotary/backend/emails/api';

import { SignaturesService } from './signatures.service';
import { ElectronicSignatureProvider } from './electronic-signatures.provider';
import { SignatureRegisterUpdaterService } from './signature-register-updater.service';
import {
  EventsApiService,
  EventType,
  SignatureCanceledEvent,
  SignatureCompletedEvent
} from '@mynotary/backend/events/api';
import { SignatureProviderType, SignatureType } from '@mynotary/crossplatform/signatures/api';
import { every, filter } from 'lodash';
import { SignatureCreditsService } from './signature-credits.service';
import { NotificationApiService, SignatureNotificationType } from '@mynotary/backend/notifications/api';
import { EnvService } from '@mynotary/backend/secrets/api';
import { ContractStatus, isImportedContract } from '@mynotary/crossplatform/legals/api';
import { UsersApiService } from '@mynotary/backend/users/api';
import { ResourceTokenType } from '@mynotary/crossplatform/authorizations/api';
import { LegacyJavaClient } from '@mynotary/backend/legacy-java/api';
import { LegalsApiService } from '@mynotary/backend/legals/api';
import { getSignatureErrorContent } from './signature-error-email';
import { OrganizationsApiService } from '@mynotary/backend/organizations/api';
import { AuthenticationsApiService } from '@mynotary/backend/authentications/api';

@Injectable()
export class SignatureEventsService {
  constructor(
    private signatureRepository: SignaturesRepository,
    private emailsApiService: EmailsApiService,
    private signaturesService: SignaturesService,
    private electronicSignatureProvider: ElectronicSignatureProvider,
    private signatureRegisterUpdater: SignatureRegisterUpdaterService,
    private eventsApiService: EventsApiService,
    private authenticationsApiService: AuthenticationsApiService,
    private signatureCreditsService: SignatureCreditsService,
    private notificationApiService: NotificationApiService,
    private envService: EnvService,
    private usersApiService: UsersApiService,
    private legacyJavaClient: LegacyJavaClient,
    private legalsApiService: LegalsApiService,
    private organizationsApiService: OrganizationsApiService
  ) {}

  async onSignatureExpired(signatureId: string): Promise<void> {
    const signature = await this.signaturesService.getSignature(signatureId);

    assertIsSignatureActive(signature);

    /**
     * Some provider (eg: Yousign) have really strict timeout policy of 1second otherwise they will retry the webhook.
     * This means that we can receive the same event multiple time even if the previous event was processed successfully.
     */
    if (signature.status === SignatureStatus.EXPIRED) {
      return;
    }

    if (signature.status !== SignatureStatus.PENDING) {
      throw new Exception(`Signature ${signature.id} must be pending to be expired`);
    }

    await this.signaturesService.updateContractStatus({
      contractId: signature.contractId,
      status: ContractStatus.SIGNATURE_EXPIRED
    });

    const usersToNotify = await this.getUsersToNotify(signature);

    for (const userToNotify of usersToNotify) {
      await this.emailsApiService.sendEmail({
        data: {
          appUrl: this.getWebAppSignatureUrl({
            operationId: signature.operationId,
            signatureId: signature.id
          }),
          contractId: signature.contractId,
          documentLabel: signature.documentLabel ?? '',
          expirationTime: new Date(),
          missingSignatories: signature.signatories
            .filter((signatory) => signatory.signatureTime == null)
            .map((signatory) => ({
              firstname: signatory.firstname,
              lastname: signatory.lastname
            })),
          operationId: signature.operationId,
          organizationId: signature.organizationId,
          sender: {
            email: signature.creator.email,
            firstname: signature.creator.firstname,
            lastname: signature.creator.lastname
          },
          templateId: EmailTemplateId.SIGNATURE_DEADLINE
        },
        receiver: userToNotify.email
      });
    }

    await this.notificationApiService.createNotification({
      contractId: signature.contractId,
      expirationTime: signature.dueDate ?? '',
      operationId: signature.operationId,
      signatureId: signature.id,
      type: SignatureNotificationType.SIGNATURE_EXPIRED,
      userIds: usersToNotify.map((user) => user.id)
    });

    /**
     * We want to set the expirationTime at the end in case we have issues in production that result in a half-baked expired signature.
     * We check the expirationTime in order to run the sync admin route that resets the signature.
     */
    await this.signatureRepository.setSignatureExpiration(signatureId);

    await this.signaturesService.syncFirebaseSignature(signature);
  }

  async onSignatureCompleted(update: SignatureCompleteUpdate): Promise<void> {
    const signature = await this.signatureRepository.getSignature(update.signatureId);

    assertIsSignatureActive(signature);

    /**
     * Some provider (eg: Yousign) have really strict timeout policy of 1second otherwise they will retry the webhook.
     * This means that we can receive the same event multiple time even if the previous event was processed successfully.
     */
    if (signature.status === SignatureStatus.SIGNED || signature.status === SignatureStatus.CANCELLED) {
      return;
    }

    if (signature.status !== SignatureStatus.PENDING) {
      throw new Exception(
        `Signature "${signature.id}" with status "${signature.status}" must be pending to be completed`
      );
    }

    if (signature.type === SignatureType.ELECTRONIC) {
      await Promise.all(
        update.signatories.map(async (signatory) => {
          return await this.electronicSignatureProvider.downloadSignatoryProof({
            signatoryProviderId: signatory.providerId,
            signatureProviderId: update.signatureProviderId
          });
        })
      );

      await Promise.all(
        update.signatureFile.map(async (file) => {
          return await this.electronicSignatureProvider.downloadSignatureDocument({
            documentProviderId: file.providerId,
            signatureProviderId: update.signatureProviderId
          });
        })
      );

      await this.sendSignatureCompletedEmails(signature);
    }

    if (signature.contractId == null) {
      throw new Exception('ContractId is null');
    }

    await this.signatureRegisterUpdater.completeRegisterEntry(signature);

    await this.signaturesService.updateContractStatus({
      contractId: signature.contractId,
      status: ContractStatus.SIGNATURE_COMPLETED
    });

    const subscribersEmails = signature.subscribers.map((subscriber) => subscriber.email);

    const userIds = await this.signatureRepository.getUserIdsByEmail(subscribersEmails);

    const usersToNotify = await this.getUsersToNotify(signature);

    await this.notificationApiService.createNotification({
      contractId: signature.contractId,
      operationId: signature.operationId,
      signatureId: signature.id,
      signatureTime: new Date().toISOString(),
      type: SignatureNotificationType.SIGNATURE_COMPLETED,
      userIds: [...usersToNotify.map((user) => user.id), ...userIds]
    });

    /**
     * We want to set the signatureTime at the end in case we have issues in production that result in a half-baked completed signature.
     * We check the signatureTime in order to run the sync admin route that resets the signature.
     */
    await this.signatureRepository.updateSignatureTime(update.signatureId);
    await this.signaturesService.syncFirebaseSignature(signature);

    /**
     * Event should be the last call because we need to have access to all the data in the dto (eg: signatureTime)
     */
    await this.eventsApiService.createEvents(createSignatureCompletedEventData(signature));
  }

  async onSignatoryCompleted(update: OnSignatoryCompleted): Promise<void> {
    await this.signatureRepository.updateSignatory({
      providerId: update.signatoryProviderId,
      signatureTime: new Date().toISOString()
    });

    const signature = await this.signatureRepository.getSignature(update.signatureId);

    assertIsSignatureActive(signature);
    const operationLabel = await this.legalsApiService.getOperationLabel(signature.operationId);

    /**
     * Some provider (eg: Yousign) have really strict timeout policy of 1second otherwise they will retry the webhook.
     * This means that we can receive the same event multiple time even if the previous event was processed successfully.
     */
    if (signature.status === SignatureStatus.SIGNED || signature.status === SignatureStatus.CANCELLED) {
      return;
    }

    if (signature.status !== SignatureStatus.PENDING) {
      throw new Exception(
        `Signature ${signature.id} must be pending to update signatories. Current status: ${signature.status}`
      );
    }

    const currentSignatory = signature.signatories.find(
      (signatory) => signatory.providerId === update.signatoryProviderId
    );

    if (currentSignatory == null) {
      throw new Exception('Signatory not found');
    }

    const usersToNotify = await this.getUsersToNotify(signature);

    if (signature.shouldNotifyAfterSignature) {
      for (const userToNotify of usersToNotify) {
        await this.emailsApiService.sendEmail({
          data: {
            appUrl: this.getWebAppSignatureUrl({
              operationId: signature.operationId,
              signatureId: signature.id
            }),
            contractId: signature.contractId,
            documentLabel: signature.documentLabel,
            operationId: signature.operationId,
            operationLabel,
            organizationId: signature.organizationId,
            sender: {
              email: signature.creator.email,
              firstname: signature.creator.firstname,
              lastname: signature.creator.lastname
            },
            signatory: {
              firstname: currentSignatory.firstname,
              lastname: currentSignatory.lastname
            },
            subject: `${operationLabel} - Un signataire vient de signer le contrat ${signature.documentLabel}`,
            templateId: EmailTemplateId.SIGNATURE_SIGNATORY_SIGNED
          },
          receiver: userToNotify.email
        });
      }
    }

    if (signature.ordered) {
      const sameOrderSignatories = filter(
        signature.signatories,
        (signatory) => signatory.order === currentSignatory.order
      );

      const canMoveToTheNextSignatureOrder = every(
        sameOrderSignatories,
        (signatory) => signatory.signatureTime != null
      );

      if (canMoveToTheNextSignatureOrder) {
        for (const signatory of signature.signatories) {
          const nextSignatoryOrder = currentSignatory.order + 1;
          if (signatory.order === nextSignatoryOrder) {
            await this.signaturesService.sendSignatureRequestEmail({
              signatory,
              signature
            });
          }
        }
      }
    }

    await this.notificationApiService.createNotification({
      contractId: signature.contractId,
      operationId: signature.operationId,
      signatoryFullName: `${currentSignatory.firstname} ${currentSignatory.lastname}`,
      signatoryId: currentSignatory.id,
      signatureId: signature.id,
      signatureTime: new Date().toISOString(),
      type: SignatureNotificationType.SIGNATURE_SIGNATORY_SIGNED,
      userIds: usersToNotify.map((user) => user.id)
    });
    await this.signaturesService.syncFirebaseSignature(signature);
  }

  async onSignatureCancelation(signatureId: string): Promise<void> {
    const signature = await this.signatureRepository.getSignature(signatureId);

    const contract = await this.signatureRepository.getContract(signature.contractId);

    if (
      contract.status !== ContractStatus.SIGNATURE_DRAFT &&
      contract.status !== ContractStatus.SIGNATURE_PENDING &&
      contract.status !== ContractStatus.SIGNATURE_ERROR &&
      contract.status !== ContractStatus.SIGNATURE_EXPIRED
    ) {
      throw new SignatureContractStatusError({ contractId: signature.contractId });
    }

    if (signature.status === SignatureStatus.DRAFT) {
      await this.signatureRepository.deleteSignature(signatureId);
      await this.updateContractStatusCanceled(signature.contractId);
    } else if (signature.status === SignatureStatus.PENDING) {
      if (signature.type === SignatureType.ELECTRONIC) {
        await this.electronicSignatureProvider.cancelSignature(signature.providerId);
      }
      await this.onActiveSignatureCanceled(signature);
    } else if (signature.status === SignatureStatus.EXPIRED) {
      if (signature.type === SignatureType.ELECTRONIC) {
        await this.electronicSignatureProvider.cancelExpiredSignature(signature.providerId);
      }
      await this.onActiveSignatureCanceled(signature);
    }
  }

  async onActiveSignatureCanceled(signature: SignatureActive): Promise<void> {
    if (signature.status !== SignatureStatus.PENDING && signature.status !== SignatureStatus.EXPIRED) {
      throw new Exception(`Signature ${signature.id} must be pending  or expired to be cancelled`);
    }

    if (signature.type === SignatureType.ELECTRONIC) {
      await this.signatureCreditsService.refundUnusedCredits(signature);
    }

    await this.signatureRepository.cancelSignature({ signatureId: signature.id });

    await this.signatureRegisterUpdater.cancelRegisterEntry(signature);
    await this.eventsApiService.createEvents(createSignatureCanceledEventData(signature));

    const usersToNotify = await this.getUsersToNotify(signature);

    await this.notificationApiService.createNotification({
      cancelationTime: new Date().toISOString(),
      contractId: signature.contractId,
      operationId: signature.operationId,
      signatureId: signature.id,
      type: SignatureNotificationType.SIGNATURE_CANCELED,
      userIds: usersToNotify.map((user) => user.id)
    });

    /**
     * We want to set the expirationTime at the end in case we have issues in production that result in a half-baked cancelled signature.
     * We check the cancellationTime in order to run the sync admin route that resets the signature.
     */
    await this.updateContractStatusCanceled(signature.contractId);

    await this.signaturesService.syncFirebaseSignature(signature);
  }

  async onSignatureSync(signatureId: string): Promise<void> {
    const signature = await this.signatureRepository.getSignature(signatureId);

    assertIsSignatureActive(signature);

    if (signature.providerType !== SignatureProviderType.YOUSIGN_V3) {
      throw new Exception(`Signature ${signature.id} is not a Yousign V3 signature`);
    }

    const signatureProvider = await this.electronicSignatureProvider.getSignature(signature.providerId);

    for (const providerSignatory of signatureProvider.signatories) {
      const currentSignatory = signature.signatories.find(
        (signatory) => signatory.providerId === providerSignatory.providerId
      );

      if (currentSignatory == null) {
        throw new Exception(`Signatory with providerId ${providerSignatory.providerId} not found`);
      }

      if (providerSignatory.hasSigned && currentSignatory.signatureTime == null) {
        await this.onSignatoryCompleted({
          signatoryProviderId: providerSignatory.providerId,
          signatureId
        });
      }
    }

    if (signatureProvider.status === SignatureStatus.SIGNED) {
      await this.onSignatureCompleted({
        signatories: signatureProvider.signatories,
        signatureFile: signatureProvider.signatureFile,
        signatureId,
        signatureProviderId: signature.providerId
      });
    } else if (signatureProvider.status === SignatureStatus.EXPIRED) {
      await this.onSignatureExpired(signatureId);
    } else if (signatureProvider.status === SignatureStatus.CANCELLED) {
      await this.onActiveSignatureCanceled(signature);
    } else {
      throw new SignatureStatusNotSupportedError({ status: signatureProvider.status });
    }
  }

  async onSignatoryError(signatureId: string): Promise<void> {
    const signature = await this.signatureRepository.getSignature(signatureId);

    assertIsSignatureActive(signature);

    const operationLabel = await this.legalsApiService.getOperationLabel(signature.operationId);

    /**
     * Some provider (eg: Yousign) have really strict timeout policy of 1second otherwise they will retry the webhook.
     * This means that we can receive the same event multiple time even if the previous event was processed successfully.
     */
    if (signature.status === SignatureStatus.CANCELLED) {
      return;
    }

    if (signature.status !== SignatureStatus.PENDING) {
      throw new Exception(`Signature ${signature.id} must be pending to be cancelled`);
    }

    if (signature.type === SignatureType.ELECTRONIC) {
      await this.signatureCreditsService.refundUnusedCredits(signature);
    }
    await this.signatureRepository.cancelSignature({ signatureId: signature.id });
    await this.signatureRegisterUpdater.cancelRegisterEntry(signature);
    await this.eventsApiService.createEvents(createSignatureCanceledEventData(signature));
    /**
     * Necessary in order to have the right status when creating a draft signature
     */
    await this.updateContractStatusCanceled(signature.contractId);

    await this.signaturesService.syncFirebaseSignature(signature);

    const creationContext = await this.signatureRepository.getCreationContext(signature.id);

    const newSignature = await this.legacyJavaClient.createSignatureDraft({
      creationContext,
      creatorUserId: signature.creator.id,
      organizationId: signature.organizationId
    });

    await this.signaturesService.updateContractStatus({
      contractId: signature.contractId,
      status: ContractStatus.SIGNATURE_ERROR
    });

    const usersToNotify = await this.getUsersToNotify(signature);
    const receiver = usersToNotify.find((user) => user.id === signature.creator.id);
    assertNotNull(receiver?.email, 'receiver email');

    await this.notificationApiService.createNotification({
      cancelationTime: new Date().toISOString(),
      contractId: signature.contractId,
      newSignatureId: newSignature.id,
      operationId: signature.operationId,
      reason: 'Erreur prestataire de signature',
      signatureId: signature.id,
      type: SignatureNotificationType.SIGNATURE_SIGNATORY_ERROR,
      userIds: [receiver.id]
    });

    await this.emailsApiService.sendEmail({
      data: {
        emailContent: getSignatureErrorContent({
          contractLabel: signature.documentLabel,
          operationLabel,
          url: this.getWebAppDraftSignatureUrl({
            operationId: signature.operationId,
            signatureId: newSignature.id
          })
        }),
        subject: 'Signature annulée',
        templateId: EmailTemplateId.SIGNATURE_ERROR_POST_ACTIVATION
      },
      receiver: receiver.email
    });
  }

  private async updateContractStatusCanceled(contractId: string): Promise<void> {
    const contract = await this.signatureRepository.getContract(contractId);

    await this.signaturesService.updateContractStatus({
      contractId,
      status: isImportedContract(contract.modelId) ? ContractStatus.DRAFT : ContractStatus.VALIDATED
    });
  }

  private async sendSignatureCompletedEmails(signature: SignatureActive) {
    const operationLabel = await this.legalsApiService.getOperationLabel(signature.operationId);

    const organization = await this.organizationsApiService.getOrganization({
      organizationId: signature.organizationId
    });
    const operationId = signature.operationId;

    const documentNameWithOperation = [operationLabel, signature.documentLabel].filter(Boolean).join('-');

    const webAppUrl = this.getWebAppSignatureUrl({
      operationId: operationId,
      signatureId: signature.id
    });

    const creatorEmailData: SignatureCompletedEmail = {
      appUrl: webAppUrl,
      contractId: signature.contractId,
      documentName: documentNameWithOperation,
      isMynotaryUser: true,
      operationId: operationId,
      operationLabel,
      organizationId: signature.organizationId,
      organizationName: organization.name,
      sender: {
        email: signature.creator.email,
        firstname: signature.creator.firstname,
        lastname: signature.creator.lastname
      },
      templateId: EmailTemplateId.SIGNATURE_COMPLETED
    };

    const usersToNotify = await this.getUsersToNotify(signature);

    for (const userToNotify of usersToNotify) {
      await this.emailsApiService.sendEmail({
        data: creatorEmailData,
        receiver: userToNotify.email
      });
    }

    const token = await this.authenticationsApiService.createResourceToken({
      resourceId: signature.id,
      resourceType: ResourceTokenType.SIGNATURE
    });

    const publicUrl = this.getPublicSignatureUrl({
      signatureId: signature.id,
      token: token
    });

    const externalEmailData: Omit<SignatureCompletedEmail, 'isMynotaryUser'> = {
      appUrl: publicUrl,
      contractId: signature.contractId,
      documentName: documentNameWithOperation,
      operationId: operationId,
      operationLabel,
      organizationId: signature.organizationId,
      organizationName: organization.name,
      sender: {
        email: signature.creator.email,
        firstname: signature.creator.firstname,
        lastname: signature.creator.lastname
      },
      templateId: EmailTemplateId.SIGNATURE_COMPLETED
    };

    const signatoryUsers = await this.usersApiService.getUsers({
      emails: signature.signatories.map((signatory) => signatory.email)
    });

    for (const signatory of signature.signatories) {
      if (signatory.email !== signature.creator.email) {
        const isMynotaryUser = signatoryUsers.some((user) => user.email === signatory.email);
        /**
         * We don't want to send the full operation label to external users to avoid sharing sensitive information
         * eg: address of the house to the buyer or name of seller to the buyer.
         */
        const filteredDocumentName = isMynotaryUser ? documentNameWithOperation : `${signature.documentLabel}`;

        await this.emailsApiService.sendEmail({
          data: {
            ...externalEmailData,
            documentName: filteredDocumentName,
            isMynotaryUser
          },
          receiver: signatory.email
        });
      }
    }

    const subscriberUsers = await this.usersApiService.getUsers({
      emails: signature.subscribers.map((subscriber) => subscriber.email)
    });

    for (const subscriber of signature.subscribers) {
      const isMynotaryUser = subscriberUsers.some((subscriber) => subscriber.email === subscriber.email);

      await this.emailsApiService.sendEmail({
        data: {
          ...externalEmailData,
          isMynotaryUser
        },
        receiver: subscriber.email
      });
    }
  }

  private getWebAppSignatureUrl({ operationId, signatureId }: { operationId: string; signatureId: string }): string {
    return `${this.envService.url.mnAppUrl}/operation/${operationId}/signatures-actives/${signatureId}`;
  }

  private getWebAppDraftSignatureUrl({
    operationId,
    signatureId
  }: {
    operationId: string;
    signatureId: string;
  }): string {
    return `${this.envService.url.mnAppUrl}/operation/${operationId}/signatures/${signatureId}/recapitulatif`;
  }

  private getPublicSignatureUrl({ signatureId, token }: { signatureId: string; token: string }): string {
    return `${this.envService.url.mnAppUrl}/signatures?signatureId=${signatureId}&token=${token}`;
  }

  /**
   * Get the users to notify for a given signature.
   * It includes the creator and the activator if it is different from the creator.
   */
  private async getUsersToNotify(signature: SignatureActive): Promise<NotificationUser[]> {
    const usersToNotify: NotificationUser[] = [];

    if (isValidEmail(signature.creator.email)) {
      usersToNotify.push({ email: signature.creator.email, id: signature.creator.id });
    }

    if (signature.creator.id !== signature.activatorUserId) {
      const activatorUser = await this.usersApiService.findUser({ id: signature.activatorUserId, type: 'by-id' });
      if (activatorUser != null) {
        usersToNotify.push({ email: activatorUser.email, id: activatorUser.id });
      }
    }
    return usersToNotify;
  }
}

function createSignatureCompletedEventData(signature: Signature): SignatureCompletedEvent {
  return {
    data: createSignatureEventData(signature),
    eventType: EventType.SIGNATURE_COMPLETED,
    organizationId: signature.organizationId
  };
}

function createSignatureCanceledEventData(signature: Signature): SignatureCanceledEvent {
  return {
    data: createSignatureEventData(signature),
    eventType: EventType.SIGNATURE_CANCELED,
    organizationId: signature.organizationId
  };
}

function createSignatureEventData(signature: Signature) {
  return {
    organizationId: signature.organizationId,
    signatureId: signature.id,
    userId: signature.creator.id
  };
}

interface OnSignatoryCompleted {
  signatoryProviderId: string;
  signatureId: string;
}

interface NotificationUser {
  email: string;
  id: string;
}
