<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSPY v2004 rel. 3 U (http://www.xmlspy.com) by <PERSON><PERSON><PERSON><PERSON> (DIRECTION GENERALE DES IMPOTS) -->
<xs:schema targetNamespace="http://label.flux.teleactes.copernic.finances.gouv.fr/v2009" xmlns:composants="http://teleactes.real.not/v2009" xmlns="http://label.flux.teleactes.copernic.finances.gouv.fr/v2009" xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xs:annotation>
		<xs:documentation><![CDATA[Label Version 1.5 (2010-01-07)                                                                                                       ]]></xs:documentation>
	</xs:annotation>
	<xs:import namespace="http://teleactes.real.not/v2009" schemaLocation="composants_generiques_v2009.xsd"/>
	<xs:element name="label" type="Label_Type">
		<xs:annotation>
			<xs:documentation>Label V 1.6</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:complexType name="Label_Type">
		<xs:annotation>
			<xs:documentation>Type Label</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="code-systeme-emetteur" type="Code_Systeme_Emetteur_Type">
				<xs:annotation>
					<xs:documentation>Identifie le système émetteur</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="code-type-usager" type="Code_Type_Usager_Type">
				<xs:annotation>
					<xs:documentation>Code du type de l'usager</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reference-externe-usager">
				<xs:annotation>
					<xs:documentation>Référence unique d'un usager</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:maxLength value="10"/>
						<xs:minLength value="1"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="code-sages" type="composants:Code_CH_Type">
				<xs:annotation>
					<xs:documentation>Code sages de la CH</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reference-externe-dossier" type="Reference_Externe_Dossier_Type">
				<xs:annotation>
					<xs:documentation>N° dossier attribué par le logiciel du notaire</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="code-type-dossier" type="Code_Type_Dossier_Type">
				<xs:annotation>
					<xs:documentation>Code du type de dossier</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reference-dossier-systeme" type="Reference_Dossier_Systeme_Type">
				<xs:annotation>
					<xs:documentation>Référence (identifiant unique) du document pour l'émetteur.</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="intitule-dossier" type="composants:XSString1-30"/>
			<xs:element name="code-type-flux">
				<xs:annotation>
					<xs:documentation>Code du type de flux du label</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="Code_Type_Flux_Type"/>
				</xs:simpleType>
			</xs:element>
			<xs:element name="reference-dossier" type="Num12_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Identifiant unique du dossier dans Télé@ctes</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="code-type-action" type="Code_Type_Action_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Code type action</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="numero-action" type="Numerique2_Type" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Numero Sequenciel action CH</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="reference-action" type="composants:XSString1-30" minOccurs="0">
				<xs:annotation>
					<xs:documentation>réference usager de l'action CH</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="horodatage" type="xs:dateTime">
				<xs:annotation>
					<xs:documentation>Date et heure de constitution du flux</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<!-- ================================================================================ -->
	<!-- 	Simple Type : STRING                                                                  						-->
	<!-- ================================================================================ -->
	<xs:simpleType name="Code_Systeme_Emetteur_Type">
		<xs:annotation>
			<xs:documentation>Code du système émetteur</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="P">
				<xs:annotation>
					<xs:documentation>Planete</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Reference_Externe_Dossier_Type">
		<xs:annotation>
			<xs:documentation>Chaine de caractère Téléactes</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Za-z0-9]{1,30}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Reference_Dossier_Systeme_Type">
		<xs:annotation>
			<xs:documentation>Chaine de caractère Téléactes</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Za-z0-9]{1,30}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Reference_Externer_Usager_Type">
		<xs:annotation>
			<xs:documentation>Chaine de caractère Téléactes</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Za-z0-9]{1,10}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Code_Type_Action_Type">
		<xs:annotation>
			<xs:documentation>Code du type de l'action CH</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="P">
				<xs:annotation>
					<xs:documentation>Acte de vente</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="H">
				<xs:annotation>
					<xs:documentation>Requisition</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="F">
				<xs:annotation>
					<xs:documentation>Prorogation</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="V">
				<xs:annotation>
					<xs:documentation>Inscription</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="W">
				<xs:annotation>
					<xs:documentation>Radiation</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="C">
				<xs:annotation>
					<xs:documentation>Requisition complémentaire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="A">
				<xs:annotation>
					<xs:documentation>Avenant</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="R">
				<xs:annotation>
					<xs:documentation>Radiation Simplifiée</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="N">
				<xs:annotation>
					<xs:documentation>Convention Rechargement</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="M">
				<xs:annotation>
					<xs:documentation>Renouvellement Inscription</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="G">
				<xs:annotation>
					<xs:documentation>Regularisations</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="T">
				<xs:annotation>
					<xs:documentation>Attestations après décès</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="S">
				<xs:annotation>
					<xs:documentation>Servitudes</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="D">
				<xs:annotation>
					<xs:documentation>Demande de document</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Code_Type_Dossier_Type">
		<xs:annotation>
			<xs:documentation>Code du type dossier</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="P">
				<xs:annotation>
					<xs:documentation>Acte de vente</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="H">
				<xs:annotation>
					<xs:documentation>Requisition HF</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="W">
				<xs:annotation>
					<xs:documentation>Radiation</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="V">
				<xs:annotation>
					<xs:documentation>Inscription</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="A">
				<xs:annotation>
					<xs:documentation>Avenant</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="R">
				<xs:annotation>
					<xs:documentation>Radiation Simplifiée</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="N">
				<xs:annotation>
					<xs:documentation>Convention Rechargement</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="M">
				<xs:annotation>
					<xs:documentation>Renouvellement Inscription</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="G">
				<xs:annotation>
					<xs:documentation>Regularisations</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="T">
				<xs:annotation>
					<xs:documentation>Attestations après décès</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="S">
				<xs:annotation>
					<xs:documentation>Servitudes</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Code_Type_Flux_Type">
		<xs:annotation>
			<xs:documentation>Code du type flux</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="DO">
				<xs:annotation>
					<xs:documentation>Dossier:Contient le dossier reçu de l'émetteur dans Télé@ctes usagers</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AR">
				<xs:annotation>
					<xs:documentation>AR:Réponse Dossier inexploitable mise à disposition de Planète</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RI">
				<xs:annotation>
					<xs:documentation>Réponse d'intégration Fidji : Contient la réponse d'intégration venant de Fidji et retransmise à l'émetteur via Téléactes Gestion et Téléactes Usagers </xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="SI">
				<xs:annotation>
					<xs:documentation>Réponse CH transmise par Téléactes à l'émetteur</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="DP">
				<xs:annotation>
					<xs:documentation>Dépôt : Créé par Téléactes Usagers.Contient le dépôt pour Téléactes Gestion</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="DF">
				<xs:annotation>
					<xs:documentation>Dossier Fidji :Contient le dossier transmis par Téléactes Gestion à Fidji</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RH">
				<xs:annotation>
					<xs:documentation>Réponse CH : Flux contenant la réponse CH venant de Fidji.Concerne une action CH</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PU">
				<xs:annotation>
					<xs:documentation>Acquittement Purge : Flux contenant l'acquittement de Téléactes Gestion à destination de Fidji.Concerne une action CH</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FT">
				<xs:annotation>
					<xs:documentation>Facture dématérialisée</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Code_Type_Usager_Type">
		<xs:annotation>
			<xs:documentation>Code du type émetteur</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="N">
				<xs:annotation>
					<xs:documentation>Notaire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="A">
				<xs:annotation>
					<xs:documentation>Autres</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!-- ================================================================================ -->
	<!-- 	Simple Type : NUMERIQUE ET DECIMAL                                            						-->
	<!-- ================================================================================ -->
	<xs:simpleType name="Numerique2_Type">
		<xs:annotation>
			<xs:documentation>Un à deux chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="\d{1,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Num12_Type">
		<xs:annotation>
			<xs:documentation>Un à douze chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="\d{12}"/>
		</xs:restriction>
	</xs:simpleType>
</xs:schema>
