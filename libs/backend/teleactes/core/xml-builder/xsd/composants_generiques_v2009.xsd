<?xml version="1.0" encoding="UTF-8"?>
<!-- Version 3.27 du 26/04/2016 - Modification du schéma pour les PVI/ Etat civil / Document d'arpentage -->
<!-- Version 3.27 du 26/04/2016 -  -->
<!-- Version 3.28 du 08/09/2017 – Modifications 100pour100 T@ ref Dicdo 4.04.01 -->
<!-- Version 3.29 du 26/09/2017 – Modifications 100pour100 T@ 100%, Fusion SPF et Exo CSI - ref Dicdo 4.05 -->
<!-- Version 3.30 du 26/09/2018 – Modifications ref Dicdo 4.07.04 -->
<!-- Version 3.31 du 03/10/2020 - Modifications ref Dicdo 4.10.04 -->
<!-- Version 3.32 du 04/06/2021 - Modifications ref Dicdo 4.12.01 -->
<!--                                            Ajout de l'enumération Enum_Fond -->
<!-- Version 3.33 du 08/07/2022 - Modifications ref Dicdo 4.13.01 -->
<!--                                            Ajout de l'item HLES pour Enum_Nature_Formalite_Inscription -->


<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://teleactes.real.not/v2009" targetNamespace="http://teleactes.real.not/v2009" elementFormDefault="qualified" attributeFormDefault="unqualified" version="3.33" xml:lang="fr">
	<xs:annotation>
		<xs:documentation source="Révision 31/01/2007" xml:lang="fr">Modifié par S.LANDEAU - Real.not.
Les modifications suivantes ont été apportées :
* passage du namespace en 2007M
* introduction du type Inscription_type1_2_3_Type
Puis DMA le 26/08/08
		</xs:documentation>
		<xs:documentation source="Révision 13/03/2013" xml:lang="fr">
			Ajout de la nature VTAB pour les dépôts d'actes.
		</xs:documentation>
	</xs:annotation>
	<!--********************** TYPES SIMPLES ***********-->
	<xs:simpleType name="bool">
		<xs:restriction base="xs:NMTOKEN">
			<xs:enumeration value="oui"/>
			<xs:enumeration value="non"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Bic_Type">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Za-z0-9]{8,11}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Chaine_Alphanumerique">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Za-z0-9]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Chaine_Caractere_Type">
		<xs:annotation>
			<xs:documentation>Chaine de caractère Téléactes</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value=" ?[A-Za-z0-9&apos;@&amp;\.!&quot;#$\(\)\*,-/:;&gt;&lt;=\?\[\\\]^_\{\}\|£§¨°²~\+%€µâäàáãåèéêëìíîïôöòóõùúûüýçñÀÁÂÃÄÅÈÉÊËÌÍÎÏÒÓÔÕÖÙÚÛÜÝÇÑ]+[ A-Za-z0-9&apos;@&amp;\.!&quot;#$\(\)\*,-/:;&gt;&lt;=\?\[\\\]^_\{\}\|£§¨°²~\+%€µâäàáãåèéêëìíîïôöòóõùúûüýçñÿÀÁÂÃÄÅÈÉÊËÌÍÎÏÒÓÔÕÖÙÚÛÜÝÇÑŸ]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Chaine_Caractere_Sans_Espace_Type">
		<xs:annotation>
			<xs:documentation>Chaine de caractère Téléactes</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:whiteSpace value="preserve"/>
			<xs:pattern value="[A-Za-z0-9&apos;@&amp;\.!&quot;#$\(\)\*,-/:;&gt;&lt;=\?\[\\\]^_\{\}\|£§¨°²~\+%€µâäàáãåèéêëìíîïôöòóõùúûüýçñÿÀÁÂÃÄÅÈÉÊËÌÍÎÏÒÓÔÕÖÙÚÛÜÝÇÑŸ]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Code_Commune_Type">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Za-z0-9]{1,3}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Code_Commune_TOPAD_Type">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Za-z0-9]{1,3}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Code_Departement_Type">
		<xs:annotation>
			<xs:documentation>2 ou 3 caractères Ne doit pas comporter d'espace </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Za-z0-9]{2,3}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Code_Devise_FRF-EUR">
		<xs:annotation>
			<xs:documentation>Code devise FRF ou EUR</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:enumeration value="EUR"/>
			<xs:enumeration value="FRF"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Code_Devise_ISO">
		<xs:annotation>
			<xs:documentation>Code devise norme ISO 4217</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:length value="3"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Date_Naissance_Type">
		<xs:annotation>
			<xs:documentation>Format Date (année, mois, jour) ou Année, mois ou année</xs:documentation>
		</xs:annotation>
		<xs:union memberTypes="xs:date xs:gYear xs:gYearMonth"/>
	</xs:simpleType>
	<xs:simpleType name="Decimal2-2">
		<xs:annotation>
			<xs:documentation>montant numérique avec 2 entiers, un point décimal et 2 décimales. </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:decimal">
			<xs:pattern value="[0-9]{1,2}\.[0-9]{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Decimal4-2">
		<xs:annotation>
			<xs:documentation>montant numérique avec 4 entiers, un point décimal et 2 décimales. </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:decimal">
			<xs:pattern value="[0-9]{1,4}\.[0-9]{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Decimal2-4">
		<xs:annotation>
			<xs:documentation>montant numérique avec 2 entiers, un point décimal et 4 décimales. </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:decimal">
			<xs:pattern value="\d{1,2}\.\d{4}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Decimal6-4">
		<xs:annotation>
			<xs:documentation>montant numérique avec 6 entiers, un point et 4 décimales</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:decimal">
			<xs:pattern value="\d{1,6}\.\d{4}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Decimal10-2">
		<xs:annotation>
			<xs:documentation>montant numérique avec 10 entiers, un point décimal et 2 décimales. </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:decimal">
			<xs:pattern value="[0-9]{1,10}\.[0-9]{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Decimal11-2">
		<xs:annotation>
			<xs:documentation>montant numérique avec 11 entiers, un point décimal et 2 décimales. </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:decimal">
			<xs:pattern value="[0-9]{1,11}\.[0-9]{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Num2">
		<xs:annotation>
			<xs:documentation>Deux chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="[0-9]{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Num3">
		<xs:annotation>
			<xs:documentation>Trois chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="[0-9]{3}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Num4">
		<xs:annotation>
			<xs:documentation>Quatre chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="[0-9]{1,4}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Numerique1">
		<xs:annotation>
			<xs:documentation>Un chiffre</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="[0-9]"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Numerique2">
		<xs:annotation>
			<xs:documentation>Un ou deux chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="[0-9]{1,2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Numerique4">
		<xs:annotation>
			<xs:documentation>Un à quatre chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="[0-9]{1,4}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Numerique3">
		<xs:annotation>
			<xs:documentation>Un à trois chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="[0-9]{1,3}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Numerique5">
		<xs:annotation>
			<xs:documentation>Un à cinq chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="[0-9]{1,5}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Numerique6">
		<xs:annotation>
			<xs:documentation>Un à six chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="[0-9]{1,6}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Numerique7">
		<xs:annotation>
			<xs:documentation>Un à 7 chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="[0-9]{1,7}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Numerique9">
		<xs:annotation>
			<xs:documentation>Un à 10 chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="[0-9]{1,9}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Numerique10">
		<xs:annotation>
			<xs:documentation>Un à 10 chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="[0-9]{1,10}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="NegNumerique10">
		<xs:annotation>
			<xs:documentation>Un à 10 chiffres, négatif possible</xs:documentation>
			<xs:documentation xml:lang="fr">Le pattern est une restriction du type natif dérvié xs:integer</xs:documentation>
			<xs:documentation source="http://www.w3.org/TR/xmlschema-2/#integer" xml:lang="en">3.3.13.1 Lexical representation
integer has a lexical representation consisting of a finite-length sequence of decimal digits (#x30-#x39) with an optional leading sign. If the sign is omitted, "+" is assumed. For example: -1, 0, 12678967543233, +100000.
</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:integer">
			<xs:pattern value="(-|\+)?[0-9]{1,10}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Num5">
		<xs:annotation>
			<xs:documentation>Cinq chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{5}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Num6">
		<xs:annotation>
			<xs:documentation>Six chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:nonNegativeInteger">
			<xs:pattern value="[0-9]{6}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Str13">
		<xs:annotation>
			<xs:documentation>13 caractères alpha</xs:documentation>
		</xs:annotation>
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="13"/>
			<xs:maxLength value="13"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Numtel10">
		<xs:restriction base="xs:string">
			<xs:maxLength value="10"/>
			<xs:pattern value="[0-9]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Numtel10-10">
		<xs:annotation>
			<xs:documentation>10 chiffres</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{10}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Identifiant_Etude_Notariale_Type">
		<xs:annotation>
			<xs:documentation>Il correspond au code de l'étude notariale (code CRPCEN).  </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{6}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Identifiant_Numerique_Type">
		<xs:annotation>
			<xs:documentation>Identifiant d'une disposition</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]*"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Identifiant_Type">
		<xs:annotation>
			<xs:documentation>Identifiant d'un immeuble ou d'une partie</xs:documentation>
		</xs:annotation>
		<xs:restriction base="Chaine_Alphanumerique">
			<xs:minLength value="1"/>
			<xs:maxLength value="10"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Pourcentage-2">
		<xs:annotation>
			<xs:documentation>Pourcentage 0 à 100 avec 2 chiffres aprés la virgule</xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:string">
			<xs:pattern value="([0-9]{1,2}\.[0-9]{2})|100.00"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Str2">
		<xs:annotation>
			<xs:documentation>Exactement deux caractères</xs:documentation>
		</xs:annotation>
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="2"/>
			<xs:maxLength value="2"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1">
		<xs:restriction base="xs:string">
			<xs:maxLength value="1"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-25">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="25"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-12">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-62">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="62"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="XSString1-62">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="62"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-1">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="1"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-10">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="10"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="XSString1-10">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="10"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-100">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-2">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="2"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-20">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-26">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="26"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-200">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="200"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-250">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="250"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="XSString1-250">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="250"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-30">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="XSString1-30">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-32">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="XSString1-32">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-4">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="XSString1-400">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="400"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-50">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="XSString1-50">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="XSString1-500">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="500"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-60">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="60"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="XSString1-60">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="60"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-70">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="70"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-7">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="7"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-9">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="9"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="XSString1-9">
		<xs:restriction base="xs:string">
			<xs:minLength value="1"/>
			<xs:maxLength value="9"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String1-5">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="5"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String12">
		<xs:restriction base="xs:string">
			<xs:maxLength value="12"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String10">
		<xs:restriction base="xs:string">
			<xs:maxLength value="10"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String11">
		<xs:restriction base="xs:string">
			<xs:maxLength value="11"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String100">
		<xs:restriction base="xs:string">
			<xs:maxLength value="100"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String2">
		<xs:restriction base="xs:string">
			<xs:maxLength value="2"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String20">
		<xs:restriction base="xs:string">
			<xs:maxLength value="20"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String23">
		<xs:restriction base="xs:string">
			<xs:maxLength value="23"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String2-7">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="2"/>
			<xs:maxLength value="7"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String25">
		<xs:restriction base="xs:string">
			<xs:maxLength value="25"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String250">
		<xs:restriction base="xs:string">
			<xs:maxLength value="250"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String26">
		<xs:restriction base="xs:string">
			<xs:maxLength value="26"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String30">
		<xs:restriction base="xs:string">
			<xs:maxLength value="30"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String32">
		<xs:restriction base="xs:string">
			<xs:maxLength value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String38">
		<xs:restriction base="xs:string">
			<xs:maxLength value="38"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String4">
		<xs:restriction base="xs:string">
			<xs:maxLength value="4"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String40">
		<xs:restriction base="xs:string">
			<xs:maxLength value="40"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String-1-200">
		<xs:restriction base="xs:string">
			<xs:maxLength value="200"/>
			<xs:minLength value="1"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String-1-300">
		<xs:restriction base="xs:string">
			<xs:maxLength value="300"/>
			<xs:minLength value="1"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String400">
		<xs:restriction base="xs:string">
			<xs:maxLength value="400"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String5">
		<xs:restriction base="xs:string">
			<xs:maxLength value="5"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String50">
		<xs:restriction base="xs:string">
			<xs:maxLength value="50"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String60">
		<xs:restriction base="xs:string">
			<xs:maxLength value="60"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String62">
		<xs:restriction base="xs:string">
			<xs:maxLength value="62"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String7">
		<xs:restriction base="xs:string">
			<xs:maxLength value="7"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String9">
		<xs:restriction base="xs:string">
			<xs:maxLength value="9"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="String7-7">
		<xs:restriction base="Chaine_Caractere_Type">
			<xs:minLength value="7"/>
			<xs:maxLength value="7"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Code_CH_Type">
		<xs:restriction base="xs:string">
			<xs:pattern value="[A-Za-z0-9]{7}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Code_Sages_Type">
		<xs:restriction base="Chaine_Alphanumerique">
			<xs:minLength value="7"/>
			<xs:maxLength value="7"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="StringNum1-7">
		<xs:restriction base="xs:string">
			<xs:pattern value="[0-9]{1,7}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="StringSE1-2">
		<xs:restriction base="Chaine_Caractere_Sans_Espace_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="2"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="StringSE1-32">
		<xs:restriction base="Chaine_Caractere_Sans_Espace_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="32"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="StringSE1-7">
		<xs:annotation>
			<xs:documentation>1 à 7 caractères  Ne doit pas comporter d'espace</xs:documentation>
		</xs:annotation>
		<xs:restriction base="Chaine_Caractere_Sans_Espace_Type">
			<xs:minLength value="1"/>
			<xs:maxLength value="7"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Surface">
		<xs:annotation>
			<xs:documentation>Surface en m2 (avec 2 décimales). </xs:documentation>
		</xs:annotation>
		<xs:restriction base="xs:decimal">
			<xs:pattern value="[0-9]{1,4}\.[0-9]{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Taux">
		<xs:restriction base="xs:decimal">
			<xs:pattern value="\d{1,2}\.\d{4}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Taux2nDec">
		<xs:restriction base="xs:decimal">
			<xs:pattern value="\d{1,2}\.\d{2,}"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Taux3.2Dec">
		<xs:restriction base="xs:decimal">
			<xs:pattern value="\d{1,3}\.\d{2}"/>
		</xs:restriction>
	</xs:simpleType>
	<!--**********************  ENUMERATIONS ***********-->
	<xs:simpleType name="Enum_Action_CH">
		<xs:restriction base="xs:string">
			<xs:enumeration value="P">
				<xs:annotation>
					<xs:documentation>Acte de vente</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="H">
				<xs:annotation>
					<xs:documentation>Réquisition HF</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="C">
				<xs:annotation>
					<xs:documentation>Réquisition HF complémentaire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="F">
				<xs:annotation>
					<xs:documentation>Prorogation</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="V">
				<xs:annotation>
					<xs:documentation>Formalite_Inscription_Type</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="W">
				<xs:annotation>
					<xs:documentation>Formalite_Radiation_Type</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="A">
				<xs:annotation>
					<xs:documentation>Avenant</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="M">
				<xs:annotation>
					<xs:documentation>Convention de rechargement</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="R">
				<xs:annotation>
					<xs:documentation>Radiation simplifiée</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="N">
				<xs:annotation>
					<xs:documentation>Renouvellement</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="T">
				<xs:annotation>
					<xs:documentation>Attestations parès décès</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="G">
				<xs:annotation>
					<xs:documentation>Régularisations</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="S">
				<xs:annotation>
					<xs:documentation>Servitudes</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="D">
				<xs:annotation>
					<xs:documentation>Demande de document</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Categorie_Juridique">
		<xs:restriction base="xs:string">
			<xs:enumeration value="1100">
				<xs:annotation>
					<xs:documentation>Artisan Commerçant</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="1200">
				<xs:annotation>
					<xs:documentation>Commerçant</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="1300">
				<xs:annotation>
					<xs:documentation>Artisan</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="1400">
				<xs:annotation>
					<xs:documentation>Officier public ou ministériel</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="1500">
				<xs:annotation>
					<xs:documentation>Profession libérale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="1600">
				<xs:annotation>
					<xs:documentation>Exploitant agricole</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="1700">
				<xs:annotation>
					<xs:documentation>Agent commercial</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="1800">
				<xs:annotation>
					<xs:documentation>Associé Gérant de société</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="1900">
				<xs:annotation>
					<xs:documentation>(Autre) personne physique</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="2110">
				<xs:annotation>
					<xs:documentation>Indivision entre personnes physiques</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="2120">
				<xs:annotation>
					<xs:documentation>Indivision avec personne morale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="2210">
				<xs:annotation>
					<xs:documentation>Société créée de fait entre personnes physiques</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="2220">
				<xs:annotation>
					<xs:documentation>Société créée de fait avec personne morale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="2310">
				<xs:annotation>
					<xs:documentation>Société en participation entre personnes physiques</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="2320">
				<xs:annotation>
					<xs:documentation>Société en participation avec personne morale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="2385">
				<xs:annotation>
					<xs:documentation>Société en participation de professions libérales</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="2400"/>
			<xs:enumeration value="2700">
				<xs:annotation>
					<xs:documentation>Paroisse hors zone concordataire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="2900">
				<xs:annotation>
					<xs:documentation>Autre groupement de droit privé non doté de la personnalité morale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="3110">
				<xs:annotation>
					<xs:documentation>Représentation ou agence commerciale d'état ou organisme public étranger immatriculé au RCS</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="3120">
				<xs:annotation>
					<xs:documentation>Société étrangère immatriculée au RCS</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="3205">
				<xs:annotation>
					<xs:documentation>Organisation internationale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="3210">
				<xs:annotation>
					<xs:documentation>État collectivité ou établissement public étranger</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="3220">
				<xs:annotation>
					<xs:documentation>Société étrangère non immatriculée au RCS</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="3290">
				<xs:annotation>
					<xs:documentation>(Autre) personne morale de droit étranger</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="4110">
				<xs:annotation>
					<xs:documentation>Établissement public national à caractère industriel ou commercial doté d'un comptable public</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="4120">
				<xs:annotation>
					<xs:documentation>Établissement public national à caractère industriel ou commercial non doté d'un comptable public</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="4130">
				<xs:annotation>
					<xs:documentation>Exploitant public</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="4140">
				<xs:annotation>
					<xs:documentation>Établissement public local à caractère industriel ou commercial</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="4150">
				<xs:annotation>
					<xs:documentation>Régie d'une collectivité locale à caractère industriel ou commercial</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="4160">
				<xs:annotation>
					<xs:documentation>Institution Banque de France</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5191">
				<xs:annotation>
					<xs:documentation>Société de caution mutuelle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5192">
				<xs:annotation>
					<xs:documentation>Société coopérative de banque populaire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5193">
				<xs:annotation>
					<xs:documentation>Caisse de crédit maritime mutuel</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5194">
				<xs:annotation>
					<xs:documentation>Caisse (fédérale) de crédit mutuel</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5195">
				<xs:annotation>
					<xs:documentation>Association coopérative inscrite ( droit local Alsace Moselle )</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5196">
				<xs:annotation>
					<xs:documentation>Caisse d'épargne et de prévoyance à forme coopérative</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5202">
				<xs:annotation>
					<xs:documentation>Société en nom collectif</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5203">
				<xs:annotation>
					<xs:documentation>Société en nom collectif coopérative</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5306">
				<xs:annotation>
					<xs:documentation>Société en commandite simple</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5307">
				<xs:annotation>
					<xs:documentation>Société en commandite simple coopérative</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5308">
				<xs:annotation>
					<xs:documentation>Société en commandite par actions</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5309">
				<xs:annotation>
					<xs:documentation>Société en commandite par actions coopérative</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5385">
				<xs:annotation>
					<xs:documentation>Société d'exercice libéral en commandite par action</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5410">
				<xs:annotation>
					<xs:documentation>SARL nationale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5415">
				<xs:annotation>
					<xs:documentation>SARL d'économie mixte</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5422">
				<xs:annotation>
					<xs:documentation>SARL immobilière pour le commerce et l'industrie (SICOMI)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5426">
				<xs:annotation>
					<xs:documentation>Société immobilière de gestion</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5430">
				<xs:annotation>
					<xs:documentation>Safer en SARL</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5431">
				<xs:annotation>
					<xs:documentation>SARL mixte d'intérêt agricole (SMIA)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5432">
				<xs:annotation>
					<xs:documentation>SARL d'intérêt collectif agricole (SICA)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5442">
				<xs:annotation>
					<xs:documentation>SARL d'attribution</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5443">
				<xs:annotation>
					<xs:documentation>SARL coopérative de construction</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5451">
				<xs:annotation>
					<xs:documentation>SARL coopérative de consommation</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5453">
				<xs:annotation>
					<xs:documentation>SARL coopérative artisanale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5454">
				<xs:annotation>
					<xs:documentation>SARL coopérative d'intérêt maritime</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5455">
				<xs:annotation>
					<xs:documentation>SARL coopérative de transports</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5458">
				<xs:annotation>
					<xs:documentation>SARL coopérative ouvrière de production et de crédit (SCOP)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5459">
				<xs:annotation>
					<xs:documentation>SARL union de sociétés coopératives</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5460">
				<xs:annotation>
					<xs:documentation>Autre SARL coopérative</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5485">
				<xs:annotation>
					<xs:documentation>Société d'exercice libéral à responsabilité limitée</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5498">
				<xs:annotation>
					<xs:documentation>SARL unipersonnelle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5499">
				<xs:annotation>
					<xs:documentation>Autre société à responsabilité limitée</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5505">
				<xs:annotation>
					<xs:documentation>SA à participation ouvrière à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5510">
				<xs:annotation>
					<xs:documentation>SA nationale à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5515">
				<xs:annotation>
					<xs:documentation>SA d'économie mixte à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5520">
				<xs:annotation>
					<xs:documentation>Société d'investissement à capital variable (SICAV) à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5522">
				<xs:annotation>
					<xs:documentation>Société anonyme immobilière pour le commerce et l'industrie (SICOMI) à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5525">
				<xs:annotation>
					<xs:documentation>Société anonyme immobilière d'investissement à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5530">
				<xs:annotation>
					<xs:documentation>Safer anonyme à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5531">
				<xs:annotation>
					<xs:documentation>Société anonyme mixte d'intérêt agricole (SMIA) à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5532">
				<xs:annotation>
					<xs:documentation>Société anonyme mixte d'intérêt collectif agricole (SICA) à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5542">
				<xs:annotation>
					<xs:documentation>Société anonyme d'attribution à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5543">
				<xs:annotation>
					<xs:documentation>Société anonyme coopérative de construction à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5546">
				<xs:annotation>
					<xs:documentation>SA de HLM à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5547">
				<xs:annotation>
					<xs:documentation>SA coopérative de production de HLM à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5548">
				<xs:annotation>
					<xs:documentation>SA de crédit immobilier à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5551">
				<xs:annotation>
					<xs:documentation>SA coopérative de consommation à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5552">
				<xs:annotation>
					<xs:documentation>SA coopérative de commerçants détaillants à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5553">
				<xs:annotation>
					<xs:documentation>SA coopérative artisanale à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5554">
				<xs:annotation>
					<xs:documentation>SA coopérative (d'intérêt) maritime à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5555">
				<xs:annotation>
					<xs:documentation>SA coopérative de transports à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5558">
				<xs:annotation>
					<xs:documentation>SA coopérative ouvrière de production et de crédit (SCOP) à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5559">
				<xs:annotation>
					<xs:documentation>SA union de sociétés coopératives à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5560">
				<xs:annotation>
					<xs:documentation>Autre SA coopérative à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5585">
				<xs:annotation>
					<xs:documentation>Société d'exercice libéral à forme anonyme à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5599">
				<xs:annotation>
					<xs:documentation>Autre SA à conseil d'administration</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5605">
				<xs:annotation>
					<xs:documentation>SA à participation ouvrière à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5610">
				<xs:annotation>
					<xs:documentation>SA nationale à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5615">
				<xs:annotation>
					<xs:documentation>SA d'économie mixte à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5620">
				<xs:annotation>
					<xs:documentation>Société d'investissement à capital variable (SICAV) à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5622">
				<xs:annotation>
					<xs:documentation>Société immobilière pour le commerce et l'industrie (SICOMI) anonyme à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5625">
				<xs:annotation>
					<xs:documentation>Société immobilière d'investissement anonyme à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5630">
				<xs:annotation>
					<xs:documentation>Safer anonyme à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5631">
				<xs:annotation>
					<xs:documentation>Société anonyme mixte d'intérêt agricole (SMIA)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5632">
				<xs:annotation>
					<xs:documentation>Société anonyme d'intérêt collectif agricole (SICA)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5642">
				<xs:annotation>
					<xs:documentation>Société anonyme d'attribution à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5643">
				<xs:annotation>
					<xs:documentation>Société anonyme coopérative de construction à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5646">
				<xs:annotation>
					<xs:documentation>Société anonyme de HLM à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5647">
				<xs:annotation>
					<xs:documentation>Société coopérative de production de HLM anonyme à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5648">
				<xs:annotation>
					<xs:documentation>SA de crédit immobilier à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5651">
				<xs:annotation>
					<xs:documentation>SA coopérative de consommation à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5652">
				<xs:annotation>
					<xs:documentation>SA coopérative de commerçants détaillants à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5653">
				<xs:annotation>
					<xs:documentation>SA coopérative artisanale à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5654">
				<xs:annotation>
					<xs:documentation>SA coopérative (d'intérêt) maritime à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5655">
				<xs:annotation>
					<xs:documentation>SA coopérative de transport à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5658">
				<xs:annotation>
					<xs:documentation>SA coopérative ouvrière de production et de crédit (SCOP) à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5659">
				<xs:annotation>
					<xs:documentation>SA union de sociétés coopératives à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5660">
				<xs:annotation>
					<xs:documentation>(Autre) SA coopérative à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5685">
				<xs:annotation>
					<xs:documentation>Société d'exercice libéral à forme anonyme à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5699">
				<xs:annotation>
					<xs:documentation>(Autre) SA à directoire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5710">
				<xs:annotation>
					<xs:documentation>Société par actions simplifiée (SAS)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5720">
				<xs:annotation>
					<xs:documentation>Société par actions simplifiée à associé unique ou société par actions simplifiée unipersonnelle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5785"/>
			<xs:enumeration value="5800"/>
			<xs:enumeration value="6100">
				<xs:annotation>
					<xs:documentation>Caisse d'épargne et de prévoyance</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6210">
				<xs:annotation>
					<xs:documentation>Groupement européen d'intérêt économique (GEIE)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6220">
				<xs:annotation>
					<xs:documentation>Groupement d'intérêt économique (GIE)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6316">
				<xs:annotation>
					<xs:documentation>Coopérative d'utilisation de matériel agricole en commun (CUMA)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6317">
				<xs:annotation>
					<xs:documentation>Société coopérative agricole</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6318">
				<xs:annotation>
					<xs:documentation>Union de sociétés coopératives agricoles</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6411">
				<xs:annotation>
					<xs:documentation>Société d'assurance mutuelle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6521">
				<xs:annotation>
					<xs:documentation>Société civile de placement collectif immobilier (SCPI)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6532">
				<xs:annotation>
					<xs:documentation>Société civile d'intérêt collectif agricole (SICA)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6533">
				<xs:annotation>
					<xs:documentation>Groupement agricole d'exploitation en commun (GAEC)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6534">
				<xs:annotation>
					<xs:documentation>Groupement foncier agricole</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6535">
				<xs:annotation>
					<xs:documentation>Groupement agricole foncier</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6536">
				<xs:annotation>
					<xs:documentation>Groupement forestier</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6537">
				<xs:annotation>
					<xs:documentation>Groupement pastoral</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6538">
				<xs:annotation>
					<xs:documentation>Groupement foncier rural</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6539">
				<xs:annotation>
					<xs:documentation>Société civile foncière</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6540">
				<xs:annotation>
					<xs:documentation>Société civile immobilière</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6541">
				<xs:annotation>
					<xs:documentation>Société civile immobilière de construction vente</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6542">
				<xs:annotation>
					<xs:documentation>Société civile d'attribution</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6543">
				<xs:annotation>
					<xs:documentation>Société civile coopérative de construction</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6551">
				<xs:annotation>
					<xs:documentation>Société civile coopérative de consommation</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6554">
				<xs:annotation>
					<xs:documentation>Société civile coopérative (d'intérêt) maritime</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6558">
				<xs:annotation>
					<xs:documentation>Société civile coopérative entre médecins</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6560">
				<xs:annotation>
					<xs:documentation>Autre société civile coopérative</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6561">
				<xs:annotation>
					<xs:documentation>SCP d'avocats</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6562">
				<xs:annotation>
					<xs:documentation>SCP d'avocats aux conseil</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6563">
				<xs:annotation>
					<xs:documentation>SCP d'avoués d'appel</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6564">
				<xs:annotation>
					<xs:documentation>SCP d'huissiers</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6565">
				<xs:annotation>
					<xs:documentation>SCP de notaires</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6566">
				<xs:annotation>
					<xs:documentation>SCP de commissaires-priseurs</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6567">
				<xs:annotation>
					<xs:documentation>SCP de greffiers de tribunal de commerce</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6568">
				<xs:annotation>
					<xs:documentation>SCP de conseils juridiques</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6569">
				<xs:annotation>
					<xs:documentation>SCP de commissaires aux comptes</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6571">
				<xs:annotation>
					<xs:documentation>SCP de médecins</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6572">
				<xs:annotation>
					<xs:documentation>SCP de dentistes</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6573">
				<xs:annotation>
					<xs:documentation>SCP d'infirmiers</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6574">
				<xs:annotation>
					<xs:documentation>SCP de masseurs kinésithérapeutes</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6575">
				<xs:annotation>
					<xs:documentation>SCP de directeurs de laboratoire d'analyse médicale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6576">
				<xs:annotation>
					<xs:documentation>SCP de vétérinaires</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6577">
				<xs:annotation>
					<xs:documentation>SCP de géomètres-experts</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6578">
				<xs:annotation>
					<xs:documentation>SCP d'architectes</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6585">
				<xs:annotation>
					<xs:documentation>(Autres) Société Civile professionnelle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6588"/>
			<xs:enumeration value="6589">
				<xs:annotation>
					<xs:documentation>Société civile de moyens</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6595">
				<xs:annotation>
					<xs:documentation>Caisse (locale) de crédit mutuel</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6596">
				<xs:annotation>
					<xs:documentation>Caisse de crédit agricole mutuel</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6597">
				<xs:annotation>
					<xs:documentation>Société civile d'exploitation agricole</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6598">
				<xs:annotation>
					<xs:documentation>Exploitation agricole à responsabilité limitée</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6599">
				<xs:annotation>
					<xs:documentation>Autre société civile</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6901">
				<xs:annotation>
					<xs:documentation>Autres personnes de droit privé inscrites au registre du commerce et des sociétés</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7111">
				<xs:annotation>
					<xs:documentation>Autorité constitutionnelle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7112">
				<xs:annotation>
					<xs:documentation>Autorité administrative indépendante</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7113">
				<xs:annotation>
					<xs:documentation>Ministère</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7120">
				<xs:annotation>
					<xs:documentation>Service central d'un ministère</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7150">
				<xs:annotation>
					<xs:documentation>Service du ministère de la Défense</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7160">
				<xs:annotation>
					<xs:documentation>Service déconcentré à compétence nation. D'un ministère (hors Défense)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7171">
				<xs:annotation>
					<xs:documentation>Service déconcentré de l'État à compétence (inter) régionale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7172">
				<xs:annotation>
					<xs:documentation>Service déconcentré de l'État à compétence (inter) départementale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7179">
				<xs:annotation>
					<xs:documentation>(Autre) Service déconcentré de l'État à compétence territoriale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7190">
				<xs:annotation>
					<xs:documentation>Ecole nationale non dotée de la personnalité morale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7210">
				<xs:annotation>
					<xs:documentation>Commune</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7220">
				<xs:annotation>
					<xs:documentation>Département</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7225">
				<xs:annotation>
					<xs:documentation>Territoire d'Outre-mer</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7229">
				<xs:annotation>
					<xs:documentation>(Autre) Collectivité territoriale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7230">
				<xs:annotation>
					<xs:documentation>Région</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7312">
				<xs:annotation>
					<xs:documentation>Commune associée</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7313">
				<xs:annotation>
					<xs:documentation>Section de commune</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7314">
				<xs:annotation>
					<xs:documentation>Ensemble urbain</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7321">
				<xs:annotation>
					<xs:documentation>Association syndicale autorisée</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7322">
				<xs:annotation>
					<xs:documentation>Association foncière urbaine</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7323">
				<xs:annotation>
					<xs:documentation>Association foncière de remembrement</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7331">
				<xs:annotation>
					<xs:documentation>Établissement public local d'enseignement</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7341">
				<xs:annotation>
					<xs:documentation>Secteur de commune</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7342">
				<xs:annotation>
					<xs:documentation>District urbain</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7343">
				<xs:annotation>
					<xs:documentation>Communauté urbaine</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7345">
				<xs:annotation>
					<xs:documentation>Syndicat intercommunal à vocation multiple (SIVOM)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7346">
				<xs:annotation>
					<xs:documentation>Communauté de communes</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7347">
				<xs:annotation>
					<xs:documentation>Communauté de villes</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7348">
				<xs:annotation>
					<xs:documentation>Communauté d'agglomération</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7349">
				<xs:annotation>
					<xs:documentation>Autre établissement public local de coopération non spécialisé ou entente</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7351">
				<xs:annotation>
					<xs:documentation>Institution interdépartemental ou entente</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7352">
				<xs:annotation>
					<xs:documentation>Institution interrégionale ou entente</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7353">
				<xs:annotation>
					<xs:documentation>Syndicat intercommunal à vocation unique (SIVU)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7354">
				<xs:annotation>
					<xs:documentation>Syndicat mixte communal</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7355">
				<xs:annotation>
					<xs:documentation>Autre syndicat mixte</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7356">
				<xs:annotation>
					<xs:documentation>Commission syndicale pour la gestion des biens indivis des communes</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7361">
				<xs:annotation>
					<xs:documentation>Centre communal d'action sociale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7362">
				<xs:annotation>
					<xs:documentation>Caisse des écoles</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7363">
				<xs:annotation>
					<xs:documentation>Caisse de crédit municipal</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7364">
				<xs:annotation>
					<xs:documentation>Établissement d'hospitalisation</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7365">
				<xs:annotation>
					<xs:documentation>Syndicat inter hospitalier</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7366">
				<xs:annotation>
					<xs:documentation>Établissement public local social et médico-social</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7371">
				<xs:annotation>
					<xs:documentation>Office public d'habitation à loyer modéré (OPHLM)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7372">
				<xs:annotation>
					<xs:documentation>Service départemental d'incendie</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7373">
				<xs:annotation>
					<xs:documentation>Établissement public local culturel</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7378">
				<xs:annotation>
					<xs:documentation>Régie d'une collectivité locale à caractère administratif</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7379">
				<xs:annotation>
					<xs:documentation>(Autre) Établissement public administratif local</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7381">
				<xs:annotation>
					<xs:documentation>Organisme consulaire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7382">
				<xs:annotation>
					<xs:documentation>Établissement public national ayant fonction d'administration centrale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7383">
				<xs:annotation>
					<xs:documentation>Établissement public national à caractère scientifique culturel et professionnel</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7384">
				<xs:annotation>
					<xs:documentation>Autre établissement public national d'enseignement</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7385">
				<xs:annotation>
					<xs:documentation>Autre établissement public national administratif à compétence territoriale limitée</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7389">
				<xs:annotation>
					<xs:documentation>Établissement public national à caractère administratif</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7410">
				<xs:annotation>
					<xs:documentation>Groupement d'intérêt public (GIP)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7430">
				<xs:annotation>
					<xs:documentation>Établissement public des cultes d'Alsace-Lorraine</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7450">
				<xs:annotation>
					<xs:documentation>Cercle et foyer dans les armées</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="7470"/>
			<xs:enumeration value="7490">
				<xs:annotation>
					<xs:documentation>Autre personne morale de droit administratif</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8110">
				<xs:annotation>
					<xs:documentation>Régime général de la sécurité sociale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8120">
				<xs:annotation>
					<xs:documentation>Régime spécial de sécurité sociale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8130">
				<xs:annotation>
					<xs:documentation>Institution de retraite complémentaire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8140">
				<xs:annotation>
					<xs:documentation>Mutualité sociale agricole</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8150">
				<xs:annotation>
					<xs:documentation>Régime maladie des non-salariés non agricoles</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8160">
				<xs:annotation>
					<xs:documentation>Régime vieillesse ne dépendant pas du régime général de la sécurité sociale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8170">
				<xs:annotation>
					<xs:documentation>Régime d'assurance chômage</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8190">
				<xs:annotation>
					<xs:documentation>Autre régime de prévoyance sociale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8210">
				<xs:annotation>
					<xs:documentation>Mutuelle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8250">
				<xs:annotation>
					<xs:documentation>Assurance mutuelle agricole</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8290">
				<xs:annotation>
					<xs:documentation>Autre organisme mutualiste</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8310">
				<xs:annotation>
					<xs:documentation>Comité central d'entreprise</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8311">
				<xs:annotation>
					<xs:documentation>Comité d'établissement</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8410">
				<xs:annotation>
					<xs:documentation>Syndicat de salariés</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8420">
				<xs:annotation>
					<xs:documentation>Syndicat patronal</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8450">
				<xs:annotation>
					<xs:documentation>Ordre professionnel ou assimilé</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8470">
				<xs:annotation>
					<xs:documentation>Centre technique industriel ou comité professionnel du développement économique</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="8490">
				<xs:annotation>
					<xs:documentation>Autre organisme professionnel</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="9110">
				<xs:annotation>
					<xs:documentation>Syndicat de copropriété</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="9150">
				<xs:annotation>
					<xs:documentation>Association syndicale libre</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="9210">
				<xs:annotation>
					<xs:documentation>Association non déclarée</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="9220">
				<xs:annotation>
					<xs:documentation>Association déclarée</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="9221">
				<xs:annotation>
					<xs:documentation>Association déclarée "entreprises d'insertion par l'économique"</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="9222">
				<xs:annotation>
					<xs:documentation>Association intermédiaire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="9223">
				<xs:annotation>
					<xs:documentation>Groupement d'employeurs</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="9230">
				<xs:annotation>
					<xs:documentation>Association déclarée reconnue d'utilité publique</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="9240">
				<xs:annotation>
					<xs:documentation>Congrégation</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="9260">
				<xs:annotation>
					<xs:documentation>Association de droit local</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="9300">
				<xs:annotation>
					<xs:documentation>Fondation</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="9900">
				<xs:annotation>
					<xs:documentation>Autre personne morale de droit privé</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="9970"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Civilite">
		<xs:restriction base="xs:string">
			<xs:enumeration value="madame"/>
			<xs:enumeration value="mademoiselle"/>
			<xs:enumeration value="monsieur"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Lettre_Enliassement">
		<xs:restriction base="xs:string">
			<xs:enumeration value="P"/>
			<xs:enumeration value="R"/>
			<xs:enumeration value="S"/>
			<xs:enumeration value="D"/>
			<xs:enumeration value="V"/>
			<xs:enumeration value="J"/>
			<xs:enumeration value="U"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Nature_Formalite_Avenant">
		<xs:restriction base="xs:string">
			<xs:enumeration value="AVHC">
				<xs:annotation>
					<xs:documentation>Avenant à l'inscription d'une hypothèque conventionnelle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AVPD">
				<xs:annotation>
					<xs:documentation>Avenant à l'inscription du privilège de prêteur de deniers</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Nature_Formalite_Attestation_Apres_Deces">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ATTE">
				<xs:annotation>
					<xs:documentation>Avenant à l'inscription d'une hypothèque conventionnelle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Nature_Formalite_Convention_Rechargement">
		<xs:restriction base="xs:string">
			<xs:enumeration value="CONR">
				<xs:annotation>
					<xs:documentation>Convention de rechargement</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Nature-Formalite-Depot-Acte">
		<xs:restriction base="xs:string">
			<xs:enumeration value="VENT">
				<xs:annotation>
					<xs:documentation>Vente</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VIMR">
				<xs:annotation>
					<xs:documentation>Immeuble rural</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VHAB">
				<xs:annotation>
					<xs:documentation>Immeuble d'habitation</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VEFA">
				<xs:annotation>
					<xs:documentation>Vente en état futur d'achèvement</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VTAB">
				<xs:annotation>
					<xs:documentation>Vente de terrain à bâtir</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Nature_Formalite_Inscription">
		<xs:restriction base="xs:string">
			<xs:enumeration value="HCON">
				<xs:annotation>
					<xs:documentation>hypothèque conventionnelle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PRPD">
				<xs:annotation>
					<xs:documentation>privilège de prêteur de deniers</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PVEN">
				<xs:annotation>
					<xs:documentation>privilège du vendeur</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PRIV">
				<xs:annotation>
					<xs:documentation>autres privilèges</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="HCOR">
				<xs:annotation>
					<xs:documentation>hypothèque conventionnelle rechargeable</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="HLES">
				<xs:annotation>
					<xs:documentation>hypothèque légale spéciale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Nature_Formalite_Radiation">
		<xs:restriction base="xs:string">
			<xs:enumeration value="RAPA">
				<xs:annotation>
					<xs:documentation>radiation partielle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RATO">
				<xs:annotation>
					<xs:documentation>radiation totale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RAPS">
				<xs:annotation>
					<xs:documentation>radiation partielle de saisie</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RADS">
				<xs:annotation>
					<xs:documentation>radiation totale de saisie</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Nature_Formalite_Radiation_Simplifiee">
		<xs:restriction base="xs:string">
			<xs:enumeration value="RASP">
				<xs:annotation>
					<xs:documentation>Radiation simplifiée partielle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RAST">
				<xs:annotation>
					<xs:documentation>Radiation simplifiée totale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Nature_Formalite_Regularisation">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ATTR"/>
			<xs:enumeration value="ACTR"/>
			<xs:enumeration value="BORR"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Nature_Formalite_Renouvellement">
		<xs:restriction base="xs:string">
			<xs:enumeration value="RINS">
				<xs:annotation>
					<xs:documentation>Renouvellement</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Nature_Formalite_Servitude">
		<xs:restriction base="xs:string">
			<xs:enumeration value="SCOC"/>
			<xs:enumeration value="PRDI"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Nature_Immeuble">
		<xs:restriction base="xs:string">
			<xs:enumeration value="parcelle avec EDD"/>
			<xs:enumeration value="parcelle sans EDD"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Regime_Matrimonial">
		<xs:restriction base="xs:string">
			<xs:enumeration value="11">
				<xs:annotation>
					<xs:documentation>Cté légale réduite aux acquêts (AC du 01/02/66)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="12">
				<xs:annotation>
					<xs:documentation>Cté légale meubles et acquêts (jusqu'au 31/01/66)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="13">
				<xs:annotation>
					<xs:documentation>Cté universelle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="14">
				<xs:annotation>
					<xs:documentation>Régime de séparation de biens</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="15">
				<xs:annotation>
					<xs:documentation>Rég. de participation aux acquêts</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="16">
				<xs:annotation>
					<xs:documentation>Séparation de biens avec société aux acquêts</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="17">
				<xs:annotation>
					<xs:documentation>Cté conventionnelle meubles et acquêts</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="18">
				<xs:annotation>
					<xs:documentation>Cté conventionnelle réduite aux acquêts</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="19">
				<xs:annotation>
					<xs:documentation>Pacte civil de solidarité</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="20">
				<xs:annotation>
					<xs:documentation>Divorce</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="21">
				<xs:annotation>
					<xs:documentation>Autre</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Situation_RCS">
		<xs:restriction base="xs:string">
			<xs:enumeration value="I">
				<xs:annotation>
					<xs:documentation>Immatriculé</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="C">
				<xs:annotation>
					<xs:documentation>En cours immatriculation</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="N">
				<xs:annotation>
					<xs:documentation>Non immatriculé</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Type_Disposition">
		<xs:restriction base="xs:string">
			<xs:enumeration value="Servitude">
				<xs:annotation>
					<xs:documentation>Avenant à l'inscription d'une hypothèque conventionnelle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Attestation immobilière après décès">
				<xs:annotation>
					<xs:documentation>Avenant à l'inscription du privilège de prêteur de deniers</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Vente"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Type_Voies">
		<xs:restriction base="xs:string">
			<xs:enumeration value="ACH">
				<xs:annotation>
					<xs:documentation>ANCIEN CHEMIN</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AER">
				<xs:annotation>
					<xs:documentation>AERODROME</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AERG">
				<xs:annotation>
					<xs:documentation>AEROGARE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AGL">
				<xs:annotation>
					<xs:documentation>AGGLOMERATION </xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AIRE">
				<xs:annotation>
					<xs:documentation>AIRE </xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ALL">
				<xs:annotation>
					<xs:documentation>Allée</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ANGL">
				<xs:annotation>
					<xs:documentation>ANGLE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ARC">
				<xs:annotation>
					<xs:documentation>ARCADE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ART">
				<xs:annotation>
					<xs:documentation>ANCIENNE ROUTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AUT">
				<xs:annotation>
					<xs:documentation>AUTOROUTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AV">
				<xs:annotation>
					<xs:documentation>Avenue</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="BASE">
				<xs:annotation>
					<xs:documentation>BASE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="BD">
				<xs:annotation>
					<xs:documentation>Boulevard</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="BER">
				<xs:annotation>
					<xs:documentation>BERGE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="BORD">
				<xs:annotation>
					<xs:documentation>BORD</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="BRE">
				<xs:annotation>
					<xs:documentation>BARRIERE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="BRG">
				<xs:annotation>
					<xs:documentation>BOURG</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="BRTL">
				<xs:annotation>
					<xs:documentation>BRETELLE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="BSN">
				<xs:annotation>
					<xs:documentation>BASSIN</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CAE">
				<xs:annotation>
					<xs:documentation>CARRIERA</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CALL">
				<xs:annotation>
					<xs:documentation>CALLE, CALLADA</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CAMI">
				<xs:annotation>
					<xs:documentation>CAMIN</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CAMP">
				<xs:annotation>
					<xs:documentation>CAMP</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CAN">
				<xs:annotation>
					<xs:documentation>CANAL</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CAR">
				<xs:annotation>
					<xs:documentation>CARREFOUR</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CARE">
				<xs:annotation>
					<xs:documentation>CARRIERE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CASR">
				<xs:annotation>
					<xs:documentation>CASERNE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CC">
				<xs:annotation>
					<xs:documentation>CHEMIN COMMUNAL</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CCAL">
				<xs:annotation>
					<xs:documentation>Centre commercial</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CD">
				<xs:annotation>
					<xs:documentation>CHEMIN DEPARTEMENTAL</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CF">
				<xs:annotation>
					<xs:documentation>CHEMIN FORESTIER</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CHA">
				<xs:annotation>
					<xs:documentation>CHASSE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CHE">
				<xs:annotation>
					<xs:documentation>CHEMIN</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CHEM">
				<xs:annotation>
					<xs:documentation>CHEMINEMENT</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CHL">
				<xs:annotation>
					<xs:documentation>CHALET</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CHP">
				<xs:annotation>
					<xs:documentation>CHAMP</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CHS">
				<xs:annotation>
					<xs:documentation>CHAUSSEE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CHT">
				<xs:annotation>
					<xs:documentation>CHATEAU</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CHV">
				<xs:annotation>
					<xs:documentation>CHEMIN VISCINAL</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CITE">
				<xs:annotation>
					<xs:documentation>CITE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CIVE">
				<xs:annotation>
					<xs:documentation>COURSIVE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CLOS">
				<xs:annotation>
					<xs:documentation>CLOS</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CLR">
				<xs:annotation>
					<xs:documentation>COULOIR</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="COIN">
				<xs:annotation>
					<xs:documentation>COIN</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="COL">
				<xs:annotation>
					<xs:documentation>COL</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="COR">
				<xs:annotation>
					<xs:documentation>CORNICHE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CORO">
				<xs:annotation>
					<xs:documentation>CORON</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="COTE">
				<xs:annotation>
					<xs:documentation>COTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="COUR">
				<xs:annotation>
					<xs:documentation>COUR</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CPG">
				<xs:annotation>
					<xs:documentation>CAMPING</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CR">
				<xs:annotation>
					<xs:documentation>CHEMIN RURAL</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CRS">
				<xs:annotation>
					<xs:documentation>COURS</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CRX">
				<xs:annotation>
					<xs:documentation>CROIX</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CTR">
				<xs:annotation>
					<xs:documentation>CONTOUR</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CTRE">
				<xs:annotation>
					<xs:documentation>CENTRE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="DARS">
				<xs:annotation>
					<xs:documentation>DARSE, DARCE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="DEVI">
				<xs:annotation>
					<xs:documentation>DEVIATION</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="DIG">
				<xs:annotation>
					<xs:documentation>DIGUE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="DOM">
				<xs:annotation>
					<xs:documentation>DOMAINE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="DRA">
				<xs:annotation>
					<xs:documentation>DRAILLE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="DSC">
				<xs:annotation>
					<xs:documentation>DESCENTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ECA">
				<xs:annotation>
					<xs:documentation>ECART</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ECL">
				<xs:annotation>
					<xs:documentation>ECLUSE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="EMBR">
				<xs:annotation>
					<xs:documentation>EMBRANCHEMENT</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="EMP">
				<xs:annotation>
					<xs:documentation>EMPLACEMENT</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ENC">
				<xs:annotation>
					<xs:documentation>ENCLOS</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ENV">
				<xs:annotation>
					<xs:documentation>ENCLAVE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ESC">
				<xs:annotation>
					<xs:documentation>ESCALIER</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ESP">
				<xs:annotation>
					<xs:documentation>ESPLANADE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ESPA">
				<xs:annotation>
					<xs:documentation>ESPACE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ETNG">
				<xs:annotation>
					<xs:documentation>ETANG</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FD">
				<xs:annotation>
					<xs:documentation>FOND</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FG">
				<xs:annotation>
					<xs:documentation>FAUBOURG</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FON">
				<xs:annotation>
					<xs:documentation>FONTAINE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FOR">
				<xs:annotation>
					<xs:documentation>FORET</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FORT">
				<xs:annotation>
					<xs:documentation>FORT</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FOS">
				<xs:annotation>
					<xs:documentation>FOSSE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FRM">
				<xs:annotation>
					<xs:documentation>FERME</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="GAL">
				<xs:annotation>
					<xs:documentation>GALERIE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="GARE">
				<xs:annotation>
					<xs:documentation>GARE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="GBD">
				<xs:annotation>
					<xs:documentation>GRAND BOULEVARD</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="GPL">
				<xs:annotation>
					<xs:documentation>GRANDE PLACE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="GR">
				<xs:annotation>
					<xs:documentation>GRANDE RUE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="GREV">
				<xs:annotation>
					<xs:documentation>GREVE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="HAB">
				<xs:annotation>
					<xs:documentation>HABITATION</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="HAM">
				<xs:annotation>
					<xs:documentation>HAMEAU</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="HIP">
				<xs:annotation>
					<xs:documentation>HIPPODROME</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="HLE">
				<xs:annotation>
					<xs:documentation>HALLE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="HLG">
				<xs:annotation>
					<xs:documentation>HALAGE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="HLM">
				<xs:annotation>
					<xs:documentation>HLM</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="HTR">
				<xs:annotation>
					<xs:documentation>HAUTEUR</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ILE">
				<xs:annotation>
					<xs:documentation>ILE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ILOT">
				<xs:annotation>
					<xs:documentation>ILOT</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="IMM">
				<xs:annotation>
					<xs:documentation>Immeuble(s)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="IMP">
				<xs:annotation>
					<xs:documentation>Impasse</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="JARD">
				<xs:annotation>
					<xs:documentation>JARDIN</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="JTE">
				<xs:annotation>
					<xs:documentation>JETEE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="LAC">
				<xs:annotation>
					<xs:documentation>LAC</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="LD">
				<xs:annotation>
					<xs:documentation>Lieu-dit</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="LEVE">
				<xs:annotation>
					<xs:documentation>LEVEE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="LICE">
				<xs:annotation>
					<xs:documentation>LICES</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="LIGN">
				<xs:annotation>
					<xs:documentation>LIGNE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="LOT">
				<xs:annotation>
					<xs:documentation>Lotissement</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="MAIL">
				<xs:annotation>
					<xs:documentation>MAIL</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="MAIS">
				<xs:annotation>
					<xs:documentation>MAISON</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="MAR">
				<xs:annotation>
					<xs:documentation>MARCHE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="MARE">
				<xs:annotation>
					<xs:documentation>MARE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="MAS">
				<xs:annotation>
					<xs:documentation>MAS</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="MNE">
				<xs:annotation>
					<xs:documentation>MORNE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="MRN">
				<xs:annotation>
					<xs:documentation>MARINA</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="MTE">
				<xs:annotation>
					<xs:documentation>MONTEE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="NTE">
				<xs:annotation>
					<xs:documentation>NOUVELLE ROUTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PAE">
				<xs:annotation>
					<xs:documentation>PETITE AVENUE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PARC">
				<xs:annotation>
					<xs:documentation>PARC</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PAS">
				<xs:annotation>
					<xs:documentation>Passage</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PCH">
				<xs:annotation>
					<xs:documentation>PETIT CHEMIN</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PCHE">
				<xs:annotation>
					<xs:documentation>PORCHE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PHAR">
				<xs:annotation>
					<xs:documentation>PHARE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PIST">
				<xs:annotation>
					<xs:documentation>PISTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PKG">
				<xs:annotation>
					<xs:documentation>PARKING</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PL">
				<xs:annotation>
					<xs:documentation>Place</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PLA">
				<xs:annotation>
					<xs:documentation>PLACA</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PLAG">
				<xs:annotation>
					<xs:documentation>PLAGE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PLAN">
				<xs:annotation>
					<xs:documentation>PLAN</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PLCI">
				<xs:annotation>
					<xs:documentation>PLACIS</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PLE">
				<xs:annotation>
					<xs:documentation>PASSERELLE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PLN">
				<xs:annotation>
					<xs:documentation>PLAINE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PLT">
				<xs:annotation>
					<xs:documentation>PLATEAU</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PNT">
				<xs:annotation>
					<xs:documentation>POINTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PONT">
				<xs:annotation>
					<xs:documentation>PONT</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PORT">
				<xs:annotation>
					<xs:documentation>PORT</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="POST">
				<xs:annotation>
					<xs:documentation>POSTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="POT">
				<xs:annotation>
					<xs:documentation>POTERNE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PROM">
				<xs:annotation>
					<xs:documentation>PROMENADE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PRT">
				<xs:annotation>
					<xs:documentation>PETITE ROUTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PRV">
				<xs:annotation>
					<xs:documentation>PARVIS</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PTA">
				<xs:annotation>
					<xs:documentation>PETITE ALLEE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PTE">
				<xs:annotation>
					<xs:documentation>PORTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PTR">
				<xs:annotation>
					<xs:documentation>PETITE RUE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PTTE">
				<xs:annotation>
					<xs:documentation>PLACETTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="QUA">
				<xs:annotation>
					<xs:documentation>QUARTIER</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="QUAI">
				<xs:annotation>
					<xs:documentation>QUAI</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RAC">
				<xs:annotation>
					<xs:documentation>RACCOURCI</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="REM">
				<xs:annotation>
					<xs:documentation>REMPART</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RES">
				<xs:annotation>
					<xs:documentation>Résidence</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RIVE">
				<xs:annotation>
					<xs:documentation>RIVE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RLE">
				<xs:annotation>
					<xs:documentation>RUELLE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ROC">
				<xs:annotation>
					<xs:documentation>ROCADE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RPE">
				<xs:annotation>
					<xs:documentation>RAMPE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RPT">
				<xs:annotation>
					<xs:documentation>Rond-point</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RTD">
				<xs:annotation>
					<xs:documentation>ROTONDE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RTE">
				<xs:annotation>
					<xs:documentation>Route</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RUE">
				<xs:annotation>
					<xs:documentation>RUE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RUET">
				<xs:annotation>
					<xs:documentation>RUETTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RUIS">
				<xs:annotation>
					<xs:documentation>RUISSEAU</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RULT">
				<xs:annotation>
					<xs:documentation>RUELLETTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RVE">
				<xs:annotation>
					<xs:documentation>RAVINE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="SAS">
				<xs:annotation>
					<xs:documentation>SAS</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="SEN">
				<xs:annotation>
					<xs:documentation>SENTIER, SENTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="SQ">
				<xs:annotation>
					<xs:documentation>Square</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="STDE">
				<xs:annotation>
					<xs:documentation>STADE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="TER">
				<xs:annotation>
					<xs:documentation>TERRE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="TOUR">
				<xs:annotation>
					<xs:documentation>TOUR</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="TPL">
				<xs:annotation>
					<xs:documentation>TERRE PLEIN</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="TRA">
				<xs:annotation>
					<xs:documentation>TRAVERSE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="TRAB">
				<xs:annotation>
					<xs:documentation>TRABOULE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="TRN">
				<xs:annotation>
					<xs:documentation>TERRAIN</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="TRT">
				<xs:annotation>
					<xs:documentation>TERTRE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="TSSE">
				<xs:annotation>
					<xs:documentation>TERRASSE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="TUN">
				<xs:annotation>
					<xs:documentation>TUNNEL</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VAL">
				<xs:annotation>
					<xs:documentation>VAL</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VALL">
				<xs:annotation>
					<xs:documentation>VALLON, VALLEE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VC">
				<xs:annotation>
					<xs:documentation>VOIE COMMUNALE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VCHE">
				<xs:annotation>
					<xs:documentation>VIEUX CHEMIN</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VEN">
				<xs:annotation>
					<xs:documentation>VENELLE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VIA">
				<xs:annotation>
					<xs:documentation>VIA</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VIAD">
				<xs:annotation>
					<xs:documentation>VIADUC</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VIL">
				<xs:annotation>
					<xs:documentation>VILLE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VGE">
				<xs:annotation>
					<xs:documentation>Village</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VLA">
				<xs:annotation>
					<xs:documentation>VILLA</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VOIE">
				<xs:annotation>
					<xs:documentation>VOIE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VOIR">
				<xs:annotation>
					<xs:documentation>VOIRIE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VOUT">
				<xs:annotation>
					<xs:documentation>VOUTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VOY">
				<xs:annotation>
					<xs:documentation>VOYEUL</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="VTE">
				<xs:annotation>
					<xs:documentation>VIEILLE ROUTE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ZA">
				<xs:annotation>
					<xs:documentation>Zone d'activité</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ZAC">
				<xs:annotation>
					<xs:documentation>Zone d'aménagement concerté</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ZAD">
				<xs:annotation>
					<xs:documentation>Zone d'aménagement différé</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ZI">
				<xs:annotation>
					<xs:documentation>Zone industrielle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ZONE">
				<xs:annotation>
					<xs:documentation>ZONE</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ZUP">
				<xs:annotation>
					<xs:documentation>ZUP</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="AEP">
				<xs:annotation>
					<xs:documentation> Aéroport</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="BAT">
				<xs:annotation>
					<xs:documentation>Bâtiment</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="BOIS">
				<xs:annotation>
					<xs:documentation> Bois</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="BOUC">
				<xs:annotation>
					<xs:documentation> Boucle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CA">
				<xs:annotation>
					<xs:documentation> Carre</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CERC">
				<xs:annotation>
					<xs:documentation> Cercle</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="CLO">
				<xs:annotation>
					<xs:documentation> Cloître</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="COMM">
				<xs:annotation>
					<xs:documentation> ch Communal</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="COTT">
				<xs:annotation>
					<xs:documentation> Cottage</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="GAV">
				<xs:annotation>
					<xs:documentation> Grande Avenue</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="GPE">
				<xs:annotation>
					<xs:documentation> Groupe</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="MLN">
				<xs:annotation>
					<xs:documentation> Moulin</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PAL">
				<xs:annotation>
					<xs:documentation> Palais</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PAV">
				<xs:annotation>
					<xs:documentation> Pavillon</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PRA">
				<xs:annotation>
					<xs:documentation> Prairie</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PRE">
				<xs:annotation>
					<xs:documentation> Pré</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PVE">
				<xs:annotation>
					<xs:documentation> Pavée</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RD">
				<xs:annotation>
					<xs:documentation> Route départementale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="RN">
				<xs:annotation>
					<xs:documentation> Route nationale</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="SENT">
				<xs:annotation>
					<xs:documentation> Sentier</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="GD">
				<xs:annotation>
					<xs:documentation> Grand</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="STA">
				<xs:annotation>
					<xs:documentation> Station</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="HT">
				<xs:annotation>
					<xs:documentation> Haut</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PT">
				<xs:annotation>
					<xs:documentation> Petit</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="ROND">
				<xs:annotation>
					<xs:documentation> Rond</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Modalites_Paiement">
		<xs:restriction base="xs:string">
			<xs:enumeration value="1">
				<xs:annotation>
					<xs:documentation>Hors la vue du notaire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="2">
				<xs:annotation>
					<xs:documentation>Rente viagère</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="3">
				<xs:annotation>
					<xs:documentation>Comptant</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="4">
				<xs:annotation>
					<xs:documentation>Par prêt</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="5">
				<xs:annotation>
					<xs:documentation>Inconnu ou indéterminé</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="6"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Qualite-D-B">
		<xs:restriction base="xs:string">
			<xs:enumeration value="D">
				<xs:annotation>
					<xs:documentation>Disposant (vendeur)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="B">
				<xs:annotation>
					<xs:documentation>Bénéficiaire (acquéreur)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Qualite-D-B-X">
		<xs:restriction base="xs:string">
			<xs:enumeration value="D">
				<xs:annotation>
					<xs:documentation>Disposant (vendeur)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="B">
				<xs:annotation>
					<xs:documentation>Bénéficiaire (acquéreur)</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="X"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Qualite-T-C-A-Y">
		<xs:restriction base="xs:string">
			<xs:enumeration value="T">
				<xs:annotation>
					<xs:documentation>débiteur</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="C">
				<xs:annotation>
					<xs:documentation>caution</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="A">
				<xs:annotation>
					<xs:documentation>créancier</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Y">
				<xs:annotation>
					<xs:documentation>débiteur et caution</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Qualite-D-B-T-C-A-Y">
		<xs:restriction base="xs:string">
			<xs:enumeration value="D"/>
			<xs:enumeration value="B"/>
			<xs:enumeration value="T">
				<xs:annotation>
					<xs:documentation>débiteur</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="C">
				<xs:annotation>
					<xs:documentation>caution</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="A">
				<xs:annotation>
					<xs:documentation>créancier</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="Y">
				<xs:annotation>
					<xs:documentation>débiteur et caution</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Qualite-T-A">
		<xs:restriction base="xs:string">
			<xs:enumeration value="T">
				<xs:annotation>
					<xs:documentation>débiteur</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="A">
				<xs:annotation>
					<xs:documentation>créancier</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Type_Droit">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NI">
				<xs:annotation>
					<xs:documentation>Nue-propriété en indivision</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="NP">
				<xs:annotation>
					<xs:documentation>Nue-propriété</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="PI">
				<xs:annotation>
					<xs:documentation>Indivision en pleine propriété</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="TP">
				<xs:annotation>
					<xs:documentation>Toute propriété</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="UH">
				<xs:annotation>
					<xs:documentation>Droit d'usage et d'habitation</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="UI">
				<xs:annotation>
					<xs:documentation>Usufruit en indivision</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="US">
				<xs:annotation>
					<xs:documentation>Usufruit</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Type_Usager">
		<xs:restriction base="xs:string">
			<xs:enumeration value="NOT">
				<xs:annotation>
					<xs:documentation>Notaire</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="SCP"/>
			<xs:enumeration value="AUT"/>
			<xs:enumeration value="SEL"/>
			<xs:enumeration value="SELARL"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:simpleType name="Enum_Fond">
		<xs:restriction base="xs:string">
			<xs:enumeration value="FS">
				<xs:annotation>
					<xs:documentation>Fonds servants</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FD">
				<xs:annotation>
					<xs:documentation>Fonds dominants</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
			<xs:enumeration value="FDS">
				<xs:annotation>
					<xs:documentation>Fonds dominants et servants</xs:documentation>
				</xs:annotation>
			</xs:enumeration>
		</xs:restriction>
	</xs:simpleType>
	<!--************************* TYPES COMPLEXES ET ELEMENTS ********-->
	<xs:complexType name="Action_CH_Type">
		<xs:sequence>
			<xs:element name="type-action" type="Enum_Action_CH"/>
			<xs:element name="numero-action" type="Num2"/>
			<xs:element name="reference-action" type="XSString1-30"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Adresse_Type">
		<xs:attributeGroup ref="Adresse_Type.att"/>
	</xs:complexType>
	<xs:attributeGroup name="Adresse_Type.att">
		<xs:attribute name="ligne2" type="String-1-200" use="optional"/>
		<xs:attribute name="ligne3" type="String-1-200" use="optional"/>
		<xs:attribute name="bp" type="Numerique5" use="optional"/>
		<xs:attribute name="voie-numero" type="Numerique4" use="optional"/>
		<xs:attribute name="voie-extension" use="optional">
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:pattern value="[A-Za-z0-9]"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="voie-type" type="Enum_Type_Voies" use="optional"/>
		<xs:attribute name="voie" type="String-1-200" use="optional"/>
		<xs:attribute name="lieu-dit" type="String-1-200" use="optional"/>
		<xs:attribute name="code-postal" type="Numerique5" use="optional"/>
		<xs:attribute name="localite" type="String-1-200" use="required"/>
		<xs:attribute name="libelle-pays" type="String-1-200" use="required"/>
		<xs:attribute name="code-pays" type="Numerique5" use="optional"/>
	</xs:attributeGroup>
	<xs:complexType name="Adresse_Usager_Type">
		<xs:attributeGroup ref="Adresse_Usager_Type.att"/>
	</xs:complexType>
	<xs:attributeGroup name="Adresse_Usager_Type.att">
		<xs:attribute name="residence-batiment" type="String-1-200"/>
		<xs:attribute name="numero-libelle" type="String-1-200"/>
		<xs:attribute name="lieu-dit" type="String-1-200"/>
		<xs:attribute name="code-postal" type="Numerique5" use="required"/>
		<xs:attribute name="commune" type="String-1-200" use="required"/>
		<xs:attribute name="lieu-residence" type="String-1-200"/>
		<xs:attribute name="telephone" type="Numtel10-10"/>
		<xs:attribute name="fax" type="Numtel10-10"/>
		<xs:attribute name="info-mel" type="xs:boolean" use="required"/>
		<xs:attribute name="mel">
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:minLength value="1"/>
					<xs:maxLength value="100"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<xs:complexType name="Article_CGI_Type">
		<xs:sequence>
			<xs:element name="numero" type="Numerique5"/>
			<xs:element name="libelle" type="String-1-200"/>
			<xs:element name="niveau1" type="XSString1-30" minOccurs="0"/>
			<xs:element name="niveau2" type="XSString1-30" minOccurs="0"/>
			<xs:element name="niveau3" type="XSString1-30" minOccurs="0"/>
			<xs:element name="niveau4" type="XSString1-30" minOccurs="0"/>
			<xs:element name="niveau5" type="XSString1-30" minOccurs="0"/>
			<xs:element name="niveau6" type="XSString1-30" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Autre_Formalite_Publiee_Type">
		<xs:sequence>
			<xs:element name="reference-autre-formalite" type="Reference_Formalite_Type" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="Autre_Formalite_Publiee_Type.att"/>
	</xs:complexType>
	<xs:attributeGroup name="Autre_Formalite_Publiee_Type.att">
		<xs:attribute name="code-ch-depot" type="Code_CH_Type" use="optional"/>
	</xs:attributeGroup>
	<xs:complexType name="Disposant_Beneficiaire_Type">
		<xs:sequence>
			<xs:element name="disposant" type="String-1-200"/>
			<xs:element name="beneficiaire" type="String-1-200"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Disposant_BeneficiaireN_Type">
		<xs:sequence>
			<xs:element name="disposant" type="String-1-200"/>
			<xs:element name="beneficiaire" type="String-1-200" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Droits_Type">
		<xs:sequence>
			<xs:element name="article-cgi" type="Article_CGI_Type"/>
			<xs:element name="code-departement" type="Code_Departement_Type"/>
			<xs:element name="code-commune" type="Code_Commune_TOPAD_Type"/>
			<xs:element name="base-taxable" type="Decimal11-2"/>
			<xs:element name="base-taxable2" type="Decimal11-2" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Etat_Civil_Type">
		<xs:sequence>
			<xs:element name="prenom" type="Prenom_Etat_Civil_Type" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attributeGroup ref="Etat_Civil_Type.att"/>
	</xs:complexType>
	<xs:attributeGroup name="Etat_Civil_Type.att">
		<xs:attribute name="civilite" type="Enum_Civilite" use="required"/>
		<xs:attribute name="nom" type="String-1-200" use="required"/>
		<xs:attribute name="date-naissance" type="Date_Naissance_Type" use="required"/>
		<xs:attribute name="libelle-commune-naissance" type="String-1-200" use="required"/>
		<xs:attribute name="code-commune-naissance" type="Code_Commune_Type"/>
		<xs:attribute name="departement-naissance" type="Code_Departement_Type"/>
		<xs:attribute name="libelle-pays-naissance" type="String-1-200" use="required"/>
	</xs:attributeGroup>
	<xs:complexType name="Effet_Relatif_Type">
		<xs:sequence>
			<xs:element name="reference" type="Reference_Formalite_Type" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="Effet_Relatif_Type.att"/>
	</xs:complexType>
	<xs:attributeGroup name="Effet_Relatif_Type.att">
		<xs:attribute name="code-ch-depot" type="Code_CH_Type" use="optional"/>
	</xs:attributeGroup>
	<xs:complexType name="Extrait_M1">
		<xs:sequence>
			<xs:element name="numero">
				<xs:simpleType>
					<xs:restriction base="Chaine_Alphanumerique">
						<xs:minLength value="1"/>
						<xs:maxLength value="12"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="date" type="xs:date"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Formalite_Type">
		<xs:sequence>
			<xs:element name="code-ch-depot" type="Code_CH_Type"/>
			<xs:element name="reference-inscription-initiale" type="Reference-Formalite-Type1ou2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Formalite_CH_Optional_Type">
		<xs:sequence>
			<xs:element name="code-ch-depot" type="Code_CH_Type" minOccurs="0"/>
			<xs:element name="reference-inscription-initiale" type="Reference-Formalite-Type1ou2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Formalite_Initiale_Type">
		<xs:sequence>
			<xs:element name="code-ch-depot" type="Code_CH_Type" minOccurs="0"/>
			<xs:element name="reference-inscription-initiale" type="Reference_Formalite_Type"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Formalite_Renouv_Type">
		<xs:sequence>
			<xs:element name="code-ch-depot" type="Code_CH_Type"/>
			<xs:element name="reference-renouvellement-inscription" type="Reference-Formalite-Type1ou2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Formalite_Renouv_CH_Optional_Type">
		<xs:sequence>
			<xs:element name="code-ch-depot" type="Code_CH_Type" minOccurs="0"/>
			<xs:element name="reference-renouvellement-inscription" type="Reference-Formalite-Type1ou2"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Formalite_Renouvouvellement_Type">
		<xs:sequence>
			<xs:element name="code-ch-depot" type="Code_CH_Type" minOccurs="0"/>
			<xs:element name="reference-renouvellement-inscription" type="Reference_Formalite_Type"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Formalite_Et_Date_Type">
		<xs:sequence>
			<xs:element name="date-formalite" type="xs:date"/>
			<xs:element name="code-ch-depot" type="Code_CH_Type" minOccurs="0"/>
			<xs:element name="reference-formalite" type="Ref_Type1_Type"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Iban_Type">
		<xs:sequence>
			<xs:element name="zone-cpyctr">
				<xs:simpleType>
					<xs:restriction base="Chaine_Alphanumerique">
						<xs:minLength value="4"/>
						<xs:maxLength value="4"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="zone-bban" type="XSString1-30"/>
			<xs:element name="domiciliation" type="XSString1-30"/>
			<xs:element name="bic">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="zone-bic" type="Bic_Type"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Immeuble_Type">
		<xs:sequence>
			<xs:element name="description" type="XSString1-500" minOccurs="0"/>
			<xs:element name="adresse" type="Adresse_Type" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="volume" type="Volume_Teleactes_Type" maxOccurs="unbounded"/>
			<xs:element name="lot" type="Lot_Copropriete_Type" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="autre-formalite-publiee" type="Autre_Formalite_Publiee_Type" minOccurs="0" maxOccurs="unbounded"/>
			<xs:element name="effet-relatif" type="Effet_Relatif_Type" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attributeGroup ref="Immeuble_Type.att"/>
	</xs:complexType>
	<xs:attributeGroup name="Immeuble_Type.att">
		<xs:attribute name="identifiant" type="Identifiant_Type" use="required"/>
		<xs:attribute name="nature-immeuble" type="Enum_Nature_Immeuble" use="required"/>
	</xs:attributeGroup>
	<xs:complexType name="Lot_Copropriete_Type">
		<xs:attributeGroup ref="Lot_Copropriete_Type.att"/>
	</xs:complexType>
	<xs:attributeGroup name="Lot_Copropriete_Type.att">
		<xs:attribute name="numero" use="required">
			<xs:simpleType>
				<xs:restriction base="Chaine_Alphanumerique">
					<xs:minLength value="1"/>
					<xs:maxLength value="7"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="surface" type="Surface"/>
	</xs:attributeGroup>
	<xs:complexType name="Personne_Morale_Type">
		<xs:sequence>
			<xs:element name="associes" minOccurs="0">
				<xs:complexType>
					<xs:sequence>
						<xs:element name="index-associe" type="Identifiant_Type" maxOccurs="unbounded"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="adresse" type="Adresse_Type" minOccurs="0"/>
			<xs:element name="associe-siren" type="Numerique9" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="Personne_Morale_Type.att"/>
	</xs:complexType>
	<xs:attributeGroup name="Personne_Morale_Type.att">
		<xs:attribute name="raison-sociale" type="XSString1-250" use="required"/>
		<xs:attribute name="categorie-juridique" type="Enum_Categorie_Juridique" use="required"/>
		<xs:attribute name="ville-siege-social" type="String-1-200" use="optional"/>
		<xs:attribute name="ville-declaration-rcs" type="String-1-200" use="optional"/>
		<xs:attribute name="situation-rcs" type="Enum_Situation_RCS" use="required"/>
		<xs:attribute name="immatriculation" type="Numerique9" use="optional"/>
		<xs:attribute name="sigle" type="XSString1-10" use="optional"/>
		<xs:attribute name="date-depot-statuts" type="xs:date" use="optional"/>
		<xs:attribute name="lieu-existence" type="String-1-200" use="optional"/>
		<xs:attribute name="date-dissolution" type="xs:date" use="optional"/>
	</xs:attributeGroup>
	<xs:complexType name="Personne_Physique_Type">
		<xs:sequence>
			<xs:element name="etat-civil" type="Etat_Civil_Type"/>
			<xs:element name="adresse" type="Adresse_Type" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="Personne_Physique_Type.att"/>
	</xs:complexType>
	<xs:attributeGroup name="Personne_Physique_Type.att">
		<xs:attribute name="regime-matrimonial" type="Enum_Regime_Matrimonial"/>
		<xs:attribute name="nom-conjoint" type="String-1-200"/>
	</xs:attributeGroup>
	<xs:complexType name="Prenom_Etat_Civil_Type">
		<xs:attributeGroup ref="Prenom_Etat_Civil_Type.att"/>
	</xs:complexType>
	<xs:attributeGroup name="Prenom_Etat_Civil_Type.att">
		<xs:attribute name="numero" type="xs:positiveInteger" use="required"/>
		<xs:attribute name="prenom" type="String-1-200" use="required"/>
	</xs:attributeGroup>
	<xs:complexType name="Redacteur_Type">
		<xs:sequence>
			<xs:element name="nom" type="String-1-200"/>
			<xs:element name="lieu-residence" type="String-1-200"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Ref_Type1_Type">
		<xs:sequence>
			<xs:element name="code-sages" type="Code_Sages_Type" minOccurs="0"/>
			<xs:element name="annee" type="xs:gYear"/>
			<xs:element name="lettre-enliassement" type="Enum_Lettre_Enliassement"/>
			<xs:element name="numero-enliassement" type="Numerique10">
				<xs:annotation>
					<xs:documentation>Un à 10 chiffres</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Ref_Type1_Date_Type">
		<xs:sequence>
			<xs:element name="code-sages" type="Code_Sages_Type" minOccurs="0"/>
			<xs:element name="annee" type="xs:gYear"/>
			<xs:element name="lettre-enliassement">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:enumeration value="J"/>
						<xs:enumeration value="P"/>
						<xs:enumeration value="S"/>
						<xs:enumeration value="V"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="numero-enliassement" type="Numerique10">
				<xs:annotation>
					<xs:documentation>Un à 10 chiffres</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="date" type="xs:date"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Ref_Type2_Type">
		<xs:sequence>
			<xs:element name="code-sages" type="Code_Sages_Type" minOccurs="0"/>
			<xs:element name="date" type="xs:date"/>
			<xs:element name="volume" type="Numerique5">
				<xs:annotation>
					<xs:documentation>Un à cinq chiffres</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="numero" type="Numerique5">
				<xs:annotation>
					<xs:documentation>Un à cinq chiffres</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Refus_Type">
		<xs:sequence>
			<xs:element name="code-sages" type="Code_Sages_Type" minOccurs="0"/>
			<xs:element name="annee" type="xs:gYear"/>
			<xs:element name="code">
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:minLength value="1"/>
						<xs:maxLength value="1"/>
						<xs:enumeration value="U"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="sequence" type="Numerique10">
				<xs:annotation>
					<xs:documentation>Un à 10 chiffres</xs:documentation>
				</xs:annotation>
			</xs:element>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Reference_Cadastrale_Teleactes_Type">
		<xs:sequence>
			<xs:element name="ref-cadastrale-adresse-cadastre-non-renove" type="String-1-300" minOccurs="0"/>
			<xs:element name="jouissance" type="xs:boolean" minOccurs="0"/>
		</xs:sequence>
		<xs:attributeGroup ref="Reference_Cadastrale_Teleactes_Type.att"/>
	</xs:complexType>
	<xs:attributeGroup name="Reference_Cadastrale_Teleactes_Type.att">
		<xs:attribute name="code-departement" type="Code_Departement_Type" use="required"/>
		<xs:attribute name="code-commune" type="Code_Commune_TOPAD_Type" use="required"/>
		<xs:attribute name="commune" type="String-1-200" use="required">
			<xs:annotation>
				<xs:documentation>Libellé de la commune</xs:documentation>
			</xs:annotation>
		</xs:attribute>
		<xs:attribute name="quartier" type="XSString1-32"/>
		<xs:attribute name="prefixe" type="Num3"/>
		<xs:attribute name="section" use="required">
			<xs:simpleType>
				<xs:restriction base="Chaine_Alphanumerique">
					<xs:minLength value="1"/>
					<xs:maxLength value="2"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="numero" use="required">
			<xs:simpleType>
				<xs:restriction base="Num4"/>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="contenance-ha" type="Numerique7"/>
		<xs:attribute name="contenance-ares" type="Numerique2"/>
		<xs:attribute name="contenance-ca" type="Numerique2"/>
	</xs:attributeGroup>
	<xs:complexType name="Reference_Formalite_Type">
		<xs:choice>
			<xs:element name="reference-formalite-type1" type="Ref_Type1_Type"/>
			<xs:element name="reference-formalite-type2" type="Ref_Type2_Type"/>
			<xs:element name="reference-formalite-type3" type="XSString1-400" minOccurs="0" maxOccurs="unbounded"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="Reference-Formalite-Type1ou2">
		<xs:choice>
			<xs:element name="reference-formalite-type1" type="Ref_Type1_Type"/>
			<xs:element name="reference-formalite-type2" type="Ref_Type2_Type"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="Reference-Formalite-Regul-Type1ou2">
		<xs:sequence>
			<xs:choice>
				<xs:element name="reference-formalite-type1" type="Ref_Type1_Type"/>
				<xs:element name="reference-formalite-type2" type="Ref_Type2_Type"/>
			</xs:choice>
			<xs:element name="rejet-numero" minOccurs="0">
				<xs:simpleType>
					<xs:restriction base="Chaine_Alphanumerique">
						<xs:minLength value="1"/>
						<xs:maxLength value="10"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="rejet-date" type="xs:date"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Reference_Document_Type">
		<xs:sequence>
			<xs:choice>
				<xs:element name="reference-formalite-type1" type="Ref_Type1_Date_Type"/>
				<xs:element name="reference-formalite-type2" type="Ref_Type2_Type"/>
			</xs:choice>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Representant_Personne_Morale_Type">
		<xs:sequence>
			<xs:element name="raison-sociale" type="XSString1-250" minOccurs="0"/>
			<xs:element name="categorie-juridique" type="Enum_Categorie_Juridique" minOccurs="0"/>
			<xs:element name="adresse" type="Adresse_Type" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Representant_Personne_Physique_Type">
		<xs:sequence>
			<xs:element name="nom" type="String-1-200"/>
			<xs:element name="prenom" type="Prenom_Etat_Civil_Type" maxOccurs="unbounded"/>
			<xs:element name="adresse" type="Adresse_Type" minOccurs="0"/>
			<xs:element name="date-naissance" type="Date_Naissance_Type" minOccurs="0"/>
			<xs:element name="commune-naissance" type="String-1-200" minOccurs="0"/>
			<xs:element name="libelle-pays-naissance" type="String-1-200" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Rib_Bancaire_Type">
		<xs:sequence>
			<xs:element name="code-banque" type="Num5" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Cinq chiffres</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="code-guichet" type="Num5" minOccurs="0">
				<xs:annotation>
					<xs:documentation>Cinq chiffres</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="compte" minOccurs="0">
				<xs:annotation>
					<xs:documentation>13 caractères alpha</xs:documentation>
				</xs:annotation>
				<xs:simpleType>
					<xs:restriction base="xs:string">
						<xs:pattern value="\w{13}"/>
					</xs:restriction>
				</xs:simpleType>
			</xs:element>
			<xs:element name="domiciliation" type="XSString1-30" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="Usager" type="Usager_Type"/>
	<xs:complexType name="Usager_Type">
		<xs:sequence>
			<xs:element name="crpcen" type="Identifiant_Etude_Notariale_Type">
				<xs:annotation>
					<xs:documentation>
				</xs:documentation>
				</xs:annotation>
			</xs:element>
			<xs:element name="nom" type="String-1-200"/>
			<xs:element name="type-usager" type="Enum_Type_Usager"/>
			<xs:element name="perception-frais" type="xs:boolean"/>
			<xs:element name="adresse-usager" type="Adresse_Usager_Type"/>
			<xs:element name="rib-usager" type="Rib_Bancaire_Type" minOccurs="0"/>
			<xs:element name="iban-usager" type="Iban_Type"/>
		</xs:sequence>
	</xs:complexType>
	<xs:element name="Virement" type="Virement"/>
	<xs:complexType name="Virement">
		<xs:sequence>
			<xs:element name="numero" type="StringNum1-7"/>
			<xs:element name="montant" type="Decimal10-2"/>
			<xs:element name="libelle" type="XSString1-62"/>
			<xs:element name="date" type="xs:date"/>
			<xs:element name="reference-externe" type="XSString1-10"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Volume_Teleactes_Type">
		<xs:sequence>
			<xs:element name="cadastre" type="Reference_Cadastrale_Teleactes_Type" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attributeGroup ref="Volume_Teleactes_Type.att"/>
	</xs:complexType>
	<xs:attributeGroup name="Volume_Teleactes_Type.att">
		<xs:attribute name="numero">
			<xs:annotation>
				<xs:documentation>Numéro du volume</xs:documentation>
			</xs:annotation>
			<xs:simpleType>
				<xs:restriction base="Chaine_Alphanumerique">
					<xs:minLength value="1"/>
					<xs:maxLength value="7"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
	</xs:attributeGroup>
	<!--********  Défintion des tableaux, présentation et paragraphes ***************-->
	<xs:element name="br">
		<xs:annotation>
			<xs:documentation xml:lang="fr">Mise en forme : retour à la ligne dans le texte</xs:documentation>
		</xs:annotation>
		<xs:complexType mixed="false"/>
	</xs:element>
	<xs:complexType name="margeType">
		<xs:attribute name="haut" type="xs:positiveInteger" use="required"/>
		<xs:attribute name="bas" type="xs:positiveInteger" use="required"/>
		<xs:attribute name="gauche" type="xs:positiveInteger" use="required"/>
		<xs:attribute name="droite" type="xs:positiveInteger" use="required"/>
	</xs:complexType>
	<xs:element name="par" type="Paragraphe">
		<xs:annotation>
			<xs:documentation xml:lang="fr">Mise en forme : défini un paragraphe simple de texte</xs:documentation>
		</xs:annotation>
	</xs:element>
	<xs:complexType name="Paragraphe" mixed="true">
		<xs:annotation>
			<xs:documentation>Paragraphe de texte</xs:documentation>
		</xs:annotation>
		<xs:choice minOccurs="0" maxOccurs="unbounded">
			<xs:element ref="par"/>
			<xs:element name="table" type="Table"/>
			<xs:element ref="br"/>
		</xs:choice>
		<xs:attribute name="normalise" type="bool" default="non"/>
		<xs:attribute name="page" type="xs:positiveInteger" use="required"/>
		<xs:attribute name="niveau" type="xs:string" default="corps"/>
		<xs:attributeGroup ref="style.att"/>
	</xs:complexType>
	<xs:complexType name="Paragraphes" mixed="false">
		<xs:annotation>
			<xs:documentation>Ensemble de paragraphes</xs:documentation>
		</xs:annotation>
		<xs:choice minOccurs="0" maxOccurs="unbounded">
			<xs:element name="par" type="Paragraphe"/>
		</xs:choice>
	</xs:complexType>
	<xs:complexType name="Presentation">
		<xs:sequence>
			<xs:element name="marge" type="margeType"/>
			<xs:element name="style" type="styleType" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Table" mixed="false">
		<xs:sequence>
			<xs:element name="table-column" type="Table-Column" maxOccurs="unbounded"/>
			<xs:element name="table-body" type="Table-Body"/>
		</xs:sequence>
		<xs:attribute name="width" type="xs:nonNegativeInteger" use="optional"/>
		<xs:attribute name="height" type="xs:nonNegativeInteger" use="optional"/>
	</xs:complexType>
	<xs:complexType name="Table-Column" mixed="false">
		<xs:attribute name="column-width" type="xs:nonNegativeInteger" use="required"/>
	</xs:complexType>
	<xs:complexType name="Table-Body" mixed="false">
		<xs:sequence>
			<xs:element name="table-row" type="Table-Row" maxOccurs="unbounded"/>
		</xs:sequence>
	</xs:complexType>
	<xs:complexType name="Table-Row" mixed="false">
		<xs:sequence>
			<xs:element name="table-cell" type="Table-Cell" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="height" type="xs:nonNegativeInteger" use="optional"/>
	</xs:complexType>
	<xs:complexType name="Table-Cell" mixed="false">
		<xs:sequence>
			<xs:element ref="par" minOccurs="0" maxOccurs="unbounded"/>
		</xs:sequence>
		<xs:attribute name="width" type="xs:nonNegativeInteger" use="optional"/>
		<xs:attribute name="height" type="xs:nonNegativeInteger" use="optional"/>
		<xs:attribute name="number-columns-spanned" type="xs:positiveInteger" use="optional" default="1"/>
		<xs:attribute name="number-rows-spanned" type="xs:positiveInteger" use="optional" default="1"/>
	</xs:complexType>
	<xs:simpleType name="Taille_Police_Type">
		<xs:restriction base="xs:positiveInteger">
			<xs:minInclusive value="6"/>
			<xs:maxInclusive value="72"/>
		</xs:restriction>
	</xs:simpleType>
	<xs:attributeGroup name="style.att">
		<xs:attribute name="police" type="xs:string"/>
		<xs:attribute name="taille" type="Taille_Police_Type"/>
		<xs:attribute name="graisse">
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="normal"/>
					<xs:enumeration value="gras"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="italique" type="bool"/>
		<xs:attribute name="souligne" type="bool"/>
		<xs:attribute name="interligne">
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="auto"/>
					<xs:enumeration value="CDATA"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="alignement">
			<xs:simpleType>
				<xs:restriction base="xs:NMTOKEN">
					<xs:enumeration value="gauche"/>
					<xs:enumeration value="droite"/>
					<xs:enumeration value="centre"/>
					<xs:enumeration value="justifie"/>
				</xs:restriction>
			</xs:simpleType>
		</xs:attribute>
		<xs:attribute name="retrait-1ligne" type="xs:positiveInteger"/>
		<xs:attribute name="interligne-avant" type="xs:positiveInteger"/>
		<xs:attribute name="interligne-apres" type="xs:positiveInteger"/>
	</xs:attributeGroup>
	<xs:complexType name="styleType">
		<xs:sequence minOccurs="0">
			<xs:element name="marge" type="margeType"/>
		</xs:sequence>
		<xs:attribute name="niveau" type="xs:string" use="required"/>
		<xs:attributeGroup ref="style.att"/>
	</xs:complexType>
	<xs:element name="Exo-Csi" type="Exo_Csi_Type"/>
	<xs:complexType name="Exo_Csi_Type">
		<xs:annotation>
			<xs:documentation>Le composant EXO CSI est présent si et seulement si sa valeur est égale à 1. Si le composant EXO CSI est présent alors le composant VIREMENT est absent</xs:documentation>
		</xs:annotation>
		<xs:sequence>
			<xs:element name="exo-csi" type="xs:string" minOccurs="0"/>
		</xs:sequence>
	</xs:complexType>
</xs:schema>
