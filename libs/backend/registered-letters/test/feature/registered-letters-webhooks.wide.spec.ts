import { RegisteredLetterWebhooksController } from '@mynotary/backend/registered-letters/feature';
import { EmailsApiService, EmailTemplateId, RegisteredLetterEmail } from '@mynotary/backend/emails/api';
import { EventsApiService } from '@mynotary/backend/events/api';
import { LetterNotificationType, NotificationApiService } from '@mynotary/backend/notifications/api';
import { Ar24StatusDto, RegisteredLettersAr24SyncDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { RegisteredLettersUpdaterService } from '@mynotary/backend/registered-letters/core';
import { RegisteredLetterStatus } from '@mynotary/crossplatform/registered-letters/api';
import { randomUUID } from 'crypto';
import { AsyncTasksApiService, AsyncTaskType } from '@mynotary/backend/async-tasks/api';
import { provideRegisteredLettersTest } from '../index';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import { Civility } from '@mynotary/crossplatform/shared/users-core';
import { RegisteredLetterBatchEmail } from '@mynotary/backend/emails/api';
import { ContractStatus } from '@mynotary/crossplatform/legals/api';

jest.mock('@mynotary/crossplatform/shared/pdf-util', () => {
  return {
    isValidPdf: () => true
  };
});

describe(RegisteredLetterWebhooksController.name, () => {
  const matchRegisteredLetterBatchPageUrl = expect.stringMatching(
    /^http:\/\/localhost:9002\/operation\/\d+\/contrats\/recommande\/\d+$/
  );
  const matchRegisteredLetterBatchAnonymousPage = expect.stringMatching(
    /^http:\/\/localhost:9002\/recommandes\?token=[^&]+&batchId=\d+$/
  );

  it('should return early 200 without calling business logic when status is DP', async () => {
    const { client, registeredLettersUpdaterService } = await setup();
    const updaterServiceSpy = jest.spyOn(registeredLettersUpdaterService, 'updateRegisteredLetter').mockResolvedValue();

    const response = await client
      .post(`/registered-letters-sync/1`)
      .send(getAR24Body({ id_mail: randomUUID(), status: Ar24StatusDto.DP }));

    expect(response.status).toBe(200);
    expect(updaterServiceSpy).toHaveBeenCalledTimes(0);
  });

  it('should return early 200 when a letter is canceled', async () => {
    const { batch, client, member, registeredLettersUpdaterService, testingRepos } = await setup();
    const onUpdateSpy = jest.spyOn(registeredLettersUpdaterService, 'onRegisteredLetterUpdated').mockResolvedValue();

    const letter = await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      isCanceled: true,
      userId: member.userId.toString()
    });

    const response = await client
      .post(`/registered-letters-sync/${letter.id}`)
      .send(getAR24Body({ id_mail: letter.provider_letter_id, status: Ar24StatusDto.AR }));

    expect(response.status).toBe(200);
    expect(onUpdateSpy).toHaveBeenCalledTimes(0);
  });

  it('should return early 200 when a letter is bounced and the update has a SENT status', async () => {
    const { batch, client, member, registeredLettersUpdaterService, testingRepos } = await setup();
    const onUpdateSpy = jest.spyOn(registeredLettersUpdaterService, 'onRegisteredLetterUpdated').mockResolvedValue();

    const letter = await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      status: RegisteredLetterStatus.BOUNCED,
      userId: member.userId.toString()
    });

    const response = await client
      .post(`/registered-letters-sync/${letter.id}`)
      .send(getAR24Body({ id_mail: '123', status: Ar24StatusDto.EV }));

    expect(response.status).toBe(200);
    expect(onUpdateSpy).toHaveBeenCalledTimes(0);
  });

  it('should return early 200 if letter has received the same status event', async () => {
    const { batch, client, member, registeredLettersUpdaterService, testingRepos } = await setup();
    const onUpdateSpy = jest.spyOn(registeredLettersUpdaterService, 'onRegisteredLetterUpdated').mockResolvedValue();

    const letter = await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      status: RegisteredLetterStatus.SENT,
      userId: member.userId.toString()
    });

    const response = await client
      .post(`/registered-letters-sync/${letter.id}`)
      .send(getAR24Body({ id_mail: '123', status: Ar24StatusDto.EV }));

    expect(response.status).toBe(200);
    expect(onUpdateSpy).toHaveBeenCalledTimes(0);
  });

  it('should return early 200 if batch is completed', async () => {
    const { batch, client, member, registeredLettersUpdaterService, testingRepos } = await setup();
    const onUpdateSpy = jest.spyOn(registeredLettersUpdaterService, 'onRegisteredLetterUpdated').mockResolvedValue();

    await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      status: RegisteredLetterStatus.ACCEPTED,
      userId: member.userId.toString()
    });

    const letter = await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      status: RegisteredLetterStatus.REFUSED,
      userId: member.userId.toString()
    });

    const response = await client
      .post(`/registered-letters-sync/${letter.id}`)
      .send(getAR24Body({ id_mail: letter.provider_letter_id, status: Ar24StatusDto.EV }));

    expect(response.status).toBe(200);
    expect(onUpdateSpy).toHaveBeenCalledTimes(0);
  });

  it('should handle ERROR status', async () => {
    const { batch, client, contractId, member, testingRepos } = await setup();

    const uuid = randomUUID();

    const letter = await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      status: RegisteredLetterStatus.SUBMITTED,
      userId: member.userId.toString()
    });

    const response = await client
      .post(`/registered-letters-sync/${letter.id}`)
      .send(getAR24Body({ id_mail: uuid, status: Ar24StatusDto.FAIL }));

    const update = await testingRepos.letters.getLetter(letter.id.toString());
    const contract = await testingRepos.contracts.getContract(contractId);

    expect(response.status).toBe(200);
    expect(update.status).toBe(RegisteredLetterStatus.ERROR);
    expect(contract.status).toBe(ContractStatus.NOTIFICATION_ERROR);
  });

  it('should handle TO_SEND status', async () => {
    const { batch, client, createAsyncTask, member, testingRepos } = await setup();

    const uuid = randomUUID();

    const letter = await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      status: RegisteredLetterStatus.SUBMITTED,
      userId: member.userId.toString()
    });

    const response = await client
      .post(`/registered-letters-sync/${letter.id}`)
      .send(getAR24Body({ id_mail: uuid, status: Ar24StatusDto.CANCELLED_AFTER_7_DAYS }));

    const update = await testingRepos.letters.getLetter(letter.id.toString());

    expect(response.status).toBe(200);
    expect(update.status).toBe(RegisteredLetterStatus.SUBMITTED);
    expect(createAsyncTask).toHaveBeenNthCalledWith(1, {
      id: letter.id.toString(),
      type: AsyncTaskType.EVENT_SENDER
    });
  });

  it('should handle SENT status', async () => {
    const { batch, client, contractId, createNotificationSpy, member, operationId, testingRepos } = await setup();

    const uuid = randomUUID();

    const letter = await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      status: RegisteredLetterStatus.TO_SEND,
      userId: member.userId.toString()
    });

    const response = await client
      .post(`/registered-letters-sync/${letter.id}`)
      .send(getAR24Body({ id_mail: uuid, status: Ar24StatusDto.EV }));

    const update = await testingRepos.letters.getLetter(letter.id.toString());
    const contract = await testingRepos.contracts.getContract(contractId);

    expect(response.status).toBe(200);
    expect(update.status).toBe(RegisteredLetterStatus.SENT);
    expect(contract.status).toBe(ContractStatus.NOTIFICATION_PENDING);
    expect(createNotificationSpy).toHaveBeenNthCalledWith(1, {
      batchId: batch.id.toString(),
      contractId: contractId.toString(),
      creatorId: member.userId.toString(),
      creatorName: expect.any(String),
      depositTime: expect.any(String),
      letterId: letter.id.toString(),
      operationId: operationId.toString(),
      receiver: 'John Doe',
      type: LetterNotificationType.LETTER_SENT,
      userIds: [member.userId]
    });
  });

  it('should handle BOUNCED status', async () => {
    const {
      batch,
      client,
      contractId,
      createNotificationSpy,
      member,
      operationId,
      organizationId,
      sendEmailSpy,
      testingRepos
    } = await setup();

    const uuid = randomUUID();

    const letter = await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      status: RegisteredLetterStatus.SUBMITTED,
      userId: member.userId.toString()
    });

    const response = await client
      .post(`/registered-letters-sync/${letter.id}`)
      .send(getAR24Body({ id_mail: uuid, status: Ar24StatusDto.BOUNCED }));

    const update = await testingRepos.letters.getLetter(letter.id.toString());
    const contract = await testingRepos.contracts.getContract(contractId);

    expect(response.status).toBe(200);
    expect(update.status).toBe(RegisteredLetterStatus.BOUNCED);
    expect(contract.status).toBe(ContractStatus.NOTIFICATION_ERROR);
    expect(sendEmailSpy).toHaveBeenNthCalledWith(1, {
      data: {
        contractId: contractId.toString(),
        errorMessage: 'Mail erroné',
        operationId: operationId.toString(),
        operationLabel: expect.any(String),
        organizationId,
        receiver: {
          civility: Civility.WOMAN,
          firstname: 'John',
          lastname: 'Doe'
        },
        sender: {
          email: member.userEmail,
          firstname: 'Foo',
          lastname: 'BAR'
        },
        templateId: EmailTemplateId.REGISTER_LETTER_ERROR,
        url: matchRegisteredLetterBatchPageUrl
      } satisfies RegisteredLetterEmail,
      receiver: member.userEmail
    });
    expect(createNotificationSpy).toHaveBeenNthCalledWith(1, {
      batchId: batch.id.toString(),
      contractId: contractId.toString(),
      creatorId: member.userId.toString(),
      creatorName: expect.any(String),
      letterId: letter.id.toString(),
      operationId: operationId.toString(),
      receiver: 'John Doe',
      type: LetterNotificationType.LETTER_ERROR,
      userIds: [member.userId]
    });
  });

  it('should handle AWAITING_RECIPIENT_UPDATE status', async () => {
    const {
      batch,
      client,
      contractId,
      createNotificationSpy,
      member,
      operationId,
      organizationId,
      sendEmailSpy,
      testingRepos
    } = await setup();

    const letter = await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      status: RegisteredLetterStatus.SENT,
      userId: member.userId.toString()
    });

    const body = getAR24Body({ id_mail: letter.provider_letter_id, status: Ar24StatusDto.RECIPIENT_UPDATE });

    const response = await client.post(`/registered-letters-sync/${letter.id}`).send(body);

    const update = await testingRepos.letters.getLetter(letter.id.toString());
    const contract = await testingRepos.contracts.getContract(contractId);

    expect(response.status).toBe(200);
    expect(update.status).toBe(RegisteredLetterStatus.AWAITING_RECIPIENT_UPDATE);
    expect(contract.status).toBe(ContractStatus.NOTIFICATION_ERROR);
    expect(update.receiver_update).toMatchObject({
      firstname: 'JOHN RECIPIENT',
      lastname: 'DOE RECIPIENT',
      providerId: body.id_recipient_update,
      status: 'pending'
    });

    expect(createNotificationSpy).toHaveBeenNthCalledWith(1, {
      batchId: batch.id.toString(),
      contractId: contractId.toString(),
      creatorId: member.userId.toString(),
      creatorName: expect.any(String),
      letterId: letter.id.toString(),
      operationId: operationId.toString(),
      receiver: 'John Doe',
      type: LetterNotificationType.LETTER_ERROR,
      userIds: [member.userId]
    });

    expect(sendEmailSpy).toHaveBeenNthCalledWith(1, {
      data: {
        contractId: contractId.toString(),
        errorMessage:
          "Identité du destinataire (votre client s'est enregistré avec un prénom/nom différent de celui indiqué par vos soins)",
        operationId: operationId.toString(),
        operationLabel: expect.any(String),
        organizationId: organizationId,
        receiver: {
          civility: Civility.WOMAN,
          firstname: 'John',
          lastname: 'Doe'
        },
        sender: {
          email: member.userEmail,
          firstname: 'Foo',
          lastname: 'BAR'
        },
        templateId: EmailTemplateId.REGISTER_LETTER_ERROR,
        url: matchRegisteredLetterBatchPageUrl
      } satisfies RegisteredLetterEmail,
      receiver: member.userEmail
    });
  });

  it('should handle REFUSED status', async () => {
    const {
      batch,
      client,
      contractId,
      createNotificationSpy,
      member,
      operationId,
      organizationId,
      sendEmailSpy,
      testingRepos
    } = await setup();

    const letter = await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      status: RegisteredLetterStatus.SENT,
      userId: member.userId.toString()
    });

    const response = await client
      .post(`/registered-letters-sync/${letter.id}`)
      .send(getAR24Body({ id_mail: letter.provider_letter_id, status: Ar24StatusDto.REFUSED }));

    const update = await testingRepos.letters.getLetter(letter.id.toString());
    const contract = await testingRepos.contracts.getContract(contractId);

    expect(response.status).toBe(200);
    expect(update.status).toBe(RegisteredLetterStatus.REFUSED);
    expect(contract.status).toBe(ContractStatus.NOTIFICATION_COMPLETED);
    expect(update.refusal_time).toBeDefined();
    expect(update.refusal_file_id).toBeDefined();

    expect(sendEmailSpy).toHaveBeenCalledWith({
      data: {
        contractId: contractId.toString(),
        date: update.refusal_time?.toISOString() as string,
        operationId: operationId.toString(),
        operationLabel: expect.any(String),
        organizationId,
        receiver: {
          civility: Civility.WOMAN,
          firstname: 'John',
          lastname: 'Doe'
        },
        sender: {
          email: member.userEmail,
          firstname: 'Foo',
          lastname: 'BAR'
        },
        templateId: EmailTemplateId.REGISTER_LETTER_REFUSED,
        url: matchRegisteredLetterBatchPageUrl
      } satisfies RegisteredLetterEmail,
      receiver: member.userEmail
    });
    expect(createNotificationSpy).toHaveBeenCalledTimes(1);

    expect(createNotificationSpy).toHaveBeenNthCalledWith(1, {
      batchId: batch.id.toString(),
      contractId: contractId.toString(),
      creatorId: member.userId.toString(),
      creatorName: expect.any(String),
      letterId: letter.id.toString(),
      operationId: operationId.toString(),
      receiver: 'John Doe',
      refusalTime: expect.any(String),
      type: LetterNotificationType.LETTER_REFUSED,
      userIds: [member.userId]
    });
  });

  it('should handle EXPIRED status', async () => {
    const {
      batch,
      client,
      contractId,
      createNotificationSpy,
      member,
      operationId,
      organizationId,
      sendEmailSpy,
      testingRepos
    } = await setup();

    const letter = await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      status: RegisteredLetterStatus.SENT,
      userId: member.userId.toString()
    });

    const response = await client
      .post(`/registered-letters-sync/${letter.id}`)
      .send(getAR24Body({ id_mail: letter.provider_letter_id, status: Ar24StatusDto.NEGLIGENCE }));

    const update = await testingRepos.letters.getLetter(letter.id.toString());
    const contract = await testingRepos.contracts.getContract(contractId);

    expect(response.status).toBe(200);
    expect(update.status).toBe(RegisteredLetterStatus.EXPIRED);
    expect(contract.status).toBe(ContractStatus.NOTIFICATION_COMPLETED);
    expect(update.negligence_time).toBeDefined();
    expect(update.negligence_file_id).toBeDefined();

    expect(sendEmailSpy).toHaveBeenCalledWith({
      data: {
        contractId: contractId.toString(),
        date: update.negligence_time?.toISOString() as string,
        operationId: operationId.toString(),
        operationLabel: expect.any(String),
        organizationId,
        receiver: {
          civility: Civility.WOMAN,
          firstname: 'John',
          lastname: 'Doe'
        },
        sender: {
          email: member.userEmail,
          firstname: 'Foo',
          lastname: 'BAR'
        },
        templateId: EmailTemplateId.REGISTER_LETTER_NEGLIGENCE,
        url: matchRegisteredLetterBatchPageUrl
      } satisfies RegisteredLetterEmail,
      receiver: member.userEmail
    });

    expect(createNotificationSpy).toHaveBeenCalledTimes(1);
    expect(createNotificationSpy).toHaveBeenNthCalledWith(1, {
      batchId: batch.id.toString(),
      contractId: contractId.toString(),
      creatorId: member.userId.toString(),
      creatorName: expect.any(String),
      letterId: letter.id.toString(),
      negligenceTime: expect.any(String),
      operationId: operationId.toString(),
      receiver: 'John Doe',
      type: LetterNotificationType.LETTER_EXPIRED,
      userIds: [member.userId]
    });
  });

  it('should handle ACCEPTED status and completed batch with ACCEPTED status', async () => {
    const {
      batch,
      client,
      contractId,
      createNotificationSpy,
      member,
      operationId,
      organizationId,
      sendEmailSpy,
      testingRepos
    } = await setup();

    const subscriber = await testingRepos.users.createUser({
      verified: true
    });

    const subscriberNotMember = '<EMAIL>';

    await testingRepos.letters.createSubscribers({
      batchId: batch.id.toString(),
      emails: [subscriber.email, subscriberNotMember]
    });

    const letter = await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      status: RegisteredLetterStatus.SENT,
      userId: member.userId.toString()
    });

    const response = await client
      .post(`/registered-letters-sync/${letter.id}`)
      .send(getAR24Body({ id_mail: letter.provider_letter_id, status: Ar24StatusDto.AR }));

    const update = await testingRepos.letters.getLetter(letter.id.toString());
    const contract = await testingRepos.contracts.getContract(contractId);

    expect(response.status).toBe(200);
    expect(update.status).toBe(RegisteredLetterStatus.ACCEPTED);
    expect(contract.status).toBe(ContractStatus.NOTIFICATION_COMPLETED);
    expect(update.acceptation_time).toBeDefined();
    expect(update.acceptation_file_id).toBeDefined();

    expect(sendEmailSpy).toHaveBeenNthCalledWith(1, {
      data: {
        contractId: contractId.toString(),
        date: update.acceptation_time?.toISOString() as string,
        operationId: operationId.toString(),
        operationLabel: expect.any(String),
        organizationId,
        receiver: {
          civility: Civility.WOMAN,
          firstname: 'John',
          lastname: 'Doe'
        },
        sender: {
          email: member.userEmail,
          firstname: 'Foo',
          lastname: 'BAR'
        },
        templateId: EmailTemplateId.REGISTER_LETTER_ACCEPTED,
        url: matchRegisteredLetterBatchPageUrl
      } satisfies RegisteredLetterEmail,
      receiver: member.userEmail
    });

    expect(sendEmailSpy).toHaveBeenNthCalledWith(2, {
      data: {
        batchId: batch.id.toString(),
        contractId: contractId.toString(),
        operationId: operationId.toString(),
        operationLabel: expect.any(String),
        organizationId,
        sender: {
          email: member.userEmail,
          firstname: 'Foo',
          lastname: 'BAR'
        },
        templateId: EmailTemplateId.REGISTER_LETTER_COMPLETED_SHARED,
        url: matchRegisteredLetterBatchAnonymousPage
      } satisfies RegisteredLetterBatchEmail,
      receiver: subscriber.email
    });

    expect(sendEmailSpy).toHaveBeenNthCalledWith(3, {
      data: {
        batchId: batch.id.toString(),
        contractId: contractId.toString(),
        operationId: operationId.toString(),
        operationLabel: expect.any(String),
        organizationId,
        sender: {
          email: member.userEmail,
          firstname: 'Foo',
          lastname: 'BAR'
        },
        templateId: EmailTemplateId.REGISTER_LETTER_COMPLETED_SHARED,
        url: matchRegisteredLetterBatchAnonymousPage
      } satisfies RegisteredLetterBatchEmail,
      receiver: subscriberNotMember
    });

    expect(sendEmailSpy).toHaveBeenNthCalledWith(4, {
      data: {
        batchId: batch.id.toString(),
        contractId: contractId.toString(),
        operationId: operationId.toString(),
        operationLabel: expect.any(String),
        organizationId,
        sender: {
          email: member.userEmail,
          firstname: 'Foo',
          lastname: 'BAR'
        },
        templateId: EmailTemplateId.REGISTER_LETTER_COMPLETED,
        url: matchRegisteredLetterBatchPageUrl
      } satisfies RegisteredLetterBatchEmail,
      receiver: member.userEmail
    });

    expect(createNotificationSpy).toHaveBeenNthCalledWith(1, {
      acceptationTime: expect.any(String),
      batchId: batch.id.toString(),
      contractId: contractId.toString(),
      creatorId: member.userId.toString(),
      creatorName: expect.any(String),
      letterId: letter.id.toString(),
      operationId: operationId.toString(),
      receiver: 'John Doe',
      type: LetterNotificationType.LETTER_ACCEPTED,
      userIds: [member.userId]
    });

    expect(createNotificationSpy).toHaveBeenNthCalledWith(2, {
      acceptationTime: expect.any(String),
      batchId: batch.id.toString(),
      contractId: contractId.toString(),
      creatorId: member.userId.toString(),
      creatorName: expect.any(String),
      operationId: operationId.toString(),
      type: LetterNotificationType.BATCH_ACCEPTED,
      userIds: [member.userId, subscriber.id]
    });
  });

  it('should allow to update deposit file when batch is completed', async () => {
    const { batch, client, member, registeredLettersUpdaterService, testingRepos } = await setup();

    const letter = await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      status: RegisteredLetterStatus.ACCEPTED,
      userId: member.userId.toString()
    });

    await testingRepos.letters.createLetter({
      batchId: batch.id.toString(),
      status: RegisteredLetterStatus.SENT,
      userId: member.userId.toString()
    });

    const onRegisteredLetterUpdatedSpy = jest
      .spyOn(registeredLettersUpdaterService, 'onRegisteredLetterUpdated')
      .mockResolvedValue();

    const response = await client
      .post(`/registered-letters-sync/${letter.id}`)
      .send(getAR24Body({ id_mail: letter.provider_letter_id, status: Ar24StatusDto.EV }));

    const update = await testingRepos.letters.getLetter(letter.id.toString());

    expect(response.status).toBe(200);
    expect(update.status).toBe(RegisteredLetterStatus.ACCEPTED);
    expect(update.deposit_file_id).toBeDefined();
    expect(update.deposit_time).toBeDefined();
    expect(onRegisteredLetterUpdatedSpy).toHaveBeenCalledTimes(0);
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,
      controller: RegisteredLetterWebhooksController,
      providers: provideRegisteredLettersTest()
    });

    const testingRepos = getService(TestingRepositories);
    const emailApiService = getService(EmailsApiService);
    const notificationApiService = getService(NotificationApiService);

    const registeredLettersUpdaterService = getService(RegisteredLettersUpdaterService);

    const sendEmailSpy = jest.spyOn(emailApiService, 'sendEmail').mockResolvedValue();
    const createNotificationSpy = jest.spyOn(notificationApiService, 'createNotification').mockResolvedValue();

    const eventApiService = getService(EventsApiService);
    const createEventsSpy = jest.spyOn(eventApiService, 'createEvents').mockResolvedValue();

    const eventAsyncTaskService = getService(AsyncTasksApiService);
    const createAsyncTask = jest.spyOn(eventAsyncTaskService, 'createAsyncTask').mockResolvedValue();

    const member = await testingRepos.createMember({});
    const operation = await testingRepos.operations.createVenteAncien({
      organizationId: member.organizationId
    });
    const contract = await testingRepos.contracts.createMandat({ operationId: operation.id, userId: member.userId });

    const batch = await testingRepos.letters.createBatch({
      contractId: contract.id,
      creatorId: member.userId,
      operationId: operation.id
    });

    return {
      batch,
      client,
      contractId: contract.id,
      createAsyncTask,
      createEventsSpy,
      createNotificationSpy,
      member,
      operationId: operation.id,
      organizationId: member.organizationId,
      registeredLettersUpdaterService,
      sendEmailSpy,
      testingRepos
    };
  }
});

const getAR24Body = ({ id_mail, status }: { id_mail: string; status: Ar24StatusDto }): RegisteredLettersAr24SyncDto => {
  switch (status) {
    case Ar24StatusDto.AR:
      return {
        id_mail,
        new_state: status,
        proof_url: 'FAKE_PROOF_URL',
        view_date: new Date().toISOString()
      };
    case Ar24StatusDto.RECIPIENT_UPDATE:
      return {
        id_mail,
        id_recipient_update: 'FAKE_RECIPIENT_UPDATE_ID',
        new_state: status
      };
    case Ar24StatusDto.NEGLIGENCE:
      return {
        id_mail,
        negligence_date: new Date().toISOString(),
        new_state: status,
        proof_url: 'FAKE_PROOF_URL'
      };
    case Ar24StatusDto.REFUSED:
      return {
        id_mail,
        new_state: status,
        proof_url: 'FAKE_PROOF_URL',
        refused_date: new Date().toISOString()
      };
    case Ar24StatusDto.DP:
    case Ar24StatusDto.EV:
      return {
        id_mail,
        new_state: status,
        proof_url: 'FAKE_PROOF_URL',
        ts_ev_date: new Date().toISOString()
      };
    case Ar24StatusDto.CANCELLED_BY_USER:
    case Ar24StatusDto.CANCELLED_AFTER_7_DAYS:
    case Ar24StatusDto.BOUNCED:
    case Ar24StatusDto.FAIL:
      return {
        id_mail,
        new_state: status
      };
    case Ar24StatusDto.CONSENT:
    case Ar24StatusDto.OPTIMAL_CHOICE_DONE:
    case Ar24StatusDto.WAITING_MANUAL_PROCESS:
    case Ar24StatusDto.PRICE_SET_TO_FREE:
      throw new Error(`Unsupported Status ${status}`);
  }
};
