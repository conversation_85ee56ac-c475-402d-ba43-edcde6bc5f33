generator client {
  provider        = "prisma-client-js"
  output          = "./generated-client"
  previewFeatures = ["postgresqlExtensions"]
  binaryTargets   = ["debian-openssl-3.0.x", "debian-openssl-1.0.x", "debian-openssl-1.1.x", "linux-musl-openssl-3.0.x", "native", "darwin-arm64"]
}

datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")
  relationMode = "foreignKeys"
  extensions   = [pgcrypto, postgis, unaccent]
}

model answer {
  id                     Int                     @id @default(autoincrement())
  answer                 Json                    @default("{}")
  migrated               Boolean?
  legal_component_record legal_component_record?
}

model apimo_agency {
  id       Int     @id(map: "apimo_agency_pk") @unique(map: "apimo_agency_id_uindex") @default(autoincrement())
  apimo_id Int?
  name     String?
}

model apimo_synchronization {
  id             Int       @unique(map: "apimo_synchronization_id_uindex") @default(autoincrement())
  type           String?
  last_update    DateTime? @db.Timestamp(6)
  organizationId Int?
}

model application {
  id                       Int                        @id(map: "application_pk") @unique(map: "application_id_uindex") @default(autoincrement())
  creation_date            DateTime?                  @default(now()) @db.Timestamp(6)
  name                     String                     @unique(map: "application_name_uindex")
  public                   Boolean?                   @default(false)
  creator_id               Int?
  description              String?
  api_key                  String
  organization_id          Int?
  organization             organization?              @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "application_organization_id_fk")
  user                     user?                      @relation(fields: [creator_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "application_user_id_fk")
  application_organization application_organization[]
  external_api_log         external_api_log[]
  internal_api_association internal_api_association[]
  webhook                  webhook[]
}

model application_organization {
  application_id  Int?
  organization_id Int          @default(1)
  creation_time   DateTime     @default(now()) @db.Timestamp(6)
  id              Int          @id @default(autoincrement())
  application     application? @relation(fields: [application_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "application_organization_application_id_fk")
}

model archive {
  id                               Int                                @id @unique(map: "archive_id_uindex") @default(autoincrement())
  label                            String
  creation_time                    DateTime                           @db.Timestamp(6)
  archived_time                    DateTime?                          @default(now()) @db.Timestamp(6)
  signature_id                     Int?
  legal_case_id                    Int?
  creator_user_id                  Int?
  user                             user?                              @relation(fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  signature                        signature?                         @relation(fields: [signature_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  archive_association_organization archive_association_organization[]
  archive_association_user         archive_association_user[]
  archive_file                     archive_file[]
}

model archive_association_organization {
  archive_id      Int
  organization_id Int
  archive         archive      @relation(fields: [archive_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  organization    organization @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict)

  @@id([archive_id, organization_id], map: "archive_association_organization_pk")
  @@index([organization_id])
}

model archive_association_user {
  archive_id Int?
  user_id    Int?
  id         Int      @id @default(autoincrement())
  archive    archive? @relation(fields: [archive_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  user       user?    @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict)

  @@index([user_id])
}

model archive_file {
  archive_id Int?
  file_id    String?
  label      String
  path       String
  id         Int      @id @default(autoincrement())
  archive    archive? @relation(fields: [archive_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "archive_file_archive_id_fk")
  file       file?    @relation(fields: [file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "archive_file_file_id_fk")

  @@index([archive_id], map: "archive_file_id_uindex")
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model authentication {
  id           Int       @id(map: "authentication_pk") @unique(map: "authentication_id_uindex") @default(autoincrement())
  type         String?
  apikey_key   String?
  apikey_value String?
  client       String?
  secret       String?
  webhook      webhook[]
}

model clause {
  id    String  @id @db.VarChar(256)
  label String?
}

model contract_last_visited {
  id                 Int                @id(map: "contract_last_visited_pk") @unique(map: "contract_last_visited_id_uindex") @default(autoincrement())
  user_id            Int
  contract_id        Int
  last_access_time   DateTime?          @db.Timestamp(6)
  organization_id    Int?
  operation_contract operation_contract @relation(fields: [contract_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "contract_last_visited_operation_contract")
  user               user               @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "contract_last_visited_user_id_fk")
  organization       organization?      @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_last_visited_organization_id_fk")

  @@unique([user_id, contract_id], map: "contract_last_visited_user_id_contract_id_uindex")
  @@index([id, organization_id], map: "contract_last_visited_id_organization_id_index")
}

model contract_model_clause {
  clause_id                String
  contract_model_id        String
  jefferson_path           String
  operation_contract_model operation_contract_model @relation(fields: [contract_model_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "contract_model_clause_operation_contract_model_id_fk")

  @@id([clause_id, contract_model_id], map: "contract_model_clause_pk")
  @@index([clause_id], map: "contract_model_clause_clause_id_index")
  @@index([contract_model_id], map: "contract_model_clause_contract_model_id_index")
}

model cron_job {
  type            String    @id(map: "cron_job_PK") @db.VarChar(32)
  last_passage    DateTime? @db.Timestamptz(6)
  cron_expression String?
}

model custom_view {
  id                   Int                    @id(map: "custom_view_pk") @unique(map: "custom_view_id_uindex") @default(autoincrement())
  creator_user_id      Int?
  organization_id      Int
  is_public            Boolean                @default(false)
  is_default           Boolean                @default(false)
  type                 String
  label                String
  filters              Json
  user                 user?                  @relation(fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "custom_view_creator_user_id_fk")
  organization         organization           @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "custom_view_organization_id_fk")
  custom_view_favorite custom_view_favorite[]

  @@index([creator_user_id], map: "custom_view_creator_user_id_uindex")
  @@index([organization_id], map: "custom_view_organization_id_uindex")
}

model custom_view_favorite {
  id             Int         @id(map: "custom_view_favorite_pk") @unique(map: "custom_view_favorite_id_uindex") @default(autoincrement())
  user_id        Int
  custom_view_id Int
  custom_view    custom_view @relation(fields: [custom_view_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "custom_view_favorite_custom_view_id_fk")
  user           user        @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "custom_view_favorite_user_id_fk")

  @@unique([user_id, custom_view_id])
  @@index([user_id], map: "custom_view_favorite_user_id_uindex")
}

model databasechangelog {
  id            String   @db.VarChar(255)
  author        String   @db.VarChar(255)
  filename      String   @db.VarChar(255)
  dateexecuted  DateTime @db.Timestamp(6)
  orderexecuted Int
  exectype      String   @db.VarChar(10)
  md5sum        String?  @db.VarChar(35)
  description   String?  @db.VarChar(255)
  comments      String?  @db.VarChar(255)
  tag           String?  @db.VarChar(255)
  liquibase     String?  @db.VarChar(20)
  contexts      String?  @db.VarChar(255)
  labels        String?  @db.VarChar(255)

  @@id([id, author, filename])
}

model databasechangeloglock {
  id          Int       @id(map: "pk_databasechangeloglock")
  locked      Boolean
  lockgranted DateTime? @db.Timestamp(6)
  lockedby    String?   @db.VarChar(255)
}

model document_request_template {
  id                       Int                      @id @default(autoincrement())
  user_id                  Int?
  organization_id          Int?
  operation_type           String
  label                    String
  creation_time            DateTime?                @default(now()) @db.Timestamp(6)
  documents                String[]
  legal_component_template legal_component_template @relation(fields: [operation_type], references: [id_str], onDelete: Restrict, onUpdate: Restrict, map: "document_request_template_operation_type_fk")
  organization             organization?            @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "document_request_template_organization_id_fk")
  user                     user?                    @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "document_request_template_user_id_fk")

  @@unique([label, organization_id], map: "document_request_template_pk")
  @@index([operation_type, user_id], map: "document_request_template_operation_user_index")
  @@index([operation_type, user_id, organization_id], map: "document_request_template_operation_user_organization_index")
}

model drive_file {
  id                        Int                       @id @default(autoincrement())
  creation_time             DateTime                  @default(now()) @db.Timestamp(6)
  file_id                   String
  user_id                   Int?
  operation_id              Int
  folder_id                 Int?
  document_label            String?
  file                      file                      @relation(fields: [file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "drive_file_file_id_fk")
  drive_folder              drive_folder?             @relation(fields: [folder_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "drive_file_folder_id_fk")
  legal_component_operation legal_component_operation @relation(fields: [operation_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "drive_file_legal_component_id_fk")
  user                      user?                     @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "drive_file_user_id_fk")

  @@unique([file_id, operation_id], map: "drive_file_label_operation_id_uindex")
}

model drive_folder {
  id                        Int                       @id @default(autoincrement())
  creation_time             DateTime                  @default(now()) @db.Timestamp(6)
  user_id                   Int
  label                     String
  operation_id              Int
  drive_file                drive_file[]
  legal_component_operation legal_component_operation @relation(fields: [operation_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "drive_folder_legal_component_id_fk")
  user                      user                      @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "drive_folder_user_id_fk")

  @@unique([label, operation_id], map: "drive_folder_label_operation_id_uindex")
}

model email_template {
  id                     String @id(map: "v3.email_template_pk") @unique(map: "v3.email_template_id_uindex")
  sendinblue_template_id Int
  name                   String
}

model event {
  id              Int         @id(map: "trigger_event_pk") @default(autoincrement())
  event_id        String
  data            Json
  creation_time   DateTime?   @default(now()) @db.Timestamptz(6)
  organization_id Int?
  status          String?
  event_log       event_log[]

  @@index([status], map: "trigger_event_status_index")
}

model event_log {
  id                Int       @id(map: "webhook_trigger_pk") @unique(map: "webhook_trigger_id_uindex") @default(autoincrement())
  webhook_id        Int?
  status            String?
  retry             Int?
  event_id          Int?
  trigger_time      DateTime? @default(now()) @db.Timestamp(6)
  error             Json?     @db.Json
  last_trigger_time DateTime? @default(now()) @db.Timestamp(6)
  strategy          String?
  stack             String?
  body              Json?     @db.Json
  event             event?    @relation(fields: [event_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "webhook_trigger_trigger_event_id_fk")
  webhook           webhook?  @relation(fields: [webhook_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "webhook_trigger_webhook_id_fk")
}

model external_api_key {
  id                  Int     @id(map: "external_api_key_pk") @unique(map: "external_api_key_id_uindex") @default(autoincrement())
  api_name            String
  username            String?
  password            String?
  mynotary_user_email String?
  organization_id     Int?
  mynotary_username   String?
  mynotary_password   String?

  @@index([organization_id], map: "external_api_key_organization_id_index")
}

model external_api_log {
  id          Int          @id(map: "audit_logging_pk") @unique(map: "external_api_log_id_uindex") @default(autoincrement())
  date        DateTime?    @default(now()) @db.Timestamp(6)
  ip_address  String?
  method      String?
  path        String?
  code        Int?
  duration    Int?
  app_id      Int?
  request     Json?        @db.Json
  response    Json?        @db.Json
  query       String?
  stack_trace Json?        @db.Json
  application application? @relation(fields: [app_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "external_api_log_application_id_fk")
}

model external_notification {
  id                Int       @id(map: "external_notification_pk") @default(autoincrement())
  creation_time     DateTime? @default(now()) @db.Timestamptz(6)
  notification_id   String
  status            String?
  notification_type String?
  transmission_time DateTime? @db.Timestamptz(6)
  data              Json

  @@index([status], map: "external_notification_status_index")
}

model file {
  id                                                            String                                      @id(map: "id_primary")
  name                                                          String                                      @db.VarChar(250)
  upload_time                                                   DateTime                                    @default(now()) @db.Timestamptz(6)
  size                                                          BigInt?
  checksum                                                      String?                                     @db.VarChar(40)
  content_type                                                  String?                                     @db.VarChar(250)
  pages                                                         Int?
  static_file                                                   Boolean?                                    @default(false)
  creator_user_id                                               Int?
  is_temporary                                                  Boolean                                     @default(false)
  bucket_name                                                   String
  status                                                        String?
  deletion_time                                                 DateTime?                                   @db.Timestamptz(6)
  archive_file                                                  archive_file[]
  drive_file                                                    drive_file[]
  user                                                          user?                                       @relation(fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "file_user_id_fk")
  invoice                                                       invoice[]
  operation_contract_operation_contract_project_file_idTofile   operation_contract[]                        @relation("operation_contract_project_file_idTofile")
  operation_contract_operation_contract_contract_file_idTofile  operation_contract[]                        @relation("operation_contract_contract_file_idTofile")
  organization                                                  organization[]
  planete_pj_recue                                              planete_pj_recue[]
  registered_letter_registered_letter_letter_file_idTofile      registered_letter[]                         @relation("registered_letter_letter_file_idTofile")
  registered_letter_registered_letter_acceptation_file_idTofile registered_letter[]                         @relation("registered_letter_acceptation_file_idTofile")
  registered_letter_registered_letter_deposit_file_idTofile     registered_letter[]                         @relation("registered_letter_deposit_file_idTofile")
  registered_letter_registered_letter_refusal_file_idTofile     registered_letter[]                         @relation("registered_letter_refusal_file_idTofile")
  registered_letter_registered_letter_negligence_file_idTofile  registered_letter[]                         @relation("registered_letter_negligence_file_idTofile")
  shared_folder_file                                            shared_folder_file[]
  signature_file_signature_file_original_file_idTofile          signature_file[]                            @relation("signature_file_original_file_idTofile")
  signature_file_signature_file_signed_file_idTofile            signature_file[]                            @relation("signature_file_signed_file_idTofile")
  signature_file_signature_file_to_sign_file_idTofile           signature_file[]                            @relation("signature_file_to_sign_file_idTofile")
  signature_provider_type_association_paper                     signature_provider_type_association_paper[]
  signature_signatory                                           signature_signatory[]
  user_user_profile_picture_file_idTofile                       user[]                                      @relation("user_profile_picture_file_idTofile")

  @@index([bucket_name], map: "file_bucket_name_index")
  @@index([creator_user_id], map: "file_creator_user_id_index")
  @@index([status], map: "file_status_index")
  @@index([id], map: "static_file_file_id_index")
}

model gel_avoir {
  id           Int       @id @default(autoincrement())
  firstnames   Json?
  lastname     String?
  denomination String?
  date         DateTime? @db.Date
  aliases      Json?
  search_terms Json?
  type_person  String?
  countries    Json?

  @@map("gel_avoir")
}

model help_resource {
  id          String  @id(map: "help_resource_pk") @unique
  title       String?
  description String?
  link        String?
}

model internal_api_association {
  id              Int           @id(map: "internal_api_association_pk") @default(autoincrement())
  from            String
  to              String?
  type            String
  context         Json?         @default("{}")
  status          String?
  creation_time   DateTime?     @default(now()) @db.Timestamp(6)
  last_update     DateTime?     @default(now()) @db.Timestamp(6)
  organization_id Int?
  application_id  Int?
  internal_type   String?
  application     application?  @relation(fields: [application_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "internal_api_association_application_id_fk")
  organization    organization? @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "internal_api_association_organization_id_fk")

  @@index([from], map: "internal_api_association_from_index")
  @@index([organization_id], map: "internal_api_association_organization_id_index")
  @@index([status], map: "internal_api_association_status_index")
  @@index([to], map: "internal_api_association_to_index")
  @@index([type], map: "internal_api_association_type_index")
}

model internal_api_key {
  id                String       @id(map: "internal_api_key_pk") @unique(map: "internal_api_key_id_uindex")
  creation_time     DateTime?    @default(now()) @db.Timestamp(6)
  modification_time DateTime?    @db.Timestamp(6)
  name              String?
  organization_id   Int
  organization      organization @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "internal_api_key_organization_id_fk")
}

model invoice {
  id                   Int                  @id(map: "invoice_pk") @unique(map: "invoice_id_uindex") @default(autoincrement())
  config_id            Int
  data                 Json
  file_id              String?
  creator_id           Int
  creation_time        DateTime             @default(now()) @db.Timestamp(6)
  status               String
  organization_id      Int
  feature_id           Int
  payment_time         DateTime?            @db.Timestamp(6)
  email                email[]
  organization_feature organization_feature @relation(fields: [feature_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "invoice_feature_id_fk")
  file                 file?                @relation(fields: [file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "invoice_file_id_fk")
  invoice_config       invoice_config       @relation(fields: [config_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "invoice_invoice_config_id_fk")
  organization         organization         @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "invoice_organization_id_fk")
  user                 user                 @relation(fields: [creator_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "invoice_user_id_fk")

  @@index([config_id], map: "invoice_config_id_index")
}

model invoice_config {
  id                   Int                  @id(map: "invoice_config_pk") @unique(map: "invoice_config_id_uindex") @default(autoincrement())
  organization_id      Int
  config               Json
  current_number       Int
  feature_id           Int
  invoice              invoice[]
  organization_feature organization_feature @relation(fields: [feature_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "invoice_config_organization_feature_id_fk")
  organization         organization         @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "invoice_config_organization_id_fk")

  @@index([organization_id], map: "invoice_config_organization_id_index")
}

model legal_component {
  id                                                                             Int                                                   @id(map: "legal_component_pk") @default(autoincrement())
  creation_time                                                                  DateTime                                              @default(now()) @db.Timestamptz(6)
  creator_user_id                                                                Int?
  template_id                                                                    Int
  deleted                                                                        Boolean                                               @default(false)
  organization_id                                                                Int?
  tags                                                                           Json?                                                 @default("{}")
  last_update_time                                                               DateTime?                                             @default(now()) @db.Timestamp(6)
  archived                                                                       Boolean                                               @default(false)
  template_id_str                                                                String
  legal_component_template_legal_component_template_idTolegal_component_template legal_component_template                              @relation("legal_component_template_idTolegal_component_template", fields: [template_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_legal_component_template_id_fk")
  legal_component_template                                                       legal_component_template                              @relation(fields: [template_id_str], references: [id_str], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_legal_component_template_id_str_fk")
  organization                                                                   organization?                                         @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_organization_id_fk")
  user                                                                           user?                                                 @relation(fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_user_id_fk")
  legal_component_branch_legal_component_branch_from_idTolegal_component         legal_component_branch[]                              @relation("legal_component_branch_from_idTolegal_component")
  legal_component_branch_legal_component_branch_to_idTolegal_component           legal_component_branch[]                              @relation("legal_component_branch_to_idTolegal_component")
  legal_component_link                                                           legal_component_link?                                 @relation("link_component")
  legal_component_link_record                                                    legal_component_link?                                 @relation("record_component")
  legal_component_operation                                                      legal_component_operation?
  legal_component_record                                                         legal_component_record?
  operation_invitation                                                           operation_invitation[]
  order                                                                          order[]
  organization_credits_history                                                   organization_credits_history[]
  registered_letter_batch_association_legal_component                            registered_letter_batch_association_legal_component[]
  signature_association_legal_component                                          signature_association_legal_component[]
  task                                                                           task[]

  @@index([creation_time(sort: Desc)], map: "legal_component_creation_time_index")
  @@index([creator_user_id], map: "legal_component_creator_user_id_index")
  @@index([deleted], map: "legal_component_deleted_index")
  @@index([organization_id], map: "legal_component_organization_id_index")
  @@index([template_id], map: "legal_component_template_id_index")
  @@index([template_id_str], map: "legal_component_template_id_str_index")
}

model legal_component_branch {
  id                                                              Int                  @id(map: "legal_component_branch_pk") @default(autoincrement())
  type                                                            String
  from_id                                                         Int?
  to_id                                                           Int?
  link_id                                                         Int
  reverse_id                                                      Int?
  specific_contract_id                                            Int?
  reverse_type                                                    String?
  legal_component_legal_component_branch_from_idTolegal_component legal_component?     @relation("legal_component_branch_from_idTolegal_component", fields: [from_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_branch_legal_component_id_fk")
  legal_component_legal_component_branch_to_idTolegal_component   legal_component?     @relation("legal_component_branch_to_idTolegal_component", fields: [to_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_branch_legal_component_id_fk_2")
  legal_component_link                                            legal_component_link @relation(fields: [link_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_branch_legal_component_link_id_fk")
  operation_contract                                              operation_contract?  @relation(fields: [specific_contract_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_branch_operation_contract_id_fk")

  @@index([from_id], map: "legal_component_branch_from_id_index")
  @@index([link_id], map: "legal_component_branch_link_id_index")
  @@index([reverse_id], map: "legal_component_branch_reverse_id_index")
  @@index([specific_contract_id], map: "legal_component_branch_specific_contract_id_index")
  @@index([to_id], map: "legal_component_branch_to_id_index")
  @@index([type], map: "legal_component_branch_type_index")
}

model legal_component_link {
  legal_component_id               Int                        @id(map: "legal_component_link_legal_component_pk")
  origin_operation_id              Int?
  record_id                        Int?                       @unique
  end_time                         DateTime?                  @db.Timestamptz(6)
  start_time                       DateTime?                  @db.Timestamptz(6)
  specific_contract_id             Int?
  legal_component_branch           legal_component_branch[]
  legal_component                  legal_component            @relation(name: "link_component", fields: [legal_component_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_link_legal_component_fk")
  legal_component_record           legal_component_record?    @relation(fields: [record_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_link_legal_component_operation_fk")
  legal_component_record_component legal_component?           @relation(name: "record_component", fields: [record_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_link_record_legal_component_fk")
  legal_component_operation        legal_component_operation? @relation(fields: [origin_operation_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_link_legal_component_record_fk")
  operation_contract               operation_contract?        @relation(fields: [specific_contract_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_link_operation_contract_id_fk")

  @@index([origin_operation_id], map: "legal_component_link_origin_operation_id_index")
  @@index([record_id], map: "legal_component_link_record_id_index")
  @@index([specific_contract_id], map: "legal_component_link_specific_contract_id_index")
}

model legal_component_operation {
  legal_component_id                                                    Int                            @id(map: "legal_component_operation_pk")
  status                                                                String?
  label                                                                 String?
  parent_operation_id                                                   Int?
  status_id                                                             Int?
  label_pattern                                                         String?
  is_operation_reference                                                Boolean                        @default(false)
  drive_file                                                            drive_file[]
  drive_folder                                                          drive_folder[]
  email                                                                 email[]
  legal_component_link                                                  legal_component_link[]
  legal_component                                                       legal_component                @relation(fields: [legal_component_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_operation_legal_component_id_fk")
  organization_operation_status                                         organization_operation_status? @relation(fields: [status_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_operation_status_id_fk")
  legal_component_operation                                             legal_component_operation?     @relation("legal_component_operationTolegal_component_operation", fields: [parent_operation_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "legal_operation_legal_operation_legal_component_id_fk")
  other_legal_component_operation                                       legal_component_operation[]    @relation("legal_component_operationTolegal_component_operation")
  lot_reservation_lot_reservation_program_idTolegal_component_operation lot_reservation[]              @relation("lot_reservation_program_idTolegal_component_operation")
  lot_reservation_lot_reservation_sale_idTolegal_component_operation    lot_reservation[]              @relation("lot_reservation_sale_idTolegal_component_operation")
  notification                                                          notification[]
  operation_contract                                                    operation_contract[]
  operation_last_visited                                                operation_last_visited[]
  operation_record_extension                                            operation_record_extension[]
  planete_dossier                                                       planete_dossier[]

  @@index([parent_operation_id], map: "legal_component_operation_parent_operation_id_index")
  @@index([status_id], map: "legal_component_operation_status_id_index")
}

model legal_component_record {
  legal_component_id                                                                      Int                                               @id(map: "legal_component_record_pk")
  answer_id                                                                               Int                                               @unique(map: "legal_component_record_answer_id_uindex")
  legal_component_link                                                                    legal_component_link[]
  answer                                                                                  answer                                            @relation(fields: [answer_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_record_answer_id_fk")
  legal_component                                                                         legal_component                                   @relation(fields: [legal_component_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "legal_component_record_legal_component_id_fk")
  lot_reservation                                                                         lot_reservation[]
  operation_contract_document                                                             operation_contract_document[]
  operation_default_record                                                                operation_default_record[]
  operation_record_extension_operation_record_extension_extensionTolegal_component_record operation_record_extension[]                      @relation("operation_record_extension_extensionTolegal_component_record")
  operation_record_extension_operation_record_extension_record_idTolegal_component_record operation_record_extension[]                      @relation("operation_record_extension_record_idTolegal_component_record")
  signature_association_legal_component                                                   signature_association_legal_component[]
  signature_signatory_association_legal_component                                         signature_signatory_association_legal_component[]
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model legal_component_template {
  id                                                                                                Int                             @id(map: "legal_component_template_pk") @default(autoincrement())
  creation_time                                                                                     DateTime                        @default(now()) @db.Timestamptz(6)
  type                                                                                              String
  label                                                                                             String
  specific_types                                                                                    String[]
  is_mynotary_template                                                                              Boolean?                        @default(false)
  origin_template                                                                                   String?
  form_path                                                                                         String?
  config_path                                                                                       String?
  id_str                                                                                            String                          @unique(map: "legal_component_template_id_str_uindex")
  document_request_template                                                                         document_request_template[]
  legal_component_legal_component_template_idTolegal_component_template                             legal_component[]               @relation("legal_component_template_idTolegal_component_template")
  legal_component                                                                                   legal_component[]
  operation_default_record_operation_default_record_operation_template_idTolegal_component_template operation_default_record[]      @relation("operation_default_record_operation_template_idTolegal_component_template")
  operation_default_record_operation_default_record_link_template_idTolegal_component_template      operation_default_record[]      @relation("operation_default_record_link_template_idTolegal_component_template")
  organization_operation_status_organization_operation_status_template_idTolegal_component_template organization_operation_status[] @relation("organization_operation_status_template_idTolegal_component_template")
  permission                                                                                        permission[]

  @@index([specific_types], map: "legal_component_template_specific_types_index", type: Gin)
}

model lot_reservation {
  id                                                                              Int                        @id(map: "lot_reservation_pk") @unique(map: "lot_reservation_id_uindex") @default(autoincrement())
  record_id                                                                       Int?
  program_id                                                                      Int?
  sale_id                                                                         Int?
  status                                                                          String?
  creator_user_id                                                                 Int?
  creation_time                                                                   DateTime?                  @default(now()) @db.Timestamp(6)
  legal_component_record                                                          legal_component_record?    @relation(fields: [record_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "lot_reservation_legal_component_record_legal_component_id_fk")
  legal_component_operation_lot_reservation_program_idTolegal_component_operation legal_component_operation? @relation("lot_reservation_program_idTolegal_component_operation", fields: [program_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "lot_reservation_program_legal_component_id_fk")
  legal_component_operation_lot_reservation_sale_idTolegal_component_operation    legal_component_operation? @relation("lot_reservation_sale_idTolegal_component_operation", fields: [sale_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "lot_reservation_sale_legal_component_id_fk")
  user                                                                            user?                      @relation(fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "lot_reservation_user_id_fk")

  @@index([record_id], map: "lot_reservation_record_id_index")
}

model notification {
  id                        Int                        @id(map: "notification_pk") @default(autoincrement())
  type                      String
  operation_id              Int?
  contract_id               Int?
  creation_time             DateTime                   @default(now()) @db.Timestamp(6)
  data                      Json
  legal_component_operation legal_component_operation? @relation(fields: [operation_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "notification_legal_component_operation_legal_component_id_fk")
  operation_contract        operation_contract?        @relation(fields: [contract_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "notification_operation_contract_id_fk")
  user_notification         user_notification[]
}

model operation_contract {
  id                                                                Int                                                   @id(map: "operation_contract_pk") @default(autoincrement())
  label                                                             String
  operation_id                                                      Int
  model_id                                                          String
  creation_time                                                     DateTime                                              @default(now()) @db.Timestamptz(6)
  creator_user_id                                                   Int
  deletion_time                                                     DateTime?                                             @db.Timestamptz(6)
  template                                                          Boolean                                               @default(false)
  project_file_id                                                   String?
  contract_file_id                                                  String?
  status                                                            String?                                               @default("REDACTION")
  validation_time                                                   DateTime?                                             @db.Timestamptz(6)
  creation_context                                                  Json?
  archive_time                                                      DateTime?                                             @db.Timestamptz(6)
  current_contract_validation_task_id                               Int?
  current_contract_reviews_task_id                                  Int?
  current_signature_id                                              Int?
  contract_last_visited                                             contract_last_visited[]
  email                                                             email[]
  legal_component_branch                                            legal_component_branch[]
  legal_component_link                                              legal_component_link[]
  notification                                                      notification[]
  file_operation_contract_project_file_idTofile                     file?                                                 @relation("operation_contract_project_file_idTofile", fields: [project_file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_contract_file_id_fk")
  file_operation_contract_contract_file_idTofile                    file?                                                 @relation("operation_contract_contract_file_idTofile", fields: [contract_file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_contract_file_id_fk_2")
  legal_component_operation                                         legal_component_operation                             @relation(fields: [operation_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "operation_contract_legal_component_operation_legal_component_id")
  operation_contract_model                                          operation_contract_model                              @relation(fields: [model_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_contract_operation_contract_model_id_fk")
  task_operation_contract_current_contract_validation_task_idTotask task?                                                 @relation("operation_contract_current_contract_validation_task_idTotask", fields: [current_contract_validation_task_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_contract_task_id_fk")
  task_operation_contract_current_contract_reviews_task_idTotask    task?                                                 @relation("operation_contract_current_contract_reviews_task_idTotask", fields: [current_contract_reviews_task_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_contract_task_id_fk_2")
  signature                                                         signature?                                            @relation(fields: [current_signature_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_contract_signature_id_fk")
  user                                                              user                                                  @relation(fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_contract_user_id_fk")
  operation_contract_clause                                         operation_contract_clause[]
  operation_contract_document                                       operation_contract_document[]
  register_entry                                                    register_entry[]
  registered_letter_batch_association_legal_component               registered_letter_batch_association_legal_component[]
  signature_association_legal_component                             signature_association_legal_component[]

  @@index([contract_file_id], map: "operation_contract_contract_file_id_index")
  @@index([creation_time(sort: Desc)], map: "operation_contract_creation_time_index")
  @@index([creator_user_id], map: "operation_contract_creator_user_id_index")
  @@index([deletion_time], map: "operation_contract_deletion_time_index")
  @@index([model_id], map: "operation_contract_model_id_index")
  @@index([operation_id], map: "operation_contract_operation_id_index")
  @@index([project_file_id], map: "operation_contract_project_file_id_index")
  @@index([status])
  @@index([current_contract_reviews_task_id], map: "operation_contract_current_contract_reviews_task_id_index")
  @@index([current_contract_validation_task_id], map: "operation_contract_current_contract_validation_task_id_index")
  @@index([current_signature_id], map: "operation_contract_current_signature_id_index")
}

model operation_contract_clause {
  id                 Int                 @id(map: "operation_contract_clause_pk") @default(autoincrement())
  user_id            Int
  contract_id        Int?
  clause_id          String
  content            String?
  creation_time      DateTime            @default(now()) @db.Timestamp(6)
  last_update        DateTime?           @default(now()) @db.Timestamp(6)
  operation_contract operation_contract? @relation(fields: [contract_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_contract_clause_operation_contract_id_fk")
  user               user                @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_contract_clause_user_id_fk")

  @@unique([contract_id, clause_id], map: "operation_contract_clause_contract_id_jefferson_cla_key")
  @@index([clause_id], map: "operation_contract_clause_clause_id_index")
  @@index([contract_id], map: "operation_contract_clause_contract_id_index")
  @@index([user_id], map: "operation_contract_clause_user_id_index")
}

model operation_contract_document {
  id                     Int                    @id @default(autoincrement())
  contract_id            Int
  record_id              Int
  document_id            String
  state                  String
  operation_contract     operation_contract     @relation(fields: [contract_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  legal_component_record legal_component_record @relation(fields: [record_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict)

  @@index([contract_id], map: "operation_contract_document_contract_id_index")
  @@index([record_id], map: "operation_contract_document_record_id_index")
}

model operation_contract_model {
  id                    String                  @id(map: "operation_contract_model_pk")
  label                 String
  jefferson_path        String
  config_path           String
  main_contract         Boolean                 @default(false)
  origin_model_id       String?
  contract_model_clause contract_model_clause[]
  operation_contract    operation_contract[]
  organization_clause   organization_clause[]
}

model operation_default_record {
  id                                                                                                Int                       @id @default(autoincrement())
  record_id                                                                                         Int?
  user_id                                                                                           Int?
  organization_id                                                                                   Int
  creation_date                                                                                     DateTime?                 @default(now()) @db.Timestamp(6)
  is_organization_record                                                                            Boolean?                  @default(false)
  operation_template_id                                                                             String?
  link_template_id                                                                                  String?
  legal_component_template_operation_default_record_operation_template_idTolegal_component_template legal_component_template? @relation("operation_default_record_operation_template_idTolegal_component_template", fields: [operation_template_id], references: [id_str], onDelete: Restrict, onUpdate: Restrict, map: "operation_default_record_legal_component_template_id_str_fk")
  legal_component_template_operation_default_record_link_template_idTolegal_component_template      legal_component_template? @relation("operation_default_record_link_template_idTolegal_component_template", fields: [link_template_id], references: [id_str], onDelete: Restrict, onUpdate: Restrict, map: "operation_default_record_legal_component_template_id_str_fk_2")
  organization                                                                                      organization              @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  legal_component_record                                                                            legal_component_record?   @relation(fields: [record_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict)
  user                                                                                              user?                     @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict)

  @@unique([operation_template_id, link_template_id, record_id], map: "operation_default_record_operation_template_id_link_templat_idx")
  @@index([operation_template_id], map: "operation_default_record_operation_template_id_str_index")
  @@index([organization_id])
  @@index([record_id])
  @@index([user_id])
}

model operation_invitation {
  id              Int             @id(map: "operation_invitation_pk") @unique(map: "operation_invitation_id_uindex") @default(autoincrement())
  operation_id    Int
  email           String
  creator_user_id Int
  role_id         Int
  creation_time   DateTime        @default(now()) @db.Timestamp(6)
  legal_component legal_component @relation(fields: [operation_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_invitation_legal_component_id_fk")
  role            role            @relation(fields: [role_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_invitation_role_id_fk")
  user            user            @relation(fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_invitation_user_id_fk")

  @@unique([operation_id, email], map: "operation_invitation_operation_id_email_uindex")
  @@unique([operation_id, email], map: "operation_invitation_pk_2")
  @@index([creator_user_id], map: "operation_invitation_creator_user_id_index")
  @@index([operation_id], map: "operation_invitation_operation_id_index")
  @@index([email], map: "operation_invitation_email_index")
}

model operation_last_visited {
  id                        Int                       @id(map: "operation_last_visited_pk") @unique(map: "operation_last_visited_id_uindex") @default(autoincrement())
  user_id                   Int
  operation_id              Int
  last_access_time          DateTime?                 @db.Timestamp(6)
  organization_id           Int?
  legal_component_operation legal_component_operation @relation(fields: [operation_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "operation_last_visited_legal_component_operation_legal_componen")
  organization              organization?             @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_last_visited_organization_id_fk")
  user                      user                      @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "operation_last_visited_user_id_fk")

  @@unique([user_id, operation_id], map: "operation_last_visited_user_id_operation_id_uindex")
  @@index([id, organization_id], map: "operation_last_visited_id_organization_id_index")
  @@index([operation_id(sort: Desc)], map: "operation_last_visited_operation_id_index")
}

model operation_record_extension {
  id                                                                                  Int                       @id(map: "operation_record_extension_id_pk") @default(autoincrement())
  record_id                                                                           Int
  operation_id                                                                        Int
  extension                                                                           Int
  legal_component_record_operation_record_extension_extensionTolegal_component_record legal_component_record    @relation("operation_record_extension_extensionTolegal_component_record", fields: [extension], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "ore_extension_fk")
  legal_component_operation                                                           legal_component_operation @relation(fields: [operation_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "ore_operation_fk")
  legal_component_record_operation_record_extension_record_idTolegal_component_record legal_component_record    @relation("operation_record_extension_record_idTolegal_component_record", fields: [record_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "ore_record_fk")

  @@index([extension], map: "operation_record_extension_extension_index")
  @@index([operation_id], map: "operation_record_extension_operation_id_index")
  @@index([record_id], map: "operation_record_extension_record_id_index")
}

model order {
  id                  Int             @id(map: "order_pk") @unique(map: "order_id_uindex") @default(autoincrement())
  type                String
  data                Json?
  status              String
  creation_time       DateTime?       @db.Timestamp(6)
  cancelation_time    DateTime?       @db.Timestamp(6)
  payment_time        DateTime?       @db.Timestamp(6)
  completion_time     DateTime?       @db.Timestamp(6)
  creator_user_id     Int
  operation_id        Int
  organization_id     Int
  payment_url         String?
  invoice_file_id     String?
  payment_provider_id String?
  payment_type        String
  operation           legal_component @relation(fields: [operation_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "order_legal_component_id_fk")
  organization        organization    @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "order_organization_id_fk")
  creator_user        user            @relation(fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "order_user_id_fk")

  @@index([creator_user_id], map: "order_creator_user_id_index")
  @@index([operation_id], map: "order_operation_id_index")
  @@index([organization_id], map: "order_organization_id_index")
  @@map("order")
}

model organization {
  id                                                                                       Int                                           @id(map: "organization_PK") @unique(map: "notary_organization_id_uindex_tmp") @default(autoincrement())
  type                                                                                     String                                        @db.VarChar(40)
  unique_identifier                                                                        String
  name                                                                                     String?
  address                                                                                  Json                                          @db.Json
  rib                                                                                      String?
  email                                                                                    String?                                       @db.VarChar(254)
  creation_time                                                                            DateTime                                      @default(now()) @db.Timestamptz(6)
  latitude                                                                                 Float?
  longitude                                                                                Float?
  default_role_id                                                                          Int?
  admin_role_id                                                                            Int?
  deletion_time                                                                            DateTime?                                     @db.Timestamptz(6)
  api_key                                                                                  String                                        @unique(map: "organization_api_key_uindex") @default(dbgenerated("public.gen_random_uuid()"))
  subscription_id                                                                          Int?
  hubspot_id                                                                               String?
  last_update_time                                                                         DateTime?                                     @default(now()) @db.Timestamp(6)
  creator_user_id                                                                          Int?
  initial_setup_time                                                                       DateTime?                                     @default(now()) @db.Timestamp(6)
  portalys_params                                                                          Json?
  application                                                                              application[]
  archive_association_organization                                                         archive_association_organization[]
  contract_last_visited                                                                    contract_last_visited[]
  custom_view                                                                              custom_view[]
  document_request_template                                                                document_request_template[]
  internal_api_association                                                                 internal_api_association[]
  internal_api_key                                                                         internal_api_key[]
  invoice                                                                                  invoice[]
  invoice_config                                                                           invoice_config[]
  legal_component                                                                          legal_component[]
  operation_default_record                                                                 operation_default_record[]
  operation_last_visited                                                                   operation_last_visited[]
  order                                                                                    order[]
  file                                                                                     file?                                         @relation(fields: [rib], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_file_rib_fk")
  role_organization_admin_role_idTorole                                                    role?                                         @relation("organization_admin_role_idTorole", fields: [admin_role_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_role_permission_role_id_fk")
  subscription                                                                             subscription?                                 @relation(fields: [subscription_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  creator                                                                                  user?                                         @relation("OrganizationCreatorUser", fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_user_id_fk")
  organization_clause                                                                      organization_clause[]
  organization_credits_history                                                             organization_credits_history[]
  organization_feature                                                                     organization_feature[]
  organization_operation_status                                                            organization_operation_status[]
  organization_organization_organization_organization_parent_organization_idToorganization organization_organization[]                   @relation("organization_organization_parent_organization_idToorganization")
  organization_organization_organization_organization_sub_organization_idToorganization    organization_organization[]                   @relation("organization_organization_sub_organization_idToorganization")
  organization_role                                                                        organization_role[]
  organization_user                                                                        organization_user[]
  register_entry                                                                           register_entry[]
  role_role_organization_idToorganization                                                  role[]                                        @relation("role_organization_idToorganization")
  signature_association_organization_document                                              signature_association_organization_document[]

  @@index([admin_role_id], map: "organization_admin_role_id_index")
  @@index([default_role_id], map: "organization_default_role_id_index")
  @@index([rib], map: "organization_rib_index")
  @@index([type], map: "organization_type_index")
}

model organization_clause {
  id                       Int                      @id(map: "organization_clause_pk") @unique(map: "organization_clause_id_uindex") @default(autoincrement())
  organization_id          Int
  contract_model_id        String
  clause_id                String
  content                  String?
  user_id                  Int
  creation_time            DateTime                 @default(now()) @db.Timestamp(6)
  last_update              DateTime?                @default(now()) @db.Timestamp(6)
  operation_contract_model operation_contract_model @relation(fields: [contract_model_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_clause_operation_contract_model_id_fk")
  organization             organization             @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_clause_organization_id_fk")
  user                     user                     @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_clause_user_id_fk")

  @@unique([organization_id, contract_model_id, clause_id], map: "organization_clause_pk_2")
  @@index([contract_model_id], map: "organization_clause_contract_model_id_index")
  @@index([organization_id, contract_model_id], map: "organization_clause_organization_id_contract_model_id_index")
  @@index([organization_id], map: "organization_clause_organization_id_index")
  @@index([user_id], map: "organization_clause_user_id_index")
}

model organization_credits_history {
  id                 Int              @id(map: "organization_credits_history_pk") @default(autoincrement())
  type               String?          @db.VarChar(64)
  organization_id    Int
  credits            Int
  creation_time      DateTime         @default(now()) @db.Timestamptz(6)
  user_id            Int?
  label              String?
  legal_component_id Int?
  organization       organization     @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_credits_history_fk_organization_id")
  user               user?            @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_credits_history_fk_user_id")
  legal_component    legal_component? @relation(fields: [legal_component_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_credits_history_legal_component_id_fk_2")

  @@index([type], map: "organization_credits_history_type_index")
  @@index([user_id], map: "organization_credits_history_user_id_index")
}

model organization_feature {
  id                 Int              @id @default(autoincrement())
  organization_id    Int
  feature_id         String           @db.VarChar(32)
  feature_parameters Json             @default("{}")
  feature_state      Json             @default("{}")
  creation_time      DateTime         @default(now()) @db.Timestamptz(6)
  enabled            Boolean          @default(true)
  invoice            invoice[]
  invoice_config     invoice_config[]
  organization       organization     @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_feature_organization_id_fk")
  register_entry     register_entry[]

  @@index([feature_id], map: "organization_feature_feature_id_index")
  @@index([organization_id], map: "organization_feature_organization_id_index")
}

model organization_operation_status {
  id                                                                                           Int                         @id(map: "organization_operation_status_pk") @unique(map: "organization_operation_status_id_uindex") @default(autoincrement())
  label                                                                                        String
  creator_user_id                                                                              Int
  organization_id                                                                              Int
  creation_time                                                                                DateTime                    @default(now()) @db.Timestamp(6)
  template_id                                                                                  String
  legal_component_operation                                                                    legal_component_operation[]
  legal_component_template_organization_operation_status_template_idTolegal_component_template legal_component_template    @relation("organization_operation_status_template_idTolegal_component_template", fields: [template_id], references: [id_str], onDelete: Restrict, onUpdate: Restrict, map: "organization_operation_status_legal_template_id_str_fk")
  organization                                                                                 organization                @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_operation_status_organization_id_fk")
  user                                                                                         user                        @relation(fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_operation_status_user_id_fk")

  @@unique([organization_id, label, template_id], map: "organization_operation_status_pk_2")
}

model organization_organization {
  parent_organization_id                                                      Int
  sub_organization_id                                                         Int
  organization_organization_organization_parent_organization_idToorganization organization @relation("organization_organization_parent_organization_idToorganization", fields: [parent_organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_organization_fk_parent_organization")
  organization_organization_organization_sub_organization_idToorganization    organization @relation("organization_organization_sub_organization_idToorganization", fields: [sub_organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_organization_fk_sub_organization")

  @@id([parent_organization_id, sub_organization_id], map: "organization_organization_parent_organization_id_sub_organizati")
}

model organization_role {
  id              Int          @id(map: "organization_role_pk") @default(autoincrement())
  label           String
  context         Json         @default("{}") @db.Json
  organization_id Int
  organization    organization @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_role_organization_id_fk")

  @@index([organization_id], map: "organization_role_organization_id_index")
}

model organization_user {
  id                     Int          @id(map: "organization_user_PK") @default(autoincrement())
  organization_id        Int
  user_id                Int?
  admin                  Boolean?     @default(false)
  handle                 String?      @unique(map: "organization_user_handle_index") @db.VarChar(40)
  email                  String?
  creation_time          DateTime     @default(now()) @db.Timestamptz(6)
  cancelation_time       DateTime?    @db.Timestamptz(6)
  role_id                Int?
  is_deleted_from_kw_job Boolean?     @default(false)
  organization           organization @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_user_organization_fk")
  role                   role?        @relation(fields: [role_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_user_role_id_fk")
  user                   user?        @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "organization_user_user_fk")

  @@unique([organization_id, user_id], map: "organization_user_organization_id_user_id_index")
  @@index([role_id], map: "organization_user_role_id_index")
  @@index([user_id], map: "organization_user_user_id_index")
}

model permission {
  type                     String
  entity_type              String
  sub_entity_type          String                    @default("NONE")
  id                       Int                       @id(map: "permission_pk") @unique(map: "permission_id_uindex") @default(autoincrement())
  legal_template_id        Int?
  legal_component_template legal_component_template? @relation(fields: [legal_template_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "permission_legal_component_template_id_fk")
  role_permission          role_permission[]

  @@unique([type, entity_type, sub_entity_type], map: "permission_pk_2")
  @@index([entity_type], map: "permission_entity_type_index")
  @@index([sub_entity_type], map: "permission_sub_entity_type_index")
  @@index([type], map: "permission_type_index")
  @@index([legal_template_id], map: "permission_legal_template_id_index")
}

model planete_dossier {
  id                        String                     @id @default(uuid())
  operation_id              Int?
  planete_id                String?
  conversation_id           String?
  protocol                  String
  type                      String
  sub_type                  String?
  status                    String
  recipient_id              String?
  label                     String
  creation_time             DateTime                   @default(now()) @db.Timestamp(6)
  sent_time                 DateTime?                  @db.Timestamp(6)
  due_date                  DateTime?                  @db.Date
  last_modified_time        DateTime                   @db.Timestamp(6)
  user_id                   Int?
  legal_component_operation legal_component_operation? @relation(fields: [operation_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict)
  user                      user?                      @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  planete_historique        planete_historique[]
  planete_pj_recue          planete_pj_recue[]
  planete_produit           planete_produit[]

  @@index([operation_id], map: "planete_dossier_operation_id_index")
}

model planete_historique {
  id               String             @id @default(uuid())
  dossier_id       String
  origin_user_id   Int?
  origin_server    String?
  message_id       String?
  type             String
  description      String?
  subject          String?
  event_time       DateTime           @default(now()) @db.Timestamp(6)
  unread           Boolean            @default(true)
  description_obj  Json?              @db.Json
  planete_dossier  planete_dossier    @relation(fields: [dossier_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  user             user?              @relation(fields: [origin_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  planete_pj_recue planete_pj_recue[]

  @@index([dossier_id], map: "planete_historique_dossier_id_index")
  @@index([origin_user_id], map: "planete_historique_origin_user_id_index")
}

model planete_pj_recue {
  id                 String              @id @default(uuid())
  dossier_id         String
  content_file_id    String?
  historique_id      String?
  type               String
  label              String
  received_time      DateTime            @db.Timestamp(6)
  validity_date      DateTime?           @db.Date
  content_string     String?
  file               file?               @relation(fields: [content_file_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  planete_dossier    planete_dossier     @relation(fields: [dossier_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  planete_historique planete_historique? @relation(fields: [historique_id], references: [id], onDelete: Restrict, onUpdate: Restrict)

  @@index([content_file_id], map: "planete_pj_recue_content_file_id_index")
  @@index([dossier_id], map: "planete_pj_recue_dossier_id_index")
  @@index([historique_id], map: "planete_pj_recue_historique_id_index")
}

model planete_produit {
  id               String             @id @default(uuid())
  dossier_id       String
  label            String
  type             String
  status           String
  contents         Json               @db.Json
  planete_dossier  planete_dossier    @relation(fields: [dossier_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  planete_virement planete_virement[]

  @@index([dossier_id], map: "planete_produit_dossier_id_index")
  @@index([status], map: "planete_produit_status_index")
  @@index([type], map: "planete_produit_type_index")
}

model planete_virement {
  id                 String          @id @default(uuid())
  produit_id         String
  label              String?
  status             String
  montant            Decimal         @db.Decimal(12, 2)
  execution_date     DateTime        @db.Date
  numero             String?
  compte_client      String?
  demandeur          String?
  destinataire_code  String?
  destinataire_type  String
  memo               String?
  operation_id       Int?
  type_transaction   String
  iban_bban          String?
  iban_bic           String?
  iban_cle           String?
  iban_domiciliation String?
  iban_guichet       String?
  iban_pays          String?
  iban_titulaire     String?
  planete_produit    planete_produit @relation(fields: [produit_id], references: [id], onDelete: Restrict, onUpdate: Restrict)

  @@index([produit_id], map: "planete_virement_produit_id_index")
  @@index([status], map: "planete_virement_status_index")
}

model ref_comedec_commune {
  id               String   @id @default(uuid())
  code_insee       String
  code_departement String
  nom              String
  ancien_nom       String?
  service_vad      Boolean?
  service_vam      Boolean?
  service_van      Boolean?

  @@index([code_insee], map: "ref_comedec_commune_code_insee_index")
  @@index([nom], map: "ref_comedec_commune_nom_index")
  @@index([ancien_nom], map: "ref_comedec_commune_ancien_nom_index")
}

model ref_dgfip_services {
  sages String @id
  label String
  type  String
  iban  String
  bic   String
}

model ref_topad_commune {
  code             String   @id
  label            String
  code_departement String
  code_commune     String
  sages            String
  codes_postaux    String[]
}

model ref_topad_pays {
  code  String @id
  label String
}

model ref_update {
  id           String    @id
  update_time  DateTime  @default(now()) @db.Timestamp(6)
  checksum     String
  last_attempt DateTime? @db.Timestamp(6)
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model register_entry {
  id                   Int                  @id(map: "register_entry_pk") @default(autoincrement())
  type                 String
  status               String
  creation_time        DateTime             @default(now()) @db.Timestamp(6)
  creator_user_id      Int
  organization_id      Int
  feature_id           Int
  contract_id          Int?
  answer               Json
  operation_contract   operation_contract?  @relation(fields: [contract_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "register_entry_contract_id_fk")
  organization_feature organization_feature @relation(fields: [feature_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "register_entry_organization_feature_id_fk")
  organization         organization         @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "register_entry_organization_id_fk")
  user                 user                 @relation(fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "register_entry_user_id_fk")
}

model registered_letter {
  id                                               Int                     @id @default(autoincrement())
  provider_letter_id                               String?
  deposit_file_id                                  String?
  acceptation_file_id                              String?
  deposit_time                                     DateTime?               @db.Timestamptz(6)
  acceptation_time                                 DateTime?               @db.Timestamptz(6)
  type                                             String                  @db.VarChar(32)
  sender_user_id                                   Int
  reminder_sent_time                               DateTime?               @db.Timestamptz(6)
  sender                                           Json?                   @db.Json
  receiver                                         Json?                   @db.Json
  status                                           String                  @default("TO_SEND") @db.VarChar(40)
  letter_file_id                                   String?
  subject                                          String?
  provider_type                                    String?                 @db.VarChar(40)
  negligence_time                                  DateTime?               @db.Timestamptz(6)
  negligence_file_id                               String?
  creation_time                                    DateTime                @default(now()) @db.Timestamptz(6)
  batch_id                                         Int
  cancelation_time                                 DateTime?               @db.Timestamptz(6)
  receiver_update                                  Json?                   @db.Json
  refusal_time                                     DateTime?               @db.Timestamptz(6)
  refusal_file_id                                  String?
  archived                                         Boolean?                @default(false)
  file_registered_letter_letter_file_idTofile      file?                   @relation("registered_letter_letter_file_idTofile", fields: [letter_file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "registered_letter_FK_letter_file_id")
  file_registered_letter_acceptation_file_idTofile file?                   @relation("registered_letter_acceptation_file_idTofile", fields: [acceptation_file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "registered_letter_acceptation_file_id_fk")
  file_registered_letter_deposit_file_idTofile     file?                   @relation("registered_letter_deposit_file_idTofile", fields: [deposit_file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "registered_letter_deposit_file_id_fk")
  file_registered_letter_refusal_file_idTofile     file?                   @relation("registered_letter_refusal_file_idTofile", fields: [refusal_file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "registered_letter_file_id_fk")
  file_registered_letter_negligence_file_idTofile  file?                   @relation("registered_letter_negligence_file_idTofile", fields: [negligence_file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "registered_letter_negligence_file_id_fk")
  registered_letter_batch                          registered_letter_batch @relation(fields: [batch_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "registered_letter_registered_letter_batch_id_fk")
  user                                             user                    @relation(fields: [sender_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "registered_letter_user__fk")

  @@index([acceptation_file_id], map: "registered_letter_acceptation_file_id_index")
  @@index([batch_id], map: "registered_letter_batch_id_index")
  @@index([deposit_file_id], map: "registered_letter_deposit_file_id_index")
  @@index([letter_file_id], map: "registered_letter_letter_file_id_index")
  @@index([negligence_file_id], map: "registered_letter_negligence_file_id_index")
  @@index([provider_type], map: "registered_letter_provider_type_index")
  @@index([sender_user_id], map: "registered_letter_sender_user_id_index")
  @@index([status], map: "registered_letter_status_index")
  @@index([status, type], map: "registered_letter_status_type_index")
}

model registered_letter_batch {
  id                                                  Int                                                  @id(map: "registered_letter_batch_pk") @default(autoincrement())
  creation_time                                       DateTime                                             @default(now()) @db.Timestamptz(6)
  association_type                                    String
  creator_user_id                                     Int
  label                                               String?
  cancelation_time                                    DateTime?                                            @db.Timestamptz(6)
  contract                                            String?
  draft                                               Boolean?                                             @default(false)
  creation_context                                    Json?
  registered_letter                                   registered_letter[]
  user                                                user                                                 @relation(fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "registered_letter_batch_user_id_fk")
  registered_letter_batch_association_legal_component registered_letter_batch_association_legal_component?
  registered_letter_batch_subscriber                  registered_letter_batch_subscriber[]

  @@index([association_type], map: "registered_letter_batch_association_type_index")
  @@index([creator_user_id], map: "registered_letter_batch_provider_type_index")
}

model registered_letter_batch_association_legal_component {
  registered_letter_batch_id Int                     @id(map: "registered_letter_batch_association_legal_component_pk")
  legal_component_id         Int
  contract_id                Int
  signature_id               Int?
  external_api_status        String?
  legal_component            legal_component         @relation(fields: [legal_component_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "registered_letter_batch_association_legal_component_legal_fk")
  operation_contract         operation_contract      @relation(fields: [contract_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "registered_letter_batch_association_legal_component_operation_c")
  registered_letter_batch    registered_letter_batch @relation(fields: [registered_letter_batch_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "registered_letter_batch_association_legal_component_registered_")
  signature                  signature?              @relation(fields: [signature_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "registered_letter_batch_association_legal_component_sig_fk")

  @@index([external_api_status], map: "registered_letter_batch_asso_lc_external_api_status_index")
  @@index([contract_id], map: "registered_letter_batch_association_legal_component_contract_id")
  @@index([legal_component_id], map: "registered_letter_batch_association_legal_component_legal_compo")
  @@index([signature_id], map: "registered_letter_batch_association_legal_component_signature_i")
}

model registered_letter_batch_subscriber {
  id                         Int                      @id @default(autoincrement())
  email                      String
  registered_letter_batch_id Int?
  registered_letter_batch    registered_letter_batch? @relation(fields: [registered_letter_batch_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "registered_letter_batch_subscri_registered_letter_batch_id_fkey")

  @@index([registered_letter_batch_id], map: "registered_letter_batch_subscriber_batch_id_index")
}

model role {
  id                                              Int                       @id(map: "role_pk") @unique(map: "role_id_uindex") @default(autoincrement())
  name                                            String
  organization_id                                 Int
  is_admin                                        Boolean                   @default(false)
  is_default                                      Boolean                   @default(false)
  creation_time                                   DateTime                  @default(now()) @db.Timestamp(6)
  invitation_allowed_role_allowed_role_id         invitation_allowed_role[] @relation("allowed_role_id")
  invitation_allowed_role_role_id                 invitation_allowed_role[] @relation("role_id")
  operation_invitation                            operation_invitation[]
  organization_organization_admin_role_idTorole   organization[]            @relation("organization_admin_role_idTorole")
  organization_user                               organization_user[]
  organization_role_organization_idToorganization organization              @relation("role_organization_idToorganization", fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "role_organization_id_fk")
  role_permission                                 role_permission[]

  @@unique([organization_id, name])
  @@index([organization_id], map: "role_organization_id_index")
}

model role_permission {
  role_id       Int
  permission_id Int
  role          role       @relation(fields: [role_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "role_id_role_fk")
  permission    permission @relation(fields: [permission_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "role_permission_permission_id_fk")

  @@unique([role_id, permission_id])
  @@index([permission_id], map: "role_permission_permission_id_index")
  @@index([role_id], map: "role_permission_role_id_index")
}

model shared_folder {
  id                 Int                  @id(map: "shared_folder_pk") @default(autoincrement())
  handle             String               @unique(map: "shared_folder_handle_uindex") @default(dbgenerated("public.gen_random_uuid()")) @db.Uuid
  creation_time      DateTime             @default(now()) @db.Timestamptz(6)
  expiration_time    DateTime             @default(dbgenerated("(now() + '15 days'::interval)")) @db.Timestamptz(6)
  creator_user_id    Int
  recipient_email    String?
  name               String
  user               user                 @relation(fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "shared_folder_user_id_fk")
  shared_folder_file shared_folder_file[]

  @@index([creator_user_id], map: "shared_folder_creator_user_id_index")
}

model shared_folder_file {
  id               Int           @id(map: "shared_folder_file_pk") @default(autoincrement())
  shared_folder_id Int
  file_id          String
  path             String
  file             file          @relation(fields: [file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "shared_folder_file_file_id_fk")
  shared_folder    shared_folder @relation(fields: [shared_folder_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "shared_folder_file_shared_folder_id_fk")

  @@index([file_id], map: "shared_folder_file_file_id_index")
  @@index([shared_folder_id], map: "shared_folder_file_shared_folder_id_index")
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model signature {
  id                                                  Int                                                   @id @default(autoincrement())
  creator_user_id                                     Int?
  creation_time                                       DateTime                                              @default(now()) @db.Timestamptz(6)
  document_type                                       String                                                @db.VarChar(32)
  signature_time                                      DateTime?                                             @db.Timestamptz(6)
  cancelation_time                                    DateTime?                                             @db.Timestamptz(6)
  association_type                                    String                                                @db.VarChar(32)
  association_unique_id                               String?                                               @db.VarChar(40)
  document_name                                       String?
  document_type_label                                 String?
  provider_type                                       String                                                @db.VarChar(32)
  provider_id                                         String?
  token                                               String                                                @unique(map: "signature_token_uindex")
  deleted                                             Boolean                                               @default(false)
  email_template                                      Json?                                                 @default("{}") @db.Json
  expiration_time                                     DateTime?                                             @db.Timestamptz(6)
  creation_context                                    Json?
  method                                              String?                                               @default("SIMPLE")
  due_date                                            DateTime?                                             @db.Timestamptz(6)
  should_notify_after_signature                       Boolean?                                              @default(true)
  draft                                               Boolean?                                              @default(false)
  archived                                            Boolean?                                              @default(false)
  activation_user_id                                  Int?
  archive                                             archive[]
  operation_contract                                  operation_contract[]
  registered_letter_batch_association_legal_component registered_letter_batch_association_legal_component[]
  user                                                user?                                                 @relation("SignatureCreatorUser", fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "signature_fk_creator_user_id")
  activation_user                                     user?                                                 @relation("SignatureActivationUser", fields: [activation_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "signature_fk_activation_user_id")
  signature_association_legal_component               signature_association_legal_component[]
  signature_association_organization_document         signature_association_organization_document[]
  signature_file                                      signature_file[]
  signature_provider_type_association_paper           signature_provider_type_association_paper?
  signature_signatory                                 signature_signatory[]
  signature_subscriber                                signature_subscriber[]

  @@index([association_type], map: "signature_association_type_index")
  @@index([creation_time(sort: Desc)], map: "signature_creation_time_index")
  @@index([creator_user_id], map: "signature_creator_user_id_index")
  @@index([document_type], map: "signature_document_type_index")
  @@index([activation_user_id], map: "signature_activation_user_id_index")
}

model signature_association_legal_component {
  id                     Int                     @id @default(autoincrement())
  signature_id           Int
  legal_component_id     Int
  contract_id            Int?
  record_document_id     Int?
  document_id            String?
  external_api_status    String?
  legal_component_record legal_component_record? @relation(fields: [record_document_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "fk_signature_association_legal_component_FK_legal_component")
  legal_component        legal_component         @relation(fields: [legal_component_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "signature_association_legal_component_legal_component_id_fk")
  operation_contract     operation_contract?     @relation(fields: [contract_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "signature_association_legal_component_operation_contract_id_fk")
  signature              signature               @relation(fields: [signature_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "signature_association_legal_component_signature_id_fk")

  @@index([record_document_id], map: "fki_signature_association_legal_component_FK_legal_component")
  @@index([contract_id], map: "signature_association_legal_component_contract_id_index")
  @@index([external_api_status], map: "signature_association_legal_component_external_api_status_index")
  @@index([signature_id], map: "signature_association_legal_component_signature_id_index")
  @@index([legal_component_id], map: "signature_association_legal_component_user_id_index")
}

model signature_association_organization_document {
  id              Int          @id @default(autoincrement())
  signature_id    Int
  organization_id Int
  organization    organization @relation(fields: [organization_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "signature_association_organization_document_organization_id_fk")
  signature       signature    @relation(fields: [signature_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "signature_association_organization_document_signature_id_fk")

  @@index([signature_id], map: "signature_association_organization_document_signature_id_index")
}

model signature_file {
  id                                         Int       @id @default(autoincrement())
  signature_id                               Int
  to_sign_file_id                            String
  signed_file_id                             String?
  order                                      Int
  original_file_id                           String?
  provider_id                                String?
  is_contract                                Boolean   @default(false)
  file_signature_file_original_file_idTofile file?     @relation("signature_file_original_file_idTofile", fields: [original_file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "signature_file_file_id_fk")
  signature                                  signature @relation(fields: [signature_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "signature_file_fk_signature")
  file_signature_file_signed_file_idTofile   file?     @relation("signature_file_signed_file_idTofile", fields: [signed_file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "signature_file_fk_signed_file_id")
  file_signature_file_to_sign_file_idTofile  file      @relation("signature_file_to_sign_file_idTofile", fields: [to_sign_file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "signature_file_fk_to_sign_file_id")

  @@index([original_file_id], map: "signature_file_original_file_id_index")
  @@index([signature_id], map: "signature_file_signature_id_index")
  @@index([signed_file_id], map: "signature_file_signed_file_id_index")
  @@index([to_sign_file_id], map: "signature_file_to_sign_file_id_index")
  @@index([provider_id], map: "signature_file_provider_id_index")
}

model signature_provider_type_association_paper {
  id           Int        @id @default(autoincrement())
  signature_id Int?       @unique(map: "signature_provider_type_association_paper_signature_id_uindex")
  file_id      String?
  file         file?      @relation(fields: [file_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
  signature    signature? @relation(fields: [signature_id], references: [id], onDelete: Restrict, onUpdate: Restrict)
}

model signature_signatory {
  id                                              Int                                               @id @default(autoincrement())
  signature_id                                    Int
  signature_place                                 String                                            @db.VarChar(32)
  firstname                                       String
  lastname                                        String
  phone                                           String?
  email                                           String?                                           @db.VarChar(254)
  signature_time                                  DateTime?                                         @db.Timestamptz(6)
  order                                           Int
  last_reminder_time                              DateTime?                                         @db.Timestamptz(6)
  signature_order                                 Int?
  provider_id                                     String?
  proof_file_id                                   String?
  consent                                         Boolean                                           @default(false)
  file                                            file?                                             @relation(fields: [proof_file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "signature_signatory_fk_file_proof")
  signature                                       signature                                         @relation(fields: [signature_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "signature_signer_fk_signature")
  signature_signatory_association_legal_component signature_signatory_association_legal_component[]

  @@index([proof_file_id], map: "signature_signatory_proof_file_id_index")
  @@index([signature_id], map: "signature_signatory_signature_id_index")
  @@index([signature_place], map: "signature_signatory_signature_place_index")
  @@index([provider_id], map: "signature_signatory_provider_id_index")
}

model signature_signatory_association_legal_component {
  id                     Int                    @id @default(autoincrement())
  signature_signatory_id Int
  record_id              Int
  signature_signatory    signature_signatory    @relation(fields: [signature_signatory_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "signature_signatory_association_leg_signature_signatory_id_fkey")
  legal_component_record legal_component_record @relation(fields: [record_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict)

  @@index([signature_signatory_id], map: "signature_signatory_association_legal_component_signature_signa")
}

model signature_subscriber {
  id           Int        @id @default(autoincrement())
  email        String
  signature_id Int?
  signature    signature? @relation(fields: [signature_id], references: [id], onDelete: Restrict, onUpdate: Restrict)

  @@index([signature_id], map: "signature_subscriber_signature_id_index")
}

model subscription {
  id                         Int            @id(map: "subscription_pkey1") @default(autoincrement())
  status                     String
  payment_url                String
  plan_type                  String
  addons                     String[]       @default([])
  licence_count              Int            @default(0)
  provider_subscription_id   String?        @unique(map: "subscription_provider_subscription_id_uindex")
  creation_time              DateTime?      @default(now()) @db.Timestamp(6)
  tax_type                   String
  customer_lastname          String
  customer_firstname         String
  customer_civility          String
  customer_address           Json
  customer_siren             String
  customer_organization_name String
  customer_email             String
  licence_used_count         Int            @default(0)
  update_time                DateTime?      @db.Timestamptz(6)
  organization               organization[]

  @@index([id], map: "subscription_id_index")
  @@index([plan_type], map: "subscription_plan_type_index")
  @@index([status], map: "subscription_status_index")
}

model tableau_cities {
  id              Int      @id(map: "tableau_cities_pk")
  department_code Int?
  insee_code      Int?
  zip_code        Int?
  name            String?
  slug            String?
  gps_lat         Decimal? @db.Decimal
  gps_lng         Decimal? @db.Decimal
}

model task {
  id                                                                              Int                  @id(map: "task_pk") @default(autoincrement())
  legal_component_id                                                              Int
  creator_user_id                                                                 Int
  creation_time                                                                   DateTime             @default(now()) @db.Timestamptz(6)
  type                                                                            String
  title                                                                           String
  reference                                                                       Json                 @default("{}")
  due_date                                                                        DateTime?            @db.Timestamptz(6)
  reminder_type                                                                   String?
  completion_time                                                                 DateTime?            @db.Timestamptz(6)
  description                                                                     String?
  emails                                                                          String[]
  seen                                                                            Boolean?             @default(true)
  uuid                                                                            String?              @unique(map: "task_uuid_uindex")
  expiration_time                                                                 DateTime?            @db.Timestamptz(6)
  reminder_time                                                                   DateTime?            @db.Timestamptz(6)
  email                                                                           email[]
  operation_contract_operation_contract_current_contract_validation_task_idTotask operation_contract[] @relation("operation_contract_current_contract_validation_task_idTotask")
  operation_contract_operation_contract_current_contract_reviews_task_idTotask    operation_contract[] @relation("operation_contract_current_contract_reviews_task_idTotask")
  legal_component                                                                 legal_component      @relation(fields: [legal_component_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "task_legal_component_legal_component_id_fk")
  user                                                                            user                 @relation(fields: [creator_user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "task_user_id_fk")
  task_assignee                                                                   task_assignee[]

  @@index([creator_user_id], map: "task_creator_user_id_index")
  @@index([legal_component_id], map: "task_legal_component_id_index")
  @@index([reminder_type], map: "task_reminder_type_index")
  @@index([type], map: "task_type_index")
}

model task_assignee {
  task_id Int
  email   String
  seen    Boolean? @default(false)
  task    task     @relation(fields: [task_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "task_assignee_task_id_fk")

  @@id([task_id, email], map: "task_assignee_pk")
  @@index([task_id], map: "task_assignee_task_id_index")
}

model token {
  id              String   @id @db.VarChar(36)
  creation_time   DateTime @default(now()) @db.Timestamp(6)
  expiration_time DateTime @db.Timestamp(6)
  revoked         Boolean  @default(false)
  resource_id     String
  resource_type   String

  @@index([id], map: "token_id_index")
}

/// This model contains an expression index which requires additional setup for migrations. Visit https://pris.ly/d/expression-indexes for more info.
model user {
  id                                      Int                             @id(map: "user_PK") @default(autoincrement())
  email                                   String                          @unique(map: "user_email_index") @db.VarChar(254)
  password                                String?                         @db.VarChar(40)
  is_admin                                Boolean                         @default(false)
  firstname                               String
  lastname                                String
  verified                                Boolean                         @default(false)
  creation_time                           DateTime                        @default(now()) @db.Timestamptz(6)
  phone                                   String?
  civility                                String                          @db.VarChar(32)
  last_connection_time                    DateTime?                       @default(now()) @db.Timestamptz(6)
  code                                    String?
  inactive                                Boolean?                        @default(false)
  authentication_token                    String?                         @db.VarChar(40)
  salt                                    String                          @default(dbgenerated("encode(digest((random())::text, 'sha1'::text), 'hex'::text)")) @db.VarChar(40)
  admin_type                              String?                         @db.VarChar(32)
  last_authentication_time                DateTime?                       @db.Timestamp(6)
  certificate_dn                          String?
  profile_picture_file_id                 String?
  application                             application[]
  archive                                 archive[]
  archive_association_user                archive_association_user[]
  contract_last_visited                   contract_last_visited[]
  custom_view                             custom_view[]
  custom_view_favorite                    custom_view_favorite[]
  document_request_template               document_request_template[]
  drive_file                              drive_file[]
  drive_folder                            drive_folder[]
  file                                    file[]
  invoice                                 invoice[]
  legal_component                         legal_component[]
  lot_reservation                         lot_reservation[]
  operation_contract                      operation_contract[]
  operation_contract_clause               operation_contract_clause[]
  operation_default_record                operation_default_record[]
  operation_invitation                    operation_invitation[]
  operation_last_visited                  operation_last_visited[]
  order                                   order[]
  organization                            organization[]                  @relation("OrganizationCreatorUser")
  organization_clause                     organization_clause[]
  organization_credits_history            organization_credits_history[]
  organization_operation_status           organization_operation_status[]
  organization_user                       organization_user[]
  planete_dossier                         planete_dossier[]
  planete_historique                      planete_historique[]
  register_entry                          register_entry[]
  registered_letter                       registered_letter[]
  registered_letter_batch                 registered_letter_batch[]
  shared_folder                           shared_folder[]
  signature                               signature[]                     @relation("SignatureCreatorUser")
  signaturesActivation                    signature[]                     @relation("SignatureActivationUser")
  task                                    task[]
  file_user_profile_picture_file_idTofile file?                           @relation("user_profile_picture_file_idTofile", fields: [profile_picture_file_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "fk_user_profile_picture")
  user_metadata                           user_metadata?
  user_notification                       user_notification[]

  @@index([admin_type], map: "user_admin_type_index")
  @@index([civility], map: "user_civility_index")
}

model user_metadata {
  id          Int      @id @unique(map: "user_metadata_id_uindex") @default(autoincrement())
  user_id     Int      @unique(map: "user_metadata_pk")
  has_archive Boolean? @default(false)
  user        user     @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "user_metadata_user_id_fk")
}

model user_notification {
  id              Int          @id(map: "user_notification_pk") @default(autoincrement())
  notification_id Int
  user_id         Int
  seen            Boolean      @default(false)
  archived        Boolean      @default(false)
  notification    notification @relation(fields: [notification_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "user_notification_notification_id_fk")
  user            user         @relation(fields: [user_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "user_notification_user_id_fk")

  @@index([user_id], map: "user_notification_user_id_index")
}

model webhook {
  id                Int            @id(map: "webhook_pk") @unique(map: "webhook_id_uindex") @default(autoincrement())
  url               String?
  app_id            Int?
  authentication_id Int
  event_log         event_log[]
  application       application?   @relation(fields: [app_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "webhook_application_id_fk")
  authentication    authentication @relation(fields: [authentication_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "webhook_authentication_id_fk")
}

model invitation_allowed_role {
  id              Int  @id @default(autoincrement())
  allowed_role_id Int
  role_id         Int
  allowedRole     role @relation("allowed_role_id", fields: [allowed_role_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "invitation_allowed_role_allowed_role_id_fk")
  Role            role @relation("role_id", fields: [role_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "invitation_allowed_role_role_id_fk")

  @@unique([allowed_role_id, role_id], map: "invitation_allowed_role_unique_idx")
  @@index([role_id], map: "invitation_allowed_role_role_id_index")
}

model copropriete {
  numero_immatriculation             String                                @id
  address                            Json
  geo_point                          Unsupported("GEOGRAPHY(Point, 4326)")
  presence_syndic                    String?
  gestion_copropriete_syndic_pro_nom String?
  denomination                       String?
  nombre_lots                        Int
  construction                       String?

  @@index([geo_point], name: "idx_copropriete_geo_point")
}

model email {
  id                 Int                        @id @default(autoincrement())
  receiver           String
  operation_id       Int?
  invoice_id         Int?
  provider_id        String?                    @unique(map: "email_provider_id_uindex")
  type               String
  creation_time      DateTime                   @default(now()) @db.Timestamp(6)
  data               Json
  contract_id        Int?
  task_id            Int?
  invoice            invoice?                   @relation(fields: [invoice_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "email_invoice_id_fk")
  operation_contract operation_contract?        @relation(fields: [contract_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "email_operation_contract_id_fk")
  operation          legal_component_operation? @relation(fields: [operation_id], references: [legal_component_id], onDelete: Restrict, onUpdate: Restrict, map: "email_operation_id_fk")
  task               task?                      @relation(fields: [task_id], references: [id], onUpdate: Restrict, map: "email_task_id_fk")
  email_logs         email_log[]

  @@index([operation_id], map: "email_operation_id_index")
  @@index([invoice_id], map: "email_invoice_id_index")
  @@index([contract_id], map: "email_contract_id_index")
  @@index([task_id], map: "email_task_id_index")
}

model email_log {
  id            Int      @id @default(autoincrement())
  email_id      Int
  status        String
  creation_time DateTime @default(now()) @db.Timestamp(6)
  email         email    @relation(fields: [email_id], references: [id], onDelete: Restrict, onUpdate: Restrict, map: "email_log_email_id_fk")

  @@index([email_id], map: "email_log_email_id_index")
}

model orpi_contract {
  id                 String   @id
  allverified        String?
  archive            String?
  checked            String?
  clone_id           String?
  code_age           String
  completion         String?
  date_created       DateTime @db.Timestamp(6)
  date_last_modified DateTime @db.Timestamp(6)
  doctype            String?
  files              String?
  lre                String?
  modele             String?
  modele_id          String?
  numero             String?
  numero_avenant     String?
  reference          String?
  signed             String?
  state              String?
  team_id            String?
  version            String?
}
