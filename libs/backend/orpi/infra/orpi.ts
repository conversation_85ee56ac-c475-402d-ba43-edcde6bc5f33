export type TelemacError = { error: string };
export type TelemacDto<T> = T | TelemacError;

export interface CardPro {
  delivered_by: string;
  handling_of_funds: number;
  number: string;
  owner: string;
  type: string;
  validity: string | null;
}

interface Person {
  address: string | null;
  address_extra: string | null;
  city: string | null;
  email: string | null;
  email_orpi: string;
  firstname: string;
  in_documents: number;
  lastname: string;
  postal_code: string | null;
}

export interface FinancialGuarantee {
  address: string | null;
  address_2: string | null;
  city: string | null;
  expiration_date: string;
  name: string;
  number: string;
  postal_code: string | null;
  type: string;
  value: number;
}

export interface RcP {
  address: string;
  address_2: string | null;
  city: string;
  expiration_date: string;
  name: string;
  number: string;
  postal_code: string;
  type: string;
}

export interface OrpiMemberDto {
  contract_type: string | null;
  display_sao: number;
  email: string | null;
  email_orpi: string | null;
  end_date: string | null;
  function: string | null;
  id: number;
  mobile: string | null;
  name: string;
  nom: string;
  permissions: Array<string>;
  photo_path: string;
  prenom: string;
  role: string | null;
  rsac: string | null;
  rsac_city: string | null;
  rsac_postal_code: string | null;
  start_date: string | null;
  telephone: string | null;
}

interface Shareholder {
  address: string | null;
  address_extra: string | null;
  city: string | null;
  email: string | null;
  email_orpi: string;
  firstname: string;
  lastname: string;
  postal_code: string | null;
}

interface ShareholderCorporate {
  address: string | null;
  address_extra: string | null;
  city: string | null;
  name: string;
  postal_code: string | null;
  siret: string;
}

export interface OrpiAgencyDto {
  achievements: object;
  activity_types: string[];
  address: string;
  address_2: string | null;
  address_3: string | null;
  address_extra: string | null;
  address_extra_2: string | null;
  address_extra_3: string | null;
  adherent_id: number;
  adhesion_amepi: boolean | null;
  adhesion_fnaim: string | null;
  affiliate_link: string | null;
  agency_profile: string | null;
  agency_sold: string | null;
  agreement_number: string;
  bareme_pdf: string;
  capital: number;
  cartepro_delivered_by: string;
  cartepro_handling_of_funds: number;
  cartepro_number: string;
  cartepros: CardPro[];
  city: string;
  city_2: string | null;
  city_3: string | null;
  code: string;
  code_ape: string;
  company_name: string;
  company_type: string;
  contacts: OrpiMemberDto[];
  country: string;
  country_2: string | null;
  country_2_code: string | null;
  country_3: string | null;
  country_3_code: string | null;
  country_code: string;
  create_date: string | null;
  date_modified: string;
  date_network_entry: string;
  date_network_estimated_entry: string | null;
  date_network_estimated_exit: string | null;
  date_network_exit: string | null;
  date_network_resignation: string | null;
  email: string;
  exit_reason: string | null;
  facebook: string | null;
  fax: string | null;
  financial_guarantee_address: string | null;
  financial_guarantee_address_2: string | null;
  financial_guarantee_city: string | null;
  financial_guarantee_expiration_date: string;
  financial_guarantee_name: string;
  financial_guarantee_number: string;
  financial_guarantee_postal_code: string | null;
  financial_guarantee_value: number;
  financial_guarantees: FinancialGuarantee[];
  floor_adresse: number;
  funds_detention: number;
  gie: string;
  gie_name: string;
  gmb: string | null;
  google_group: string | null;
  headquarter_city: string;
  id: number;
  id_entity_sweepbright: string;
  id_mynotary: string | null;
  id_sweepbright: string;
  instagram: string | null;
  internet_name: string;
  linkedin: string | null;
  mediator_address: string | null;
  mediator_address_2: string | null;
  mediator_city: string | null;
  mediator_name: string | null;
  mediator_postal_code: string | null;
  member_number: string;
  modelo_code: string;
  modelo_key: string;
  name: string;
  non_detention_fond: number;
  officekey: string;
  opinion_system_company: string;
  opinion_system_key: string | null;
  orias: string | null;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  other_agencies: any[];
  patrons: string[];
  phone: string;
  photo_path: string;
  pinterest: string | null;
  postal_code: string;
  postal_code_2: string | null;
  postal_code_3: string | null;
  rcp_address: string;
  rcp_expiration_date: string;
  rcp_name: string;
  rcp_number: string;
  rcps: RcP[];
  rcs: string;
  rcs_address: string;
  rcs_city: string;
  register_date: string | null;
  responsable_legal: Person[];
  schedule: object;
  schedule_remarks: string;
  sequestre_account: string;
  sequestre_bank: string;
  service_types: string[];
  service_types_extra: string[];
  shareholders: Shareholder[];
  shareholders_corporate: ShareholderCorporate[];
  siret: string;
  site_internet_orpi: string;
  status: OrpiAgencyStatus;
  telephone_achat: string | null;
  telephone_alt_location: string | null;
  telephone_gestionlocative: string | null;
  telephone_immopro: string | null;
  telephone_leboncoin: string;
  telephone_lefigaro: string;
  telephone_location: string | null;
  telephone_locsais: string | null;
  telephone_main: string;
  telephone_orpicom: string | null;
  telephone_seloger: string;
  telephone_syndic: string | null;
  telephone_vente: string | null;
  tiktok: string | null;
  twitter: string | null;
  type: string;
  type_mandataire: string | null;
  vat_intracommunity: string;
  youtube: string | null;
}

export enum OrpiAgencyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

export interface OrpiUserDto {
  address: string | null;
  address_extra: string | null;
  agencies: Array<{ code: string; id: number; phone: string }>;
  city: string | null;
  country: string | null;
  country_code: string | null;
  date_modification: string;
  date_of_birth: string;
  email: string;
  email_extra: string | null;
  email_orpi: string;
  gender: 'M.' | 'Mme' | 'Mlle';
  id: number;
  id_user_intercom: number | null;
  id_user_sweepbright: number | null;
  inactive: number;
  is_deleted: number;
  mobile: string | null;
  nom: string;
  phone: string | null;
  photo_path: string;
  place_of_birth: string | null;
  postal_code: string | null;
  prenom: string;
  rsac: string | null;
  rsac_city: string | null;
  rsac_postal_code: string | null;
}

export interface OrpiPersonDto {
  agencies: OrpiAgencySmallDto[];
  email_orpi: string;
  gender: string;
  id: number;
  mobile: string | null;
  nom: string;
  phone: string | null;
  prenom: string;
}

interface OrpiAgencySmallDto {
  code: string;
  id: number;
  phone: string;
}
