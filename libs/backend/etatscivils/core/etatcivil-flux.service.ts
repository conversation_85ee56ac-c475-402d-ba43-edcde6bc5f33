import { EtatcivilService } from './etatcivil.service';
import {
  PlaneteAttachment,
  PlaneteHistoriqueServer,
  PlaneteHistoriqueType,
  PlanetePjRecueType,
  PlaneteSendParameters,
  PlaneteStatus,
  PlaneteTypeFlux,
  PlaneteUpdate,
  PlaneteUpdateType,
  SignAttachType,
  SignParameters
} from '@mynotary/crossplatform/api-adsn/api';
import {
  EtatCivilDemande,
  EtatCivilResponseCode,
  EtatCivilTypeActe,
  EtatCivilTypeDestinataire,
  PlaneteFluxEtatCivil,
  SignerRole
} from '@mynotary/crossplatform/api-etatscivils/api';
import { ParametresEtudeType, UpdatePlaneteProduitArgs } from '@mynotary/crossplatform/api-teleactes/api';
import { EtatcivilFluxProvider } from './etatcivil-flux.provider';
import { LegalsApiService } from '@mynotary/backend/legals/api';
import { UsersApiService } from '@mynotary/backend/users/api';
import { assertNotNull, Exception } from '@mynotary/crossplatform/shared/util';
import { Injectable } from '@nestjs/common';
import { DrivesApiService } from '@mynotary/backend/drives/api';
import { FilesClient } from '@mynotary/crossplatform/files-client/api';
import { getParametresEtude, PlaneteApiService } from '@mynotary/backend/planete/api';
import { OrganizationsApiService } from '@mynotary/backend/organizations/api';

@Injectable()
export class EtatcivilFluxService {
  constructor(
    private etatCivilService: EtatcivilService,
    private etatCivilFluxProvider: EtatcivilFluxProvider,
    private legalsApiService: LegalsApiService,
    private organizationsApiService: OrganizationsApiService,
    private usersApiService: UsersApiService,
    private planeteApiService: PlaneteApiService,
    private drivesApiService: DrivesApiService,
    private filesClient: FilesClient
  ) {}

  async getPlaneteEtatCivil(id: string) {
    const etatCivil = await this.etatCivilService.getEtatcivilById(id);
    switch (etatCivil.status) {
      case PlaneteStatus.DRAFT:
      case PlaneteStatus.TO_SIGN:
        return await this.getPlaneteEtatCivilForSigning(etatCivil);
      case PlaneteStatus.TO_SEND:
        return await this.getPlaneteEtatCivilForSending(etatCivil);
    }
  }

  async updatePlaneteEtatCivil({ id, message }: UpdatePlaneteProduitArgs) {
    const demande = await this.etatCivilService.getEtatcivilById(id);
    if (!message.updateType) {
      /* undefined message type straight from Planete */
      if (typeof message.data === 'string') {
        throw new Exception(`updatePlaneteEtatCivil : cannot determine type of Planete message ${demande.id}`);
      }
      /* technically these terms are reserved to Teleactes.
      Still:
      a response that tells us the request has been processed => similar to SI
      a response with an error code and message => similar to AR
      */
      message.updateType =
        message.data.dmg === PlaneteFluxEtatCivil.ETATCIVIL_DEMANDE_STATUT
          ? PlaneteUpdateType.AR
          : PlaneteUpdateType.SI;
    }
    switch (message.updateType) {
      case PlaneteUpdateType.SIGNED:
        await this.updatePlaneteSigned({ demande, update: message });
        break;
      case PlaneteUpdateType.SENT_OK:
      case PlaneteUpdateType.SENT_KO:
        await this.updatePlaneteSent({ demande, update: message });
        break;
      case PlaneteUpdateType.AR:
        await this.updatePlaneteStatus({ demande, update: message });
        break;
      case PlaneteUpdateType.SI:
        await this.updatePlaneteResponse({ demande, update: message });
        break;
      default:
        console.error(`PlaneteUpdateType not supported: ${message.updateType}`);
    }
  }

  private async getPlaneteEtatCivilForSigning(etatCivil: EtatCivilDemande) {
    if (!etatCivil.signUserId) {
      /* simulation mode */
      etatCivil.signUserId = etatCivil.userId;
      etatCivil.signerRealCardId = '1234567890';
      etatCivil.signerRole = SignerRole.NOTAIRE;
    }
    assertNotNull(etatCivil.signUserId, 'signUserId');
    const organization = await this.organizationsApiService.getOrganization({
      operationId: etatCivil.operationId
    });
    const parametresEtude = getParametresEtude(organization);
    const operation = await this.legalsApiService.getOperation(etatCivil.operationId);
    const signParam = this.createSignParameters(parametresEtude);
    const signUser = await this.usersApiService.findUser({ id: etatCivil.signUserId, type: 'by-id' });
    assertNotNull(signUser, 'signUser');

    const xml =
      etatCivil.version === 'V4'
        ? this.etatCivilFluxProvider.buildXmlMessageV4({
            demande: etatCivil,
            operation,
            parametres: parametresEtude,
            signUser
          })
        : this.etatCivilFluxProvider.buildXmlMessageV5({
            demande: etatCivil,
            operation,
            parametres: parametresEtude,
            signUser
          });

    return {
      document: xml,
      signParameters: signParam
    };
  }

  private async getPlaneteEtatCivilForSending(etatCivil: EtatCivilDemande) {
    assertNotNull(etatCivil.signUserId, 'signUserId');
    const organization = await this.organizationsApiService.getOrganization({
      operationId: etatCivil.operationId
    });
    const parametresEtude = getParametresEtude(organization);
    const operation = await this.legalsApiService.getOperation(etatCivil.operationId);
    const sendParam = this.createSendParameters(etatCivil);
    const signUser = await this.usersApiService.findUser({ id: etatCivil.signUserId, type: 'by-id' });
    assertNotNull(signUser, 'signUser');

    /* fetch signed document from PJ */
    const pjs = await this.planeteApiService.findPlanetePjRecue({
      dossierId: etatCivil.id,
      type: PlanetePjRecueType.SIGNED_XML
    });
    if (pjs.length === 0 || !pjs[0].content) {
      throw new Exception(`No signed document found for product ${etatCivil.id}`);
    }

    const xmlEnveloppe =
      etatCivil.version === 'V4'
        ? this.etatCivilFluxProvider.buildXmlEnvelopeV4({
            demande: etatCivil,
            operation,
            parametres: parametresEtude,
            signUser
          })
        : this.etatCivilFluxProvider.buildXmlEnvelopeV5({
            demande: etatCivil,
            operation,
            parametres: parametresEtude,
            signUser
          });

    return {
      documents: [
        {
          attachment: pjs[0].content,
          contentType: 'text/xml; Charset=UTF-8',
          id: 'demande'
        },
        {
          attachment: xmlEnveloppe,
          contentType: 'text/xml; Charset=UTF-8',
          id: 'enveloppeDemande'
        }
      ],
      sendParameters: sendParam
    };
  }

  private createSignParameters(parametresEtude: ParametresEtudeType): SignParameters {
    return {
      location: {
        city: parametresEtude.adresse.commune,
        country: 'France',
        postalCode: parametresEtude.adresse.codePostal
      },
      profile: 'SigREAL_ETAT_CIVIL',
      xmlParams: {
        attachType: SignAttachType.ENVELOPED,
        documentUri: 'http://xml.ants.interieur.gouv.fr/schema',
        signatureId: 'actionRef'
      }
    };
  }

  private async updatePlaneteSigned({ demande, update: { data, userId } }: UpdateEtatCivilTypedArgs) {
    try {
      assertNotNull(demande.id, 'produitPlanete.id');

      /* add entry to history */
      const histoId = await this.planeteApiService.createPlaneteHistorique({
        dossierId: demande.id,
        eventTime: new Date(),
        originUserId: userId,
        subject: demande.label,
        type: PlaneteHistoriqueType.SIGNED
      });
      /* save signed document */
      if (typeof data === 'string') {
        await this.planeteApiService.createPlanetePjRecue({
          content: data,
          dossierId: demande.id,
          historiqueId: histoId,
          label: 'Flux XML signé',
          receivedTime: new Date(),
          type: PlanetePjRecueType.SIGNED_XML
        });
      }
      /* update status */
      await this.etatCivilService.updateEtatcivil({ ...demande, status: PlaneteStatus.SIGNED });
    } catch (e) {
      throw new Exception(`Error updating demande ${demande.id} with signed document`, { cause: e });
    }
  }

  private async updatePlaneteSent({ demande, update }: UpdateEtatCivilTypedArgs) {
    //
    try {
      const ok = update.updateType === PlaneteUpdateType.SENT_OK;
      /* add entry to history */
      await this.planeteApiService.createPlaneteHistorique({
        descriptionObj: {
          errorCode: update.errorCode,
          errorDetailedCode: update.detailedErrorCode,
          errorMsg: update.data,
          ok: update.updateType === PlaneteUpdateType.SENT_OK
        },
        dossierId: demande.id,
        eventTime: new Date(),
        originServer: PlaneteHistoriqueServer.PLANETE,
        subject: demande.label,
        type: PlaneteHistoriqueType.SENT
      });

      /* update status */
      await this.etatCivilService.updateEtatcivil({
        ...demande,
        status: ok ? PlaneteStatus.SENT : PlaneteStatus.SIGNED
      });
    } catch (e) {
      throw new Exception(`Error updating produit ${demande.id} with signed document`, { cause: e });
    }
  }

  private createSendParameters(etatCivil: EtatCivilDemande): PlaneteSendParameters {
    const type: PlaneteTypeFlux =
      etatCivil.typeDestinataire === EtatCivilTypeDestinataire.SCEC
        ? PlaneteTypeFlux.ETATCIVIL_DEMANDE_SSII_V1
        : etatCivil.type === EtatCivilTypeActe.VAN
          ? PlaneteTypeFlux.ETATCIVIL_DEMANDE_VAN_SSII_V5
          : [EtatCivilTypeActe.VAD, EtatCivilTypeActe.CAD].includes(etatCivil.type)
            ? PlaneteTypeFlux.ETATCIVIL_DEMANDE_VAD_SSII_V5
            : PlaneteTypeFlux.ETATCIVIL_DEMANDE_VAM_SSII_V5;

    return {
      balDest: etatCivil.typeDestinataire === EtatCivilTypeDestinataire.ANTS ? 'satellite_etatcivil' : 'scec',
      planeteId: etatCivil.idFluxPlanete ?? '0_FAKE_0_0', // no value => test only, faking the value for now
      type
    };
  }

  private async updatePlaneteStatus({ demande, update }: UpdateEtatCivilTypedArgs) {
    if (typeof update.data === 'string') {
      throw new Exception(`updatePlaneteEtatCivil : cannot determine type of Planete message ${demande.id}`);
    }
    const statusPayload = update.data.pjs[0].content;
    // parse payload, get status code and Motif
    const { code, motif } = this.etatCivilFluxProvider.parseStatusPayload(statusPayload);
    /* add entry to history */
    await this.planeteApiService.createPlaneteHistorique({
      descriptionObj: {
        errorCode: code,
        errorMsg: motif
      },
      dossierId: demande.id,
      eventTime: new Date(),
      originServer: PlaneteHistoriqueServer.PLANETE,
      subject: demande.label,
      type: PlaneteHistoriqueType.ERROR_RECEIVED
    });

    /* update status */
    await this.etatCivilService.updateEtatcivil({
      ...demande,
      status: PlaneteStatus.REFUSED
    });
  }

  private async updatePlaneteResponse({ demande, update }: UpdateEtatCivilTypedArgs) {
    try {
      if (typeof update.data !== 'string') {
        const envelope = update.data.pjs.find((pj) => pj.code === 'enveloppeReponse');
        assertNotNull(envelope, 'enveloppeReponse');
        assertNotNull(envelope.content, 'enveloppeReponse content');
        const responseDetails = this.etatCivilFluxProvider.parseResponseEnvelope(
          Buffer.from(envelope.content, 'base64').toString('utf8')
        );
        /* add entry to history */
        const histoId = await this.planeteApiService.createPlaneteHistorique({
          descriptionObj: {
            errorCode: update.errorCode,
            errorDetailedCode: update.detailedErrorCode,
            errorMsg: update.data,
            responseDetails
          },
          dossierId: demande.id,
          eventTime: new Date(),
          originServer: PlaneteHistoriqueServer.PLANETE,
          subject: demande.label,
          type: PlaneteHistoriqueType.RESPONSE_RECEIVED
        });

        /* update status */
        await this.etatCivilService.updateEtatcivil({
          ...demande,
          status: responseDetails.code === EtatCivilResponseCode.FOUND ? PlaneteStatus.RECEIVED : PlaneteStatus.REJECTED
        });

        /* save response documents */
        await this.saveAttachments({
          date: new Date(responseDetails.date),
          demande,
          histoId,
          pjs: update.data.pjs
        });
      }
    } catch (e) {
      throw new Exception(`Error updating produit ${demande.id} with error status`, { cause: e });
    }
  }

  private async saveAttachments({ date, demande, histoId, pjs }: SaveAttachmentsArgs) {
    const enveloppeReponse = pjs.find((pj) => pj.code === 'enveloppeReponse');
    const pdf = pjs.find((pj) => pj.code === 'reponse');
    const signature = pjs.find((pj) => pj.code === 'signature');

    if (enveloppeReponse && enveloppeReponse.content) {
      /* save enveloppe in PlanetePjRecue directly */
      await this.planeteApiService.createPlanetePjRecue({
        content: Buffer.from(enveloppeReponse.content, 'base64').toString('utf8'),
        dossierId: demande.id,
        historiqueId: histoId,
        label: enveloppeReponse.code,
        receivedTime: date,
        type: PlanetePjRecueType.ENVELOPPE_REPONSE
      });
    }

    if (pdf && pdf.content) {
      /* save PDF as a Drive file */
      const pdfLabel = `Réponse à ${demande.label}`;
      const pdfFile = await this.filesClient.createFile({ name: pdfLabel });
      await this.drivesApiService.createDriveFile({
        documentLabel: pdfLabel,
        fileId: pdfFile.id,
        operationId: demande.operationId,
        userId: demande.userId
      });
      assertNotNull(pdf.content, 'PDF content');
      await this.filesClient.uploadFile({
        data: Buffer.from(pdf.content, 'base64'),
        fileId: pdfFile.id,
        mimetype: pdf.type,
        uploadUrl: pdfFile.uploadUrl
      });
      await this.planeteApiService.createPlanetePjRecue({
        contentFileId: pdfFile.id,
        dossierId: demande.id,
        historiqueId: histoId,
        label: pdfLabel,
        receivedTime: date,
        type: PlanetePjRecueType.DOCUMENT
      });

      /* If PDF is an ANTS response, extract metadata and store it as a separate PJ */
      if (demande.typeDestinataire === EtatCivilTypeDestinataire.ANTS) {
        try {
          const { mentions, responseXml } = this.etatCivilFluxProvider.extractResponseFromPdf(
            Buffer.from(pdf.content, 'base64')
          );
          await this.planeteApiService.createPlanetePjRecue({
            content: responseXml,
            dossierId: demande.id,
            historiqueId: histoId,
            label: 'Données structurées de la réponse',
            receivedTime: date,
            type: PlanetePjRecueType.REPONSE_STRUCTUREE
          });

          if (mentions) {
            const histo = (await this.planeteApiService.findPlaneteHistorique({ id: histoId }))[0];
            await this.planeteApiService.updatePlaneteHistorique({
              ...histo,
              descriptionObj: { ...histo.descriptionObj, mentions }
            });
          }
        } catch (e) {
          console.error(e); /* invalid PDF, no structured data found */
        }
      }
    }

    if (signature && signature.content) {
      /* save signature in PlanetePjRecue directly */
      await this.planeteApiService.createPlanetePjRecue({
        content: Buffer.from(signature.content, 'base64').toString('utf8'),
        dossierId: demande.id,
        historiqueId: histoId,
        label: signature.code,
        receivedTime: date,
        type: PlanetePjRecueType.SIGNATURE
      });
    }
  }

  async updatePlaneteRetour({ id, message }: UpdatePlaneteProduitArgs) {
    try {
      /* get PlaneteDossier from conversation id */
      const dossiers = await this.planeteApiService.findPlaneteDossier({ conversationId: id });
      if (dossiers.length === 0) {
        throw new Exception(`No dossier found for conversation id ${id}`);
      }
      const dossier = dossiers[0];
      await this.updatePlaneteEtatCivil({ id: dossier.id, message });
    } catch (e) {
      throw new Exception('Error uploading retour planete', { cause: e });
    }
  }

  async getEtatCivilResponse(id: string) {
    const pjs = await this.planeteApiService.findPlanetePjRecue({
      dossierId: id,
      type: PlanetePjRecueType.REPONSE_STRUCTUREE
    });
    if (pjs.length === 0) {
      throw new Exception('No structured response stored for this Demande');
    }
    const responseXml = pjs[0].content;
    return this.etatCivilFluxProvider.parseResponse(responseXml);
  }
}

type UpdateEtatCivilTypedArgs = {
  demande: EtatCivilDemande;
  update: PlaneteUpdate;
};

type SaveAttachmentsArgs = {
  date: Date;
  demande: EtatCivilDemande;
  histoId: string;
  pjs: PlaneteAttachment[];
};
