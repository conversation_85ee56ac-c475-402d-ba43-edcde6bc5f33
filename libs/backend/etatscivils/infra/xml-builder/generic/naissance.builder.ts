import {
  <PERSON><PERSON><PERSON>,
  NestedBuilder,
  PlaneteFluxConflicting<PERSON><PERSON>ues<PERSON>rror,
  Validator
} from '@mynotary/backend/shared/xml-builder-util';
import {
  ETATCIVIL_PATTERN_ALPHA,
  ETATCIVIL_PATTERN_ALPHANUM,
  ETATCIVIL_PATTERN_ANNEE,
  ETATCIVIL_PATTERN_JOUR,
  ETATCIVIL_PATTERN_MOIS,
  EtatCivilLibelleCommuneLength
} from '@mynotary/backend/etatscivils/core';

export class NaissanceBuilder<P extends Builder<unknown>> extends NestedBuilder<P, NaissanceData, NaissanceValidator> {
  constructor(parent: P) {
    super({
      initialValues: {},
      newValidator: () => new NaissanceValidator(),
      parent,
      startingElementName: 'naissance'
    });
  }

  withJour(jour?: string): this {
    this.data.jour = jour;
    return this;
  }

  withMois(mois?: string): this {
    this.data.mois = mois;
    return this;
  }

  with<PERSON><PERSON><PERSON>(annee?: string): this {
    this.data.annee = annee;
    return this;
  }

  withLibellePays(libellePays?: string): this {
    this.data.libellePays = libellePays;
    return this;
  }

  withCodeCommune(codeCommune?: string): this {
    this.data.codeCommune = codeCommune;
    return this;
  }

  withLibelleCommune(libelleCommune?: string): this {
    this.data.libelleCommune = libelleCommune;
    return this;
  }

  withLibelleCommuneLength(libelleCommuneLength: EtatCivilLibelleCommuneLength): this {
    this.data.libelleCommuneLength = libelleCommuneLength;
    return this;
  }

  withDepartement(departement?: string): this {
    this.data.departement = departement;
    return this;
  }

  protected buildImpl(): void {
    // prettier-ignore
    this.getStartingElement()
      .node('date')
        .optionalNode('jour', this.data.jour).parent()
        .optionalNode('mois', this.data.mois).parent()
        .optionalNode('annee', this.data.annee).parent()
        .parent()
      .node('commune')
        .optionalNode('libelle_pays', this.data.libellePays).parent()
        .optionalNode('code_commune', this.data.codeCommune).parent()
        .optionalNode('libelle_commune', this.data.libelleCommune).parent()
        .optionalNode('departement', this.data.departement)
    ;
  }
}

class NaissanceValidator extends Validator<NaissanceData> {
  validate(data: NaissanceData): void {
    this.validateString(data.annee, 'annee', false, 4, ETATCIVIL_PATTERN_ANNEE);
    this.validateString(data.mois, 'mois', false, 2, ETATCIVIL_PATTERN_MOIS);
    this.validateString(data.jour, 'jour', false, 2, ETATCIVIL_PATTERN_JOUR);

    this.validateString(data.libellePays, 'libellePays', false, 50, ETATCIVIL_PATTERN_ALPHA);
    this.validateString(data.codeCommune, 'codeCommune', false, 5, ETATCIVIL_PATTERN_ALPHANUM);
    this.validateMandatory(data.libelleCommuneLength, 'libelleCommuneLength');
    this.validateString(
      data.libelleCommune,
      'libelleCommune',
      false,
      data.libelleCommuneLength,
      ETATCIVIL_PATTERN_ALPHANUM
    );
    this.validateString(data.departement, 'departement', false, 3, ETATCIVIL_PATTERN_ALPHANUM);

    if ((data.jour && (!data.mois || !data.annee)) || (data.mois && !data.annee)) {
      throw new PlaneteFluxConflictingValuesError({ annee: data.annee, jour: data.jour, mois: data.mois }, 'RGD403');
    }
  }
}

type NaissanceData = {
  annee: string;
  codeCommune?: string;
  departement?: string;
  jour?: string;
  libelleCommune: string;
  libelleCommuneLength: EtatCivilLibelleCommuneLength;
  libellePays: string;
  mois?: string;
};
