import { provideReferentielsScope } from '@mynotary/backend/referentiels/providers';
import {
  ComedecCommuneRepository,
  ReferentielsRepository,
  ServiceDgfipRepository,
  TopadCommuneRepository,
  TopadPaysRepository
} from '@mynotary/backend/referentiels/core';

import { ReferentielsRepositoryFake } from './referentiels.repository.fake';
import { TopadPaysRepositoryFake } from './topad-pays.repository.fake';
import { TopadCommuneRepositoryFake } from './topad-commune.repository.fake';
import { ServiceDgfipRepositoryFake } from './service-dgfip.repository.fake';
import { ComedecCommuneRepositoryFake } from './comedec-commune.repository.fake';

export const provideReferentielsTest = () => {
  return [
    ...provideReferentielsScope(),
    { provide: ReferentielsRepository, useClass: ReferentielsRepositoryFake },
    { provide: TopadPaysRepository, useClass: TopadPaysRepositoryFake },
    { provide: TopadCommuneRepository, useClass: TopadCommuneRepositoryFake },
    { provide: ServiceDgfipRepository, useClass: ServiceDgfipRepositoryFake },
    { provide: ComedecCommuneRepository, useClass: ComedecCommuneRepositoryFake }
  ];
};
