import { Controller, Get, UseGuards } from '@nestjs/common';
import { HasLegalLinkAnonymousAccess } from '@mynotary/backend/legals/authorization';
import { ResourceTokenType } from '@mynotary/crossplatform/authorizations/api';
import { provideLegalsTest } from '../index';
import { DecodedMnToken } from '@mynotary/backend/authentications/api';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';

describe(HasLegalLinkAnonymousAccess.name, () => {
  it('should deny access if token is not valid', async () => {
    const { client, setValidToken } = await setup();

    setValidToken(undefined);
    const response = await client.get(`/links/1`).send();

    expect(response.statusCode).toBe(403);
  });

  it('should deny access when target legal link does not exist', async () => {
    const { client, record, setValidToken } = await setup();

    setValidToken(record.id);
    const INVALID_LINK_ID = '-1';

    const response = await client.get(`/links/${INVALID_LINK_ID}`).send();

    expect(response.statusCode).toBe(403);
  });

  it('should deny access if token resource id dont match ids in legal branches', async () => {
    const { client, record, setValidToken, testingRepos, user } = await setup();

    setValidToken(record.id);

    const newRecordNotAssociatedToToken = await testingRepos.records.createRecord({
      organizationId: user.organizationId,
      userId: user.userId
    });

    const branch = await testingRepos.legalBranches.createLegalBranch({
      fromLegalId: newRecordNotAssociatedToToken.id,
      organizationId: user.organizationId,
      type: 'EPOUX',
      userId: user.id
    });

    const response = await client.get(`/links/${branch.linkId}`).send();

    expect(response.statusCode).toBe(403);
  });

  it('should allow access when token match legal record attach to legal link', async () => {
    const { client, record, setValidToken, testingRepos, user } = await setup();

    setValidToken(record.id);

    const link = await testingRepos.legalLinks.createLegalLink({
      organizationId: user.organizationId,
      recordId: record.id,
      userId: user.id
    });

    const response = await client.get(`/links/${link.id}`).send();

    expect(response.statusCode).toBe(200);
  });

  it('should allow access when legal link has branches fromId', async () => {
    const { client, record, setValidToken, testingRepos, user } = await setup();

    setValidToken(record.id);

    const branch = await testingRepos.legalBranches.createLegalBranch({
      fromLegalId: record.id,
      organizationId: user.organizationId,
      type: 'EPOUX',
      userId: user.id
    });

    const response = await client.get(`/links/${branch.linkId}`).send();

    expect(response.statusCode).toBe(200);
  });

  it('should allow access when target legal link has branches toId', async () => {
    const { client, record, setValidToken, testingRepos, user } = await setup();

    setValidToken(record.id);

    const branch = await testingRepos.legalBranches.createLegalBranch({
      fromLegalId: 'another_record',
      organizationId: user.organizationId,
      toId: record.id,
      type: 'EPOUX',
      userId: user.id
    });

    const response = await client.get(`/links/${branch.linkId}`).send();

    expect(response.statusCode).toBe(200);
  });

  async function setup() {
    const { client, getService, setResourceToken } = await createTestingWideApp({
      bypassAuth: false,
      controller: TestController,
      providers: provideLegalsTest()
    });
    const testingRepos = getService(TestingRepositories);

    const user = await testingRepos.createMember({ permissions: [] });

    const record = await testingRepos.records.createRecord({
      organizationId: user.organizationId,
      userId: user.userId
    });

    return {
      client,
      record,
      setValidToken: (resourceId?: string) => {
        const token: DecodedMnToken = {
          creationTime: new Date(),
          expirationTime: new Date(),
          id: 'valid_token',
          issuer: 'issuer',
          resourceId: resourceId ?? '',
          resourceType: ResourceTokenType.LEGAL_RECORD
        };

        setResourceToken(token);
      },
      testingRepos,
      user
    };
  }
});

@Controller()
class TestController {
  @UseGuards(HasLegalLinkAnonymousAccess())
  @Get('/links/:linkId')
  getValue() {
    return '👌';
  }
}
