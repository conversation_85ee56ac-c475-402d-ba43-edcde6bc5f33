import { Injectable } from '@nestjs/common';
import { RegisterEntriesRepository, GetRegisterEntriesArgs } from './register-entries.repository';
import { RegisterEntryStatus, RegisterEntry, RegisterEntryType } from './register-entries';
import { RegisterAuthorizationsService } from './register-authorizations.service';
import { PermissionType } from '@mynotary/crossplatform/roles/api';

@Injectable()
export class RegisterEntriesService {
  constructor(
    private registerEntriesRepository: RegisterEntriesRepository,
    private registerAuthorizationsService: RegisterAuthorizationsService
  ) {}

  async findRegisterEntry(args: FindRegisterEntryArgs): Promise<RegisterEntry | null> {
    const entries = await this.registerEntriesRepository.getRegisterEntries({
      contractId: args.contractId,
      page: 0,
      pageSize: 1,
      status: args.status,
      type: args.type,
      userId: args.userId
    });

    if (entries.length === 0) {
      return null;
    }

    return entries[0];
  }

  async updateRegisterEntry(updateEntry: UpdateRegisterEntryArgs): Promise<void> {
    await this.registerEntriesRepository.updateRegisterEntry(updateEntry);
  }

  async getRegisterEntries(args: GetRegisterEntriesArgs): Promise<RegisterEntry[]> {
    // If organizationId is provided, check if user has permission to view organization register entries
    if (!args.type)
      if (args.organizationId && args.userId) {
        const hasPermission = await this.checkOrganizationRegisterPermission({
          organizationId: args.organizationId,
          registerType: args.type,
          userId: args.userId
        });

        // If user doesn't have permission, remove organizationId and only search by userId
        if (!hasPermission) {
          const modifiedArgs = { ...args };
          delete modifiedArgs.organizationId;
          return await this.registerEntriesRepository.getRegisterEntries(modifiedArgs);
        }
      }

    return await this.registerEntriesRepository.getRegisterEntries(args);
  }

  private async checkOrganizationRegisterPermission({
    organizationId,
    registerType,
    userId
  }: {
    organizationId: string;
    registerType: RegisterEntryType;
    userId: string;
  }): Promise<boolean> {
    // if (registerType) {
    return await checkOrganizationRegisterTypePermission(
      registerType,
      userId,
      organizationId,
      this.registerAuthorizationsService
    );
    // } else {
    //   const allTypes = Object.values(RegisterEntryType); // If no specific register type is provided, we need to check all register types
    //   return await Promise.all(
    //     allTypes.map((type) =>
    //       checkOrganizationRegisterTypePermission(type, userId, organizationId, this.registerAuthorizationsService)
    //     )
    //   ).then((results) => results.every(Boolean));
    // }
  }
}

async function checkOrganizationRegisterTypePermission(
  registerType: RegisterEntryType,
  userId: string,
  organizationId: string,
  registerAuthorizationsService: RegisterAuthorizationsService
): Promise<boolean> {
  if (!registerType) {
    return false;
  }
  try {
    return await registerAuthorizationsService.hasRegisterAccess({
      organizationId,
      permissionType: PermissionType.READ_ORGANIZATION_REGISTER,
      registerType: registerType,
      shouldBeOwner: false,
      userId
    });
  } catch (error) {
    return false;
  }
}

export interface UpdateRegisterEntryArgs {
  id: string;
  observations?: string;
  status?: RegisterEntryStatus;
}

export interface FindRegisterEntryArgs {
  contractId: string;
  page?: number;
  pageSize?: number;
  status: RegisterEntryStatus;
  type: RegisterEntryType;
}
