import { RegisterEntriesController } from '@mynotary/backend/registers/feature';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import { provideMembersTest } from '@mynotary/backend/members/test';
import { provideRegistersTest } from '../index';
import { RegisterEntryStatus, RegisterEntryType } from '@mynotary/backend/registers/core';
import { EntityType, PermissionType } from '@mynotary/crossplatform/roles/api';
import { PlanType } from '@mynotary/crossplatform/billings/api';
import { FeatureType } from '@mynotary/crossplatform/features/api';

describe(RegisterEntriesController.name, () => {
  it('should retrieve user entries only', async () => {
    const { client, members } = await setup();

    const response = await client
      .get(`/register-entries`)
      .query({ page: 1, pageSize: 10, type: 'TRANSACTION', userId: members.uniqueMember.userId })
      .send();

    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(1);
    expect(response.body.items[0]).toEqual({
      answer: expect.any(Object),
      contractId: expect.any(String),
      creationTime: expect.any(String),
      creatorEmail: expect.any(String),
      creatorFirstname: expect.any(String),
      creatorId: expect.any(String),
      creatorLastname: expect.any(String),
      creatorOrganizationId: expect.any(String),
      id: expect.any(String),
      legalOperationId: expect.any(String),
      operationId: expect.any(Number),
      operationLabel: expect.any(String),
      status: expect.any(String),
      type: expect.any(String)
    });
    expect(response.body.items[0].creatorId).toEqual(members.uniqueMember.userId);
  });

  it('should retrieve organization entries only', async () => {
    const { client, members } = await setup();
    const response = await client
      .get(`/register-entries`)
      .query({
        organizationId: members.baseMember.organizationId,
        page: 1,
        pageSize: 10,
        type: 'TRANSACTION',
        userId: members.baseMember.userId
      })
      .send();

    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(3);
    expect(
      response.body.items.every(
        (item: { creatorOrganizationId: string }) => item.creatorOrganizationId === members.baseMember.organizationId
      )
    );
  });

  it('should retrieve legalOperationId entry only', async () => {
    const { client, operations } = await setup();
    const response = await client
      .get(`/register-entries`)
      .query({ legalOperationId: operations.operationUnique.id, page: 1, pageSize: 10, type: 'MANAGEMENT' })
      .send();

    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(1);
    expect(response.body.items[0].legalOperationId).toEqual(operations.operationUnique.id);
  });

  it('should throw if entryNumber and search are provided', async () => {
    const { client } = await setup();
    const response = await client
      .get(`/register-entries`)
      .query({ entryNumber: 97, page: 1, pageSize: 10, search: 'Observation', type: 'TRANSACTION' })
      .send();

    expect(response.statusCode).toEqual(422);
  });

  it('should retrieve entries by entryNumber', async () => {
    const { client, registerEntries, testingRepos } = await setup();
    await testingRepos.register.updateRegisterEntry({
      entryNumber: 97,
      id: registerEntries[0].id.toString(),
      status: RegisterEntryStatus.VALIDATED
    });
    const response = await client
      .get(`/register-entries`)
      .query({ entryNumber: 97, page: 1, pageSize: 10, type: 'TRANSACTION' })
      .send();
    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(1);
    expect(response.body.items[0].answer.numero_registre.value).toEqual('97');
  });

  it('should retrieve entries by search (in entryNumber)', async () => {
    const { client, registerEntries, testingRepos } = await setup();
    await testingRepos.register.updateRegisterEntry({
      entryNumber: 980,
      id: registerEntries[0].id.toString(),
      status: RegisterEntryStatus.VALIDATED
    });
    const response = await client
      .get(`/register-entries`)
      .query({ page: 1, pageSize: 10, search: '98', type: 'TRANSACTION' })
      .send();
    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(1);
    expect(response.body.items[0].answer.numero_registre.value).toEqual('980');
  });

  it('should retrieve entries by search (in creator infos)', async () => {
    const { client, members, testingRepos } = await setup();

    await testingRepos.users.updateUser({
      firstname: 'Jean',
      id: members.baseMember.userId,
      lastname: 'Dupont'
    });

    const response = await client
      .get(`/register-entries`)
      .query({ page: 1, pageSize: 10, search: 'Dupont', type: 'TRANSACTION' })
      .send();
    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(2);
    expect(response.body.items.every((item: { creatorLastname: string }) => item.creatorLastname === 'Dupont'));
  });

  it('should retrieve entries by type', async () => {
    const { client, contracts, features, members, testingRepos } = await setup();
    await testingRepos.register.createRegisterEntry({
      contractId: contracts[2].id,
      featureId: features.featureOtherOrg.id,
      organizationId: members.uniqueMember.organizationId,
      status: RegisterEntryStatus.VALIDATED,
      type: RegisterEntryType.RECEIVERSHIP,
      userId: members.uniqueMember.organizationId
    });

    const response = await client
      .get(`/register-entries`)
      .query({ page: 1, pageSize: 10, type: 'RECEIVERSHIP' })
      .send();
    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(1);
    expect(response.body.items[0].type).toEqual('RECEIVERSHIP');
  });

  it('should retrieve entries by multiple filters', async () => {
    const { client, members, operations, testingRepos } = await setup();
    await testingRepos.users.updateUser({
      firstname: 'John',
      id: members.uniqueMember.userId,
      lastname: 'Doe'
    });

    const response = await client
      .get(`/register-entries`)
      .query({
        legalOperationId: operations.operationUnique.id,
        page: 1,
        pageSize: 10,
        search: 'Doe',
        type: 'MANAGEMENT',
        userId: members.uniqueMember.userId
      })
      .send();
    expect(response.statusCode).toEqual(200);
    expect(response.body.items.length).toEqual(1);
    expect(response.body.items[0].type).toEqual('MANAGEMENT');
    expect(response.body.items[0].creatorLastname).toEqual('Doe');
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,
      controller: RegisterEntriesController,
      providers: [...provideMembersTest(), ...provideRegistersTest()]
    });

    const testingRepos = getService(TestingRepositories);
    const createUsers = async () => {
      const baseMember = await testingRepos.createMember({
        permissions: [
          {
            entityType: EntityType.TRANSACTION_REGISTER,
            permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.TRANSACTION_REGISTER,
            permissionType: PermissionType.READ_ORGANIZATION_REGISTER
          }
        ],
        planType: PlanType.PREMIUM
      });

      const uniqueMember = await testingRepos.createMember({
        permissions: [
          {
            entityType: EntityType.TRANSACTION_REGISTER,
            permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.TRANSACTION_REGISTER,
            permissionType: PermissionType.READ_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.MANAGEMENT_REGISTER,
            permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.MANAGEMENT_REGISTER,
            permissionType: PermissionType.READ_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.RECEIVERSHIP_REGISTER,
            permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.RECEIVERSHIP_REGISTER,
            permissionType: PermissionType.READ_ORGANIZATION_REGISTER
          }
        ],
        planType: PlanType.PREMIUM
      });

      const memberSameOrg = await testingRepos.addMember({
        organizationId: baseMember.organizationId,
        permissions: [
          {
            entityType: EntityType.TRANSACTION_REGISTER,
            permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.MANAGEMENT_REGISTER,
            permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.TRANSACTION_REGISTER,
            permissionType: PermissionType.READ_ORGANIZATION_REGISTER
          },
          {
            entityType: EntityType.MANAGEMENT_REGISTER,
            permissionType: PermissionType.READ_ORGANIZATION_REGISTER
          }
        ]
      });

      return { baseMember, memberSameOrg, uniqueMember };
    };
    const createOperations = async (args: {
      baseMember: MemberTesting;
      memberSameOrg: MemberAddedTesting;
      uniqueMember: MemberTesting;
    }) => {
      const baseOperation = await testingRepos.operations.createOperation({
        isOperationReference: true,
        organizationId: args.baseMember.organizationId,
        templateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
        userId: args.baseMember.userId
      });

      const operationSameUser = await testingRepos.operations.createOperation({
        isOperationReference: true,
        organizationId: args.baseMember.organizationId,
        templateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
        userId: args.baseMember.userId
      });

      const operationSameOrg = await testingRepos.operations.createOperation({
        isOperationReference: true,
        organizationId: args.baseMember.organizationId,
        templateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
        userId: args.memberSameOrg.userId
      });

      const operationUnique = await testingRepos.operations.createOperation({
        isOperationReference: true,
        organizationId: args.uniqueMember.organizationId,
        templateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
        userId: args.uniqueMember.userId
      });

      return { baseOperation, operationSameOrg, operationSameUser, operationUnique };
    };

    const createRegisterEntry = async (args: {
      contractId: string;
      featureId: string;
      organizationId: string;
      type: RegisterEntryType;
      userId: string;
    }) => {
      return await testingRepos.register.createRegisterEntry({
        contractId: args.contractId,
        featureId: args.featureId,
        organizationId: args.organizationId,
        status: RegisterEntryStatus.VALIDATED,
        type: args.type,
        userId: args.userId
      });
    };

    const createRegisterEntries = async (
      contracts: {
        contractId: string;
        featureId: string;
        organizationId: string;
        type: RegisterEntryType;
        userId: string;
      }[]
    ) => {
      return await Promise.all(contracts.map((contract) => createRegisterEntry(contract)));
    };

    const createAllRegisterFeatures = async (args: { baseOrganizationId: string; otherOrganizationId: string }) => {
      const featureBaseOrg = await testingRepos.features.createFeature({
        organizationId: args.baseOrganizationId,
        type: FeatureType.TRANSACTION_REGISTER_ACCESS
      });

      await testingRepos.features.createFeature({
        organizationId: args.baseOrganizationId,
        type: FeatureType.MANAGEMENT_REGISTER_ACCESS
      });
      await testingRepos.features.createFeature({
        organizationId: args.baseOrganizationId,
        type: FeatureType.RECEIVERSHIP_REGISTER_ACCESS
      });

      const featureOtherOrg = await testingRepos.features.createFeature({
        organizationId: args.otherOrganizationId,
        type: FeatureType.TRANSACTION_REGISTER_ACCESS
      });

      await testingRepos.features.createFeature({
        organizationId: args.otherOrganizationId,
        type: FeatureType.MANAGEMENT_REGISTER_ACCESS
      });
      await testingRepos.features.createFeature({
        organizationId: args.otherOrganizationId,
        type: FeatureType.RECEIVERSHIP_REGISTER_ACCESS
      });

      return {
        featureBaseOrg,
        featureOtherOrg
      };
    };

    const createContracts = async (args: {
      members: {
        baseMember: MemberTesting;
        memberSameOrg: MemberAddedTesting;
        uniqueMember: MemberTesting;
      };
      operations: {
        baseOperation: OperationTesting;
        operationSameOrg: OperationTesting;
        operationSameUser: OperationTesting;
        operationUnique: OperationTesting;
      };
    }) => {
      const contract1 = await testingRepos.contracts.createMandat({
        operationId: args.operations.baseOperation.id,
        userId: args.members.baseMember.userId
      });
      const contract2 = await testingRepos.contracts.createMandat({
        operationId: args.operations.operationSameUser.id,
        userId: args.members.baseMember.userId
      });
      const contract3 = await testingRepos.contracts.createMandat({
        operationId: args.operations.operationSameOrg.id,
        userId: args.members.memberSameOrg.userId
      });
      const contract4 = await testingRepos.contracts.createMandat({
        operationId: args.operations.operationUnique.id,
        userId: args.members.uniqueMember.userId
      });
      return [contract1, contract2, contract3, contract4];
    };

    const createContext = async () => {
      const members = await createUsers();
      const operations = await createOperations(members);
      const contracts = await createContracts({ members, operations });
      const features = await createAllRegisterFeatures({
        baseOrganizationId: members.baseMember.organizationId,
        otherOrganizationId: members.uniqueMember.organizationId
      });
      const registerEntries = await createRegisterEntries([
        {
          contractId: contracts[0].id,
          featureId: features.featureBaseOrg.id,
          organizationId: members.baseMember.organizationId,
          type: RegisterEntryType.TRANSACTION,
          userId: members.baseMember.userId
        },
        {
          contractId: contracts[0].id,
          featureId: features.featureBaseOrg.id,
          organizationId: members.baseMember.organizationId,
          type: RegisterEntryType.TRANSACTION,
          userId: members.baseMember.userId
        },
        {
          contractId: contracts[1].id,
          featureId: features.featureBaseOrg.id,
          organizationId: members.baseMember.organizationId,
          type: RegisterEntryType.TRANSACTION,
          userId: members.memberSameOrg.userId
        },
        {
          contractId: contracts[2].id,
          featureId: features.featureOtherOrg.id,
          organizationId: members.uniqueMember.organizationId,
          type: RegisterEntryType.TRANSACTION,
          userId: members.uniqueMember.userId
        },
        {
          contractId: contracts[3].id,
          featureId: features.featureOtherOrg.id,
          organizationId: members.uniqueMember.organizationId,
          type: RegisterEntryType.MANAGEMENT,
          userId: members.uniqueMember.userId
        }
      ]);

      return { contracts, features, members, operations, registerEntries };
    };
    const { contracts, features, members, operations, registerEntries } = await createContext();

    return {
      client,
      contracts,
      features,
      members,
      operations,
      registerEntries,
      testingRepos
    };
  }
});

interface MemberTesting {
  id: string;
  organizationId: string;
  roleId: string;
  subscriptionId: string | undefined;
  userEmail: string;
  userFirstname: string;
  userId: string;
  userLastname?: string;
}

interface MemberAddedTesting {
  memberId: string;
  userId: string;
}

interface OperationTesting {
  entityType: EntityType;
  id: string;
  organizationId: string;
  templateId: string;
}
