import { PrismaService, Prisma, register_entry } from '@mynotary/backend/shared/prisma-infra';
import {
  GetRegisterEntriesArgs,
  RegisterEntriesRepository,
  RegisterEntry,
  RegisterEntryStatus,
  RegisterEntryType,
  RegisterEntryUpdate
} from '@mynotary/backend/registers/core';
import { convertEnum, JSONValue, NotFoundError } from '@mynotary/crossplatform/shared/util';
import { Injectable } from '@nestjs/common';
import { AnswerDict } from '@mynotary/crossplatform/records/api';

@Injectable()
export class RegisterEntriesRepositoryImpl implements RegisterEntriesRepository {
  constructor(private prisma: PrismaService) {}

  async getRegisterEntries(args: GetRegisterEntriesArgs): Promise<RegisterEntry[]> {
    // throw if either page or pageSize is not defined
    if (args.page == null || args.pageSize == null) {
      throw new Error('page and pageSize are required');
    }
    args.page = Math.max(args.page, 1);
    args.pageSize = Math.max(args.pageSize, 1);
    console.log('args', args);
    const skip = (args.page - 1) * args.pageSize;
    const take = args.pageSize ?? 10;
    const whereConditions: {
      OR?: [
        {
          user: {
            OR: [
              {
                firstname: { contains: string; mode: string };
              },
              { lastname: { contains: string; mode: string } }
            ];
          };
        },
        {
          answer: {
            path: string[];
            string_contains: string;
          };
        }
      ];
      answer?: {
        path: string[];
        string_contains: string;
      };
      creator_user_id?: number;
      operation_contract?: {
        operation_id: number;
      };
      operation_id?: number;
      organization_id?: number;
      type?: string;
    } = {};
    // const whereConditions: any = {};
    if (args.organizationId) {
      whereConditions.organization_id = parseInt(args.organizationId);
    } else if (args.legalOperationId) {
      whereConditions.operation_contract = {
        operation_id: parseInt(args.legalOperationId)
      };
    } else if (args.userId) {
      whereConditions.creator_user_id = parseInt(args.userId);
    }

    if (args.type) {
      whereConditions.type = args.type;
    }

    if (args.entryNumber) {
      whereConditions.answer = {
        path: ['numero_registre', 'value'],
        string_contains: args.entryNumber.toString()
      };
    }

    if (args.search) {
      whereConditions.OR = [
        {
          user: {
            OR: [
              { firstname: { contains: args.search, mode: 'insensitive' } },
              { lastname: { contains: args.search, mode: 'insensitive' } }
            ]
          }
        },
        {
          answer: {
            path: ['numero_registre', 'value'],
            string_contains: args.search
          }
        }
      ];
    }
    //@ts-ignore
    const entries: {
      answer: JSONValue;
      contract_id: number;
      creation_time: Date;
      id: number;
      operation_contract: {
        id: number;
        legal_component_operation: {
          label: string;
        };
        operation_id: number;
      };
      organization_id: number;
      status: string;
      type: string;
      user: {
        email: string;
        firstname: string;
        id: number;
        lastname: string;
      };
    }[] = await this.prisma.register_entry.findMany({
      orderBy: {
        creation_time: 'desc'
      },
      select: {
        answer: true,
        contract_id: true,
        creation_time: true,
        id: true,
        operation_contract: {
          select: {
            id: true,
            legal_component_operation: {
              select: {
                label: true
              }
            },
            operation_id: true
          }
        },
        organization_id: true,
        status: true,
        type: true,
        user: {
          select: {
            email: true,
            firstname: true,
            id: true,
            lastname: true
          }
        }
      },
      skip,
      take,
      where: whereConditions
    });

    // console.log('ENtries', entries);
    // // instead of using null coalescing, throw an error when any information is missing
    // assertNotNull(contract_id, 'contract_id id is required');
    // assertNotNull(entry.user, 'user is required');
    // assertNotNull(entry.organization_id, 'organization_id is required');
    // assertNotNull(entry.operation_contract, 'operation_contract is required');
    // assertNotNull(entry.operation_contract.legal_component_operation, 'legal_component_operation is required');
    // assertNotNull(entry.operation_contract.legal_component_operation.label, 'legal_component_operation.label is required');

    return entries.map((entry) => ({
      answer: entry.answer as AnswerDict,
      contractId: entry.contract_id?.toString() ?? '',
      creationTime: entry.creation_time.toISOString(),
      creatorEmail: entry.user?.email ?? '',
      creatorFirstname: entry.user?.firstname ?? '',
      creatorId: entry.user?.id.toString() ?? '',
      creatorLastname: entry.user?.lastname ?? '',
      creatorOrganizationId: entry.organization_id?.toString() ?? '',
      id: entry.id.toString(),
      operationId: entry.operation_contract?.operation_id,
      operationLabel: entry.operation_contract?.legal_component_operation?.label ?? '',
      status: convertEnum(RegisterEntryStatus, entry.status),
      type: convertEnum(RegisterEntryType, entry.type)
    }));
  }

  async findRegisterEntry(id: string): Promise<RegisterEntry | null> {
    const entry = await this.prisma.register_entry.findUnique({
      select: SELECT_REGISTER_COLUMN,
      where: { id: parseInt(id) }
    });

    return entry != null ? convertDbToCore(entry) : null;
  }

  async updateRegisterEntry(args: RegisterEntryUpdate): Promise<void> {
    const entry = await this.prisma.register_entry.findUnique({
      select: SELECT_REGISTER_COLUMN,
      where: { id: parseInt(args.id) }
    });

    if (entry == null) {
      throw new NotFoundError({ id: args.id, resource: 'RegisterEntry' });
    }

    const answer = entry.answer as JSONValue;

    await this.prisma.register_entry.update({
      data: {
        answer: {
          ...answer,
          observations_registre: {
            value: args.observations != null ? args.observations : answer?.['observations_registre']?.value
          }
        },
        status: args.status != null ? args.status : entry.status
      },
      where: { id: parseInt(args.id) }
    });
  }
}

const SELECT_REGISTER_COLUMN = {
  answer: true,
  contract_id: true,
  creation_time: true,
  creator_user_id: true,
  feature_id: true,
  id: true,
  status: true,
  type: true
};

const SELECT_REGISTER_COLUMN_WITH_USER_AND_OPERATION = Prisma.validator<Prisma.register_entrySelect>()({
  answer: true,
  contract_id: true,
  creation_time: true,
  creator_user_id: true,
  feature_id: true,
  id: true,
  operation_contract: {
    select: {
      id: true,
      legal_component_operation: {
        select: {
          label: true
        }
      },
      operation_id: true
    }
  },
  status: true,
  type: true,
  user: {
    select: {
      email: true,
      firstname: true,
      id: true,
      lastname: true
    }
  }
});

type EntryDbWithUserAndOperation = Prisma.register_entryGetPayload<{
  select: typeof SELECT_REGISTER_COLUMN_WITH_USER_AND_OPERATION;
}>;

export type EntryDb = Pick<
  register_entry,
  'id' | 'feature_id' | 'type' | 'status' | 'contract_id' | 'answer' | 'creator_user_id' | 'creation_time'
>;

function convertDbToCore(entry: EntryDb): RegisterEntry {
  const type = convertEnum(RegisterEntryType, entry.type);

  return {
    ...convertCommonEntry(entry),
    type
  };
}

function convertCommonEntry(entry: EntryDb): RegisterEntry {
  return {
    answer: entry.answer as AnswerDict,
    creationTime: entry.creation_time.toISOString(),
    creatorEmail: '',
    creatorFirstname: '',
    creatorId: entry.creator_user_id.toString(),
    creatorLastname: '',
    id: entry.id.toString(),
    status: convertEnum(RegisterEntryStatus, entry.status),
    type: convertEnum(RegisterEntryType, entry.type)
  };
}
