import { Injectable } from '@nestjs/common';
import {
  DemandeCopieDocumentRepository,
  FindDemandeCopieDocumentsArgs
} from '@mynotary/backend/demande-copie-document/core';
import { planete_produit, planete_virement, Prisma, PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { isNil } from 'lodash';
import { convertEnum, Exception, NotFoundError } from '@mynotary/crossplatform/shared/util';
import PrismaPromise = Prisma.PrismaPromise;
import { getEnumKey } from '@mynotary/crossplatform/shared/util';
import {
  DemandeCopieDocument,
  DemandeCopieDocumentType,
  ReferenceFormaliteType1,
  ReferenceFormaliteType2,
  VirementStatus
} from '@mynotary/crossplatform/api-teleactes/api';
import {
  InteropStructureDestinataireType,
  InteropTransactionLibelleType,
  ProduitsPlanete,
  PlaneteStatus,
  PlaneteProtocol
} from '@mynotary/crossplatform/api-adsn/api';

@Injectable()
export class DemandeCopieDocumentRepositoryImpl extends DemandeCopieDocumentRepository {
  constructor(private prisma: PrismaService) {
    super();
  }

  async create(demande: DemandeCopieDocument) {
    try {
      let operationIdNumber: number | undefined = Number(demande.operationId);
      if (isNaN(operationIdNumber)) {
        operationIdNumber = undefined;
      }

      const newDemande = await this.prisma.planete_dossier.create({
        data: {
          conversation_id: demande.idFluxPlanete,
          label: demande.label ?? '-',
          last_modified_time: new Date(),
          operation_id: operationIdNumber,
          planete_id: demande.idFluxPlanete,
          planete_produit: {
            create: {
              contents: {
                anf: demande.anf,
                referenceType1: isNil(demande.referenceType1) ? undefined : { ...demande.referenceType1 },
                referenceType2: isNil(demande.referenceType2) ? undefined : { ...demande.referenceType2 },
                sagesDepot: demande.sagesDepot,
                sagesEnvoi: demande.sagesEnvoi,
                type: demande.type
              },
              label: demande.label ?? '-',
              planete_virement: demande.virement
                ? {
                    create: {
                      compte_client: demande.virement.compteClientOffice,
                      demandeur: demande.virement.demandeur,
                      destinataire_code: demande.virement.destinataireCode,
                      destinataire_type: getEnumKey(
                        InteropStructureDestinataireType,
                        InteropStructureDestinataireType.SPF
                      ),
                      execution_date: demande.virement.date ?? new Date(),
                      iban_bban: demande.virement.iban?.bban,
                      iban_bic: demande.virement.iban?.bic,
                      iban_cle: demande.virement.iban?.cle,
                      iban_domiciliation: demande.virement.iban?.domiciliation,
                      iban_guichet: demande.virement.iban?.guichet,
                      iban_pays: demande.virement.iban?.pays,
                      iban_titulaire: demande.virement.iban?.titulaire,
                      label: demande.virement.label,
                      memo: demande.virement.memo,
                      montant: demande.virement.montant ?? 0,
                      numero: demande.virement.numero,
                      operation_id: operationIdNumber,
                      status: demande.virement.status ?? VirementStatus.DRAFT,
                      type_transaction: getEnumKey(InteropTransactionLibelleType, InteropTransactionLibelleType.REQUI)
                    }
                  }
                : undefined,
              status: '-',
              type: ProduitsPlanete.DEMANDE_COPIE_DOCUMENT
            }
          },
          protocol: PlaneteProtocol.TELEACTES,
          recipient_id: demande.recipientId ?? null,
          status: demande.status ?? PlaneteStatus.DRAFT,
          type: ProduitsPlanete.DEMANDE_COPIE_DOCUMENT,
          user_id: Number(demande.userId)
        }
      });
      return newDemande.id;
    } catch (e) {
      throw new Exception('Error creating demande copie document from prisma', { cause: e });
    }
  }

  async delete(id: string) {
    try {
      /* first fetch the planete_dossier record to get the associated planete_produit and planete_virement records ids */
      const dossier = await this.prisma.planete_dossier.findFirst({
        include: {
          planete_produit: {
            include: {
              planete_virement: true
            }
          }
        },
        where: { id: id }
      });
      /* then delete the planete_virement records */
      const delObjects: PrismaPromise<unknown>[] = [];
      if (
        !isNil(dossier?.planete_produit) &&
        Array.isArray(dossier.planete_produit) &&
        dossier.planete_produit.length > 0
      ) {
        const produits = dossier.planete_produit;
        delObjects.push(this.prisma.planete_virement.deleteMany({ where: { produit_id: produits[0].id } }));
      }
      /* then delete the planete_produit records */
      delObjects.push(this.prisma.planete_produit.deleteMany({ where: { dossier_id: id } }));
      /* then the history and pjRecues */
      delObjects.push(this.prisma.planete_pj_recue.deleteMany({ where: { dossier_id: id } }));
      delObjects.push(this.prisma.planete_historique.deleteMany({ where: { dossier_id: id } }));
      delObjects.push(this.prisma.planete_dossier.delete({ where: { id: id } }));
      /* all within a transaction */
      await this.prisma.$transaction(delObjects);
    } catch (e) {
      throw new Exception('Error deleting demande copie document from prisma', { cause: e });
    }
  }

  async getById(id: string) {
    const demandes = await this.find({ id: id });
    if (demandes.length === 1) {
      return demandes[0];
    }
    throw new NotFoundError({ id, resource: ProduitsPlanete.DEMANDE_COPIE_DOCUMENT });
  }
  async find(args: FindDemandeCopieDocumentsArgs) {
    try {
      let operationIdNumber: number | undefined = Number(args.operationId);
      if (isNaN(operationIdNumber)) {
        operationIdNumber = undefined;
      }
      const dossiers = await this.prisma.planete_dossier.findMany({
        include: {
          planete_produit: {
            include: {
              planete_virement: true
            }
          }
        },
        orderBy: {
          creation_time: 'desc'
        },
        where: {
          id: args.id,
          label: args.search
            ? {
                contains: args.search
              }
            : undefined,
          legal_component_operation: args.organizationId
            ? {
                legal_component: {
                  organization_id: Number(args.organizationId)
                }
              }
            : undefined,
          operation_id: operationIdNumber,
          status: args.status,
          type: ProduitsPlanete.DEMANDE_COPIE_DOCUMENT
        }
      });
      return dossiers.map((dossier) => {
        /* JsonObject traversal is complex and needs a lot of casting, so first let's define un-nested objects */
        let produit: planete_produit | null = null;
        let virement: planete_virement | null = null;
        let produitContents: ProduitContentsType | null = null;
        if (
          !isNil(dossier.planete_produit) &&
          Array.isArray(dossier.planete_produit) &&
          dossier.planete_produit.length > 0
        ) {
          produit = dossier.planete_produit[0];
          produitContents = produit.contents as unknown as ProduitContentsType;
          if (!isNil(produitContents.referenceType1) && !isNil(produitContents.referenceType1.date)) {
            produitContents.referenceType1.date = new Date(produitContents.referenceType1.date);
          }
          if (!isNil(produitContents.referenceType2) && !isNil(produitContents.referenceType2.date)) {
            produitContents.referenceType2.date = new Date(produitContents.referenceType2.date);
          }
          virement = dossier.planete_produit[0].planete_virement[0];
        }
        /* crafts a DemandeCopieDocument from a dossier DB record */
        const demande: DemandeCopieDocument = {
          anf: produitContents?.anf,
          date: dossier.creation_time,
          id: dossier.id,
          idFluxPlanete: dossier.planete_id ?? undefined,
          label: dossier.label,
          lastModifiedTime: dossier.last_modified_time,
          operationId: dossier.operation_id ? '' + dossier.operation_id : undefined,
          recipientId: dossier.recipient_id ?? undefined,
          referenceType1: produitContents?.referenceType1,
          referenceType2: produitContents?.referenceType2,
          sagesDepot: produitContents && produitContents.sagesDepot ? produitContents.sagesDepot : undefined,
          sagesEnvoi: produitContents ? produitContents.sagesEnvoi : undefined,
          status: dossier.status as PlaneteStatus,
          type: produitContents ? (produitContents.type as number) : undefined,
          userId: dossier.user_id
            ? dossier.user_id.toString()
            : '0' /* marking user_id mandatory in schema.prisma gives a weird error on field operationId... Leaving as is */,
          virement:
            virement == null
              ? undefined
              : {
                  compteClientOffice: virement.compte_client ?? undefined,
                  date: virement.execution_date ? new Date(virement.execution_date) : undefined,
                  demandeur: virement.demandeur ?? undefined,
                  destinataireCode: virement.destinataire_code ?? undefined,
                  destinataireType: convertEnum(InteropStructureDestinataireType, virement.destinataire_type),
                  iban: {
                    bban: virement.iban_bban ?? '',
                    bic: virement.iban_bic ?? '',
                    cle: virement.iban_cle ?? '',
                    domiciliation: virement.iban_domiciliation ?? '',
                    guichet: virement.iban_guichet ?? '',
                    pays: virement.iban_pays ?? '',
                    titulaire: virement.iban_titulaire ?? ''
                  },
                  id: virement.id,
                  label: virement.label ?? undefined,
                  memo: virement.memo ?? undefined,
                  montant: virement.montant ? virement.montant.toNumber() : undefined,
                  numero: virement.numero ?? undefined,
                  operationId: dossier.operation_id ? '' + dossier.operation_id : undefined,
                  status: virement.status as VirementStatus,
                  typeTransaction: convertEnum(InteropTransactionLibelleType, virement.type_transaction)
                }
        };
        return demande;
      });
    } catch (e) {
      throw new Exception('Error finding demande copie document from prisma', { cause: e });
    }
  }

  async update(demande: DemandeCopieDocument) {
    try {
      let operationIdNumber: number | null = Number(demande.operationId);
      if (isNaN(operationIdNumber)) {
        operationIdNumber = null;
      }
      const dossier = await this.prisma.planete_dossier.update({
        data: {
          conversation_id: demande.idFluxPlanete,
          label: demande.label,
          last_modified_time: new Date(),
          operation_id: operationIdNumber,
          planete_id: demande.idFluxPlanete ?? null,
          protocol: PlaneteProtocol.TELEACTES,
          recipient_id: demande.recipientId ?? null,
          status: demande.status,
          type: ProduitsPlanete.DEMANDE_COPIE_DOCUMENT
        },
        where: { id: demande.id }
      });
      await this.prisma.planete_produit.updateMany({
        data: {
          contents: {
            anf: demande.anf,
            idFluxTeleactes: demande.idFluxPlanete ?? null,
            referenceType1: isNil(demande.referenceType1) ? undefined : { ...demande.referenceType1 },
            referenceType2: isNil(demande.referenceType2) ? undefined : { ...demande.referenceType2 },
            sagesDepot: demande.sagesDepot ?? null,
            sagesEnvoi: demande.sagesEnvoi ?? null,
            type: demande.type
          },
          label: demande.label ?? '-',
          type: ProduitsPlanete.DEMANDE_COPIE_DOCUMENT
        },
        where: { dossier_id: dossier.id }
      });

      /* Tricky part: we have to fetch the updated produit to get its ID, so we can do an updateMany on planete_virement */
      const updatedProduit = await this.prisma.planete_produit.findFirst({
        where: { dossier_id: dossier.id }
      });
      if (demande.virement && updatedProduit) {
        await this.prisma.planete_virement.updateMany({
          data: {
            compte_client: demande.virement.compteClientOffice ?? null,
            demandeur: demande.virement.demandeur ?? null,
            destinataire_code: demande.virement.destinataireCode ?? null,
            destinataire_type: getEnumKey(InteropStructureDestinataireType, InteropStructureDestinataireType.SPF),
            execution_date: demande.virement.date,
            iban_bban: demande.virement.iban?.bban ?? null,
            iban_bic: demande.virement.iban?.bic ?? null,
            iban_cle: demande.virement.iban?.cle ?? null,
            iban_domiciliation: demande.virement.iban?.domiciliation ?? null,
            iban_guichet: demande.virement.iban?.guichet ?? null,
            iban_pays: demande.virement.iban?.pays ?? null,
            iban_titulaire: demande.virement.iban?.titulaire ?? null,
            label: demande.virement.label ?? null,
            memo: demande.virement.memo ?? null,
            montant: demande.virement.montant,
            numero: demande.virement.numero ?? null,
            operation_id: operationIdNumber,
            status: demande.virement.status,
            type_transaction: getEnumKey(InteropTransactionLibelleType, InteropTransactionLibelleType.REQUI)
          },
          where: { produit_id: updatedProduit.id }
        });
      }
    } catch (e) {
      throw new Exception('Error updating demande copie document from prisma', { cause: e });
    }
  }
}

type ProduitContentsType = {
  anf?: boolean;
  referenceType1?: ReferenceFormaliteType1;
  referenceType2?: ReferenceFormaliteType2;
  sagesDepot: string;
  sagesEnvoi: string;
  type: DemandeCopieDocumentType;
};
