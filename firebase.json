{"emulators": {"auth": {"port": 9099}, "hosting": {"port": 9098}, "ui": {"enabled": true, "port": 9097}}, "hosting": [{"target": "front-mynotary", "public": "dist/apps/front-mynotary", "headers": [{"source": "/**", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|js|css|eot|otf|ttf|ttc|woff|woff2|font.css)", "headers": [{"key": "Cache-Control", "value": "max-age=604800"}]}], "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}, {"target": "front-portalys", "public": "dist/apps/front-portalys", "headers": [{"source": "/**", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|js|css|eot|otf|ttf|ttc|woff|woff2|font.css)", "headers": [{"key": "Cache-Control", "value": "max-age=604800"}]}], "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}]}]}