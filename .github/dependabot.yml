# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      time: "02:00"
  - package-ecosystem: "npm"
    ignore:
      - dependency-name: "sass"
        update-types: [ "version-update:semver-patch", "version-update:semver-minor" ]
      - dependency-name: "react-pdf"
        update-types: [ "version-update:semver-patch", "version-update:semver-minor", "version-update:semver-major" ]
      - dependency-name: "@sentry*"
        update-types: [ "version-update:semver-patch", "version-update:semver-minor" ]
      - dependency-name: "@pulumi/cloudflare"
        update-types: [ "version-update:semver-patch", "version-update:semver-minor" ]
      - dependency-name: "*"
        update-types: [ "version-update:semver-patch" ]
      - dependency-name: "@pulumi*"
        update-types: [ "version-update:semver-patch", "version-update:semver-minor" ]
    directory: "/"
    open-pull-requests-limit: 15
    rebase-strategy: disabled
    schedule:
      interval: "weekly"
      time: "02:00"
    groups:
      linters:
        patterns:
          - "@commitlint*"
          - "@tanstack/eslint-plugin-query"
          - "@typescript-eslint/eslint-plugin"
          - "@typescript-eslint/parser"
          - "eslint*"
          - "jsonc-eslint-parser"
          - "stylelint*"
      dnd-kit:
        patterns:
          - "@dnd-kit*"
      firebase:
        patterns:
          - "firebase*"
      rspack:
        patterns:
          - "@rs-pack*"
          - "@rsdoctor*"
      jest:
        patterns:
          - "jest-environment-jsdom"
          - "jest-environment-node"
          - "jest-image-snapshot"
          - "jest-openapi"
          - "jest"
          - "ts-jest"
          - "babel-jest"
          - "@types/jest"
          - "@types/jest-image-snapshot"
      nestjs:
        patterns:
          - "@nestjs*"
      nx:
        patterns:
          - "nx"
          - "@nx*"
      platejs:
        patterns:
          - "slate*"
          - "@udecode*"
      prisma:
        patterns:
          - "prisma"
          - "@prisma/client"
      react:
        patterns:
          - "@types/react"
          - "react"
          - "react-dom"
      redux:
        patterns:
          - "react-redux"
          - "redux-thunk"
          - "@reduxjs/toolkit"
      sentry:
        patterns:
          - "@sentry*"
      swc:
        patterns:
          - "@swc*"
          - "swc-loader"
      react-router:
        patterns:
          - "react-router"
          - "react-router-dom"
      tauri:
        patterns:
          - "@tauri*"
