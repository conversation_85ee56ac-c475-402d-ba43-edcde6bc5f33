name: Main Pipeline

concurrency:
  group: "main"
  cancel-in-progress: true
on:
  push:
    branches:
      - main

jobs:
  wait:
    name: Wait for other changes to be merged
    runs-on: ubuntu-latest
    steps:
      - name: Wait for 30 seconds
        run: sleep 30

  tests:
    needs: [wait]
    uses: ./.github/workflows/tests.yml
    secrets: inherit

  builds:
    needs: [wait]
    uses: ./.github/workflows/builds.yml
    secrets: inherit

  deploy:
    needs: [tests, builds]
    uses: ./.github/workflows/deploys.yml
    with:
      front-mynotary-built: ${{ needs.builds.outputs.front-mynotary-built == 'true' }}
    secrets: inherit

  e2e:
    needs: [deploy]
    uses: ./.github/workflows/test-e2e.yml
    secrets: inherit

  notify:
    name: Notify team if job fails
    if: ${{ always() && contains(needs.*.result, 'failure') }}
    runs-on: ubuntu-latest
    needs:
      - wait
      - tests
      - builds
      - deploy
      - e2e

    steps:
      - uses: actions/checkout@v4

      - name: Notify team if job fails
        uses: ./.github/actions/notify
        with:
          slack-webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
          footer: 'Playwright report: https://mynotary.github.io/mynotary/'
