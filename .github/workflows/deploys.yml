name: Deployments

on:
  workflow_call:
    inputs:
      front-mynotary-built:
        type: boolean
        required: true
    secrets:
      GCP_SERVICE_ACCOUNT_KEY:
        required: true

env:
  GCP_PROJECT: mynotary-preproduction
  CONFIGURATION: preproduction

jobs:
  deploy-front-mynotary:
    name: 💻 Deploy Front MyNotary
    if: ${{ inputs.front-mynotary-built }}
    runs-on: ubuntu-latest
    environment: 'preproduction'
    env:
      FIREBASE_TARGET: front-mynotary
    steps:
      - uses: actions/checkout@v4
      - name: Download front-mynotary-build
        uses: actions/download-artifact@v4
        with:
          name: front-mynotary-build
          path: dist
      - uses: ./.github/actions/setup
      - name: Authenticate to G<PERSON>
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}
          export_environment_variables: true
          create_credentials_file: true
      - name: Deploy Front MyNotary
        run: pnpm nx run front-mynotary:deploy-client --configuration $CONFIGURATION

  deploy-servers:
    name: 🚀 Deploy Cloud run services
    runs-on: ubuntu-latest
    environment: 'preproduction'
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup
      - name: Authenticate to GCP
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}
          export_environment_variables: true
          create_credentials_file: true
      - name: Use GCP Auth in Docker
        run: gcloud auth configure-docker europe-west9-docker.pkg.dev
      - name: Deploy with Pulumi
        uses: pulumi/actions@v6
        with:
          command: up
          stack-name: 'preproduction'
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}
