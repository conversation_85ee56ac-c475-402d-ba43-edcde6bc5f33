name: Build and test targets

concurrency:
  group: ci-${{ github.ref }}
  cancel-in-progress: true

on:
  workflow_dispatch:
    inputs:
      targets:
        description: "Nx targets names. Example: api-mynotary,front-mynotary or * for all"
        required: true

jobs:
  build-apps:
    name: 🏗️ Build applications
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js and install dependencies
        uses: ./.github/actions/setup

      - name: Build apps
        shell: bash
        run: pnpm nx run-many --target=build --projects='${{ github.event.inputs.targets }}' --parallel=3

  test-apps:
    name: 🏗️ Test applications
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js and install dependencies
        uses: ./.github/actions/setup

      - name: Build apps
        shell: bash
        run: pnpm nx run-many --target=build --projects='${{ github.event.inputs.targets }}'
