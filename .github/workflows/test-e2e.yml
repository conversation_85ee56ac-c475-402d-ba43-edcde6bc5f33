name: Run e2e tests

on:
  workflow_dispatch:
  workflow_call: {}

concurrency:
  group: "e2e-tests"
  cancel-in-progress: true

jobs:
  test-e2e:
    name: 🪐 Tests E2E
    runs-on: ubuntu-latest
    if: ${{ github.event_name != 'workflow_run' || github.event.workflow_run.conclusion == 'success' }}
    strategy:
      fail-fast: false
      matrix:
        shardIndex: [ 1, 2, 3, 4 ]
        shardTotal: [ 4 ]
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup

      - name: Start all APIs on preproduction where min instances is zero.
        run: pnpm nx run tools:ci-wake-up-apis

      - name: Playwright browsers install
        run: pnpm playwright install

      # Fix weird error saying that file is missing while it should say gs is missing
      - name: Setup ImageMagick deps and policy to allow pdf->* conversion.
        shell: bash
        run: sudo apt-get update && sudo apt-get install ghostscript imagemagick && sudo sed -i 's/^.*policy.*coder.*none.*PDF.*//' /etc/ImageMagick-*/policy.xml

      - name: Run E2E Tests
        run: pnpm nx run-many -t e2e --skip-nx-cache --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }}

      - name: Upload blob report to GitHub Actions Artifacts
        if: ${{ !cancelled() }}
        uses: actions/upload-artifact@v4
        with:
          name: blob-report-${{ matrix.shardIndex }}
          path: blob-report
          retention-days: 1

  merge-reports:
    # Merge reports after playwright-tests, even if some shards have failed
    if: ${{ needs.test-e2e.result != 'skipped' && !cancelled() }}
    needs:
      - test-e2e

    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup

      - name: Download blob reports from GitHub Actions Artifacts
        uses: actions/download-artifact@v4
        with:
          path: all-blob-reports
          pattern: blob-report-*
          merge-multiple: true

      - name: Merge into HTML Report
        run: npx playwright merge-reports --reporter html ./all-blob-reports

      - name: Upload playwright report as artifact
        id: deployment
        uses: actions/upload-pages-artifact@v3
        with:
          name: playwright-report
          path: playwright-report

  deploy-github-page:
    name: Deploy playwright report to GitHub Page
    if: ${{ needs.test-e2e.result != 'skipped' && !cancelled() }}
    needs: merge-reports

    permissions:
      pages: write      # to deploy to Pages
      id-token: write   # to verify the deployment originates from an appropriate source

    # Deploy to the github-pages environment (recommended in the docs)
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}

    runs-on: ubuntu-latest
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
        with:
          artifact_name: playwright-report
