name: Pull Request Pipeline

# Avoid race conditions
concurrency:
  group: ci-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    branches:
      - main

jobs:
  tests:
    if: ${{ github.actor != 'dependabot[bot]' }}
    uses: ./.github/workflows/tests.yml

  notify:
    name: Notify team if job fails
    if: ${{ always() && contains(needs.*.result, 'failure') }}
    runs-on: ubuntu-latest
    needs:
      - tests

    steps:
      - uses: actions/checkout@v4

      - name: Notify team if job fails
        uses: ./.github/actions/notify
        with:
          slack-webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
          footer: 'Playwright report: https://mynotary.github.io/mynotary/'
