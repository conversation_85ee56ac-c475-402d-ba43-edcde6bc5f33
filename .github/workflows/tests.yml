name: Tests

on:
  workflow_call: {}

permissions:
  actions: read
  contents: read

jobs:
  test-guards-goldens:
    name: 🛡️ Test Guards Goldens
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: ./.github/actions/setup
      - uses: nrwl/nx-set-shas@v4
      - name: Run Guards Goldens Tests
        run: pnpm nx affected --target generate-guards-goldens
      - name: Check that goldens are up to date
        run: tools/check-guards-goldens.sh

  list-affected-test-backend:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      hasAffectedProjects: ${{ steps.set-hasAffectedProjects.outputs.hasAffectedProjects }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: ./.github/actions/setup
      - uses: nrwl/nx-set-shas@v4
      - name: Generate json file with affected backend projects
        run: pnpm nx run tools:list-affected-test-backend
      - name: Set hasAffectedProjects
        if: hashFiles('list-affected-test-backend-output.json') != ''
        id: set-hasAffectedProjects
        run: echo "hasAffectedProjects=true" >> $GITHUB_OUTPUT
      - name: Set matrix data
        if: hashFiles('list-affected-test-backend-output.json') != ''
        id: set-matrix
        run: echo "matrix=$(jq -c . < ./list-affected-test-backend-output.json)" >> $GITHUB_OUTPUT

  test-backend:
    if: ${{ needs.list-affected-test-backend.outputs.hasAffectedProjects == 'true' }}
    name: 🚧 Tests backend - ${{ matrix.projects }} ${{ matrix.index }}
    runs-on: ubuntu-latest
    needs: list-affected-test-backend
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.list-affected-test-backend.outputs.matrix) }}
    services:
      postgres:
        image: postgis/postgis:16-3.5-alpine
        env:
          POSTGRES_USER: api
          POSTGRES_PASSWORD: api
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup
        if: ${{ matrix.projects != '' }}
      - name: Run Tests backend
        if: ${{ matrix.projects != '' }}
        run: pnpm nx run-many -t test -p ${{ matrix.projects }} --parallel=5

  test-backend-snapshot:
    name: 🚧 Tests backend - Snapshot
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgis/postgis:16-3.5-alpine
        env:
          POSTGRES_USER: api
          POSTGRES_PASSWORD: api
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: ./.github/actions/setup
      - name: Setup ImageMagick deps
        run: sudo apt-get update && sudo apt-get install ghostscript imagemagick && sudo sed -i 's/^.*policy.*coder.*none.*PDF.*//' /etc/ImageMagick-*/policy.xml
      - uses: nrwl/nx-set-shas@v4
      - run: pnpm nx affected --target test-snapshot --parallel=5

  test-frontend:
    name: 🚧 Tests frontend
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: ./.github/actions/setup
      - uses: nrwl/nx-set-shas@v4
      - name: Run Tests frontend
        run: pnpm nx affected --target test --exclude='*,!tag:platform:frontend' --parallel=3

  test-crossplatform:
    name: 🚧 Tests crossplatform
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: ./.github/actions/setup
      - uses: nrwl/nx-set-shas@v4
      - name: Run Tests crossplatform
        run: pnpm nx affected --target test --exclude='*,!tag:platform:crossplatform' --parallel=3
