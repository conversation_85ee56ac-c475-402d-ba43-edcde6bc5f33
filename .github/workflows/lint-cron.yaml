name: Lint - Scheduled

# BEWARE: a scheduled github cron job is triggered only if the workflow is pushed to the default branch
# an alternative for testing would be to set 'pull_request: {}' in the "on" property.

on:
  schedule:
    # At 08:30 AM, every wednesday
    - cron: "30 6 * * 3"

jobs:
  lint:
    name: 🧹 <PERSON><PERSON> & Stylelint
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Node.js, cache and install dependencies etc...
        uses: ./.github/actions/setup

      - uses: nrwl/nx-set-shas@v4

      - name: Lint
        run: pnpm nx affected --target lint, stylelint --max-warnings=0

  notify:
    name: Notify team if job fails
    if: ${{ always() && contains(needs.*.result, 'failure') && !startsWith(github.head_ref, 'dependabot') }}
    runs-on: ubuntu-latest
    needs:
      - lint

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - uses: ravsamhq/notify-slack-action@v2
        with:
          notification_title: '🧹 Le lint ne passe pas.  La personne au support doit clean: <{run_url}|View Run>'
          status: 'failure'
          footer: ""
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
