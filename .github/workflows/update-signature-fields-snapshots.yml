# This is a basic workflow that is manually triggered

name: Update signature fields snapshots

on:
  workflow_dispatch:

jobs:
  test-snapshot-signature-fields:
    name: 🪐 Test Signature Fields
    runs-on: ubuntu-latest

    services:
      # DB is needed by wide tests.
      postgres:
        image: postgis/postgis:16-3.5-alpine
        env:
          POSTGRES_USER: api
          POSTGRES_PASSWORD: api
        # Set health checks to wait until postg<PERSON> has started
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js and install dependencies
        uses: ./.github/actions/setup

      - name: Setup ImageMagick deps and policy to allow pdf->* conversion.
        shell: bash
        run: sudo apt-get update && sudo apt-get install ghostscript imagemagick && sudo sed -i 's/^.*policy.*coder.*none.*PDF.*//' /etc/ImageMagick-*/policy.xml

      - name: Run update signature field snapshots
        run: pnpm nx run backend-signatures-infra:test --updateSnapshot

      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: signature-fields-report
          path: libs/backend/signatures/infra/__image_snapshots__
          retention-days: 1
