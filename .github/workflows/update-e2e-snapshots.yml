# This is a basic workflow that is manually triggered

name: Update E2E Snapshots

on:
  workflow_dispatch:
    inputs:
      test-name:
        description: 'Test à exécuter'
        required: true
        type: choice
        options:
          - 'pdf-content'
          - 'existing-edited-contract'
        default: 'pdf-content'

jobs:
  test-e2e:
    name: 🪐 Update E2E Snapshots
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js and install dependencies
        uses: ./.github/actions/setup

      - name: Playwright browsers install
        run: pnpm playwright install

      - name: Setup ImageMagick deps and policy to allow pdf->* conversion.
        shell: bash
        run: sudo apt-get update && sudo apt-get install ghostscript imagemagick && sudo sed -i 's/^.*policy.*coder.*none.*PDF.*//' /etc/ImageMagick-*/policy.xml

      - name: Run E2E Tests
        run: pnpm nx e2e front-mynotary --grep="${{ inputs.test-name }}" --skip-nx-cache --update-snapshots

      - uses: actions/upload-artifact@v4
        if: always()
        with:
          name: pdf-snapshots
          path: apps/front-mynotary/e2e/tests
          retention-days: 1

