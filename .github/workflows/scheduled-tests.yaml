name: Scheduled Tests

# BEWARE: a scheduled github cron job is triggered only if the workflow is pushed to the default branch
# an alternative for testing would be to set 'pull_request: {}' in the "on" property.

on:
  workflow_dispatch:
  schedule:
    # At 08:30 AM, Monday through Friday
    - cron: "30 6 * * 1-5"

jobs:
  test:
    strategy:
      matrix:
        environment: [preproduction, production]

    environment: ${{ matrix.environment }}
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Authenticate to G<PERSON>
        id: auth
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}
          export_environment_variables: true
          create_credentials_file: true

      - name: Set up Node.js, cache and install dependencies etc...
        uses: ./.github/actions/setup

      - name: Run tests
        run: pnpm nx run-many --targets=test-scheduled --skip-nx-cache

  notify:
    name: Notify team if job fails
    if: ${{ always() && contains(needs.*.result, 'failure') && !startsWith(github.head_ref, 'dependabot') }}
    runs-on: ubuntu-latest
    needs:
      - test

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - uses: ravsamhq/notify-slack-action@v2
        with:
          mention_groups: 'S8L88QPEY'
          notification_title: '💰 Les tests journaliers ne passe pas, la personne au support doit clean: <{run_url}|View Run>'
          status: 'failure'
          footer: ""
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
