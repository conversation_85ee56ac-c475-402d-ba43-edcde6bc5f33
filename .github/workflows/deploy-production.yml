# This is a basic workflow that is manually triggered

name: Deploy production

# Controls when the action will run. Workflow runs when manually triggered using the UI
# or API.
on:
  workflow_dispatch:
    inputs:
      name:
        description: 'Deploy api services and front applications in production ?'
        default: 'OK'
        required: true

env:
  # GCP_PROJECT is used in firebase-deploy.sh to deploy react app to firebase hosting
  GCP_PROJECT: mynotary-production
  # CONFIGURATION is used to deploy frontend application with the right environment files
  CONFIGURATION: production

jobs:
  build-front-mynotary:
    name: 🛠️ Build front-mynotary
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest

    outputs:
      front-mynotary-built: ${{ steps.check-artifact.outputs.front-mynotary-built }}
    environment: 'production'
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: ./.github/actions/setup

      - uses: nrwl/nx-set-shas@v4

      - name: Build front-mynotary
        shell: bash
        run: pnpm nx affected --base build-front-mynotary-production --target build --exclude='*,!tag:scope:front-mynotary' --configuration $CONFIGURATION

      - name: Post build tasks
        if: ${{ hashFiles('dist/') != '' }}
        shell: bash
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
        run: pnpm nx run front-mynotary:post-build

      - name: Check if front-mynotary build exists
        shell: bash
        id: check-artifact
        if: ${{ hashFiles('dist/') != '' }}
        run: echo "front-mynotary-built=true" >> "$GITHUB_OUTPUT"

      - name: Upload front mynotary build
        uses: actions/upload-artifact@v4
        if: ${{ hashFiles('dist/') != '' }}
        with:
          name: front-mynotary-build-${{ github.sha }}
          path: dist

      - name: Tag build-front-mynotary-production
        uses: ./.github/actions/tag-and-push
        if: ${{ hashFiles('dist/') != '' }}
        with:
          githubToken: ${{ secrets.GITHUB_TOKEN }}
          tag: build-front-mynotary-production

  deploy-front-mynotary:
    name: 💻 Deploy Front MyNotary
    if: github.ref == 'refs/heads/main' && needs.build-front-mynotary.outputs.front-mynotary-built == 'true'
    runs-on: ubuntu-latest
    needs:
      - build-front-mynotary
    environment: 'production'
    env:
      FIREBASE_TARGET: front-mynotary

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: ./.github/actions/setup

      - name: Authenticate to GCP
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}
          export_environment_variables: true
          create_credentials_file: true

      - name: Download front-mynotary-build
        uses: actions/download-artifact@v4
        with:
          name: front-mynotary-build-${{ github.sha }}
          path: dist

      - name: Deploy Front MyNotary
        shell: bash
        run: pnpm nx run front-mynotary:deploy-client --configuration $CONFIGURATION

  deploy-servers:
    name: 🚀 Deploy Cloud run services
    if: github.ref == 'refs/heads/main'
    environment: 'production'
    # Avoid race condition.
    concurrency:
      group: ${{ github.ref }}

    runs-on: ubuntu-latest
    needs:
      - build-front-mynotary

    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup

      - name: Authenticate to GCP
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}
          export_environment_variables: true
          create_credentials_file: true

      - name: Use GCP Auth in Docker
        shell: bash
        run: gcloud auth configure-docker europe-west9-docker.pkg.dev

      - name: Deploy with Pulumi
        uses: pulumi/actions@v6
        with:
          command: up
          stack-name: 'production'
        env:
          PULUMI_ACCESS_TOKEN: ${{ secrets.PULUMI_ACCESS_TOKEN }}

  notify:
    name: Notify team if job fails
    if: ${{ always() && contains(needs.*.result, 'failure') }}
    runs-on: ubuntu-latest
    needs:
      - deploy-front-mynotary
      - deploy-servers

    steps:
      - uses: actions/checkout@v4

      - name: Notify team if job fails
        uses: ./.github/actions/notify
        with:
          slack-webhook-url: ${{ secrets.SLACK_WEBHOOK_URL }}
