name: Tag & Push

description: Tag & Push

runs:
  using: composite
  steps:
    - name: Tag
      shell: bash
      run: git tag -f ${{ inputs.tag }}

    - name: Setup git user to "🤖 MyNotary Bot"
      shell: bash
      run: git config user.email "-" && git config user.name "🤖 MyNotary Bot"

    - uses: ad-m/github-push-action@master
      with:
        github_token: ${{ inputs.githubToken }}
        branch: ${{ github.head_ref }}
        force: true
        tags: true

inputs:
  githubToken:
    description: Github Token
    required: true
  tag:
    description: Tag
    required: true
