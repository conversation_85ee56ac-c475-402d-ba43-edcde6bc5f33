name: Notify team on Slack

description: Notify team on Slack

inputs:
  footer:
    description: 'Footer message'
    required: false
    default: ''
  slack-webhook-url:
      description: 'Slack webhook url (e.g. secrets.SLACK_WEBHOOK_URL)'
      required: true

runs:
  using: composite
  steps:
    - uses: ravsamhq/notify-slack-action@v2
      with:
        notification_title: '🚨 Ci failed <{run_url}|View Run>'
        status: 'failure'
        mention_users: ${{ fromJSON(env.GITHUB_SLACK_USERS)[github.actor_id].slackId }}
        footer: ${{ inputs.footer }}
      env:
        SLACK_WEBHOOK_URL: ${{ inputs.slack-webhook-url }} # required
        # To retrieve github actor id https://api.github.com/users/yjaaidi
        GITHUB_SLACK_USERS: |
          {
            155431281: {"name": "<PERSON><PERSON><PERSON>", "slackId": "U06BX2PUZQW"},
            110811517: {"name": "<PERSON>", "slackId": "U01HG4NC5K9"},
            110811069: {"name": "Seb", "slackId": "UGLMX7F1T"},
            211303100: {"name": "Valentin", "slackId": "U08NXETFFHD"},
            138800669: {"name": "Vincent", "slackId": "U05J0HQU4CX"}
          }
