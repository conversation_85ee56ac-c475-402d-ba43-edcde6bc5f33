import * as fs from 'fs';
import { filter, map, uniq } from 'lodash';
import * as path from 'path';

const traverseDirectory = (currentPath: string, cb: (args: string) => void) => {
  const entries = fs.readdirSync(currentPath, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = path.join(currentPath, entry.name);
    if (entry.isDirectory()) {
      traverseDirectory(fullPath, cb);
    } else if (entry.isFile() && path.extname(entry.name) === '.json') {
      cb(fullPath);
    }
  }
};

const columnIds: string[] = [];
const columnFound: string[] = [];

const processJsonFile = (dir: string, attribute: string) => {
  // Function to process JSON files
  const processFile = (filePath: string) => {
    // Read the file
    const data = fs.readFileSync(filePath, 'utf8');
    let json = JSON.parse(data);

    // Delete the attribute
    if (json.hasOwnProperty("batchImport")) {
      map(json.batchImport.columns, (column) => {
        traverseDirectory("/Users/<USER>/work/api-java-mynotary/contracts/v3", (contractFilePath)=> {
          const contractData = fs.readFileSync(contractFilePath, 'utf8');
          const contractString = JSON.stringify(contractData);
          const regex = new RegExp(column.columnId, 'i'); // case-insensitive se
          columnIds.push(column.columnId);
          const isFound = columnFound.includes(column.columnId);

          if (!isFound && regex.test(contractString)) {
            columnFound.push(column.columnId);
          }
        })
      })

      // Write the file back
      fs.writeFileSync(filePath, JSON.stringify(json, null, 2), 'utf8');
      console.log(`Processed and updated: ${filePath}`);
    }
  };

  traverseDirectory(dir, processFile);
};

// Call the function with the directory and attribute to delete
processJsonFile('/Users/<USER>/work/api-java-mynotary/src/main/resources/files/config/record/bien', 'templatesToLoad');

const useless = filter(columnIds, (columnId) => !columnFound.includes(columnId));
console.log('uselessColumn', uniq(useless));

