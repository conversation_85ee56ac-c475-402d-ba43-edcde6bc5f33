import * as process from 'node:process';
import { LegalTemplateGeneratorsRepositoryImpl } from '../../apps/tool-legal-templates/legal-template-generators.repository.impl';
import { legalOperationTemplateMap } from '@mynotary/crossplatform/legal-operation-templates/core';
import {
  LegalOperationTemplate,
  LegalOperationTemplateId,
  OperationConfigLink
} from '@mynotary/crossplatform/legal-templates/core';

/**
 * This script compares all legal operation templates with their derivatives.
 * It will output the custom links that are present in the derivatives but not in the original template.
 * Needs DB access (see ./legal-templates.repository.impl.ts for config).
 *
 * usage:
 *   pnpm template-compare [originTemplate]
 *     originTemplate (optional): the id of the template to compare with its derivatives (ex: OPERATION__IMMOBILIER__VENTE_ANCIEN)
 */
class CompareTemplates {
  private legalTemplateRepository;

  constructor() {
    this.legalTemplateRepository = new LegalTemplateGeneratorsRepositoryImpl();
  }

  async compareTemplates(originTemplate?: string) {
    if (!originTemplate) {
      console.log('Comparing all templates...');
    } else {
      console.log('Comparing templates with origin: ', originTemplate);
    }

    // get all templates from db. A bit violent but there's not that much data.
    const templatesDb = await this.legalTemplateRepository.getTemplates({
      type: 'OPERATION'
    });
    const myNotaryTemplatesDb = templatesDb.filter((template) =>
      originTemplate ? template.id === originTemplate : template.mynotaryTemplate
    );
    let grandTotal = 0;

    // iterate over MyNotary templates
    for (const mnTemplateDb of myNotaryTemplatesDb) {
      const mnTemplate = legalOperationTemplateMap[mnTemplateDb.id as LegalOperationTemplateId];
      const mnLinks = this.getLinks(mnTemplate);
      const derivedTemplatesDb = templatesDb.filter(
        (template) => template.originTemplate === mnTemplateDb.id && template.id !== mnTemplateDb.id
      );
      let totalCustomLinks = 0;
      console.log(`\\ ${mnTemplateDb.label}`);

      // iterate over derived templates
      for (const derivedTemplateDb of derivedTemplatesDb) {
        const derivedTemplate = legalOperationTemplateMap[derivedTemplateDb.id as LegalOperationTemplateId];
        const derivedLinks = this.getLinks(derivedTemplate);
        const customLinks = derivedLinks.filter((link) => !mnLinks.includes(link));

        if (customLinks.length > 0) {
          console.log(` \\ ${derivedTemplateDb.label}`);
          customLinks.forEach((link) => console.log(`  | ${link}`));
          console.log(` / Total for ${derivedTemplateDb.label} : ${customLinks.length} custom link(s)`);
          totalCustomLinks += customLinks.length;
        }
      }
      console.log(
        `/ Total for ${mnTemplateDb.label} : ${derivedTemplatesDb.length} derivatives, ${totalCustomLinks} custom link(s)`
      );
      grandTotal += totalCustomLinks;
    }
    if (!originTemplate) {
      console.log(`Grand Total : ${grandTotal} custom link(s)`);
    }
  }

  private getLinks(template: LegalOperationTemplate): string[] {
    return template.config.recordLinks.map((link: OperationConfigLink) => link.specificTypes.join('/'));
  }
}

new CompareTemplates()
  .compareTemplates(process.argv[2])
  .then(() => console.log('End of comparison'))
  .catch((e) => console.error(e));
