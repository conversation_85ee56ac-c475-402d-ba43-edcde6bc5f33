#!/bin/bash

# Define an array of service names
SERVICE_NAMES=("api-mynotary" "api-signatures" "api-emails" "auth" "files" "pdf" "api-java")
REGION="europe-west9"
PROJECT_ID="mynotary-production"

# Loop through each service name
for SERVICE_NAME in "${SERVICE_NAMES[@]}"; do

  # Get the list of revisions sorted by creation time (newest first)
  revisions=$(gcloud run revisions list --service=$SERVICE_NAME --region=$REGION --project=$PROJECT_ID --sort-by="~createTime" --format="value(METADATA.name)")

  # Count the number of revisions
  revision_count=$(echo "$revisions" | wc -l)
  echo "Processing service: [$SERVICE_NAME] with $revision_count revisions."

  # Calculate the number of revisions to delete (all but the last 2)
  delete_count=$((revision_count - 2))

  if (( delete_count > 0 )); then
    # Get the revisions to delete (skip the first 2)
    revisions_to_delete=$(echo "$revisions" | tail -n +3)

    # Delete each revision
    echo "$revisions_to_delete" | while read -r REVISION_NAME; do
      gcloud run revisions delete $REVISION_NAME --region=$REGION --project=$PROJECT_ID --quiet
    done
  else
    echo "There are 2 or fewer revisions for service $SERVICE_NAME, no revisions to delete."
  fi
done
