#!/usr/bin/env bash

set -e

if [ $# != 2 ]
then
  echo "Usage:"
  echo "    $0 <openapi_file> <output_dir>"
  exit 1
fi

OPENAPI_FILE="$1"
OUTPUT_DIR="$2"

rm -rf "$OUTPUT_DIR/model"

# stringEnums=false allow usage of string literal duck typing
pnpm openapi-generator-cli generate -i "$OPENAPI_FILE" -g typescript-angular -o "$OUTPUT_DIR" --additional-properties enumPropertyNaming=UPPERCASE,stringEnums=true,fileNaming=kebab-case,modelSuffix=dto,removeEnumValuePrefix=false --global-property models,skipFormModel=false

# Apply custom overrides which are used when we are not satisfied with the generated code.
if [ -d "$OUTPUT_DIR/overrides" ]
then
  cp $OUTPUT_DIR/overrides/*.ts "$OUTPUT_DIR/model"
fi

# @hack fix stupid double suffix behavior..."
if [ "$(uname -s)" = "Darwin" ]
then
  sed -i '' 's/dto-dto/dto/g' $OUTPUT_DIR/model/*.ts
else
  sed -i 's/dto-dto/dto/g' $OUTPUT_DIR/model/*.ts
fi

# Export dto to index.ts

# Define the name of the index.ts file
INDEX_FILE="$OUTPUT_DIR/index.ts"

# Create an empty index.ts file or overwrite it if it exists
rm -f "$INDEX_FILE"
touch "$INDEX_FILE"

# Iterate through all TypeScript files in the model directory and re-export them in index.ts
for model_file in $OUTPUT_DIR/model/*.ts; do
    # Extract the base filename (without .ts extension)
    model_name=$(basename "$model_file" .ts)

    # Append an export statement to index.ts
    echo "export * from './model/$model_name';" >> "$INDEX_FILE"
done

