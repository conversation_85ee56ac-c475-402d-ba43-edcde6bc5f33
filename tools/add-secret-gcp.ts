import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import { google } from '@google-cloud/secret-manager/build/protos/protos';
import { select, checkbox, input } from '@inquirer/prompts';
import * as fs from 'fs';

export async function createSecret({ client, project, secretId, secretValue }: SecretNewArgs) {
  try {
    const [secret] = await client.createSecret({
      parent: `projects/${project}`,
      secret: {
        name: secretId,
        replication: {
          automatic: {}
        }
      },
      secretId: secretId
    });
    console.log(`Created secret: ${secret.name}`);

    await addSecretVersion({ client, project, secretId, secretValue });
  } catch (error) {
    const googleError = error as GoogleSecretError;
    console.error(`Error creating secret on ${project}`, googleError.details);

    if (googleError.code === 6) {
      await handleSecretDuplicate({ client, project, secretId, secretValue });
    }
  }
}

async function getSecret({ client, project, secretId }: SecretGetArgs): Promise<google.cloud.secretmanager.v1.ISecret> {
  const [secret] = await client.getSecret({
    name: `projects/${project}/secrets/${secretId}`
  });

  if (!secret) {
    console.error(`Secret ${secretId} not found on ${project}`);
    throw new Error(`Secret ${secretId} not found on ${project}`);
  }

  return secret;
}

async function addSecretVersion({ client, project, secretId, secretValue }: SecretNewArgs) {
  try {
    const [version] = await client.addSecretVersion({
      parent: `projects/${project}/secrets/${secretId}`,
      payload: {
        data: Buffer.from(secretValue, 'utf8')
      }
    });

    console.log(`Added secret version: ${version.name}`);
  } catch (error) {
    console.error(`Error creating secret on ${project}`, error);
  }
}

async function handleSecretDuplicate({ client, project, secretId, secretValue }: SecretNewArgs) {
  const choice = await select({
    choices: [
      {
        name: 'Yes',
        value: true
      },
      {
        name: 'No',
        value: false
      }
    ],
    message: `Secret ${secretId} already exists. Do you want to add a new version?`
  });

  if (choice) {
    await addSecretVersion({ client, project, secretId, secretValue });
  }
}

async function addSecretIdToEnv({ client, project, secretId, secretValue }: SecretNewArgs) {
  const dotenvConfig = require('dotenv').config({ path: '.env' });

  if (dotenvConfig.error) {
    console.error('Error loading .env file');
    return;
  }

  if (!dotenvConfig.parsed[secretId]) {
    fs.writeFileSync('.env', `${secretId}=${secretId}\n`, { flag: 'a' });
  }

  console.log(`Added secret ${secretId} to .env file`);
}

async function main() {
  const projects = await checkbox<Project>({
    choices: [{ value: 'mynotary-production' as Project }, { value: 'mynotary-preproduction' as Project }],
    message: 'On What platform would you like to add a secret?',
    validate: (res) => (res.length === 0 ? 'You must select at least one project' : true)
  });

  const secretId = await input({
    message: 'Enter the secret ID/name:'
  });

  const secretValue = await input({
    message: 'Enter the secret value:'
  });

  for (const project of projects) {
    console.log(`Creating secret on ${project}...`);
    const client = new SecretManagerServiceClient({
      /**
       * Should use a specific service account who have this permission :
       * 'secretmanager.secrets.create'
       */
      keyFilename: './gcp-service-account-secret-adder.json',
      projectId: project
    });

    await createSecret({
      client,
      project,
      secretId,
      secretValue
    });

    const check = await select({
      choices: [
        {
          name: 'Yes',
          value: true
        },
        {
          name: 'No',
          value: false
        }
      ],
      message: `Do you want to add the secret ${secretId} to the .env file?`
    });

    if (check) {
      await addSecretIdToEnv({
        client,
        project,
        secretId,
        secretValue
      });
    }
  }
}

main().catch(console.error);

export type Project = 'mynotary-production' | 'mynotary-development' | 'mynotary-preproduction';

interface SecretNewArgs {
  client: SecretManagerServiceClient;
  project: Project;
  secretId: string;
  secretValue: string;
}

interface SecretGetArgs {
  client: SecretManagerServiceClient;
  project: Project;
  secretId: string;
}

type GoogleSecretError = Error & { code: number; details: string };
