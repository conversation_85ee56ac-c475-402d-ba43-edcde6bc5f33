import * as firebaseAdmin from 'firebase-admin';

/**
 * This script deletes all users from Firebase except the default ones.
 * This must be used for development purposes only.
 * usage: GOOGLE_APPLICATION_CREDENTIALS=./gcp-service-account.json tsx tools/clean-firebase-users.ts
 */
const firebaseAdminApp = firebaseAdmin.initializeApp({
  credential: firebaseAdmin.credential.applicationDefault(),
  projectId: 'mynotary-development'
});

const deleteUserExceptTestUser = async () => {
  const users = await firebaseAdminApp.auth().listUsers();
  const defaultEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];

  const usersToDelete = users.users.filter((user) => !defaultEmails.includes(user.email ?? ''));

  for (const user of usersToDelete) {
    console.log('Deleting user:', user.email);
    await firebaseAdminApp.auth().deleteUser(user.uid);
    await new Promise((resolve) => setTimeout(resolve, 50));
  }

  console.log('Users deleted successfully.');
};

deleteUserExceptTestUser().catch((error) => {
  console.error('Error deleting users:', error);
});
