import * as fs from 'fs';
import * as path from 'path';

async function removeOperationDocumentsFromOperationConfigs(directoryPath: string): Promise<void> {
  try {
    const files = fs.readdirSync(directoryPath);

    for (const file of files) {
      const filePath = path.join(directoryPath, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        await removeOperationDocumentsFromOperationConfigs(filePath);
      } else if (path.extname(file).toLowerCase() === '.json') {
        await removeOperationDocumentsFromOperationConfig(filePath);
      }
    }
  } catch (error) {
    console.error(`Error processing directory: ${directoryPath}`, error);
  }
}

async function removeOperationDocumentsFromOperationConfig(filePath: string): Promise<void> {
  try {
    const jsonString = fs.readFileSync(filePath, 'utf-8');
    const jsonObject = JSON.parse(jsonString);

    if (jsonObject.hasOwnProperty('operationDocuments')) {
      jsonObject.documentsToExclude = jsonObject.operationDocuments.documentsToExclude;
      jsonObject.documentsToInclude = jsonObject.operationDocuments.documentsToInclude;
      delete jsonObject.operationDocuments;
    }

    const modifiedJsonString = JSON.stringify(jsonObject, null, 2);

    fs.writeFileSync(filePath, modifiedJsonString, 'utf-8');

  } catch (error) {
    console.error(`Error processing file: ${filePath}`, error);
  }
}

const directoryPath = '/Users/<USER>/work/api-java-mynotary/src/main/resources/files/config/operation';
removeOperationDocumentsFromOperationConfigs(directoryPath);
