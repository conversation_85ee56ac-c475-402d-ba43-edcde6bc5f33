type OperationConfig = {
  id: string;
  config_path: string;
  documentsToInclude: string[];
  documentsToExclude: string[];
}

export type OperationConfigId = OperationConfig['id'];

export const operationConfigs: OperationConfig[] = [
  {
    id: 'AGENCE_DIRECTE__IMMOBILIER',
    config_path: 'operation/agenceDirecte/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'ALLOWA__IMMOBILIER',
    config_path: 'operation/allowa/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'AUTRE__LIBRE',
    config_path: 'operation/autre/libre.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'BENEDIC__IMMOBILIER',
    config_path: 'operation/benedic/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'BLOT__IMMOBILIER',
    config_path: 'operation/blot/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'C2I__CONTRAT_RECRUTEMENT',
    config_path: 'operation/c2I/contratRecrutement.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'C2I__FORMATION',
    config_path: 'operation/c2I/formation.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'CLESENCE__IMMOBILIER__PROGRAMME',
    config_path: 'operation/clesence/immobilier/programme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'CLESENCE__IMMOBILIER__VENTE',
    config_path: 'operation/clesence/immobilier/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'COLDWELL_BANKER__CONTRAT_NEGOCIATEUR',
    config_path: 'operation/coldwellBanker/contratNegociateur.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'COLDWELL_BANKER__IMMOBILIER',
    config_path: 'operation/coldwellBanker/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'COTE_PARTICULIERS__IMMOBILIER__VENTE',
    config_path: 'operation/coteParticuliers/immobilier/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'DESIMO__IMMOBILIER__PROGRAMME',
    config_path: 'operation/desimo/immobilier/programme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'DESIMO__IMMOBILIER__VENTE',
    config_path: 'operation/desimo/immobilier/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME',
    config_path: 'operation/domofrance/immobilierAcheve/Programme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'DOMOFRANCE__IMMOBILIER_ACHEVE__VENTE',
    config_path: 'operation/domofrance/immobilierAcheve/Vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'DOMOFRANCE__IMMOBILIER__PROGRAMME',
    config_path: 'operation/domofrance/immobilier/programme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'DOMOFRANCE__IMMOBILIER__VENTE',
    config_path: 'operation/domofrance/immobilier/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'DUVAL__IMMOBILIER__PROGRAMME_VEFA',
    config_path: 'operation/duval/immobilier/programmeVefa.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'DUVAL__IMMOBILIER__VEFA_RESERVATION',
    config_path: 'operation/duval/immobilier/vefaReservation.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'EFFICITY__IMMOBILIER',
    config_path: 'operation/efficity/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'EFFICITY__IMMOBILIER__LOCATION__COMMERCIAL',
    config_path: 'operation/efficity/immobilier/location/commercial.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'EFFICITY__IMMOBILIER__LOCATION__HABITATION',
    config_path: 'operation/efficity/immobilier/location/habitation.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'ERA__IMMOBILIER__VENTE',
    config_path: 'operation/era/immobilier/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'FOLLIOT__IMMOBILIER',
    config_path: 'operation/folliot/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'FOLLIOT__IMMOBILIER_PRO',
    config_path: 'operation/folliot/immobilierPro.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'GIBOIRE__IMMOBILIER__VENTE',
    config_path: 'operation/giboire/immobilier/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'I3F__IMMOBILIER__BRS_PRELIMINAIRE',
    config_path: 'operation/i3F/immobilier/brsPreliminaire.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'I3F__IMMOBILIER__BRS_PROGRAMME',
    config_path: 'operation/i3F/immobilier/brsProgramme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'I3F__IMMOBILIER__PSLA_PRELIMINAIRE',
    config_path: 'operation/i3F/immobilier/pslaPreliminaire.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'I3F__IMMOBILIER__PSLA_PROGRAMME',
    config_path: 'operation/i3F/immobilier/pslaProgramme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'I3F__IMMOBILIER__VEFA_PROGRAMME',
    config_path: 'operation/i3F/immobilier/vefaProgramme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'I3F__IMMOBILIER__VEFA_RESERVATION',
    config_path: 'operation/i3F/immobilier/vefaReservation.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IDEAL__IMMOBILIER__PROGRAMME',
    config_path: 'operation/ideal/immobilier/programme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IDEAL__IMMOBILIER__VENTE',
    config_path: 'operation/ideal/immobilier/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__BRS_PROGRAMME_HYBRIDE',
    config_path: 'operation/immobilier/brsProgrammeHybride.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__BRS_VENTE_HYBRIDE',
    config_path: 'operation/immobilier/brsVenteHybride.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE',
    config_path: 'operation/immobilier/constructionMaisonIndividuelle.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__LOCATION_COMMERCIAL',
    config_path: 'operation/immobilier/locationCommercial.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__LOCATION',
    config_path: 'operation/immobilier/location.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__LOCATION_VISITE',
    config_path: 'operation/immobilier/locationVisite.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__LOTISSEMENT_PROGRAMME',
    config_path: 'operation/immobilier/lotissementProgramme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__LOTISSEMENT_VENTE',
    config_path: 'operation/immobilier/lotissementVente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__POLYNESIE__VENTE',
    config_path: 'operation/immobilier/polynesie/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__PSLA_PRELIMINAIRE',
    config_path: 'operation/immobilier/pslaPreliminaire.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__PSLA_PROGRAMME',
    config_path: 'operation/immobilier/pslaProgramme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__SOCIAL_PROGRAMME',
    config_path: 'operation/immobilier/socialProgramme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__SOCIAL_VENTE',
    config_path: 'operation/immobilier/socialVente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__VENTE_ANCIEN',
    config_path: 'operation/immobilier/venteAncien.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__VENTE_BIEN_PROFESSIONNEL',
    config_path: 'operation/immobilier/venteBienProfessionnel.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__VENTE_NEUF',
    config_path: 'operation/immobilier/venteNeuf.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__VENTES_PROGRAMME',
    config_path: 'operation/immobilier/ventesProgramme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMMOBILIER__VENTE_VIAGER',
    config_path: 'operation/immobilier/venteViager.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'IMOCONSEIL__IMMOBILIER__VENTE',
    config_path: 'operation/imoconseil/immobilier/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'I_PARTICULIERS__IMMOBILIER__ESPAGNE',
    config_path: 'operation/iParticuliers/immobilier/espagne.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'I_PARTICULIERS__IMMOBILIER__HABITATION',
    config_path: 'operation/iParticuliers/immobilier/habitation.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'I_PARTICULIERS__IMMOBILIER__LUXE',
    config_path: 'operation/iParticuliers/immobilier/luxe.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL',
    config_path: 'operation/iParticuliers/immobilier/professionnel.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'I_PARTICULIERS__IMMOBILIER__SUISSE',
    config_path: 'operation/iParticuliers/immobilier/suisse.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'ISM__IMMOBILIER',
    config_path: 'operation/ism/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL',
    config_path: 'operation/kellerWilliams/immobilierCommercial.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'KELLER_WILLIAMS__IMMOBILIER',
    config_path: 'operation/kellerWilliams/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'KELLER_WILLIAMS__IMMOBILIER_LOCATION_HABITATION',
    config_path: 'operation/kellerWilliams/immobilierLocationHabitation.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'KELLER_WILLIAMS__IMMOBILIER_VIAGER',
    config_path: 'operation/kellerWilliams/immobilierViager.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'KELLER_WILLIAMS__LOCATION_COMMERCIAL',
    config_path: 'operation/kellerWilliams/locationCommercial.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'KW_ABONDANCE__IMMOBILIER',
    config_path: 'operation/kwAbondance/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'LFEUR__IMMOBILIER__PROGRAMME',
    config_path: 'operation/lfeur/immobilier/programme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'LFEUR__IMMOBILIER__VENTE',
    config_path: 'operation/lfeur/immobilier/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'LGM__IMMOBILIER',
    config_path: 'operation/lgm/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'MLS__IMMOBILIER',
    config_path: 'operation/mls/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'MY_CHEZ_MOI__IMMOBILIER__VENTE',
    config_path: 'operation/myChezMoi/immobilier/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'MYNOTARY__CONTRAT_SAAS',
    config_path: 'operation/mynotary/contratSaas.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'ORGANISATION_INTERNE',
    config_path: 'operation/organisationInterne.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'ORPI__IMMOBILIER__VENTE',
    config_path: 'operation/orpi/immobilier/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'OZANAM__IMMOBILIER__PSLA_PRELIMINAIRE',
    config_path: 'operation/ozanam/immobilier/pslaPreliminaire.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'OZANAM__IMMOBILIER__PSLA_PROGRAMME',
    config_path: 'operation/ozanam/immobilier/pslaProgramme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PIERRE_REVENTE__IMMOBILIER',
    config_path: 'operation/pierreRevente/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PODELIHA__IMMOBILIER__PROGRAMME',
    config_path: 'operation/podeliha/immobilier/programme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PODELIHA__IMMOBILIER__VENTE',
    config_path: 'operation/podeliha/immobilier/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PRELLO__IMMOBILIER__LOCATION',
    config_path: 'operation/prello/immobilier/location.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PROPRIETES_PRIVEES__BUSINESS',
    config_path: 'operation/proprietesPrivees/business.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PROPRIETES_PRIVEES__IMMOBILIER',
    config_path: 'operation/proprietesPrivees/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PROPRIETES_PRIVEES__IMMOBILIER_LOCATION',
    config_path: 'operation/proprietesPrivees/immobilierLocation.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PROPRIETES_PRIVEES__IMMORESEAU_BUSINESS',
    config_path: 'operation/proprietesPrivees/immoreseauBusiness.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PROPRIETES_PRIVEES__IMMORESEAU',
    config_path: 'operation/proprietesPrivees/immoreseau.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PROPRIETES_PRIVEES__IMMORESEAU_LOCATION',
    config_path: 'operation/proprietesPrivees/immoreseauLocation.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PROPRIETES_PRIVEES__MIZAPRI',
    config_path: 'operation/proprietesPrivees/mizapri.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PROPRIETES_PRIVEES__REZOXIMO',
    config_path: 'operation/proprietesPrivees/rezoximo.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PROPRIETES_PRIVEES__VIAGER',
    config_path: 'operation/proprietesPrivees/viager.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE',
    config_path: 'operation/pvciSens/immobilier/programmeAcheve.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PVCI_SENS__IMMOBILIER__PROGRAMME',
    config_path: 'operation/pvciSens/immobilier/programme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PVCI_SENS__IMMOBILIER__VENTE_ACHEVE',
    config_path: 'operation/pvciSens/immobilier/venteAcheve.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PVCI_SENS__IMMOBILIER__VENTE',
    config_path: 'operation/pvciSens/immobilier/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'PVCI_SENS__IMMOBILIER__VENTE_SEULE',
    config_path: 'operation/pvciSens/immobilier/venteSeule.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'RECRUTEMENT__AGENT',
    config_path: 'operation/recrutement/agent.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'RECRUTEMENT__KW_EXPAND__AGENT',
    config_path: 'operation/recrutement/kwExpand/agent.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'RECRUTEMENT__KW_POLYNESIE__AGENT',
    config_path: 'operation/recrutement/kwPolynesie/agent.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'RECRUTEMENT__MYNOTARY__AGENT',
    config_path: 'operation/recrutement/mynotary/agent.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'SD_ACCESS__IMMOBILIER_HYBRIDE__PROGRAMME',
    config_path: 'operation/sdAccess/immobilierHybride/programme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'SD_ACCESS__IMMOBILIER_HYBRIDE__VENTE',
    config_path: 'operation/sdAccess/immobilierHybride/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'SD_ACCESS__IMMOBILIER__PROGRAMME',
    config_path: 'operation/sdAccess/immobilier/programme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'SD_ACCESS__IMMOBILIER__VENTE',
    config_path: 'operation/sdAccess/immobilier/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'SEXTANT__IMMOBILIER_COMMERCIAL',
    config_path: 'operation/sextant/immobilierCommercial.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'SEXTANT__IMMOBILIER',
    config_path: 'operation/sextant/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'SYNDIC__GENERAL',
    config_path: 'operation/syndic/general.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'UNIS__COPROPRIETE',
    config_path: 'operation/unis/copropriete.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'UNIS__IMMOBILIER__COMMERCIAL',
    config_path: 'operation/unis/immobilier/commercial.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'UNIS__IMMOBILIER__HABITATION',
    config_path: 'operation/unis/immobilier/habitation.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'UNIS__LOCATION__COMMERCIAL',
    config_path: 'operation/unis/location/commercial.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'UNIS__LOCATION__HABITATION',
    config_path: 'operation/unis/location/habitation.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'UNIS__ORGANISATION_INTERNE',
    config_path: 'operation/unis/organisationInterne.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'UNIS__SOCIAL',
    config_path: 'operation/unis/social.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME',
    config_path: 'operation/valloire/immobilierAcheve/programme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'VALLOIRE__IMMOBILIER_ACHEVE__VENTE',
    config_path: 'operation/valloire/immobilierAcheve/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'VALLOIRE__IMMOBILIER_NEUF__PROGRAMME',
    config_path: 'operation/valloire/immobilierNeuf/programme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'VALLOIRE__IMMOBILIER_NEUF__VENTE',
    config_path: 'operation/valloire/immobilierNeuf/vente.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'VIAGER_CONSULTING__IMMOBILIER',
    config_path: 'operation/viagerConsulting/immobilier.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'WHITEBIRD__GENERAL_LOCATION',
    config_path: 'operation/whitebird/generalLocation.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'WHITEBIRD__GENERAL_PROGRAMME',
    config_path: 'operation/whitebird/generalProgramme.json',
    documentsToExclude: [],
    documentsToInclude: []
  },
  {
    id: 'WHITEBIRD__LOCATION_HABITATION',
    config_path: 'operation/whitebird/locationHabitation.json',
    documentsToExclude: [],
    documentsToInclude: []
  }
];
