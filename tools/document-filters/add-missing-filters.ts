import * as fs from 'fs';
import * as path from 'path';
import { forEach } from 'lodash';

interface FormNode {
  id: string;
  type: string;
  children?: FormNode[];
  filters?: {
    "excludedByDefault"?: true;
    "includedByDefault"?: true;
  }
}

let currentFilter: any = null;

/**
 * Add mustBeIncludedInOperationConfig or mustBeExcludedInOperationConfig to filters object
 */
function addQuestionFilters(formNode: FormNode) {
  if (formNode.filters?.excludedByDefault) {
    currentFilter[formNode.id] = {
      ...currentFilter[formNode.id],
      mustBeIncludedInOperationConfig: true
    };
    }

    if (formNode.filters?.includedByDefault) {
      currentFilter[formNode.id] = {
        ...currentFilter[formNode.id],
        mustBeExcludedInOperationConfig: true
      };
  }

  if (formNode.children) {
    forEach(formNode.children, (child) => addQuestionFilters(child)).flat();
  }
}


/**
 * Open filters.json corresponding to the computed form json file
 */
function openFiltersFile(formPath: string): string {
  let filtersPath = formPath.replace('.json', '');
  filtersPath = filtersPath.replace('src/main/resources/files/forms/johnson', 'forms');
  filtersPath = `${filtersPath}/filters.json`;
  const jsonString = fs.readFileSync(filtersPath, 'utf-8');
  currentFilter = JSON.parse(jsonString);

  return filtersPath;
}

/**
 * Recursively open all forms.json.
 *
 * @param directoryPath
 */
export async function addMissingFilters(directoryPath: string): Promise<void> {
  try {
    const files = fs.readdirSync(directoryPath);

    for (const file of files) {
      const filePath = path.join(directoryPath, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        await addMissingFilters(filePath);
      } else if (filePath.includes('.json')) {
        const filtersPath  = openFiltersFile(filePath);
        const jsonString = fs.readFileSync(filePath, 'utf-8');
        const formNodes: FormNode[] = JSON.parse(jsonString);
        forEach(formNodes, (formNode) => addQuestionFilters(formNode));

        const modifiedJsonString = JSON.stringify(currentFilter, null, 2);
        fs.writeFileSync(filtersPath, modifiedJsonString, 'utf-8');
      }
    }
  } catch (error) {
    console.error(`Error processing directory: ${directoryPath}`, error);
  }
}

addMissingFilters('/Users/<USER>/work/api-java-mynotary/src/main/resources/files/forms/johnson/');