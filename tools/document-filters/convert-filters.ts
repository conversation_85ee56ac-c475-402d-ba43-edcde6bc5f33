import * as fs from 'fs';
import * as path from 'path';
import { operationConfigs, OperationConfigId } from './operation-configs';
import { forEach, find, uniq, map, isEmpty } from 'lodash';
import { extractDocumentIds, documentIds } from './extract-document-ids';

interface FormFilter {
  operationTemplates: {
    included?: OperationConfigId[][];
    excluded?: OperationConfigId[][];
  };
}

type JsonStructure = Record<string, FormFilter>;

/**
 * Add document ids to operation configs.
 * Remove or convert operationTemplates in filters.json.
 */
async function convertFormFilters(filePath: string): Promise<void> {
  try {
    const jsonString = fs.readFileSync(filePath, 'utf-8');
    const jsonObject: JsonStructure = JSON.parse(jsonString);

    for (const [questionId, filter] of Object.entries(jsonObject)) {
      if (filter.operationTemplates != null && documentIds.has(questionId)) {
        addDocumentIdsToConfigs(questionId, filter, 'included');
        addDocumentIdsToConfigs(questionId, filter, 'excluded');
      }

      removeOperationTypesFromFormFilters(questionId, filter);
      if (isEmpty(filter)) {
        // @ts-ignore
        delete jsonObject[questionId];
      }
    }

    const modifiedJsonString = JSON.stringify(jsonObject, null, 2);
    fs.writeFileSync(filePath, modifiedJsonString, 'utf-8');
  } catch (error) {
    console.error(`Error processing file: ${filePath}`, error);
  }
}

/**
 * Add questionId to documentsToInclude or documentsToExclude in operationConfigs.
 */
function addDocumentIdsToConfigs(questionId: string, filter: FormFilter, type: 'included' | 'excluded') {
  const specificTypes = map(filter.operationTemplates[type], (array) => array.join('__'));

  forEach(specificTypes, (targetId) => {
    const config = find(operationConfigs, (config) => config.id === targetId);

    if (config != null && type === 'excluded') {
      config.documentsToExclude.push(questionId);
    } else if (config != null && type === 'included') {
      config.documentsToInclude.push(questionId);
    }
  });
}

/**
 * Remove operationTemplates from form filters if question is not a document otherwise convert to boolean.
 */
function removeOperationTypesFromFormFilters(questionId: string, filter: FormFilter) {
  // @ts-ignore
  if (!documentIds.has(questionId)) {
    // @ts-ignore
    delete filter.operationTemplates;
  } else if (filter.operationTemplates?.excluded != null) {
    // @ts-ignore
    filter.mustBeExcludedInOperationConfig = true;
  } else if (filter.operationTemplates?.included != null) {
    // @ts-ignore
    filter.mustBeIncludedInOperationConfig = true;
  }

  // @ts-ignore
  delete filter.operationTemplates;
}

/**
 * Recursively open all filters.json.
 */
async function convertFormsFilters(directoryPath: string): Promise<void> {
  try {
    const files = fs.readdirSync(directoryPath);

    for (const file of files) {
      const filePath = path.join(directoryPath, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        await convertFormsFilters(filePath);
      } else if (filePath.includes('filters.json')) {
        await convertFormFilters(filePath);
      }
    }
  } catch (error) {
    console.error(`Error processing directory: ${directoryPath}`, error);
  }
}

/**
 * Add documentsToInclude and documentsToExclude to config files.
 */
function updateOperationConfigFiles(): void {
  forEach(operationConfigs, (config) => {
    const jsonString = fs.readFileSync(
      `/Users/<USER>/work/api-java-mynotary/src/main/resources/files/config/${config.config_path}`,
      'utf-8'
    );
    const jsonObject: any = JSON.parse(jsonString);


    jsonObject.documentsToExclude = uniq([...jsonObject.documentsToExclude, ...config.documentsToExclude]).sort();
    jsonObject.documentsToInclude = uniq([...jsonObject.documentsToInclude, ...config.documentsToInclude]).sort();

    const modifiedJsonString = JSON.stringify(jsonObject, null, 2);
    fs.writeFileSync(
      `/Users/<USER>/work/api-java-mynotary/src/main/resources/files/config/${config.config_path}`,
      modifiedJsonString,
      'utf-8'
    );
  });
  console.log(`Done !`);
}

async function main() {
  await extractDocumentIds('/Users/<USER>/work/api-java-mynotary/src/main/resources/files/forms/johnson/');
  await convertFormsFilters('/Users/<USER>/work/api-java-mynotary/forms');
  updateOperationConfigFiles();
}
main();
