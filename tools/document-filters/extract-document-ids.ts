import * as fs from 'fs';
import * as path from 'path';
import { forEach } from 'lodash';

export let documentIds: Set<string> = new Set();

interface FormNode {
  id: string;
  type: string;
  children?: FormNode[];
}

/**
 * Extract all form nodes with type 'UPLOAD' recursively
 * @param FormNode
 */
function extractUploadNodes(formNode: FormNode) {
  if (formNode.type === 'UPLOAD') {
    documentIds.add(formNode.id);
  }

  if (formNode.children) {
    forEach(formNode.children, (child) => extractUploadNodes(child)).flat();
  }
}

/**
 * Recursively open all forms.json.
 * Extract all form nodes with type 'UPLOAD' and add them to documentIds.
 * @param directoryPath
 */
export async function extractDocumentIds(directoryPath: string): Promise<void> {
  try {
    const files = fs.readdirSync(directoryPath);

    for (const file of files) {
      const filePath = path.join(directoryPath, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        await extractDocumentIds(filePath);
      } else if (filePath.includes('.json')) {
        const jsonString = fs.readFileSync(filePath, 'utf-8');
        const formNodes: FormNode[] = JSON.parse(jsonString);

        forEach(formNodes, (formNode) => extractUploadNodes(formNode));
      }
    }
  } catch (error) {
    console.error(`Error processing directory: ${directoryPath}`, error);
  }
}