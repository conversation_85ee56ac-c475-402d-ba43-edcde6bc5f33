

-- Add test user
INSERT INTO public."user" (id, email, is_admin, firstname, lastname, verified, civility, authentication_token, admin_type)
VALUES (1, '<EMAIL>', TRUE, 'Jean', 'DUPOND', TRUE, 'MAN', '<EMAIL>', 'DEV');

INSERT INTO public."user" (id, email, is_admin, firstname, lastname, verified, civility, authentication_token, admin_type)
VALUES (2, '<EMAIL>', FALSE, 'Jean', 'MARTIN', TRUE, 'MAN', '<EMAIL>', null);

INSERT INTO public."user" (id, email, is_admin, firstname, lastname, verified, civility, authentication_token, admin_type)
VALUES (3, '<EMAIL>', TRUE, 'Jean', 'LADMIN', TRUE, 'MAN', '<EMAIL>', 'DEV');

INSERT INTO public."user" (id, email, is_admin, firstname, lastname, verified, civility, authentication_token, admin_type)
VALUES (4, '<EMAIL>', FALSE, 'Jean', 'FEATURE_FLAG', TRUE, 'MAN', '<EMAIL>', null);

INSERT INTO public."user" (id, email, is_admin, firstname, lastname, verified, civility, authentication_token, admin_type)
VALUES (5, '<EMAIL>', FALSE, 'Jean', 'INVITED', TRUE, 'MAN', '<EMAIL>', null);


-- Add subscription
INSERT INTO public.subscription (id, status, payment_url, plan_type, addons, licence_count, provider_subscription_id, creation_time, tax_type, customer_lastname, customer_firstname, customer_civility, customer_address, customer_siren, customer_organization_name, customer_email, licence_used_count, update_time) VALUES (1, 'LIVE', 'https://subscriptions.zoho.com/hostedpage/2-3afcb16a774802164de8b6e57ab42472b89c394ef6262865fa0e1d38444a7054c8187aee372ec2a22f424b9bc80d0d4f/checkout', 'PREMIUM', '{PERSONNALISATION_APPLICATION}', 4, '3033897000001308021', '2023-09-01 14:09:14.285000', 'TVA_20', 'SALETES', 'Sebastien', 'WOMAN', '{"zip": "75010", "city": "Paris", "street": "1 Rue de Paradis, Paris, France, Paris, France (75010) ", "address": "1 Rue de Paradis, Paris, France, Paris, France (75010) , Paris, France", "formattedAddress": "1 Rue de Paradis, Paris, France, Paris, France (75010) , Paris, France (75010) "}', '*********', 'Sebastien SALETES', '<EMAIL>', 3, '2023-10-17 10:15:13.459000 +00:00');

-- Add organization
INSERT INTO public.organization (id,
                                 type,
                                 unique_identifier,
                                 name,
                                 address,
                                 creation_time,
                                 latitude,
                                 longitude,
                                 default_role_id,
                                 subscription_id
)
VALUES (1,
        'AGENCY',
        '111111111$$69006',
        'Agence de test',
        '{
          "city": "Lyon",
          "zip": "69006",
          "location": {"lat": 45.765646, "lng": 4.8473815},
          "address": "6 Rue Amédée Bonnet, 69006 Lyon, France",
          "placeId": "ChIJfWoYtfXq9EcREU82zELXwJ0",
          "formatted_address": "6 Rue Amédée Bonnet, 69006 Lyon, France"
        }',
        '2021-03-03 16:58:31.383776',
        45.765646,
        4.8473815,
        1,
        1);


-- Add organization roles
INSERT INTO public.role (id, organization_id, name, is_default, is_admin)
VALUES (1, 1, 'Admin', true, true);
INSERT INTO public.role (id, organization_id, name, is_default, is_admin)
VALUES (2, 1, 'Responsable', true, false);
INSERT INTO public.role (id, organization_id, name, is_default, is_admin)
VALUES (3, 1, 'Collaborateur', true, false);
INSERT INTO public.role (id, organization_id, name, is_default, is_admin)
VALUES (4, 1, 'Notaire', true, false);

INSERT INTO public.role_permission (role_id, permission_id)
SELECT 1, id FROM public.permission;

-- Add organization user
INSERT INTO public.organization_user (id, organization_id, user_id, email, role_id)
VALUES (1, 1, 1, '<EMAIL>', 1);

INSERT INTO public.organization_user (id, organization_id, user_id, email, role_id)
VALUES (2, 1, 4, '<EMAIL>', 1);

-- Add organization feature
INSERT INTO public.organization_feature (id, organization_id, feature_id, feature_parameters, enabled)
VALUES (1, 1, 'V3_ACCESS', '{"v3Access": {"operations": [["AUTRE", "LIBRE"], ["DUVAL", "IMMOBILIER", "PROGRAMME_VEFA"], ["IMMOBILIER", "VENTE_ANCIEN"], ["IMMOBILIER", "VENTE_VIAGER"], ["DUVAL", "IMMOBILIER", "VEFA_RESERVATION"]]}}', TRUE);

INSERT INTO public.organization_feature (id, organization_id, feature_id, feature_state, enabled)
VALUES (2, 1, 'SIGNATURE_CREDITS', '{"signatureCredits": {"number": 500}}', TRUE);
INSERT INTO public.organization_feature (id, organization_id, feature_id, feature_state, enabled)
VALUES (3, 1, 'REGISTERED_LETTER_CREDITS', '{"registeredLetterCredits": {"number": 500}}', TRUE);
INSERT INTO public.organization_feature (id, organization_id, feature_id, feature_state, enabled)
VALUES (4, 1, 'VALIDATION_CUSTOMIZATION', '[]', TRUE);
INSERT INTO public.organization_feature (id, organization_id, feature_id, feature_state, enabled)
VALUES (5, 1, 'ADVANCED_SIGNATURE_CREDITS', '{"advancedSignatureCredits": {"number": 0}}', TRUE);
INSERT INTO public.organization_feature (id, organization_id, feature_id, feature_state, enabled)
VALUES (6, 1, 'TRANSACTION_REGISTER_ACCESS', '{"registerStart": {"number": 1, "cardNumber": "carte transaction"}}', TRUE);
INSERT INTO public.organization_feature (id, organization_id, feature_id, enabled) VALUES (7, 1, 'RECORD_SHARING', TRUE);
INSERT INTO public.organization_feature (id, organization_id, feature_id, enabled) VALUES (8, 1, 'ROLE_MANAGEMENT', true);
INSERT INTO public.organization_feature (id, organization_id, feature_id,  enabled) VALUES (9, 1, 'EXTERNAL_PARTICIPANTS', true);
INSERT INTO public.organization_feature (id, organization_id, feature_id, feature_state, enabled)
VALUES (10, 1, 'PRE_ETAT_DATE_CREDITS', '{"preEtatDateCredits": {"number": 500}}', TRUE);


-- Add default views
INSERT INTO public.custom_view (id, creator_user_id, organization_id, is_public, is_default, type, label, filters) VALUES (1, null, 1, true, true, 'OPERATION', 'Tous les dossiers', '{}');
INSERT INTO public.custom_view (id, creator_user_id, organization_id, is_public, is_default, type, label, filters) VALUES (2, null, 1, true, true, 'OPERATION', 'Consultés récemment', '{"LAST_ACCESS": {"id": "LAST_ACCESS", "label": "Accès", "values": {"MONTH": {"id": "MONTH", "label": "30 derniers jours", "selected": true}}}}');
INSERT INTO public.custom_view (id, creator_user_id, organization_id, is_public, is_default, type, label, filters) VALUES (3, null, 1, true, true, 'OPERATION', 'Dossiers archivés', '{"ARCHIVE_OPERATION": {"id": "ARCHIVE_OPERATION", "label": "État", "values": {"ACTIVE": {"id": "ARCHIVED", "label": "Archivé", "selected": true}}}}');
INSERT INTO public.custom_view (id, creator_user_id, organization_id, is_public, is_default, type, label, filters) VALUES (4, null, 1, true, true, 'OPERATION_CONTRACT', 'Tous les contrats', '{}');
INSERT INTO public.custom_view (id, creator_user_id, organization_id, is_public, is_default, type, label, filters) VALUES (5, null, 1, true, true, 'OPERATION_CONTRACT', 'Consultés récemment', '{"LAST_ACCESS": {"id": "LAST_ACCESS", "label": "Accès", "values": {"MONTH": {"id": "MONTH", "label": "30 derniers jours", "selected": true}}}}');

-- Add register mandate signature
INSERT INTO public.signature (id, creator_user_id, token, association_type, document_type, provider_type)
VALUES (1, 1, '', '', '', 'YOUSIGN_V2');
INSERT INTO public.signature_association_organization_document (id, signature_id, organization_id) VALUES (1, 1, 1);
INSERT INTO public.signature_file (id, signature_id, signed_file_id, original_file_id, to_sign_file_id, "order")
VALUES (1, 1, 'STATIC_FILES_MYNOTARY_LOGO', 'STATIC_FILES_MYNOTARY_LOGO', 'STATIC_FILES_MYNOTARY_LOGO', 1);


-- Add mandatories applications
INSERT INTO public.application (name, public, api_key) VALUES ('MYNOTARY', true, 'fake-mynotary-key');
INSERT INTO public.application (name, public, api_key) VALUES ('GCP', true, gen_random_uuid());

INSERT INTO public.application_organization VALUES (1, 1);

-- Increment all sequences by 10000 to avoid duplicate primary key on table where we have inserted data
DO $$
  DECLARE
    seq RECORD;
  BEGIN
    -- Loop through all sequences in the current database
    FOR seq IN
      SELECT c.relname AS sequence_name, n.nspname AS schema_name
      FROM pg_class c
             JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE c.relkind = 'S' -- 'S' is the type for sequences
      LOOP
        -- Construct and execute the ALTER SEQUENCE statement
        EXECUTE 'ALTER SEQUENCE ' || quote_ident(seq.schema_name) || '.' || quote_ident(seq.sequence_name) || ' RESTART WITH ' || (SELECT nextval(seq.schema_name || '.' || seq.sequence_name) + 10000);
      END LOOP;
  END $$;
