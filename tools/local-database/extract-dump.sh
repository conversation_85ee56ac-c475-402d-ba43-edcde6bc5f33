export HOST=''
export PGPASSWORD=''

# Schema dump
pg_dump --schema-only --no-owner --no-privileges -h $HOST -U api -d mynotary -f schema_only.sql

# Data dump for specific tables using INSERT statements
pg_dump --data-only -h $HOST -U api -d mynotary \
    -t clause \
    -t contract_model_clause \
    -t email_template \
    -t legal_component_template \
    -t operation_contract_model \
    -f data_only.sql

# Combine the dumps
cat schema_only.sql >> final_dump.sql
cat data_only.sql >> final_dump.sql
rm schema_only.sql data_only.sql
mv final_dump.sql ./dump-prod.sql
