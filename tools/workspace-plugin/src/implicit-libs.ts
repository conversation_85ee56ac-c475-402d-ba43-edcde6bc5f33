import { dirname } from 'node:path';
import { CreateNodes } from '@nx/devkit';

export const createNodes: CreateNodes = [
  '**/index.ts',
  (indexFilePath) => {
    const projectPath = dirname(indexFilePath);
    const tokens = indexFilePath.split('/');
    const platform = tokens[tokens.length - 4];
    const scope = tokens[tokens.length - 3];
    const projectName = tokens[tokens.length - 2];
    let type = projectName;

    /**
     * This is a special case for the shared folder.
     * Folder name can represent the scope and the type eg : files-util, nestjs-infra...
     * In this case we want to extract the type from the folder name.
     */
    if (scope === 'shared' && projectName.includes('-')) {
      type = type.split('-').pop() ?? projectName;
    }

    if (
      ![
        'api',
        'authorization',
        'core',
        'feature',
        'infra',
        'openapi',
        'providers',
        'store',
        'test',
        'ui',
        'util'
      ].includes(type)
    ) {
      return {};
    }

    return {
      projects: {
        [projectPath]: {
          name: `${platform}-${scope}-${projectName}`,
          root: projectPath,
          tags: [`platform:${platform}`, `scope:${scope}`, `type:${type}`]
        }
      }
    };
  }
];
