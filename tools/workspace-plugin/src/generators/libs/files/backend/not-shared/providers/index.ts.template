<%- hasTypeApi   ? `import { provide${ scopePascal }Api } from '@mynotary/${ platform }/${ scope }/api';` : '' %>
<%- hasTypeCore  ? `import { provide${ scopePascal }Core } from '@mynotary/${ platform }/${ scope }/core';` : '' %>
<%- hasTypeInfra ? `import { provide${ scopePascal }Infra } from '@mynotary/${ platform }/${ scope }/infra';` : '' %>

export const provide<%= scopePascal %>Scope = () => {
  return [<%= hasTypeApi ? `...provide${ scopePascal }Api(), ` : '' %><%= hasTypeCore ? `...provide${ scopePascal }Core(), ` : '' %><%= hasTypeInfra ? `...provide${ scopePascal }Infra(), ` : '' %>];
}
