import { Controller, Get } from '@nestjs/common';
import { <%= scopePascal %>Service } from '@mynotary/backend/<%= scope %>/core';

@Controller()
export class <%= scopePascal %>Controller {

  constructor(private <%= scopeCamel %>Service: <%= scopePascal %>Service) {}

  @Get('/<%= scope %>')
  async get<%= scopePascal %>() {
    return await this.<%= scopeCamel%>Service.get<%= scopePascal %>();
  }
}
