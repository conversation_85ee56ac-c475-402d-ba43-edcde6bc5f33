import { <%= scopePascal %>Controller } from './<%= scope %>.controller';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import { provide<%= scopePascal %>Scope } from '@mynotary/backend/<%= scope %>/providers';

describe(<%= scopePascal %>Controller.name, () => {

  it.todo('MUST TEST <%= scopePascal %>Controller');

  async function setup() {
    const { client, getService, setCurrentUser } = await createTestingWideApp({
      bypassAuth: true,
      controller: <%= scopePascal %>Controller,
      providers: provide<%= scopePascal %>Scope()
    });

    const testingRepos = getService(TestingRepositories);

    return {
      client,
      testingRepos
    };
  }
});
