import { PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { Injectable } from '@nestjs/common';
import { <%= scopePascal %>Repository, Get<%= scopePascal %>Args } from '@mynotary/backend/<%= scope %>/core';

@Injectable()
export class <%= scopePascal %>RepositoryImpl implements <%= scopePascal %>Repository {
  constructor(private prisma: PrismaService) {}

  async get<%= scopePascal %>(args: Get<%= scopePascal %>Args) {
    throw new Error('Method not implemented.');
  }
}
