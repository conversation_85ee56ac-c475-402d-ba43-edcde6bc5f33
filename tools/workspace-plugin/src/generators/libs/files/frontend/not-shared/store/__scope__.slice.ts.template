import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface <%= scopePascal %> {};
const initialState: <%= scopePascal %> = {};

export const <%= scopeCamel %>Slice = createSlice({
  initialState,
  name: '<%= scopeCamel %>',
  reducers: {
    add<%= scopePascal %>: (state, action: PayloadAction<{}>) => {

    }
  }
});

export const { add<%= scopePascal %> } = <%= scopeCamel %>Slice.actions;

export interface <%= scopePascal %>State {
  [<%= scopeCamel %>Slice.name]: Roles;
}

export const select<%= scopePascal %>Feature = (state: <%= scopePascal %>State) => state[<%= scopeCamel %>Slice.name];
