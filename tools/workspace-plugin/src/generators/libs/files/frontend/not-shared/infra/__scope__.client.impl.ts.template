import { createAxiosInstance } from '@mynotary/frontend/shared/axios-util';
import { <%= scopePascal %>Client, Get<%= scopePascal %>Args } from '@mynotary/frontend/<%= scope %>/core';


export class <%= scopePascal %>ClientImpl implements <%= scopePascal %>Client {
  apiClient = createAxiosInstance({ baseURL: `` });

  async get<%= scopePascal %>(args: Get<%= scopePascal %>Args) {
    throw new Error('Method not implemented.');
  }
}
