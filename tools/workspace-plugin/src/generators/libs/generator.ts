import { formatFiles, generateFiles, Tree, updateJson } from '@nx/devkit';
import { checkbox, input, select } from '@inquirer/prompts';
import { forEach, camelCase, upperFirst } from 'lodash';
import * as path from 'path';
import * as fs from 'fs';

export async function scopeGenerator(tree: Tree, __options: unknown) {
  const data = await getDataFromPrompt();
  addNxBoundariesScopeConstraint(tree, data.scope);
  addIndexFilesToLibs(tree, data);
  addPathToTsConfigBase(tree, data);
  await formatFiles(tree);
}

async function getDataFromPrompt(): Promise<ScopeGeneratorArgs> {
  const platform = await select<Platform>({
    choices: [{ value: 'frontend' }, { value: 'backend' }, { value: 'crossplatform' }],
    message: 'What platform would you like to use?'
  });
  const scope = await input({
    message: 'What functional scope would you like to use? (e.g. files, operations, users, shared, etc...))',
    validate: (res) => (res.length < 3 ? 'Scope must be at least 3 characters long' : true)
  });

  const allowedTypes = getAllowedLibTypes({ platform, scope });
  const types = await checkbox({
    choices: allowedTypes.map((scope) => ({ name: scope, value: scope })),
    message: 'What type of library is it?',
    required: true
  });

  let name = '';

  if (scope === 'shared') {
    name = await input({
      message: 'Would you like to name your shared library? (e.g. nestjs-infra, files-util, forms-util, etc...))',
      validate: (res) => (res.length < 3 ? 'Name must be at least 3 characters long' : true)
    });
  }
  return { name, platform, scope, types };
}

const addNxBoundariesScopeConstraint = (tree: Tree, scope: string) => {
  const scopeTag = `scope:${scope}`;

  updateJson(tree, '.eslintrc.json', (eslintConfig) => {
    const depConstraints = eslintConfig.overrides[0].rules['@nx/enforce-module-boundaries'][1]['depConstraints'];

    /* Update the file only if the constraint is not already present. */
    if (!depConstraints.some((constraint: { sourceTag: string }) => constraint.sourceTag === scopeTag)) {
      depConstraints.push({
        onlyDependOnLibsWithTags: [scopeTag, 'scope:shared', 'type:api', 'type:openapi', 'type:providers'],
        sourceTag: scopeTag
      });
    }

    return eslintConfig;
  });
};

/**
 * Returns the substitutions for the templates
 * @param types : LibType[] LibTypes to be generated
 * @param platform : Platform
 * @param scope : string the business scope
 */
function getSubstitutions(types: LibType[], platform: Platform, scope: string) {
  return {
    hasTypeApi: types.includes('api'),
    hasTypeCore: types.includes('core'),
    hasTypeInfra: types.includes('infra'),
    platform: platform,
    scope: scope,
    scopeCamel: camelCase(scope),
    scopePascal: upperFirst(camelCase(scope))
  };
}

/**
 * Finds the specific template directory if any, or the default one
 * Templates are organized in the files/ directory using the following structure: {platform}/{'shared' | 'not-shared'}/{type}
 * @param tree  : Tree nx tree
 * @param platform : Platform
 * @param scope : string the business scope
 * @param type : LibType the type of the library
 */
function getTemplateDir(tree: Tree, platform: Platform, scope: string, type: LibType) {
  const defaultPath = path.join(__dirname, 'files', 'default');
  let templateDir = path.join(__dirname, 'files', platform, scope !== 'shared' ? 'not-shared' : 'shared', type);
  // if templateDir does not exist, use defaultPath
  if (!fs.existsSync(templateDir)) {
    templateDir = defaultPath;
  }

  return templateDir;
}

function addIndexFilesToLibs(tree: Tree, args: ScopeGeneratorArgs) {
  const { name, platform, scope, types } = args;

  forEach(types, (type) => {
    const fullType = name ? `${name}-${type}` : `${type}`;
    const projectRoot = `libs/${platform}/${scope}/${fullType}`;
    const templateDir = getTemplateDir(tree, platform, scope, type);
    const substitutions = getSubstitutions(types, platform, scope);
    generateFiles(tree, templateDir, projectRoot, substitutions);
  });
}

function addPathToTsConfigBase(tree: Tree, args: ScopeGeneratorArgs) {
  const { name, platform, scope, types } = args;

  forEach(types, (type) => {
    const fullType = name ? `${name}-${type}` : `${type}`;
    updateJson(tree, 'tsconfig.base.json', (tsConfig) => {
      const paths = tsConfig['compilerOptions']['paths'];
      paths[`@mynotary/${platform}/${scope}/${fullType}`] = [`libs/${platform}/${scope}/${fullType}/index.ts`];
      return tsConfig;
    });
  });
}

const getAllowedLibTypes = ({ platform, scope }: { platform: Platform; scope: string }): LibType[] => {
  return scope === 'shared' ? _scopeSharedLibTypes[platform] : _platformLibTypes[platform];
};

const _platformLibTypes: Record<Platform, LibType[]> = {
  backend: ['api', 'authorization', 'core', 'feature', 'infra', 'providers'],
  crossplatform: ['api', 'core', 'infra', 'openapi'],
  frontend: ['api', 'core', 'feature', 'infra', 'store', 'ui']
};
const _scopeSharedLibTypes: Record<Platform, LibType[]> = {
  backend: ['authorization', 'infra', 'util'],
  crossplatform: ['core', 'infra', 'util'],
  frontend: ['infra', 'ui', 'util']
};

interface ScopeGeneratorArgs {
  name?: string;
  platform: Platform;
  scope: string;
  types: LibType[];
}

type LibType = 'api' | 'authorization' | 'core' | 'feature' | 'infra' | 'providers' | 'openapi' | 'store' | 'ui' | 'util';

type Platform = 'frontend' | 'backend' | 'crossplatform';

export default scopeGenerator;
