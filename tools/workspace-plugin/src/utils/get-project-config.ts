import { dirname, join } from 'node:path';
import { readFileSync } from 'node:fs';
import { ProjectConfiguration } from '@nx/devkit';

export function getProjectConfig(testPath: string) {
  const parentPath = dirname(testPath);

  /* No project.json. */
  if (parentPath === testPath) {
    throw new Error(`Could not find project.json for "${testPath}".`);
  }

  const configPath = join(parentPath, 'project.json');

  try {
    return {
      projectConfig: JSON.parse(readFileSync(configPath, 'utf-8')) as ProjectConfiguration,
      projectPath: parentPath
    };
  } catch (e) {
    if (e != null && typeof e === 'object' && 'code' in e && e.code === 'ENOENT') {
      return getProjectConfig(parentPath);
    }
    throw e;
  }
}