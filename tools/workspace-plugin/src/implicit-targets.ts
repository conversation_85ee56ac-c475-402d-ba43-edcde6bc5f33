import { CreateNodes } from '@nx/devkit';
import { dirname } from 'node:path';
import { globIterateSync } from 'glob';

/**
 * Create implicit targets for all library projects.
 *
 * Depending on the directory structure and the files present in the library project, the following implicit targets are created:
 * - `lint`: Lint typescript files of the library project.
 * - `test`: Run the tests of the library project.
 * - `test-scheduled`: Run the scheduled tests of the library project.
 * - `prisma-codegen`: Generate the Prisma client for the library project.
 * - `prisma-setup-testing`: Setup the testing database by overiding prisma schema and seed intial data.
 * - `codegen`: Generate code from the OpenAPI spec for the library project.
 * - `generate-guards-goldens`: Generate the guards goldens for the library project.
 * - `stylelint`: Lint the SCSS files of the library project.
 *
 * @param indexPath The path to the index file of the library project.
 * @returns The implicit targets for the library project.
 */
export const createNodes: CreateNodes = [
  'libs/*/*/*/index.ts',
  (indexPath) => {
    const projectPath = dirname(indexPath);
    const [libs, platform, scope, projectName] = indexPath.split('/');

    if (!/(api|authorization|core|feature|infra|openapi|providers|store|test|ui|util)$/.test(projectName)) {
      return {};
    }

    const hasScheduledTests = hasFileMatching(`${projectPath}/**/*.scheduled.spec.ts`);
    const hasSnapshotTests = hasFileMatching(`${projectPath}/**/*.snapshot.spec.ts`);
    const hasPrismaSchema = hasFileMatching(`${projectPath}/schema.prisma`);
    const hasOpenApiSpec = hasFileMatching(`${projectPath}/**/*.openapi.yaml`);
    const hasController = hasFileMatching(`${projectPath}/**/*.controller.ts`);
    const hasScss = hasFileMatching(`${projectPath}/**/*.scss`);
    const hasTest = hasFileMatching(`${projectPath}/**/*.spec.ts`);
    const isBackend = platform === 'backend';

    return {
      projects: {
        [projectPath]: {
          name: `${platform}-${scope}-${projectName}`,
          root: projectPath,
          targets: {
            lint: {
              cache: true,
              executor: '@nx/eslint:lint',
              inputs: ['{projectRoot}/**/*.ts', '{projectRoot}/**/*.tsx', '{workspaceRoot}/.eslintrc.json'],
              options: {
                lintFilePatterns: [`${projectPath}/**/*.{ts,tsx}`]
              }
            },
            ...(hasTest
              ? {
                  test: {
                    cache: true,
                    configurations: {
                      ci: {
                        ci: true,
                        codeCoverage: true
                      }
                    },
                    dependsOn: isBackend
                      ? [
                          'prisma-codegen',
                          '^prisma-codegen',
                          {
                            projects: ['backend-shared-prisma-infra'],
                            target: 'prisma-setup-testing'
                          }
                        ]
                      : [],
                    executor: '@nx/jest:jest',
                    options: {
                      jestConfig: `${libs}/${platform}/jest.config.ts`,
                      passWithNoTests: true,
                      testPathIgnorePatterns: [`.*\\.(snapshot|scheduled)(?:\\.wide)?\\.spec\\.ts$`],
                      testPathPattern: [`${platform}/${scope}/${projectName}/.*\\.spec\\.ts`]
                    },
                    outputs: ['{workspaceRoot}/coverage/{projectRoot}']
                  }
                }
              : {}),
            ...(hasScheduledTests
              ? {
                  'test-scheduled': {
                    cache: true,
                    configurations: {
                      ci: {
                        ci: true,
                        codeCoverage: true
                      }
                    },
                    dependsOn: ['prisma-codegen', '^prisma-codegen'],
                    executor: '@nx/jest:jest',
                    options: {
                      jestConfig: `${libs}/${platform}/jest.config.ts`,
                      passWithNoTests: true,
                      testPathIgnorePatterns: [],
                      testPathPattern: [`${platform}/${scope}/${projectName}/.*\\.scheduled\\.spec\\.ts`]
                    },
                    outputs: ['{workspaceRoot}/coverage/{projectRoot}']
                  }
                }
              : {}),
            ...(hasSnapshotTests
              ? {
                'test-snapshot': {
                  cache: true,
                  configurations: {
                    ci: {
                      ci: true,
                      codeCoverage: true
                    }
                  },
                  dependsOn: ['prisma-codegen', '^prisma-codegen'],
                  executor: '@nx/jest:jest',
                  options: {
                    jestConfig: `${libs}/${platform}/jest.config.ts`,
                    passWithNoTests: true,
                    testPathIgnorePatterns: [],
                    testPathPattern: [`${platform}/${scope}/${projectName}/.*\\.snapshot\\.spec\\.ts`]
                  },
                  outputs: ['{workspaceRoot}/coverage/{projectRoot}']
                }
              }
              : {}),
            ...(hasPrismaSchema
              ? {
                  'prisma-codegen': {
                    command: 'prisma generate --schema {projectRoot}/schema.prisma > /dev/null',
                    inputs: ['{projectRoot}/**/*.prisma', '{projectRoot}/project.json'],
                    outputs: ['{projectRoot}/generated-client']
                  },
                  'prisma-setup-testing': {
                    dependsOn: ['prisma-codegen', '^prisma-codegen'],
                    executor: 'nx:run-commands',
                    options: {
                      command: 'tools/prisma/prisma-setup-testing.sh'
                    }
                  }
                }
              : {}),
            ...(hasOpenApiSpec
              ? {
                  codegen: {
                    cache: true,
                    executor: 'nx:run-commands',
                    inputs: [
                      '{projectRoot}/**/*.openapi.yaml',
                      '{projectRoot}/project.json',
                      '{workspaceRoot}/**/generate-openapi-models.sh'
                    ],
                    options: {
                      commands: [
                        `tools/generate-openapi-models.sh libs/crossplatform/${scope}/openapi/${scope}.openapi.yaml libs/crossplatform/${scope}/openapi`
                      ]
                    }
                  }
                }
              : {}),
            ...(hasController
              ? {
                  'generate-guards-goldens': {
                    cache: true,
                    command: `pnpm tsx apps/tools/src/generate-guards-goldens.ts ${projectPath}`,
                    inputs: ['{projectRoot}/**/*.controller.ts'],
                    outputs: ['{projectRoot}/GUARDS_GOLDENS.md']
                  }
                }
              : {}),
            ...(hasScss
              ? {
                  stylelint: {
                    cache: true,
                    executor: 'nx:run-commands',
                    inputs: ['{projectRoot}/**/*.scss', '{workspaceRoot}/.stylelintrc.json'],
                    options: { commands: ['pnpm stylelint {projectRoot}/**/*.scss'] }
                  }
                }
              : {})
          }
        }
      }
    };
  }
];

function hasFileMatching(pattern: string, condition: (file: string) => boolean = () => true) {
  for (const file of globIterateSync(pattern)) {
    if (condition?.(file)) {
      return true;
    }
  }
  return false;
}
