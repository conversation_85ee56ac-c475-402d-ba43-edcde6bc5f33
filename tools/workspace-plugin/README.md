# Workspace Plugin

This workspace-specific plugin contains the generators required to create new libraries and such.

This is the default plugin of the workspace as you can see in the `nx.json` file.
This means that even if there are other plugins with name collisions (like `@nx/react:lib`),
Nx will not prompt you to choose which one to use and will automatically use this one.

## The `lib` generator

Usage:
```sh
  pnpm nx g libs
# ... and let the prompts drive you
```
