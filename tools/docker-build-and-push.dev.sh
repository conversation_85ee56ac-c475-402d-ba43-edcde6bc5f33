#!/usr/bin/env bash

set -e

if [ $# -lt 2 ]
then
  echo "Usage:"
  echo "    $0 <project_name> <image_name> (eg: $0 api test-local-img )"
  exit 1
fi

pnpm nx run "$1":build:production

GCP_PROJECT=mynotary-preproduction
GCP_REGION=europe-west9
PROJECT_NAME=$1
IMAGE_NAME=$2

TAG="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT/docker-repository/${IMAGE_NAME}"


# Prisma schema & generated client.
cp libs/backend/shared/prisma-infra/schema.prisma "dist/apps/${PROJECT_NAME}"
cp libs/backend/shared/prisma-infra/generated-client/libquery*.so.node "dist/apps/${PROJECT_NAME}"

docker build \
  --progress=plain \
  --platform linux/amd64 . \
  -f "apps/${PROJECT_NAME}/Dockerfile" \
  -t "$TAG"
docker push "$TAG"
