import { execSync } from 'child_process';
import { sortBy } from 'lodash';

interface Config {
  projectId: string;
  repository: string;
  cloudRunServices: string[];
  region: string;
  keepLatest: number;
}

interface Version {
  createTime: string;
  metadata: {
    buildTime: string;
    imageSizeBytes: string;
    mediaType: string;
    name: string;
  };
  name: string;
  updateTime: string;
}

// Configuration
const config: Config = {
  projectId: 'mynotary-preproduction',
  repository: 'docker-repository',
  cloudRunServices: ['api-auth', 'api-emails', 'api-files', 'api-mynotary', 'api-signatures', 'api-java', 'jobs'],
  region: 'europe-west9',
  keepLatest: 3
};

const runCommand = (command: string) => {
  try {
    return execSync(command, {
      encoding: 'utf-8',
      env: {
        ...process.env
      }
    });
  } catch (error) {
    console.error(`Error executing command: ${command}`, error);
    return null;
  }
};

const processPackage = (packageName: string) => {
  console.log(`Processing package: ${packageName}`);

  // List all versions of the package
  const versionsJson = runCommand(
    `gcloud artifacts versions list --project=${config.projectId} --repository=${config.repository} --package=${packageName} --location=${config.region} --format="json"  --sort-by=createTime`
  );
  if (!versionsJson) {
    console.error(`Failed to list versions for package ${packageName}`);
    return;
  }

  const versions: Version[] = JSON.parse(versionsJson);
  let sortedVersions = sortBy(versions, (version) => new Date(version.createTime).getTime());

  sortedVersions = sortedVersions.slice(0, sortedVersions.length - config.keepLatest);

  if (sortedVersions.length <= config.keepLatest) {
    console.log(`Nothing to delete for package ${packageName}, there are not more than ${config.keepLatest} versions.`);
    return;
  }
  for (const version of sortedVersions) {
    const versionHash = version.name.split('/').pop();
    runCommand(
      `gcloud artifacts versions delete ${versionHash} --project=${config.projectId} --repository=${config.repository} --package=${packageName} --location=${config.region} --delete-tags --quiet`
    );
    console.log(`Deleted version: ${version.createTime} for package ${packageName}`);
  }

  console.log(`Finished processing package: ${packageName}`);
};

config.cloudRunServices.forEach(processPackage);
