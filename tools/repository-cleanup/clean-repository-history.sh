#!/bin/bash

analyze_and_invert() {
    local source_file=$1
    git filter-repo --analyze --force
    tail +3 .git/filter-repo/analysis/$source_file \
        | tr -s ' ' \
        | cut -d ' ' -f 5- \
        > ./path-deleted.txt

    > path-deleted-check.txt  # Clear the output file if it exists

    while read -r path; do
        if [ ! -e "$path" ]; then
            echo "$path" >> path-deleted-check.txt
        fi
    done < path-deleted.txt

    git filter-repo --invert-paths --paths-from-file ./path-deleted-check.txt --force
}

analyze_and_invert "directories-deleted-sizes.txt"
analyze_and_invert "directories-deleted-sizes.txt"
analyze_and_invert "directories-deleted-sizes.txt"
analyze_and_invert "path-deleted-sizes.txt"
analyze_and_invert "path-deleted-sizes.txt"
