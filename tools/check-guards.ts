import ts from 'typescript';
import * as fs from 'fs';
import * as path from 'path';

function checkUseGuardsInFile(filePath: string): void {
  const sourceFile = ts.createSourceFile(filePath, fs.readFileSync(filePath, 'utf8'), ts.ScriptTarget.Latest, true);

  function visitNode(node: ts.Node) {
    if (ts.isClassDeclaration(node)) {
      const isController = node.modifiers?.some(
        (modifier) =>
          ts.isDecorator(modifier) &&
          ts.isCallExpression(modifier.expression) &&
          ts.isIdentifier(modifier.expression.expression) &&
          modifier.expression.expression.text === 'Controller'
      );

      if (isController) {
        checkUseGuardsInClass(node);
      }
    }

    ts.forEachChild(node, visitNode);
  }

  function checkUseGuardsInClass(classNode: ts.ClassDeclaration) {
    classNode.members.forEach((member) => {
      if (ts.isMethodDeclaration(member)) {
        const useGuardsDecorator = member.modifiers?.find(
          (modifier) =>
            ts.isDecorator(modifier) &&
            ts.isCallExpression(modifier.expression) &&
            ts.isIdentifier(modifier.expression.expression) &&
            modifier.expression.expression.text === 'UseGuards'
        );

        if (useGuardsDecorator && ts.isDecorator(useGuardsDecorator)) {
          const decoratorExpr = useGuardsDecorator.expression as ts.CallExpression;
          decoratorExpr.arguments.forEach((arg) => {
            if (!isValidGuardUsage(arg)) {
              console.error(`Error in ${filePath}:`);
              console.error(
                `  Method ${member.name?.getText()} has @UseGuards that doesn't call a function or use a valid identifier`
              );
            }
          });
        }
      }
    });
  }

  visitNode(sourceFile);
}

function isValidGuardUsage(arg: ts.Expression): boolean {
  // Check if it's a call expression (function call)
  if (ts.isCallExpression(arg)) {
    return true;
  }

  /**
   * functions found that don't require a call expression
   */
  if (
    arg.getText() === 'IsUserVerified' ||
    arg.getText() === 'IsPublic' ||
    arg.getText() === 'IsLegacyApp' ||
    arg.getText() === 'IsForbidden' ||
    arg.getText() === 'TestAuthorization' ||
    arg.getText() === 'IsPublicWithDependency'
  ) {
    return true;
  }

  return false;
}

function checkDirectory(dirPath: string): void {
  const files = fs.readdirSync(dirPath);

  files.forEach((file) => {
    const filePath = path.join(dirPath, file);
    const stats = fs.statSync(filePath);

    if (stats.isDirectory()) {
      checkDirectory(filePath);
    } else if (stats.isFile() && (file.endsWith('.ts') || file.endsWith('.tsx'))) {
      checkUseGuardsInFile(filePath);
    }
  });
}

// Usage
const projectRoot = process.argv[2] || './src';
checkDirectory(projectRoot);
console.log('Finished checking @UseGuards usage.');
