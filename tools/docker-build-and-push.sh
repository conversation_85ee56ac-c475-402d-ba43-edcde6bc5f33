#!/usr/bin/env bash

set -e

GCP_PROJECT=mynotary-preproduction
GCP_REGION=europe-west9

PROJECT_PATH=$1
PROJECT_NAME="$(basename "$PROJECT_PATH")"
VERSION="$(git rev-parse --short=8 HEAD)"
TAG_PREFIX="$GCP_REGION-docker.pkg.dev/$GCP_PROJECT/docker-repository/$PROJECT_NAME"
TAG_VERSION="$TAG_PREFIX:$VERSION"
TAG_LATEST="$TAG_PREFIX:latest"


# Prisma schema & generated client.
cp libs/backend/shared/prisma-infra/schema.prisma "dist/apps/${PROJECT_NAME}"
cp libs/backend/shared/prisma-infra/generated-client/libquery*.so.node "dist/apps/${PROJECT_NAME}"

docker build \
  --platform linux/amd64 . \
  -f "$PROJECT_PATH/Dockerfile" \
  -t "$TAG_LATEST" \
  -t "$TAG_VERSION"
docker push "$TAG_LATEST"
docker push "$TAG_VERSION"
