# https://hub.docker.com/_/node
FROM debian:trixie-slim
ENV NODE_ENV=production
ENV PDFTK_VERSION="v3.3.3"
# We don't need the standalone Chromium since we install chromium separately
ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
ENV NODE_VERSION=20.10.0

# Install Node.js: https://github.com/nodesource/distributions/blob/master/README.md#debian-and-ubuntu-based-distributions
RUN apt-get update && apt-get install -y curl && curl -fsSL https://deb.nodesource.com/setup_20.x | bash - &&\
    apt-get install -y nodejs && corepack enable

# Install Pdf manipulation related packages
RUN apt-get update \
    && apt-get install -y qpdf mupdf-tools

# Install Google Chrome Stable and fonts, this installs the necessary libs to make the browser work with Puppeteer.
RUN apt-get update && apt-get install gnupg wget -y && \
  wget --quiet --output-document=- https://dl-ssl.google.com/linux/linux_signing_key.pub | gpg --dearmor > /etc/apt/trusted.gpg.d/google-archive.gpg && \
  sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list' && \
  apt-get update && \
  apt-get install google-chrome-stable fonts-ipafont-gothic fonts-wqy-zenhei fonts-thai-tlwg fonts-freefont-ttf libxss1 -y --no-install-recommends && \
  rm -rf /var/lib/apt/lists/*


# Install OpenJDK-17 to run PDFtk
RUN apt-get update && \
apt-get install -y --no-install-recommends \
        openjdk-17-jre

# Install PDFtk
RUN \
    wget -O /usr/bin/pdftk-all.jar "https://gitlab.com/api/v4/projects/5024297/packages/generic/pdftk-java/$PDFTK_VERSION/pdftk-all.jar" &&\
    chmod a+x /usr/bin/pdftk-all.jar &&\
    echo '#!/bin/bash\n\nexec java -jar /usr/bin/pdftk-all.jar "$@"' > /usr/bin/pdftk && \
    chmod +x /usr/bin/pdftk &&\
    apt-get update -qq &&\
    # Cleanup.
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*


RUN npm i -g corepack@latest
