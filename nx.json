{"$schema": "./node_modules/nx/schemas/nx-schema.json", "parallel": 5, "targetDefaults": {"build": {"dependsOn": ["prisma-codegen", "^build", "^prisma-codegen"], "inputs": ["production", "^production"], "cache": true}, "deploy-client": {"cache": true}, "deploy-docker-image": {"cache": true}, "e2e": {"inputs": ["default", "^production"], "cache": true}, "serve": {"dependsOn": []}, "@nx/jest:jest": {"cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}}, "generators": {"@nx/react": {"application": {"style": "scss", "linter": "eslint", "bundler": "webpack", "babel": true}, "library": {"style": "scss", "linter": "eslint", "unitTestRunner": "jest"}, "component": {"style": "scss"}}, "@nx/workspace:move": {"projectNameAndRootFormat": "as-provided"}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "sharedGlobals": ["{workspaceRoot}/babel.config.json"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/src/test-setup.[jt]s"]}, "plugins": ["./tools/workspace-plugin/src/implicit-libs.ts", "./tools/workspace-plugin/src/implicit-targets.ts"], "defaultBase": "main", "useLegacyCache": true}