---
description: Help agent to understand project
globs: 
alwaysApply: true
---

# Your rule content
The project is declined like that:
Inside libs directory at root we can find something that we call "platforms", it can be :
- frontend
- backend
- crossplatform (usually we share inside interface, functions that are similars between backend and frontend)

Inside each folders there is something that we call "scopes". it can be differents things but its the core of the business
inside each scopes there is folders:
- api (functions, interface that can be shared between scopes) [emails-api.service.ts](mdc:libs/backend/emails/api/emails-api.service.ts) [index.ts](mdc:libs/backend/drives/api/index.ts)
- authorization (logic about guards access to a resource (controller)) [has-drive-access.ts](mdc:libs/backend/drives/authorization/has-drive-access.ts)
- core (business logic)
- feature (backend -> controllers, frontend -> react components)
- infra (backend -> database insert update delete.. frontend -> client to call an endpoint)
- test (e2e testing or functions testing) example : [orders.controller.wide.spec.ts](mdc:libs/backend/orders/test/feature/orders.controller.wide.spec.ts)

About Controllers:
there is a decorator UseGuards to add an authorization , if we want to check an authorization or another one, we should add the decorator OrGuard [legal-branches.controller.ts](mdc:libs/backend/legals/feature/legal-branches/legal-branches.controller.ts)


The dto should be created using Openapi , usually we register our route inside [api-mynotary.openapi.yaml](mdc:libs/crossplatform/api-mynotary/openapi/api-mynotary.openapi.yaml) if the file not exists, creating a file inside specs like [drives.openapi.yaml](mdc:libs/crossplatform/api-mynotary/openapi/specs/drives.openapi.yaml). 
Never create the dto directly use only the specs file yaml.

We launch separatly a command : pnpm codegen
It will create a dto inside model folder see [drive-dto.ts](mdc:libs/crossplatform/api-mynotary/openapi/model/drive-dto.ts)