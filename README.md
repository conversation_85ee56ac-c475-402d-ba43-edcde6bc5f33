# MyNotary

## Core Values and Principles

### Simplicity and Readability

At MyNotary, we prioritize code simplicity and readability above all else. Complex solutions might seem impressive, but
they often lead to maintenance challenges and bugs. We follow the principle that "simple is better than complex",
"explicit is better than implicit" and "flat is better than nested." Every developer should strive to write code that is
easy to understand at first glance.

### Consistency

Consistency throughout the codebase is essential. When examining any file in our project, it should be impossible to
determine which developer wrote it. We try to achieve this by strictly adhering to established naming conventions, code
organization patterns, and architectural boundaries. This consistency makes onboarding new developers easier and reduces
cognitive load when switching between different parts of the application.

### Developer Collaboration

Our codebase is a shared resource. Every developer should consider how their code will be understood and maintained by
others. Before submitting code, take a step back and consider: "Is this implementation clear to someone who hasn't been
involved in this task?" This mindset promotes better documentation, clearer variable naming, and more thoughtful
architecture decisions.

### Code Ownership

While individuals may write specific parts of the code, we practice collective code ownership. Every developer should
feel empowered to improve any part of the codebase, following our established patterns and principles. This approach
ensures that knowledge is shared across the team and that the codebase evolves cohesively rather than becoming a
collection of isolated components.

### Stability and Testing

All parts of our codebase must be testable and tested. Tests are crucial for maintaining platform stability during
evolution and changes. They provide a safety net that allows us to confidently refactor and enhance our code without
introducing regressions. Additionally, comprehensive tests enable less experienced developers to work confidently on
unfamiliar parts of the codebase, knowing they can verify their changes don't break existing functionality. Testing is
not an afterthought but an integral part of our development process that supports both code quality and team
collaboration.

### Continuous Improvement Mindset

We acknowledge that some parts of our codebase do not yet fully adhere to our core values and principles, largely due to
the project's evolution detailed below. However, it is essential to keep these values in mind when implementing new
features or migrating existing ones to ensure continuous improvement of our codebase. It is crucial that every developer
working on this project maintains a constant pursuit of improvement, which requires identifying elements that hinder
velocity. These elements may include certain parts of the code, processes, or tools we currently use.

## Project Evolution

Understanding where we've come from, where we are now, and where we're heading is crucial for making informed decisions
about our codebase. This historical context provides valuable insights into why certain choices were made and helps
guide our future direction.

### 2017-2020: The Beginning

The project began in 2017 with priorities different from today's focus. The technologies chosen were not necessarily
coherent and some were already deprecated. There was a tendency to rebuild existing tools rather than leveraging
established solutions. The main technologies used until 2020 were: AngularJS version 1 Java API with an unmaintained
framework (Restlet, discontinued since 2015) Native mobile applications (Android and Swift), deployment using scripts in
various languages (Python, Bash, JavaScript), infrastructure technologies like Rancher and later Kubernetes, despite
having only about a hundred clients, a custom-built backup system for files and database, proprietary JSON format for
legal contracts that required technical team integration.

These choices negatively impacted the technical team's velocity, product stability, and were costly to maintain.

### 2020-2022: New Team and V2 Development

The technical team changed during this period, but the company was in a difficult economic situation and significantly
behind competitors. These two years were focused on catching up by rapidly developing missing features. The technical
debt was so substantial that the decision was made to completely replace the AngularJS frontend with a new application
built with React and TypeScript. A proprietary tool was also created to empower legal experts to integrate contracts
independently, freeing developers from this task.

### 2022-2023: New Repository Foundation

With improved economic stability, the company brought in a consultant to establish the foundations of this repository.
All technology choices were made with the goal of helping developers adhere to the core values described in the previous
section.

A key decision was to standardize on a single programming language (TypeScript) to improve consistency and simplify the
codebase. The team also focused on streamlining cloud infrastructure and deployment processes while significantly
enhancing test coverage. During this period, several foundational technologies were implemented that now serve as
pillars of our development approach:

- **NX**: For monorepo management and build optimization
- **Pulumi**: For infrastructure as code
- **GitHub Actions**: For CI/CD pipelines
- **Google Cloud Platform**: As our cloud provider
- **Jest and Playwright**: For unit and end-to-end testing
- **NestJS**: For backend API development
- **Prisma**: For database access and management

These technologies form the foundation of the new repository and our modern approach to software development.

### 2023-2025: Migration and Feature Development

For the past two years, the focus has been on balancing new revenue-generating features with the gradual migration of
legacy components. The team follows a simple rule: when implementing a new feature that requires modifying legacy code,
take the time to migrate that code to the new format and add tests. This approach ensures continuous improvement of the
codebase while delivering business value.

### 2025 and Beyond: Enhanced Stability and Monitoring

Moving forward, we will continue to migrate legacy components and develop new features to meet business needs. However,
a significant portion of our efforts will be directed towards improving platform monitoring, error handling, and overall
stability. By investing in these areas, we aim to create a more resilient platform that can better serve our growing
user base while providing developers with the tools they need to quickly identify and resolve issues. This focus on
operational excellence will complement our ongoing code improvements and feature development.

## Project Structure

This section provides an overview of the repository structure and key files:

### Directories

- **.github**: Contains all CI workflow files. For more information, see `docs/CI.md`.
- **apps**: Contains all project applications. An application can be a NestJS server, a React frontend application, a
  cron job, or tools that generate files or help with maintenance. See `docs/APPS.md` for more details.
- **docs**: Contains all project documentation.
- **libs**: Contains code that can be reused across multiple applications. More information in `docs/LIBS.md`.
- **pulumi**: Contains TypeScript files for deploying our applications on Google Cloud Platform.
- **sql-queries**: Contains utility queries used for analytics or maintenance.
- **tools**: Contains various scripts for application deployment and maintenance. These tools should be moved to the
  apps directory and rewritten in TypeScript whenever possible.

### Configuration Files

- **.commitlintrc.json**: Configuration for commit message linting.
- **.env**, **.env.local**: Files for adding environment variables used by local applications.
- **.eslintignore**, **.eslintrc.json**: ESLint configuration files.
- **.firebaserc**: Configuration file for deploying frontend applications to Firebase Hosting.
- **.prettierignore**, **.prettierrc**: Prettier configuration files.
- **.stylelintignore**, **.stylelintrc.json**: Stylelint configuration files.
- **.swcrc**: Configuration file for SWC.
- **babel.config.json**: Configuration file used by NX for building.
- **biome.json**: Configuration file for the Rust-based Biome linter. This linter is much faster than ESLint but doesn't
  yet have all the rules we use, so it's only used in certain scripts.
- **firebase.json**: Configuration file for deploying frontend applications to Firebase Hosting.
- **gcp-service-account-secret-adder.json**: Service account with permissions to add new secrets on GCP.
- **jest.config.ts**, **jest.preset.js**: Global configuration files for Jest and NX.
- **openapitools.json**: Configuration file for the DTO generator.
- **nx.json**: Configuration file for NX.
- **package.json**, **pnpm-lock.yaml**: Contains the list of all project dependencies and various scripts for running
  applications or performing maintenance.
- **Pulumi.yaml**: Pulumi configuration file.
- **tsconfig.base.json**: TypeScript configuration file. It notably contains the list of all paths.

## Contributing

### Documentation First

Before writing any code, it is essential to read all the documentation files in the top level of the `docs` directory.
These files provide detailed explanations of the fundamental tools and different parts of the application. Take the time
to read and understand them, and don't hesitate to ask questions if anything is unclear.

Here are the key documentation files you should read, in the recommended order for new team members:

1. [**CORE_CONCEPTS.md**](docs/CORE_CONCEPTS.md): Overview of the project's architecture, boundaries between scopes, and
   the rules we follow. Essential reading to understand the project structure.

2. [**DEVELOPMENT_GUIDELINES.md**](docs/DEVELOPMENT_GUIDELINES.md): Coding standards, naming conventions, and best
   practices for development. Helps maintain consistency across the codebase.

3. [**BACKEND.md**](docs/BACKEND.md): Details about the backend architecture, technologies (NestJS, Prisma, PostgreSQL),
   and organization of backend code.

4. [**FRONTEND.md**](docs/FRONTEND.md): Information about the frontend architecture, technologies (React), and
   organization of frontend code.

5. [**DATABASE.md**](docs/DATABASE.md): Documentation on our PostgreSQL database, extensions, environments, and
   guidelines for database management.

6. [**TESTS.md**](docs/TESTS.md): Testing strategies, tools, and best practices for ensuring code quality and stability.

7. [**ERRORS.md**](docs/ERRORS.md): Error handling approach, types of errors, and how errors are logged and displayed to
   users.

8. [**CI.md**](docs/CI.md): Overview of our CI/CD pipelines implemented with GitHub Actions, including workflows for
   testing, building, and deployment.

9. [**DEVOPS.md**](docs/DEVOPS.md): Information about our microservices architecture, deployment processes, and DevOps
   tools and scripts.

10. [**JOBS.md**](docs/JOBS.md): Documentation on background jobs, how they're scheduled, and how to create and test new
    jobs.

11. [**MAINTENANCE.md**](docs/MAINTENANCE.md): Guidelines for maintaining the project, including updating dependencies
    and managing infrastructure.

For more specific information about a particular scope, refer to the documentation located in the `docs/scope`
directory.

### Getting Started

To run the application for the first time, follow the onboarding documentation in `docs/onboarding`.
