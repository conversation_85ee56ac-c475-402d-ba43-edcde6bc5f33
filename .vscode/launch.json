{
  // Utilisez IntelliSense pour en savoir plus sur les attributs possibles.
  // Pointez pour afficher la description des attributs existants.
  // Pour plus d'informations, visitez : https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "MyNotary API",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["start", "api-mynotary"],
      "console": "integratedTerminal"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Files API",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["start", "files"],
      "console": "integratedTerminal"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "PDF API",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["start", "pdf"],
      "console": "integratedTerminal"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "Auth API",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["start", "auth"],
      "console": "integratedTerminal"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "React App",
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["start", "front-mynotary"],
      "console": "integratedTerminal"
    }
  ],
  "compounds": [
    {
      "name": "Servers/Client",
      "configurations": ["Auth API", "Files API", "PDF API", "MyNotary API", "React App"],
      "stopAll": true
    },
    {
      "name": "API/Client",
      "configurations": ["MyNotary API", "React App"],
      "stopAll": true
    }
  ]
}
