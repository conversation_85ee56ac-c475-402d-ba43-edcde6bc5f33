{"files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true}, "conventionalCommits.scopes": ["files", "build", "roles", "pdf", "tableau", "register", "organizations", "users", "auth", "form", "admin", "emails", "wip", "records", "contracts", "emails", "operations", "admin", "wip", "user-session", "documents", "features", "billings", "themes", "tasks", "unis", "authorizations", "registered-letter", "function", "template update"], "csscomb.formatOnSave": true, "explorerExclude.backup": {}, "explorerExclude.showPicker": true, "eslint.validate": ["json"]}