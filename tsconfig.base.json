{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "allowJs": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "ES2021", "module": "esnext", "lib": ["es2021", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "strict": true, "paths": {"@mynotary/backend/adsn-client/api": ["libs/backend/adsn-client/api/index.ts"], "@mynotary/backend/adsn-client/core": ["libs/backend/adsn-client/core/index.ts"], "@mynotary/backend/adsn-client/infra": ["libs/backend/adsn-client/infra/index.ts"], "@mynotary/backend/adsn-client/providers": ["libs/backend/adsn-client/providers/index.ts"], "@mynotary/backend/adsn-client/test": ["libs/backend/adsn-client/test/index.ts"], "@mynotary/backend/anf-companion/core": ["libs/backend/anf-companion/core/index.ts"], "@mynotary/backend/anf-companion/feature": ["libs/backend/anf-companion/feature/index.ts"], "@mynotary/backend/anf-companion/infra": ["libs/backend/anf-companion/infra/index.ts"], "@mynotary/backend/anf-companion/providers": ["libs/backend/anf-companion/providers/index.ts"], "@mynotary/backend/anf/core": ["libs/backend/anf/core/index.ts"], "@mynotary/backend/anf/feature": ["libs/backend/anf/feature/index.ts"], "@mynotary/backend/anf/providers": ["libs/backend/anf/providers/index.ts"], "@mynotary/backend/anf/test": ["libs/backend/anf/test/index.ts"], "@mynotary/backend/async-tasks/api": ["libs/backend/async-tasks/api/index.ts"], "@mynotary/backend/async-tasks/core": ["libs/backend/async-tasks/core/index.ts"], "@mynotary/backend/async-tasks/infra": ["libs/backend/async-tasks/infra/index.ts"], "@mynotary/backend/async-tasks/providers": ["libs/backend/async-tasks/providers/index.ts"], "@mynotary/backend/async-tasks/test": ["libs/backend/async-tasks/test/index.ts"], "@mynotary/backend/authentications/api": ["libs/backend/authentications/api/index.ts"], "@mynotary/backend/authentications/authorization": ["libs/backend/authentications/authorization/index.ts"], "@mynotary/backend/authentications/core": ["libs/backend/authentications/core/index.ts"], "@mynotary/backend/authentications/feature": ["libs/backend/authentications/feature/index.ts"], "@mynotary/backend/authentications/infra": ["libs/backend/authentications/infra/index.ts"], "@mynotary/backend/authentications/providers": ["libs/backend/authentications/providers/index.ts"], "@mynotary/backend/authentications/test": ["libs/backend/authentications/test/index.ts"], "@mynotary/backend/authorizations/api": ["libs/backend/authorizations/api/index.ts"], "@mynotary/backend/authorizations/authorization": ["libs/backend/authorizations/authorization/index.ts"], "@mynotary/backend/authorizations/core": ["libs/backend/authorizations/core/index.ts"], "@mynotary/backend/authorizations/feature": ["libs/backend/authorizations/feature/index.ts"], "@mynotary/backend/authorizations/infra": ["libs/backend/authorizations/infra/index.ts"], "@mynotary/backend/authorizations/providers": ["libs/backend/authorizations/providers/index.ts"], "@mynotary/backend/authorizations/test": ["libs/backend/authorizations/test/index.ts"], "@mynotary/backend/background-jobs/core": ["libs/backend/background-jobs/core/index.ts"], "@mynotary/backend/background-jobs/infra": ["libs/backend/background-jobs/infra/index.ts"], "@mynotary/backend/background-jobs/providers": ["libs/backend/background-jobs/providers/index.ts"], "@mynotary/backend/billings/api": ["libs/backend/billings/api/index.ts"], "@mynotary/backend/billings/authorization": ["libs/backend/billings/authorization/index.ts"], "@mynotary/backend/billings/core": ["libs/backend/billings/core/index.ts"], "@mynotary/backend/billings/feature": ["libs/backend/billings/feature/index.ts"], "@mynotary/backend/billings/infra": ["libs/backend/billings/infra/index.ts"], "@mynotary/backend/billings/providers": ["libs/backend/billings/providers/index.ts"], "@mynotary/backend/billings/test": ["libs/backend/billings/test/index.ts"], "@mynotary/backend/cardreader/api": ["libs/backend/cardreader/api/index.ts"], "@mynotary/backend/cardreader/core": ["libs/backend/cardreader/core/index.ts"], "@mynotary/backend/cardreader/feature": ["libs/backend/cardreader/feature/index.ts"], "@mynotary/backend/cardreader/infra": ["libs/backend/cardreader/infra/index.ts"], "@mynotary/backend/cardreader/providers": ["libs/backend/cardreader/providers/index.ts"], "@mynotary/backend/cardreader/test": ["libs/backend/cardreader/test/index.ts"], "@mynotary/backend/contract-reviews/authorization": ["libs/backend/contract-reviews/authorization/index.ts"], "@mynotary/backend/contract-reviews/core": ["libs/backend/contract-reviews/core/index.ts"], "@mynotary/backend/contract-reviews/feature": ["libs/backend/contract-reviews/feature/index.ts"], "@mynotary/backend/contract-reviews/providers": ["libs/backend/contract-reviews/providers/index.ts"], "@mynotary/backend/contract-reviews/test": ["libs/backend/contract-reviews/test/index.ts"], "@mynotary/backend/contract-validations/authorization": ["libs/backend/contract-validations/authorization/index.ts"], "@mynotary/backend/contract-validations/core": ["libs/backend/contract-validations/core/index.ts"], "@mynotary/backend/contract-validations/feature": ["libs/backend/contract-validations/feature/index.ts"], "@mynotary/backend/contract-validations/providers": ["libs/backend/contract-validations/providers/index.ts"], "@mynotary/backend/contract-validations/test": ["libs/backend/contract-validations/test/index.ts"], "@mynotary/backend/contract-validators/api": ["libs/backend/contract-validators/api/index.ts"], "@mynotary/backend/contract-validators/authorization": ["libs/backend/contract-validators/authorization/index.ts"], "@mynotary/backend/contract-validators/core": ["libs/backend/contract-validators/core/index.ts"], "@mynotary/backend/contract-validators/feature": ["libs/backend/contract-validators/feature/index.ts"], "@mynotary/backend/contract-validators/infra": ["libs/backend/contract-validators/infra/index.ts"], "@mynotary/backend/contract-validators/providers": ["libs/backend/contract-validators/providers/index.ts"], "@mynotary/backend/contract-validators/test": ["libs/backend/contract-validators/test/index.ts"], "@mynotary/backend/contract-views/core": ["libs/backend/contract-views/core/index.ts"], "@mynotary/backend/contract-views/feature": ["libs/backend/contract-views/feature/index.ts"], "@mynotary/backend/contract-views/infra": ["libs/backend/contract-views/infra/index.ts"], "@mynotary/backend/contract-views/providers": ["libs/backend/contract-views/providers/index.ts"], "@mynotary/backend/contract-views/test": ["libs/backend/contract-views/test/index.ts"], "@mynotary/backend/coproprietes/core": ["libs/backend/coproprietes/core/index.ts"], "@mynotary/backend/coproprietes/feature": ["libs/backend/coproprietes/feature/index.ts"], "@mynotary/backend/coproprietes/infra": ["libs/backend/coproprietes/infra/index.ts"], "@mynotary/backend/coproprietes/providers": ["libs/backend/coproprietes/providers/index.ts"], "@mynotary/backend/coproprietes/test": ["libs/backend/coproprietes/test/index.ts"], "@mynotary/backend/custom-views/api": ["libs/backend/custom-views/api/index.ts"], "@mynotary/backend/custom-views/core": ["libs/backend/custom-views/core/index.ts"], "@mynotary/backend/custom-views/feature": ["libs/backend/custom-views/feature/index.ts"], "@mynotary/backend/custom-views/infra": ["libs/backend/custom-views/infra/index.ts"], "@mynotary/backend/custom-views/providers": ["libs/backend/custom-views/providers/index.ts"], "@mynotary/backend/custom-views/test": ["libs/backend/custom-views/test/index.ts"], "@mynotary/backend/data-analytics/core": ["libs/backend/data-analytics/core/index.ts"], "@mynotary/backend/data-analytics/feature": ["libs/backend/data-analytics/feature/index.ts"], "@mynotary/backend/data-analytics/infra": ["libs/backend/data-analytics/infra/index.ts"], "@mynotary/backend/data-analytics/providers": ["libs/backend/data-analytics/providers/index.ts"], "@mynotary/backend/data-analytics/test": ["libs/backend/data-analytics/test/index.ts"], "@mynotary/backend/data-sync/api": ["libs/backend/data-sync/api/index.ts"], "@mynotary/backend/data-sync/core": ["libs/backend/data-sync/core/index.ts"], "@mynotary/backend/data-sync/infra": ["libs/backend/data-sync/infra/index.ts"], "@mynotary/backend/data-sync/providers": ["libs/backend/data-sync/providers/index.ts"], "@mynotary/backend/data-sync/test": ["libs/backend/data-sync/test/index.ts"], "@mynotary/backend/data-tableau/core": ["libs/backend/data-tableau/core/index.ts"], "@mynotary/backend/data-tableau/feature": ["libs/backend/data-tableau/feature/index.ts"], "@mynotary/backend/data-tableau/infra": ["libs/backend/data-tableau/infra/index.ts"], "@mynotary/backend/data-tableau/providers": ["libs/backend/data-tableau/providers/index.ts"], "@mynotary/backend/data-tableau/test": ["libs/backend/data-tableau/test/index.ts"], "@mynotary/backend/default-records/api": ["libs/backend/default-records/api/index.ts"], "@mynotary/backend/default-records/core": ["libs/backend/default-records/core/index.ts"], "@mynotary/backend/default-records/feature": ["libs/backend/default-records/feature/index.ts"], "@mynotary/backend/default-records/infra": ["libs/backend/default-records/infra/index.ts"], "@mynotary/backend/default-records/providers": ["libs/backend/default-records/providers/index.ts"], "@mynotary/backend/default-records/test": ["libs/backend/default-records/test/index.ts"], "@mynotary/backend/demande-copie-document/api": ["libs/backend/demande-copie-document/api/index.ts"], "@mynotary/backend/demande-copie-document/core": ["libs/backend/demande-copie-document/core/index.ts"], "@mynotary/backend/demande-copie-document/feature": ["libs/backend/demande-copie-document/feature/index.ts"], "@mynotary/backend/demande-copie-document/infra": ["libs/backend/demande-copie-document/infra/index.ts"], "@mynotary/backend/demande-copie-document/providers": ["libs/backend/demande-copie-document/providers/index.ts"], "@mynotary/backend/demande-copie-document/test": ["libs/backend/demande-copie-document/test/index.ts"], "@mynotary/backend/document-requests/authorization": ["libs/backend/document-requests/authorization/index.ts"], "@mynotary/backend/document-requests/core": ["libs/backend/document-requests/core/index.ts"], "@mynotary/backend/document-requests/feature": ["libs/backend/document-requests/feature/index.ts"], "@mynotary/backend/document-requests/infra": ["libs/backend/document-requests/infra/index.ts"], "@mynotary/backend/document-requests/providers": ["libs/backend/document-requests/providers/index.ts"], "@mynotary/backend/document-requests/test": ["libs/backend/document-requests/test/index.ts"], "@mynotary/backend/drives/api": ["libs/backend/drives/api/index.ts"], "@mynotary/backend/drives/authorization": ["libs/backend/drives/authorization/index.ts"], "@mynotary/backend/drives/core": ["libs/backend/drives/core/index.ts"], "@mynotary/backend/drives/feature": ["libs/backend/drives/feature/index.ts"], "@mynotary/backend/drives/infra": ["libs/backend/drives/infra/index.ts"], "@mynotary/backend/drives/providers": ["libs/backend/drives/providers/index.ts"], "@mynotary/backend/drives/test": ["libs/backend/drives/test/index.ts"], "@mynotary/backend/emails/api": ["libs/backend/emails/api/index.ts"], "@mynotary/backend/emails/authorization": ["libs/backend/emails/authorization/index.ts"], "@mynotary/backend/emails/core": ["libs/backend/emails/core/index.ts"], "@mynotary/backend/emails/feature": ["libs/backend/emails/feature/index.ts"], "@mynotary/backend/emails/infra": ["libs/backend/emails/infra/index.ts"], "@mynotary/backend/emails/providers": ["libs/backend/emails/providers/index.ts"], "@mynotary/backend/emails/test": ["libs/backend/emails/test/index.ts"], "@mynotary/backend/etatscivils-bff/api": ["libs/backend/etatscivils-bff/api/index.ts"], "@mynotary/backend/etatscivils-bff/core": ["libs/backend/etatscivils-bff/core/index.ts"], "@mynotary/backend/etatscivils-bff/feature": ["libs/backend/etatscivils-bff/feature/index.ts"], "@mynotary/backend/etatscivils-bff/infra": ["libs/backend/etatscivils-bff/infra/index.ts"], "@mynotary/backend/etatscivils-bff/providers": ["libs/backend/etatscivils-bff/providers/index.ts"], "@mynotary/backend/etatscivils-bff/test": ["libs/backend/etatscivils-bff/test/index.ts"], "@mynotary/backend/etatscivils-client/api": ["libs/backend/etatscivils-client/api/index.ts"], "@mynotary/backend/etatscivils-client/core": ["libs/backend/etatscivils-client/core/index.ts"], "@mynotary/backend/etatscivils-client/infra": ["libs/backend/etatscivils-client/infra/index.ts"], "@mynotary/backend/etatscivils-client/providers": ["libs/backend/etatscivils-client/providers/index.ts"], "@mynotary/backend/etatscivils-client/test": ["libs/backend/etatscivils-client/test/index.ts"], "@mynotary/backend/etatscivils/core": ["libs/backend/etatscivils/core/index.ts"], "@mynotary/backend/etatscivils/feature": ["libs/backend/etatscivils/feature/index.ts"], "@mynotary/backend/etatscivils/infra": ["libs/backend/etatscivils/infra/index.ts"], "@mynotary/backend/etatscivils/providers": ["libs/backend/etatscivils/providers/index.ts"], "@mynotary/backend/etatscivils/test": ["libs/backend/etatscivils/test/index.ts"], "@mynotary/backend/events/api": ["libs/backend/events/api/index.ts"], "@mynotary/backend/events/core": ["libs/backend/events/core/index.ts"], "@mynotary/backend/events/infra": ["libs/backend/events/infra/index.ts"], "@mynotary/backend/events/providers": ["libs/backend/events/providers/index.ts"], "@mynotary/backend/events/test": ["libs/backend/events/test/index.ts"], "@mynotary/backend/external-apps/api": ["libs/backend/external-apps/api/index.ts"], "@mynotary/backend/external-apps/core": ["libs/backend/external-apps/core/index.ts"], "@mynotary/backend/external-apps/feature": ["libs/backend/external-apps/feature/index.ts"], "@mynotary/backend/external-apps/infra": ["libs/backend/external-apps/infra/index.ts"], "@mynotary/backend/external-apps/providers": ["libs/backend/external-apps/providers/index.ts"], "@mynotary/backend/external-apps/test": ["libs/backend/external-apps/test/index.ts"], "@mynotary/backend/feature-organizations/api": ["libs/backend/feature-organizations/api/index.ts"], "@mynotary/backend/feature-organizations/core": ["libs/backend/feature-organizations/core/index.ts"], "@mynotary/backend/feature-organizations/infra": ["libs/backend/feature-organizations/infra/index.ts"], "@mynotary/backend/feature-organizations/providers": ["libs/backend/feature-organizations/providers/index.ts"], "@mynotary/backend/feature-organizations/test": ["libs/backend/feature-organizations/test/index.ts"], "@mynotary/backend/features/api": ["libs/backend/features/api/index.ts"], "@mynotary/backend/features/authorization": ["libs/backend/features/authorization/index.ts"], "@mynotary/backend/features/core": ["libs/backend/features/core/index.ts"], "@mynotary/backend/features/feature": ["libs/backend/features/feature/index.ts"], "@mynotary/backend/features/infra": ["libs/backend/features/infra/index.ts"], "@mynotary/backend/features/providers": ["libs/backend/features/providers/index.ts"], "@mynotary/backend/features/test": ["libs/backend/features/test/index.ts"], "@mynotary/backend/files-client/infra": ["libs/backend/files-client/infra/index.ts"], "@mynotary/backend/files-client/providers": ["libs/backend/files-client/providers/index.ts"], "@mynotary/backend/files-client/test": ["libs/backend/files-client/test/index.ts"], "@mynotary/backend/files/api": ["libs/backend/files/api/index.ts"], "@mynotary/backend/files/core": ["libs/backend/files/core/index.ts"], "@mynotary/backend/files/feature": ["libs/backend/files/feature/index.ts"], "@mynotary/backend/files/infra": ["libs/backend/files/infra/index.ts"], "@mynotary/backend/files/providers": ["libs/backend/files/providers/index.ts"], "@mynotary/backend/files/test": ["libs/backend/files/test/index.ts"], "@mynotary/backend/gel-avoirs/core": ["libs/backend/gel-avoirs/core/index.ts"], "@mynotary/backend/gel-avoirs/feature": ["libs/backend/gel-avoirs/feature/index.ts"], "@mynotary/backend/gel-avoirs/infra": ["libs/backend/gel-avoirs/infra/index.ts"], "@mynotary/backend/gel-avoirs/providers": ["libs/backend/gel-avoirs/providers/index.ts"], "@mynotary/backend/gel-avoirs/test": ["libs/backend/gel-avoirs/test/index.ts"], "@mynotary/backend/inquiries/core": ["libs/backend/inquiries/core/index.ts"], "@mynotary/backend/inquiries/feature": ["libs/backend/inquiries/feature/index.ts"], "@mynotary/backend/inquiries/providers": ["libs/backend/inquiries/providers/index.ts"], "@mynotary/backend/inquiries/test": ["libs/backend/inquiries/test/index.ts"], "@mynotary/backend/internal-notifications/api": ["libs/backend/internal-notifications/api/index.ts"], "@mynotary/backend/internal-notifications/core": ["libs/backend/internal-notifications/core/index.ts"], "@mynotary/backend/internal-notifications/infra": ["libs/backend/internal-notifications/infra/index.ts"], "@mynotary/backend/internal-notifications/providers": ["libs/backend/internal-notifications/providers/index.ts"], "@mynotary/backend/internal-notifications/test": ["libs/backend/internal-notifications/test/index.ts"], "@mynotary/backend/interop-compta-companion/core": ["libs/backend/interop-compta-companion/core/index.ts"], "@mynotary/backend/interop-compta-companion/feature": ["libs/backend/interop-compta-companion/feature/index.ts"], "@mynotary/backend/interop-compta-companion/providers": ["libs/backend/interop-compta-companion/providers/index.ts"], "@mynotary/backend/interop-compta-companion/test": ["libs/backend/interop-compta-companion/test/index.ts"], "@mynotary/backend/interop-compta/api": ["libs/backend/interop-compta/api/index.ts"], "@mynotary/backend/interop-compta/core": ["libs/backend/interop-compta/core/index.ts"], "@mynotary/backend/interop-compta/feature": ["libs/backend/interop-compta/feature/index.ts"], "@mynotary/backend/interop-compta/infra": ["libs/backend/interop-compta/infra/index.ts"], "@mynotary/backend/interop-compta/providers": ["libs/backend/interop-compta/providers/index.ts"], "@mynotary/backend/interop-compta/test": ["libs/backend/interop-compta/test/index.ts"], "@mynotary/backend/invoices/authorization": ["libs/backend/invoices/authorization/index.ts"], "@mynotary/backend/invoices/core": ["libs/backend/invoices/core/index.ts"], "@mynotary/backend/invoices/feature": ["libs/backend/invoices/feature/index.ts"], "@mynotary/backend/invoices/infra": ["libs/backend/invoices/infra/index.ts"], "@mynotary/backend/invoices/providers": ["libs/backend/invoices/providers/index.ts"], "@mynotary/backend/invoices/test": ["libs/backend/invoices/test/index.ts"], "@mynotary/backend/learn-worlds/core": ["libs/backend/learn-worlds/core/index.ts"], "@mynotary/backend/learn-worlds/feature": ["libs/backend/learn-worlds/feature/index.ts"], "@mynotary/backend/learn-worlds/infra": ["libs/backend/learn-worlds/infra/index.ts"], "@mynotary/backend/learn-worlds/providers": ["libs/backend/learn-worlds/providers/index.ts"], "@mynotary/backend/learn-worlds/test": ["libs/backend/learn-worlds/test/index.ts"], "@mynotary/backend/legacy-java/api": ["libs/backend/legacy-java/api/index.ts"], "@mynotary/backend/legacy-java/core": ["libs/backend/legacy-java/core/index.ts"], "@mynotary/backend/legacy-java/infra": ["libs/backend/legacy-java/infra/index.ts"], "@mynotary/backend/legacy-java/providers": ["libs/backend/legacy-java/providers/index.ts"], "@mynotary/backend/legacy-java/test": ["libs/backend/legacy-java/test/index.ts"], "@mynotary/backend/legal-record-exports/core": ["libs/backend/legal-record-exports/core/index.ts"], "@mynotary/backend/legal-record-exports/feature": ["libs/backend/legal-record-exports/feature/index.ts"], "@mynotary/backend/legal-record-exports/infra": ["libs/backend/legal-record-exports/infra/index.ts"], "@mynotary/backend/legal-record-exports/providers": ["libs/backend/legal-record-exports/providers/index.ts"], "@mynotary/backend/legal-record-exports/test": ["libs/backend/legal-record-exports/test/index.ts"], "@mynotary/backend/legal-templates/api": ["libs/backend/legal-templates/api/index.ts"], "@mynotary/backend/legal-templates/core": ["libs/backend/legal-templates/core/index.ts"], "@mynotary/backend/legal-templates/feature": ["libs/backend/legal-templates/feature/index.ts"], "@mynotary/backend/legal-templates/infra": ["libs/backend/legal-templates/infra/index.ts"], "@mynotary/backend/legal-templates/providers": ["libs/backend/legal-templates/providers/index.ts"], "@mynotary/backend/legal-templates/test": ["libs/backend/legal-templates/test/index.ts"], "@mynotary/backend/legals/api": ["libs/backend/legals/api/index.ts"], "@mynotary/backend/legals/authorization": ["libs/backend/legals/authorization/index.ts"], "@mynotary/backend/legals/core": ["libs/backend/legals/core/index.ts"], "@mynotary/backend/legals/feature": ["libs/backend/legals/feature/index.ts"], "@mynotary/backend/legals/infra": ["libs/backend/legals/infra/index.ts"], "@mynotary/backend/legals/providers": ["libs/backend/legals/providers/index.ts"], "@mynotary/backend/legals/test": ["libs/backend/legals/test/index.ts"], "@mynotary/backend/localqueues/api": ["libs/backend/localqueues/api/index.ts"], "@mynotary/backend/localqueues/core": ["libs/backend/localqueues/core/index.ts"], "@mynotary/backend/localqueues/feature": ["libs/backend/localqueues/feature/index.ts"], "@mynotary/backend/localqueues/infra": ["libs/backend/localqueues/infra/index.ts"], "@mynotary/backend/localqueues/providers": ["libs/backend/localqueues/providers/index.ts"], "@mynotary/backend/localqueues/test": ["libs/backend/localqueues/test/index.ts"], "@mynotary/backend/member-setups/core": ["libs/backend/member-setups/core/index.ts"], "@mynotary/backend/member-setups/feature": ["libs/backend/member-setups/feature/index.ts"], "@mynotary/backend/member-setups/infra": ["libs/backend/member-setups/infra/index.ts"], "@mynotary/backend/member-setups/providers": ["libs/backend/member-setups/providers/index.ts"], "@mynotary/backend/member-setups/test": ["libs/backend/member-setups/test/index.ts"], "@mynotary/backend/members-bff/core": ["libs/backend/members-bff/core/index.ts"], "@mynotary/backend/members-bff/feature": ["libs/backend/members-bff/feature/index.ts"], "@mynotary/backend/members-bff/providers": ["libs/backend/members-bff/providers/index.ts"], "@mynotary/backend/members-bff/test": ["libs/backend/members-bff/test/index.ts"], "@mynotary/backend/members/api": ["libs/backend/members/api/index.ts"], "@mynotary/backend/members/authorization": ["libs/backend/members/authorization/index.ts"], "@mynotary/backend/members/core": ["libs/backend/members/core/index.ts"], "@mynotary/backend/members/feature": ["libs/backend/members/feature/index.ts"], "@mynotary/backend/members/infra": ["libs/backend/members/infra/index.ts"], "@mynotary/backend/members/providers": ["libs/backend/members/providers/index.ts"], "@mynotary/backend/members/test": ["libs/backend/members/test/index.ts"], "@mynotary/backend/mnapi-client/api": ["libs/backend/mnapi-client/api/index.ts"], "@mynotary/backend/mnapi-client/core": ["libs/backend/mnapi-client/core/index.ts"], "@mynotary/backend/mnapi-client/infra": ["libs/backend/mnapi-client/infra/index.ts"], "@mynotary/backend/mnapi-client/providers": ["libs/backend/mnapi-client/providers/index.ts"], "@mynotary/backend/mnapi-client/test": ["libs/backend/mnapi-client/test/index.ts"], "@mynotary/backend/notifications/api": ["libs/backend/notifications/api/index.ts"], "@mynotary/backend/notifications/authorization": ["libs/backend/notifications/authorization/index.ts"], "@mynotary/backend/notifications/core": ["libs/backend/notifications/core/index.ts"], "@mynotary/backend/notifications/feature": ["libs/backend/notifications/feature/index.ts"], "@mynotary/backend/notifications/infra": ["libs/backend/notifications/infra/index.ts"], "@mynotary/backend/notifications/providers": ["libs/backend/notifications/providers/index.ts"], "@mynotary/backend/notifications/test": ["libs/backend/notifications/test/index.ts"], "@mynotary/backend/operation-access-update/api": ["libs/backend/operation-access-update/api/index.ts"], "@mynotary/backend/operation-access-update/core": ["libs/backend/operation-access-update/core/index.ts"], "@mynotary/backend/operation-access-update/feature": ["libs/backend/operation-access-update/feature/index.ts"], "@mynotary/backend/operation-access-update/infra": ["libs/backend/operation-access-update/infra/index.ts"], "@mynotary/backend/operation-access-update/providers": ["libs/backend/operation-access-update/providers/index.ts"], "@mynotary/backend/operation-access-update/test": ["libs/backend/operation-access-update/test/index.ts"], "@mynotary/backend/operation-access/api": ["libs/backend/operation-access/api/index.ts"], "@mynotary/backend/operation-access/core": ["libs/backend/operation-access/core/index.ts"], "@mynotary/backend/operation-access/feature": ["libs/backend/operation-access/feature/index.ts"], "@mynotary/backend/operation-access/infra": ["libs/backend/operation-access/infra/index.ts"], "@mynotary/backend/operation-access/providers": ["libs/backend/operation-access/providers/index.ts"], "@mynotary/backend/operation-access/test": ["libs/backend/operation-access/test/index.ts"], "@mynotary/backend/operation-invitations/authorization": ["libs/backend/operation-invitations/authorization/index.ts"], "@mynotary/backend/operation-invitations/core": ["libs/backend/operation-invitations/core/index.ts"], "@mynotary/backend/operation-invitations/feature": ["libs/backend/operation-invitations/feature/index.ts"], "@mynotary/backend/operation-invitations/infra": ["libs/backend/operation-invitations/infra/index.ts"], "@mynotary/backend/operation-invitations/providers": ["libs/backend/operation-invitations/providers/index.ts"], "@mynotary/backend/operation-invitations/test": ["libs/backend/operation-invitations/test/index.ts"], "@mynotary/backend/operation-views/core": ["libs/backend/operation-views/core/index.ts"], "@mynotary/backend/operation-views/feature": ["libs/backend/operation-views/feature/index.ts"], "@mynotary/backend/operation-views/infra": ["libs/backend/operation-views/infra/index.ts"], "@mynotary/backend/operation-views/providers": ["libs/backend/operation-views/providers/index.ts"], "@mynotary/backend/operation-views/test": ["libs/backend/operation-views/test/index.ts"], "@mynotary/backend/operations-bff/api": ["libs/backend/operations-bff/api/index.ts"], "@mynotary/backend/operations-bff/core": ["libs/backend/operations-bff/core/index.ts"], "@mynotary/backend/operations-bff/feature": ["libs/backend/operations-bff/feature/index.ts"], "@mynotary/backend/operations-bff/providers": ["libs/backend/operations-bff/providers/index.ts"], "@mynotary/backend/operations-bff/test": ["libs/backend/operations-bff/test/index.ts"], "@mynotary/backend/orders/api": ["libs/backend/orders/api/index.ts"], "@mynotary/backend/orders/authorization": ["libs/backend/orders/authorization/index.ts"], "@mynotary/backend/orders/core": ["libs/backend/orders/core/index.ts"], "@mynotary/backend/orders/feature": ["libs/backend/orders/feature/index.ts"], "@mynotary/backend/orders/infra": ["libs/backend/orders/infra/index.ts"], "@mynotary/backend/orders/providers": ["libs/backend/orders/providers/index.ts"], "@mynotary/backend/orders/test": ["libs/backend/orders/test/index.ts"], "@mynotary/backend/organization-data-transfers/core": ["libs/backend/organization-data-transfers/core/index.ts"], "@mynotary/backend/organization-data-transfers/feature": ["libs/backend/organization-data-transfers/feature/index.ts"], "@mynotary/backend/organization-data-transfers/infra": ["libs/backend/organization-data-transfers/infra/index.ts"], "@mynotary/backend/organization-data-transfers/providers": ["libs/backend/organization-data-transfers/providers/index.ts"], "@mynotary/backend/organization-data-transfers/test": ["libs/backend/organization-data-transfers/test/index.ts"], "@mynotary/backend/organization-holdings/api": ["libs/backend/organization-holdings/api/index.ts"], "@mynotary/backend/organization-holdings/core": ["libs/backend/organization-holdings/core/index.ts"], "@mynotary/backend/organization-holdings/infra": ["libs/backend/organization-holdings/infra/index.ts"], "@mynotary/backend/organization-holdings/providers": ["libs/backend/organization-holdings/providers/index.ts"], "@mynotary/backend/organization-holdings/test": ["libs/backend/organization-holdings/test/index.ts"], "@mynotary/backend/organization-setups/core": ["libs/backend/organization-setups/core/index.ts"], "@mynotary/backend/organization-setups/feature": ["libs/backend/organization-setups/feature/index.ts"], "@mynotary/backend/organization-setups/infra": ["libs/backend/organization-setups/infra/index.ts"], "@mynotary/backend/organization-setups/providers": ["libs/backend/organization-setups/providers/index.ts"], "@mynotary/backend/organization-setups/test": ["libs/backend/organization-setups/test/index.ts"], "@mynotary/backend/organizations-bff/core": ["libs/backend/organizations-bff/core/index.ts"], "@mynotary/backend/organizations-bff/feature": ["libs/backend/organizations-bff/feature/index.ts"], "@mynotary/backend/organizations-bff/providers": ["libs/backend/organizations-bff/providers/index.ts"], "@mynotary/backend/organizations-bff/test": ["libs/backend/organizations-bff/test/index.ts"], "@mynotary/backend/organizations/api": ["libs/backend/organizations/api/index.ts"], "@mynotary/backend/organizations/authorization": ["libs/backend/organizations/authorization/index.ts"], "@mynotary/backend/organizations/core": ["libs/backend/organizations/core/index.ts"], "@mynotary/backend/organizations/feature": ["libs/backend/organizations/feature/index.ts"], "@mynotary/backend/organizations/infra": ["libs/backend/organizations/infra/index.ts"], "@mynotary/backend/organizations/providers": ["libs/backend/organizations/providers/index.ts"], "@mynotary/backend/organizations/test": ["libs/backend/organizations/test/index.ts"], "@mynotary/backend/orpi/core": ["libs/backend/orpi/core/index.ts"], "@mynotary/backend/orpi/feature": ["libs/backend/orpi/feature/index.ts"], "@mynotary/backend/orpi/infra": ["libs/backend/orpi/infra/index.ts"], "@mynotary/backend/orpi/providers": ["libs/backend/orpi/providers/index.ts"], "@mynotary/backend/orpi/test": ["libs/backend/orpi/test/index.ts"], "@mynotary/backend/pdf-client/infra": ["libs/backend/pdf-client/infra/index.ts"], "@mynotary/backend/pdf-client/providers": ["libs/backend/pdf-client/providers/index.ts"], "@mynotary/backend/pdf-client/test": ["libs/backend/pdf-client/test/index.ts"], "@mynotary/backend/pdf/api": ["libs/backend/pdf/api/index.ts"], "@mynotary/backend/pdf/core": ["libs/backend/pdf/core/index.ts"], "@mynotary/backend/pdf/feature": ["libs/backend/pdf/feature/index.ts"], "@mynotary/backend/pdf/infra": ["libs/backend/pdf/infra/index.ts"], "@mynotary/backend/pdf/providers": ["libs/backend/pdf/providers/index.ts"], "@mynotary/backend/pdf/test": ["libs/backend/pdf/test/index.ts"], "@mynotary/backend/planete-companion/core": ["libs/backend/planete-companion/core/index.ts"], "@mynotary/backend/planete-companion/feature": ["libs/backend/planete-companion/feature/index.ts"], "@mynotary/backend/planete-companion/infra": ["libs/backend/planete-companion/infra/index.ts"], "@mynotary/backend/planete-companion/providers": ["libs/backend/planete-companion/providers/index.ts"], "@mynotary/backend/planete-companion/test": ["libs/backend/planete-companion/test/index.ts"], "@mynotary/backend/planete-dispatcher/core": ["libs/backend/planete-dispatcher/core/index.ts"], "@mynotary/backend/planete-dispatcher/feature": ["libs/backend/planete-dispatcher/feature/index.ts"], "@mynotary/backend/planete-dispatcher/infra": ["libs/backend/planete-dispatcher/infra/index.ts"], "@mynotary/backend/planete-dispatcher/providers": ["libs/backend/planete-dispatcher/providers/index.ts"], "@mynotary/backend/planete-dispatcher/test": ["libs/backend/planete-dispatcher/test/index.ts"], "@mynotary/backend/planete/api": ["libs/backend/planete/api/index.ts"], "@mynotary/backend/planete/core": ["libs/backend/planete/core/index.ts"], "@mynotary/backend/planete/feature": ["libs/backend/planete/feature/index.ts"], "@mynotary/backend/planete/infra": ["libs/backend/planete/infra/index.ts"], "@mynotary/backend/planete/providers": ["libs/backend/planete/providers/index.ts"], "@mynotary/backend/planete/test": ["libs/backend/planete/test/index.ts"], "@mynotary/backend/pre-etat-dates/authorization": ["libs/backend/pre-etat-dates/authorization/index.ts"], "@mynotary/backend/pre-etat-dates/core": ["libs/backend/pre-etat-dates/core/index.ts"], "@mynotary/backend/pre-etat-dates/feature": ["libs/backend/pre-etat-dates/feature/index.ts"], "@mynotary/backend/pre-etat-dates/infra": ["libs/backend/pre-etat-dates/infra/index.ts"], "@mynotary/backend/pre-etat-dates/providers": ["libs/backend/pre-etat-dates/providers/index.ts"], "@mynotary/backend/pre-etat-dates/test": ["libs/backend/pre-etat-dates/test/index.ts"], "@mynotary/backend/programs/core": ["libs/backend/programs/core/index.ts"], "@mynotary/backend/programs/feature": ["libs/backend/programs/feature/index.ts"], "@mynotary/backend/programs/infra": ["libs/backend/programs/infra/index.ts"], "@mynotary/backend/programs/providers": ["libs/backend/programs/providers/index.ts"], "@mynotary/backend/programs/test": ["libs/backend/programs/test/index.ts"], "@mynotary/backend/referentiels-bff/core": ["libs/backend/referentiels-bff/core/index.ts"], "@mynotary/backend/referentiels-bff/feature": ["libs/backend/referentiels-bff/feature/index.ts"], "@mynotary/backend/referentiels-bff/providers": ["libs/backend/referentiels-bff/providers/index.ts"], "@mynotary/backend/referentiels-bff/test": ["libs/backend/referentiels-bff/test/index.ts"], "@mynotary/backend/referentiels-companion/core": ["libs/backend/referentiels-companion/core/index.ts"], "@mynotary/backend/referentiels-companion/feature": ["libs/backend/referentiels-companion/feature/index.ts"], "@mynotary/backend/referentiels-companion/infra": ["libs/backend/referentiels-companion/infra/index.ts"], "@mynotary/backend/referentiels-companion/providers": ["libs/backend/referentiels-companion/providers/index.ts"], "@mynotary/backend/referentiels-companion/test": ["libs/backend/referentiels-companion/test/index.ts"], "@mynotary/backend/referentiels/api": ["libs/backend/referentiels/api/index.ts"], "@mynotary/backend/referentiels/core": ["libs/backend/referentiels/core/index.ts"], "@mynotary/backend/referentiels/feature": ["libs/backend/referentiels/feature/index.ts"], "@mynotary/backend/referentiels/infra": ["libs/backend/referentiels/infra/index.ts"], "@mynotary/backend/referentiels/providers": ["libs/backend/referentiels/providers/index.ts"], "@mynotary/backend/referentiels/test": ["libs/backend/referentiels/test/index.ts"], "@mynotary/backend/registered-letters/authorization": ["libs/backend/registered-letters/authorization/index.ts"], "@mynotary/backend/registered-letters/core": ["libs/backend/registered-letters/core/index.ts"], "@mynotary/backend/registered-letters/feature": ["libs/backend/registered-letters/feature/index.ts"], "@mynotary/backend/registered-letters/infra": ["libs/backend/registered-letters/infra/index.ts"], "@mynotary/backend/registered-letters/providers": ["libs/backend/registered-letters/providers/index.ts"], "@mynotary/backend/registered-letters/test": ["libs/backend/registered-letters/test/index.ts"], "@mynotary/backend/registers/api": ["libs/backend/registers/api/index.ts"], "@mynotary/backend/registers/authorization": ["libs/backend/registers/authorization/index.ts"], "@mynotary/backend/registers/core": ["libs/backend/registers/core/index.ts"], "@mynotary/backend/registers/feature": ["libs/backend/registers/feature/index.ts"], "@mynotary/backend/registers/infra": ["libs/backend/registers/infra/index.ts"], "@mynotary/backend/registers/providers": ["libs/backend/registers/providers/index.ts"], "@mynotary/backend/registers/test": ["libs/backend/registers/test/index.ts"], "@mynotary/backend/requisitions/api": ["libs/backend/requisitions/api/index.ts"], "@mynotary/backend/requisitions/core": ["libs/backend/requisitions/core/index.ts"], "@mynotary/backend/requisitions/feature": ["libs/backend/requisitions/feature/index.ts"], "@mynotary/backend/requisitions/infra": ["libs/backend/requisitions/infra/index.ts"], "@mynotary/backend/requisitions/providers": ["libs/backend/requisitions/providers/index.ts"], "@mynotary/backend/requisitions/test": ["libs/backend/requisitions/test/index.ts"], "@mynotary/backend/roles-bff/core": ["libs/backend/roles-bff/core/index.ts"], "@mynotary/backend/roles-bff/feature": ["libs/backend/roles-bff/feature/index.ts"], "@mynotary/backend/roles-bff/providers": ["libs/backend/roles-bff/providers/index.ts"], "@mynotary/backend/roles-bff/test": ["libs/backend/roles-bff/test/index.ts"], "@mynotary/backend/roles/api": ["libs/backend/roles/api/index.ts"], "@mynotary/backend/roles/authorization": ["libs/backend/roles/authorization/index.ts"], "@mynotary/backend/roles/core": ["libs/backend/roles/core/index.ts"], "@mynotary/backend/roles/feature": ["libs/backend/roles/feature/index.ts"], "@mynotary/backend/roles/infra": ["libs/backend/roles/infra/index.ts"], "@mynotary/backend/roles/providers": ["libs/backend/roles/providers/index.ts"], "@mynotary/backend/roles/test": ["libs/backend/roles/test/index.ts"], "@mynotary/backend/secrets/api": ["libs/backend/secrets/api/index.ts"], "@mynotary/backend/secrets/core": ["libs/backend/secrets/core/index.ts"], "@mynotary/backend/secrets/infra": ["libs/backend/secrets/infra/index.ts"], "@mynotary/backend/secrets/providers": ["libs/backend/secrets/providers/index.ts"], "@mynotary/backend/secrets/test": ["libs/backend/secrets/test/index.ts"], "@mynotary/backend/shared/auth-util": ["libs/backend/shared/auth-util/index.ts"], "@mynotary/backend/shared/authorization": ["libs/backend/shared/authorization/index.ts"], "@mynotary/backend/shared/features-util": ["libs/backend/shared/features-util/index.ts"], "@mynotary/backend/shared/nest-infra": ["libs/backend/shared/nest-infra/index.ts"], "@mynotary/backend/shared/openapi-validator-infra": ["libs/backend/shared/openapi-validator-infra/index.ts"], "@mynotary/backend/shared/prisma-infra": ["libs/backend/shared/prisma-infra/index.ts"], "@mynotary/backend/shared/test": ["libs/backend/shared/test/index.ts"], "@mynotary/backend/shared/testing-util": ["libs/backend/shared/testing-util/index.ts"], "@mynotary/backend/shared/util": ["libs/backend/shared/util/index.ts"], "@mynotary/backend/shared/xml-builder-util": ["libs/backend/shared/xml-builder-util/index.ts"], "@mynotary/backend/signatures/authorization": ["libs/backend/signatures/authorization/index.ts"], "@mynotary/backend/signatures/core": ["libs/backend/signatures/core/index.ts"], "@mynotary/backend/signatures/feature": ["libs/backend/signatures/feature/index.ts"], "@mynotary/backend/signatures/infra": ["libs/backend/signatures/infra/index.ts"], "@mynotary/backend/signatures/providers": ["libs/backend/signatures/providers/index.ts"], "@mynotary/backend/signatures/test": ["libs/backend/signatures/test/index.ts"], "@mynotary/backend/teleactes-bff/api": ["libs/backend/teleactes-bff/api/index.ts"], "@mynotary/backend/teleactes-bff/core": ["libs/backend/teleactes-bff/core/index.ts"], "@mynotary/backend/teleactes-bff/feature": ["libs/backend/teleactes-bff/feature/index.ts"], "@mynotary/backend/teleactes-bff/providers": ["libs/backend/teleactes-bff/providers/index.ts"], "@mynotary/backend/teleactes-bff/test": ["libs/backend/teleactes-bff/test/index.ts"], "@mynotary/backend/teleactes-client/api": ["libs/backend/teleactes-client/api/index.ts"], "@mynotary/backend/teleactes-client/core": ["libs/backend/teleactes-client/core/index.ts"], "@mynotary/backend/teleactes-client/infra": ["libs/backend/teleactes-client/infra/index.ts"], "@mynotary/backend/teleactes-client/providers": ["libs/backend/teleactes-client/providers/index.ts"], "@mynotary/backend/teleactes-client/test": ["libs/backend/teleactes-client/test/index.ts"], "@mynotary/backend/teleactes-dispatcher/core": ["libs/backend/teleactes-dispatcher/core/index.ts"], "@mynotary/backend/teleactes-dispatcher/feature": ["libs/backend/teleactes-dispatcher/feature/index.ts"], "@mynotary/backend/teleactes-dispatcher/providers": ["libs/backend/teleactes-dispatcher/providers/index.ts"], "@mynotary/backend/teleactes-dispatcher/test": ["libs/backend/teleactes-dispatcher/test/index.ts"], "@mynotary/backend/teleactes/api": ["libs/backend/teleactes/api/index.ts"], "@mynotary/backend/teleactes/core": ["libs/backend/teleactes/core/index.ts"], "@mynotary/backend/teleactes/infra": ["libs/backend/teleactes/infra/index.ts"], "@mynotary/backend/teleactes/providers": ["libs/backend/teleactes/providers/index.ts"], "@mynotary/backend/teleactes/test": ["libs/backend/teleactes/test/index.ts"], "@mynotary/backend/themes/api": ["libs/backend/themes/api/index.ts"], "@mynotary/backend/themes/core": ["libs/backend/themes/core/index.ts"], "@mynotary/backend/themes/feature": ["libs/backend/themes/feature/index.ts"], "@mynotary/backend/themes/infra": ["libs/backend/themes/infra/index.ts"], "@mynotary/backend/themes/providers": ["libs/backend/themes/providers/index.ts"], "@mynotary/backend/themes/test": ["libs/backend/themes/test/index.ts"], "@mynotary/backend/unis/core": ["libs/backend/unis/core/index.ts"], "@mynotary/backend/unis/feature": ["libs/backend/unis/feature/index.ts"], "@mynotary/backend/unis/infra": ["libs/backend/unis/infra/index.ts"], "@mynotary/backend/unis/providers": ["libs/backend/unis/providers/index.ts"], "@mynotary/backend/user-events-bff/core": ["libs/backend/user-events-bff/core/index.ts"], "@mynotary/backend/user-events-bff/feature": ["libs/backend/user-events-bff/feature/index.ts"], "@mynotary/backend/user-events-bff/providers": ["libs/backend/user-events-bff/providers/index.ts"], "@mynotary/backend/user-events-bff/test": ["libs/backend/user-events-bff/test/index.ts"], "@mynotary/backend/user-events/core": ["libs/backend/user-events/core/index.ts"], "@mynotary/backend/user-events/feature": ["libs/backend/user-events/feature/index.ts"], "@mynotary/backend/user-events/infra": ["libs/backend/user-events/infra/index.ts"], "@mynotary/backend/user-events/providers": ["libs/backend/user-events/providers/index.ts"], "@mynotary/backend/user-events/test": ["libs/backend/user-events/test/index.ts"], "@mynotary/backend/users-bff/core": ["libs/backend/users-bff/core/index.ts"], "@mynotary/backend/users-bff/feature": ["libs/backend/users-bff/feature/index.ts"], "@mynotary/backend/users-bff/providers": ["libs/backend/users-bff/providers/index.ts"], "@mynotary/backend/users-bff/test": ["libs/backend/users-bff/test/index.ts"], "@mynotary/backend/users/api": ["libs/backend/users/api/index.ts"], "@mynotary/backend/users/core": ["libs/backend/users/core/index.ts"], "@mynotary/backend/users/feature": ["libs/backend/users/feature/index.ts"], "@mynotary/backend/users/infra": ["libs/backend/users/infra/index.ts"], "@mynotary/backend/users/providers": ["libs/backend/users/providers/index.ts"], "@mynotary/backend/users/test": ["libs/backend/users/test/index.ts"], "@mynotary/crossplatform/api-adsn/api": ["libs/crossplatform/api-adsn/api/index.ts"], "@mynotary/crossplatform/api-adsn/openapi": ["libs/crossplatform/api-adsn/openapi/index.ts"], "@mynotary/crossplatform/api-auth/openapi": ["libs/crossplatform/api-auth/openapi/index.ts"], "@mynotary/crossplatform/api-emails/openapi": ["libs/crossplatform/api-emails/openapi/index.ts"], "@mynotary/crossplatform/api-etatscivils/api": ["libs/crossplatform/api-etatscivils/api/index.ts"], "@mynotary/crossplatform/api-etatscivils/openapi": ["libs/crossplatform/api-etatscivils/openapi/index.ts"], "@mynotary/crossplatform/api-files/openapi": ["libs/crossplatform/api-files/openapi/index.ts"], "@mynotary/crossplatform/api-mynotary/openapi": ["libs/crossplatform/api-mynotary/openapi/index.ts"], "@mynotary/crossplatform/api-signatures/openapi": ["libs/crossplatform/api-signatures/openapi/index.ts"], "@mynotary/crossplatform/api-teleactes/api": ["libs/crossplatform/api-teleactes/api/index.ts"], "@mynotary/crossplatform/api-teleactes/openapi": ["libs/crossplatform/api-teleactes/openapi/index.ts"], "@mynotary/crossplatform/authorizations/api": ["libs/crossplatform/authorizations/api/index.ts"], "@mynotary/crossplatform/authorizations/core": ["libs/crossplatform/authorizations/core/index.ts"], "@mynotary/crossplatform/back-portalys-companion/api": ["libs/crossplatform/back-portalys-companion/api/index.ts"], "@mynotary/crossplatform/back-portalys-companion/core": ["libs/crossplatform/back-portalys-companion/core/index.ts"], "@mynotary/crossplatform/back-portalys-companion/openapi": ["libs/crossplatform/back-portalys-companion/openapi/index.ts"], "@mynotary/crossplatform/bff-portalys/api": ["libs/crossplatform/bff-portalys/api/index.ts"], "@mynotary/crossplatform/bff-portalys/core": ["libs/crossplatform/bff-portalys/core/index.ts"], "@mynotary/crossplatform/bff-portalys/openapi": ["libs/crossplatform/bff-portalys/openapi/index.ts"], "@mynotary/crossplatform/billings/api": ["libs/crossplatform/billings/api/index.ts"], "@mynotary/crossplatform/billings/core": ["libs/crossplatform/billings/core/index.ts"], "@mynotary/crossplatform/coproprietes/api": ["libs/crossplatform/coproprietes/api/index.ts"], "@mynotary/crossplatform/coproprietes/core": ["libs/crossplatform/coproprietes/core/index.ts"], "@mynotary/crossplatform/csv-legal-records/api": ["libs/crossplatform/csv-legal-records/api/index.ts"], "@mynotary/crossplatform/csv-legal-records/core": ["libs/crossplatform/csv-legal-records/core/index.ts"], "@mynotary/crossplatform/emails/api": ["libs/crossplatform/emails/api/index.ts"], "@mynotary/crossplatform/emails/core": ["libs/crossplatform/emails/core/index.ts"], "@mynotary/crossplatform/external-apps/api": ["libs/crossplatform/external-apps/api/index.ts"], "@mynotary/crossplatform/external-apps/core": ["libs/crossplatform/external-apps/core/index.ts"], "@mynotary/crossplatform/feature-flags/api": ["libs/crossplatform/feature-flags/api/index.ts"], "@mynotary/crossplatform/feature-flags/core": ["libs/crossplatform/feature-flags/core/index.ts"], "@mynotary/crossplatform/features/api": ["libs/crossplatform/features/api/index.ts"], "@mynotary/crossplatform/features/core": ["libs/crossplatform/features/core/index.ts"], "@mynotary/crossplatform/files-client/api": ["libs/crossplatform/files-client/api/index.ts"], "@mynotary/crossplatform/files-client/core": ["libs/crossplatform/files-client/core/index.ts"], "@mynotary/crossplatform/files-client/infra": ["libs/crossplatform/files-client/infra/index.ts"], "@mynotary/crossplatform/files/api": ["libs/crossplatform/files/api/index.ts"], "@mynotary/crossplatform/files/core": ["libs/crossplatform/files/core/index.ts"], "@mynotary/crossplatform/gel-avoirs/core": ["libs/crossplatform/gel-avoirs/core/index.ts"], "@mynotary/crossplatform/invoices/api": ["libs/crossplatform/invoices/api/index.ts"], "@mynotary/crossplatform/invoices/core": ["libs/crossplatform/invoices/core/index.ts"], "@mynotary/crossplatform/legal-branches/api": ["libs/crossplatform/legal-branches/api/index.ts"], "@mynotary/crossplatform/legal-branches/core": ["libs/crossplatform/legal-branches/core/index.ts"], "@mynotary/crossplatform/legal-contract-templates/api": ["libs/crossplatform/legal-contract-templates/api/index.ts"], "@mynotary/crossplatform/legal-contract-templates/core": ["libs/crossplatform/legal-contract-templates/core/index.ts"], "@mynotary/crossplatform/legal-link-templates/api": ["libs/crossplatform/legal-link-templates/api/index.ts"], "@mynotary/crossplatform/legal-link-templates/core": ["libs/crossplatform/legal-link-templates/core/index.ts"], "@mynotary/crossplatform/legal-links/api": ["libs/crossplatform/legal-links/api/index.ts"], "@mynotary/crossplatform/legal-links/core": ["libs/crossplatform/legal-links/core/index.ts"], "@mynotary/crossplatform/legal-operation-templates/api": ["libs/crossplatform/legal-operation-templates/api/index.ts"], "@mynotary/crossplatform/legal-operation-templates/core": ["libs/crossplatform/legal-operation-templates/core/index.ts"], "@mynotary/crossplatform/legal-record-exports/core": ["libs/crossplatform/legal-record-exports/core/index.ts"], "@mynotary/crossplatform/legal-record-templates/api": ["libs/crossplatform/legal-record-templates/api/index.ts"], "@mynotary/crossplatform/legal-record-templates/core": ["libs/crossplatform/legal-record-templates/core/index.ts"], "@mynotary/crossplatform/legal-templates/api": ["libs/crossplatform/legal-templates/api/index.ts"], "@mynotary/crossplatform/legal-templates/core": ["libs/crossplatform/legal-templates/core/index.ts"], "@mynotary/crossplatform/legals/api": ["libs/crossplatform/legals/api/index.ts"], "@mynotary/crossplatform/legals/core": ["libs/crossplatform/legals/core/index.ts"], "@mynotary/crossplatform/localqueues/api": ["libs/crossplatform/localqueues/api/index.ts"], "@mynotary/crossplatform/localqueues/core": ["libs/crossplatform/localqueues/core/index.ts"], "@mynotary/crossplatform/members/api": ["libs/crossplatform/members/api/index.ts"], "@mynotary/crossplatform/members/core": ["libs/crossplatform/members/core/index.ts"], "@mynotary/crossplatform/notifications/api": ["libs/crossplatform/notifications/api/index.ts"], "@mynotary/crossplatform/notifications/core": ["libs/crossplatform/notifications/core/index.ts"], "@mynotary/crossplatform/orders/api": ["libs/crossplatform/orders/api/index.ts"], "@mynotary/crossplatform/orders/core": ["libs/crossplatform/orders/core/index.ts"], "@mynotary/crossplatform/organizations/api": ["libs/crossplatform/organizations/api/index.ts"], "@mynotary/crossplatform/pdf-client/api": ["libs/crossplatform/pdf-client/api/index.ts"], "@mynotary/crossplatform/pdf-client/core": ["libs/crossplatform/pdf-client/core/index.ts"], "@mynotary/crossplatform/pdf-client/infra": ["libs/crossplatform/pdf-client/infra/index.ts"], "@mynotary/crossplatform/programs/api": ["libs/crossplatform/programs/api/index.ts"], "@mynotary/crossplatform/programs/core": ["libs/crossplatform/programs/core/index.ts"], "@mynotary/crossplatform/records/api": ["libs/crossplatform/records/api/index.ts"], "@mynotary/crossplatform/registered-letters/api": ["libs/crossplatform/registered-letters/api/index.ts"], "@mynotary/crossplatform/roles/api": ["libs/crossplatform/roles/api/index.ts"], "@mynotary/crossplatform/shared/dates-util": ["libs/crossplatform/shared/dates-util/index.ts"], "@mynotary/crossplatform/shared/forms-util": ["libs/crossplatform/shared/forms-util/index.ts"], "@mynotary/crossplatform/shared/legals-core": ["libs/crossplatform/shared/legals-core/index.ts"], "@mynotary/crossplatform/shared/legals-util": ["libs/crossplatform/shared/legals-util/index.ts"], "@mynotary/crossplatform/shared/pdf-util": ["libs/crossplatform/shared/pdf-util/index.ts"], "@mynotary/crossplatform/shared/phones-util": ["libs/crossplatform/shared/phones-util/index.ts"], "@mynotary/crossplatform/shared/users-core": ["libs/crossplatform/shared/users-core/index.ts"], "@mynotary/crossplatform/shared/util": ["libs/crossplatform/shared/util/index.ts"], "@mynotary/crossplatform/signatures/api": ["libs/crossplatform/signatures/api/index.ts"], "@mynotary/crossplatform/themes/api": ["libs/crossplatform/themes/api/index.ts"], "@mynotary/frontend/address/feature": ["libs/frontend/address/feature/index.ts"], "@mynotary/frontend/api-connect/core": ["libs/frontend/api-connect/core/index.ts"], "@mynotary/frontend/api-connect/infra": ["libs/frontend/api-connect/infra/index.ts"], "@mynotary/frontend/archives/core": ["libs/frontend/archives/core/index.ts"], "@mynotary/frontend/archives/infra": ["libs/frontend/archives/infra/index.ts"], "@mynotary/frontend/assets/ui": ["libs/frontend/assets/ui/index.ts"], "@mynotary/frontend/auth/api": ["libs/frontend/auth/api/index.ts"], "@mynotary/frontend/auth/core": ["libs/frontend/auth/core/index.ts"], "@mynotary/frontend/auth/feature": ["libs/frontend/auth/feature/index.ts"], "@mynotary/frontend/auth/infra": ["libs/frontend/auth/infra/index.ts"], "@mynotary/frontend/auth/store": ["libs/frontend/auth/store/index.ts"], "@mynotary/frontend/billings/api": ["libs/frontend/billings/api/index.ts"], "@mynotary/frontend/billings/core": ["libs/frontend/billings/core/index.ts"], "@mynotary/frontend/billings/feature": ["libs/frontend/billings/feature/index.ts"], "@mynotary/frontend/billings/infra": ["libs/frontend/billings/infra/index.ts"], "@mynotary/frontend/billings/store": ["libs/frontend/billings/store/index.ts"], "@mynotary/frontend/billings/ui": ["libs/frontend/billings/ui/index.ts"], "@mynotary/frontend/companies/api": ["libs/frontend/companies/api/index.ts"], "@mynotary/frontend/companies/core": ["libs/frontend/companies/core/index.ts"], "@mynotary/frontend/companies/feature": ["libs/frontend/companies/feature/index.ts"], "@mynotary/frontend/companies/infra": ["libs/frontend/companies/infra/index.ts"], "@mynotary/frontend/contract-reviews/api": ["libs/frontend/contract-reviews/api/index.ts"], "@mynotary/frontend/contract-reviews/core": ["libs/frontend/contract-reviews/core/index.ts"], "@mynotary/frontend/contract-reviews/feature": ["libs/frontend/contract-reviews/feature/index.ts"], "@mynotary/frontend/contract-reviews/infra": ["libs/frontend/contract-reviews/infra/index.ts"], "@mynotary/frontend/contract-reviews/store": ["libs/frontend/contract-reviews/store/index.ts"], "@mynotary/frontend/contract-validations/api": ["libs/frontend/contract-validations/api/index.ts"], "@mynotary/frontend/contract-validations/core": ["libs/frontend/contract-validations/core/index.ts"], "@mynotary/frontend/contract-validations/feature": ["libs/frontend/contract-validations/feature/index.ts"], "@mynotary/frontend/contract-validations/infra": ["libs/frontend/contract-validations/infra/index.ts"], "@mynotary/frontend/contract-validations/store": ["libs/frontend/contract-validations/store/index.ts"], "@mynotary/frontend/contract-validators/api": ["libs/frontend/contract-validators/api/index.ts"], "@mynotary/frontend/contract-validators/core": ["libs/frontend/contract-validators/core/index.ts"], "@mynotary/frontend/contract-validators/infra": ["libs/frontend/contract-validators/infra/index.ts"], "@mynotary/frontend/contract-validators/store": ["libs/frontend/contract-validators/store/index.ts"], "@mynotary/frontend/contract-views/api": ["libs/frontend/contract-views/api/index.ts"], "@mynotary/frontend/contract-views/core": ["libs/frontend/contract-views/core/index.ts"], "@mynotary/frontend/contract-views/infra": ["libs/frontend/contract-views/infra/index.ts"], "@mynotary/frontend/contracts-dashboard/api": ["libs/frontend/contracts-dashboard/api/index.ts"], "@mynotary/frontend/contracts-dashboard/core": ["libs/frontend/contracts-dashboard/core/index.ts"], "@mynotary/frontend/contracts-dashboard/feature": ["libs/frontend/contracts-dashboard/feature/index.ts"], "@mynotary/frontend/contracts-dashboard/store": ["libs/frontend/contracts-dashboard/store/index.ts"], "@mynotary/frontend/coproprietes/api": ["libs/frontend/coproprietes/api/index.ts"], "@mynotary/frontend/coproprietes/core": ["libs/frontend/coproprietes/core/index.ts"], "@mynotary/frontend/coproprietes/feature": ["libs/frontend/coproprietes/feature/index.ts"], "@mynotary/frontend/coproprietes/infra": ["libs/frontend/coproprietes/infra/index.ts"], "@mynotary/frontend/csv-legal-records/core": ["libs/frontend/csv-legal-records/core/index.ts"], "@mynotary/frontend/csv-legal-records/feature": ["libs/frontend/csv-legal-records/feature/index.ts"], "@mynotary/frontend/csv/api": ["libs/frontend/csv/api/index.ts"], "@mynotary/frontend/csv/core": ["libs/frontend/csv/core/index.ts"], "@mynotary/frontend/csv/feature": ["libs/frontend/csv/feature/index.ts"], "@mynotary/frontend/csv/infra": ["libs/frontend/csv/infra/index.ts"], "@mynotary/frontend/current-operation/api": ["libs/frontend/current-operation/api/index.ts"], "@mynotary/frontend/current-operation/store": ["libs/frontend/current-operation/store/index.ts"], "@mynotary/frontend/custom-views/api": ["libs/frontend/custom-views/api/index.ts"], "@mynotary/frontend/custom-views/core": ["libs/frontend/custom-views/core/index.ts"], "@mynotary/frontend/custom-views/feature": ["libs/frontend/custom-views/feature/index.ts"], "@mynotary/frontend/custom-views/infra": ["libs/frontend/custom-views/infra/index.ts"], "@mynotary/frontend/custom-views/store": ["libs/frontend/custom-views/store/index.ts"], "@mynotary/frontend/customer-support/api": ["libs/frontend/customer-support/api/index.ts"], "@mynotary/frontend/customer-support/core": ["libs/frontend/customer-support/core/index.ts"], "@mynotary/frontend/customer-support/feature": ["libs/frontend/customer-support/feature/index.ts"], "@mynotary/frontend/customer-support/infra": ["libs/frontend/customer-support/infra/index.ts"], "@mynotary/frontend/customer-support/store": ["libs/frontend/customer-support/store/index.ts"], "@mynotary/frontend/dashboard-filters/api": ["libs/frontend/dashboard-filters/api/index.ts"], "@mynotary/frontend/dashboard-filters/core": ["libs/frontend/dashboard-filters/core/index.ts"], "@mynotary/frontend/dashboard-filters/feature": ["libs/frontend/dashboard-filters/feature/index.ts"], "@mynotary/frontend/dashboards-portalys/core": ["libs/frontend/dashboards-portalys/core/index.ts"], "@mynotary/frontend/dashboards-portalys/feature": ["libs/frontend/dashboards-portalys/feature/index.ts"], "@mynotary/frontend/dashboards-portalys/infra": ["libs/frontend/dashboards-portalys/infra/index.ts"], "@mynotary/frontend/data-tableau/api": ["libs/frontend/data-tableau/api/index.ts"], "@mynotary/frontend/data-tableau/core": ["libs/frontend/data-tableau/core/index.ts"], "@mynotary/frontend/data-tableau/feature": ["libs/frontend/data-tableau/feature/index.ts"], "@mynotary/frontend/data-tableau/infra": ["libs/frontend/data-tableau/infra/index.ts"], "@mynotary/frontend/data-tableau/store": ["libs/frontend/data-tableau/store/index.ts"], "@mynotary/frontend/default-records/core": ["libs/frontend/default-records/core/index.ts"], "@mynotary/frontend/default-records/feature": ["libs/frontend/default-records/feature/index.ts"], "@mynotary/frontend/default-records/infra": ["libs/frontend/default-records/infra/index.ts"], "@mynotary/frontend/default-records/store": ["libs/frontend/default-records/store/index.ts"], "@mynotary/frontend/document-requests/api": ["libs/frontend/document-requests/api/index.ts"], "@mynotary/frontend/document-requests/core": ["libs/frontend/document-requests/core/index.ts"], "@mynotary/frontend/document-requests/feature": ["libs/frontend/document-requests/feature/index.ts"], "@mynotary/frontend/document-requests/infra": ["libs/frontend/document-requests/infra/index.ts"], "@mynotary/frontend/document-requests/store": ["libs/frontend/document-requests/store/index.ts"], "@mynotary/frontend/drives/api": ["libs/frontend/drives/api/index.ts"], "@mynotary/frontend/drives/core": ["libs/frontend/drives/core/index.ts"], "@mynotary/frontend/drives/feature": ["libs/frontend/drives/feature/index.ts"], "@mynotary/frontend/drives/infra": ["libs/frontend/drives/infra/index.ts"], "@mynotary/frontend/drives/store": ["libs/frontend/drives/store/index.ts"], "@mynotary/frontend/drives/ui": ["libs/frontend/drives/ui/index.ts"], "@mynotary/frontend/email-editor/api": ["libs/frontend/email-editor/api/index.ts"], "@mynotary/frontend/email-editor/core": ["libs/frontend/email-editor/core/index.ts"], "@mynotary/frontend/email-editor/feature": ["libs/frontend/email-editor/feature/index.ts"], "@mynotary/frontend/emails/api": ["libs/frontend/emails/api/index.ts"], "@mynotary/frontend/emails/core": ["libs/frontend/emails/core/index.ts"], "@mynotary/frontend/emails/feature": ["libs/frontend/emails/feature/index.ts"], "@mynotary/frontend/emails/infra": ["libs/frontend/emails/infra/index.ts"], "@mynotary/frontend/emails/store": ["libs/frontend/emails/store/index.ts"], "@mynotary/frontend/etatcivil/api": ["libs/frontend/etatcivil/api/index.ts"], "@mynotary/frontend/etatcivil/core": ["libs/frontend/etatcivil/core/index.ts"], "@mynotary/frontend/etatcivil/feature": ["libs/frontend/etatcivil/feature/index.ts"], "@mynotary/frontend/etatcivil/infra": ["libs/frontend/etatcivil/infra/index.ts"], "@mynotary/frontend/external-apps/api": ["libs/frontend/external-apps/api/index.ts"], "@mynotary/frontend/external-apps/core": ["libs/frontend/external-apps/core/index.ts"], "@mynotary/frontend/external-apps/feature": ["libs/frontend/external-apps/feature/index.ts"], "@mynotary/frontend/external-apps/infra": ["libs/frontend/external-apps/infra/index.ts"], "@mynotary/frontend/features/api": ["libs/frontend/features/api/index.ts"], "@mynotary/frontend/features/core": ["libs/frontend/features/core/index.ts"], "@mynotary/frontend/features/feature": ["libs/frontend/features/feature/index.ts"], "@mynotary/frontend/features/infra": ["libs/frontend/features/infra/index.ts"], "@mynotary/frontend/features/store": ["libs/frontend/features/store/index.ts"], "@mynotary/frontend/files/api": ["libs/frontend/files/api/index.ts"], "@mynotary/frontend/files/core": ["libs/frontend/files/core/index.ts"], "@mynotary/frontend/files/feature": ["libs/frontend/files/feature/index.ts"], "@mynotary/frontend/files/infra": ["libs/frontend/files/infra/index.ts"], "@mynotary/frontend/files/store": ["libs/frontend/files/store/index.ts"], "@mynotary/frontend/files/ui": ["libs/frontend/files/ui/index.ts"], "@mynotary/frontend/front-mynotary-header/api": ["libs/frontend/front-mynotary-header/api/index.ts"], "@mynotary/frontend/front-mynotary-header/core": ["libs/frontend/front-mynotary-header/core/index.ts"], "@mynotary/frontend/front-mynotary-header/feature": ["libs/frontend/front-mynotary-header/feature/index.ts"], "@mynotary/frontend/front-mynotary/api": ["libs/frontend/front-mynotary/api/index.ts"], "@mynotary/frontend/front-mynotary/store": ["libs/frontend/front-mynotary/store/index.ts"], "@mynotary/frontend/gel-avoirs/api": ["libs/frontend/gel-avoirs/api/index.ts"], "@mynotary/frontend/gel-avoirs/core": ["libs/frontend/gel-avoirs/core/index.ts"], "@mynotary/frontend/gel-avoirs/feature": ["libs/frontend/gel-avoirs/feature/index.ts"], "@mynotary/frontend/gel-avoirs/infra": ["libs/frontend/gel-avoirs/infra/index.ts"], "@mynotary/frontend/gel-avoirs/store": ["libs/frontend/gel-avoirs/store/index.ts"], "@mynotary/frontend/global-popins/feature": ["libs/frontend/global-popins/feature/index.ts"], "@mynotary/frontend/inquiries-history/api": ["libs/frontend/inquiries-history/api/index.ts"], "@mynotary/frontend/inquiries-history/core": ["libs/frontend/inquiries-history/core/index.ts"], "@mynotary/frontend/inquiries-history/feature": ["libs/frontend/inquiries-history/feature/index.ts"], "@mynotary/frontend/inquiries-history/infra": ["libs/frontend/inquiries-history/infra/index.ts"], "@mynotary/frontend/inquiries-history/ui": ["libs/frontend/inquiries-history/ui/index.ts"], "@mynotary/frontend/inquiries/api": ["libs/frontend/inquiries/api/index.ts"], "@mynotary/frontend/inquiries/core": ["libs/frontend/inquiries/core/index.ts"], "@mynotary/frontend/inquiries/feature": ["libs/frontend/inquiries/feature/index.ts"], "@mynotary/frontend/inquiries/infra": ["libs/frontend/inquiries/infra/index.ts"], "@mynotary/frontend/inquiries/ui": ["libs/frontend/inquiries/ui/index.ts"], "@mynotary/frontend/invoices/core": ["libs/frontend/invoices/core/index.ts"], "@mynotary/frontend/invoices/feature": ["libs/frontend/invoices/feature/index.ts"], "@mynotary/frontend/invoices/infra": ["libs/frontend/invoices/infra/index.ts"], "@mynotary/frontend/invoices/store": ["libs/frontend/invoices/store/index.ts"], "@mynotary/frontend/invoices/ui": ["libs/frontend/invoices/ui/index.ts"], "@mynotary/frontend/learn-worlds/api": ["libs/frontend/learn-worlds/api/index.ts"], "@mynotary/frontend/learn-worlds/core": ["libs/frontend/learn-worlds/core/index.ts"], "@mynotary/frontend/learn-worlds/infra": ["libs/frontend/learn-worlds/infra/index.ts"], "@mynotary/frontend/legal-contract-creations/api": ["libs/frontend/legal-contract-creations/api/index.ts"], "@mynotary/frontend/legal-contract-creations/feature": ["libs/frontend/legal-contract-creations/feature/index.ts"], "@mynotary/frontend/legal-link-creations/api": ["libs/frontend/legal-link-creations/api/index.ts"], "@mynotary/frontend/legal-link-creations/feature": ["libs/frontend/legal-link-creations/feature/index.ts"], "@mynotary/frontend/legal-operation-creations/api": ["libs/frontend/legal-operation-creations/api/index.ts"], "@mynotary/frontend/legal-operation-creations/feature": ["libs/frontend/legal-operation-creations/feature/index.ts"], "@mynotary/frontend/legal-operations-fetch/core": ["libs/frontend/legal-operations-fetch/core/index.ts"], "@mynotary/frontend/legal-operations-fetch/feature": ["libs/frontend/legal-operations-fetch/feature/index.ts"], "@mynotary/frontend/legal-operations-fetch/infra": ["libs/frontend/legal-operations-fetch/infra/index.ts"], "@mynotary/frontend/legal-record-creations/api": ["libs/frontend/legal-record-creations/api/index.ts"], "@mynotary/frontend/legal-record-creations/feature": ["libs/frontend/legal-record-creations/feature/index.ts"], "@mynotary/frontend/legal-record-exports/core": ["libs/frontend/legal-record-exports/core/index.ts"], "@mynotary/frontend/legal-record-exports/infra": ["libs/frontend/legal-record-exports/infra/index.ts"], "@mynotary/frontend/legal-record-forms/api": ["libs/frontend/legal-record-forms/api/index.ts"], "@mynotary/frontend/legal-record-forms/feature": ["libs/frontend/legal-record-forms/feature/index.ts"], "@mynotary/frontend/legal-templates/api": ["libs/frontend/legal-templates/api/index.ts"], "@mynotary/frontend/legal-templates/core": ["libs/frontend/legal-templates/core/index.ts"], "@mynotary/frontend/legal-templates/infra": ["libs/frontend/legal-templates/infra/index.ts"], "@mynotary/frontend/legals/api": ["libs/frontend/legals/api/index.ts"], "@mynotary/frontend/legals/core": ["libs/frontend/legals/core/index.ts"], "@mynotary/frontend/legals/feature": ["libs/frontend/legals/feature/index.ts"], "@mynotary/frontend/legals/infra": ["libs/frontend/legals/infra/index.ts"], "@mynotary/frontend/legals/store": ["libs/frontend/legals/store/index.ts"], "@mynotary/frontend/legals/test": ["libs/frontend/legals/test/index.ts"], "@mynotary/frontend/legals/ui": ["libs/frontend/legals/ui/index.ts"], "@mynotary/frontend/loader/api": ["libs/frontend/loader/api/index.ts"], "@mynotary/frontend/loader/store": ["libs/frontend/loader/store/index.ts"], "@mynotary/frontend/members-portalys/api": ["libs/frontend/members-portalys/api/index.ts"], "@mynotary/frontend/members-portalys/core": ["libs/frontend/members-portalys/core/index.ts"], "@mynotary/frontend/members-portalys/feature": ["libs/frontend/members-portalys/feature/index.ts"], "@mynotary/frontend/members-portalys/infra": ["libs/frontend/members-portalys/infra/index.ts"], "@mynotary/frontend/members/api": ["libs/frontend/members/api/index.ts"], "@mynotary/frontend/members/core": ["libs/frontend/members/core/index.ts"], "@mynotary/frontend/members/feature": ["libs/frontend/members/feature/index.ts"], "@mynotary/frontend/members/infra": ["libs/frontend/members/infra/index.ts"], "@mynotary/frontend/members/store": ["libs/frontend/members/store/index.ts"], "@mynotary/frontend/notifications/api": ["libs/frontend/notifications/api/index.ts"], "@mynotary/frontend/notifications/core": ["libs/frontend/notifications/core/index.ts"], "@mynotary/frontend/notifications/feature": ["libs/frontend/notifications/feature/index.ts"], "@mynotary/frontend/notifications/infra": ["libs/frontend/notifications/infra/index.ts"], "@mynotary/frontend/notifications/store": ["libs/frontend/notifications/store/index.ts"], "@mynotary/frontend/notifications/ui": ["libs/frontend/notifications/ui/index.ts"], "@mynotary/frontend/operation-access/api": ["libs/frontend/operation-access/api/index.ts"], "@mynotary/frontend/operation-access/core": ["libs/frontend/operation-access/core/index.ts"], "@mynotary/frontend/operation-access/feature": ["libs/frontend/operation-access/feature/index.ts"], "@mynotary/frontend/operation-access/infra": ["libs/frontend/operation-access/infra/index.ts"], "@mynotary/frontend/operation-access/store": ["libs/frontend/operation-access/store/index.ts"], "@mynotary/frontend/operation-default-records/api": ["libs/frontend/operation-default-records/api/index.ts"], "@mynotary/frontend/operation-default-records/core": ["libs/frontend/operation-default-records/core/index.ts"], "@mynotary/frontend/operation-default-records/feature": ["libs/frontend/operation-default-records/feature/index.ts"], "@mynotary/frontend/operation-default-records/infra": ["libs/frontend/operation-default-records/infra/index.ts"], "@mynotary/frontend/operation-default-records/store": ["libs/frontend/operation-default-records/store/index.ts"], "@mynotary/frontend/operation-files/api": ["libs/frontend/operation-files/api/index.ts"], "@mynotary/frontend/operation-files/core": ["libs/frontend/operation-files/core/index.ts"], "@mynotary/frontend/operation-files/feature": ["libs/frontend/operation-files/feature/index.ts"], "@mynotary/frontend/operation-files/store": ["libs/frontend/operation-files/store/index.ts"], "@mynotary/frontend/operation-invitations/api": ["libs/frontend/operation-invitations/api/index.ts"], "@mynotary/frontend/operation-invitations/core": ["libs/frontend/operation-invitations/core/index.ts"], "@mynotary/frontend/operation-invitations/infra": ["libs/frontend/operation-invitations/infra/index.ts"], "@mynotary/frontend/operation-invitations/store": ["libs/frontend/operation-invitations/store/index.ts"], "@mynotary/frontend/operation-status/api": ["libs/frontend/operation-status/api/index.ts"], "@mynotary/frontend/operation-status/core": ["libs/frontend/operation-status/core/index.ts"], "@mynotary/frontend/operation-status/store": ["libs/frontend/operation-status/store/index.ts"], "@mynotary/frontend/operation-views/core": ["libs/frontend/operation-views/core/index.ts"], "@mynotary/frontend/operation-views/infra": ["libs/frontend/operation-views/infra/index.ts"], "@mynotary/frontend/operations-dashboard/api": ["libs/frontend/operations-dashboard/api/index.ts"], "@mynotary/frontend/operations-dashboard/core": ["libs/frontend/operations-dashboard/core/index.ts"], "@mynotary/frontend/operations-dashboard/store": ["libs/frontend/operations-dashboard/store/index.ts"], "@mynotary/frontend/operations-portalys/core": ["libs/frontend/operations-portalys/core/index.ts"], "@mynotary/frontend/operations-portalys/feature": ["libs/frontend/operations-portalys/feature/index.ts"], "@mynotary/frontend/operations-portalys/infra": ["libs/frontend/operations-portalys/infra/index.ts"], "@mynotary/frontend/orders/api": ["libs/frontend/orders/api/index.ts"], "@mynotary/frontend/orders/core": ["libs/frontend/orders/core/index.ts"], "@mynotary/frontend/orders/feature": ["libs/frontend/orders/feature/index.ts"], "@mynotary/frontend/orders/infra": ["libs/frontend/orders/infra/index.ts"], "@mynotary/frontend/orders/store": ["libs/frontend/orders/store/index.ts"], "@mynotary/frontend/organization-data-transfers/api": ["libs/frontend/organization-data-transfers/api/index.ts"], "@mynotary/frontend/organization-data-transfers/core": ["libs/frontend/organization-data-transfers/core/index.ts"], "@mynotary/frontend/organization-data-transfers/feature": ["libs/frontend/organization-data-transfers/feature/index.ts"], "@mynotary/frontend/organization-data-transfers/infra": ["libs/frontend/organization-data-transfers/infra/index.ts"], "@mynotary/frontend/organization-data-transfers/store": ["libs/frontend/organization-data-transfers/store/index.ts"], "@mynotary/frontend/organization-setups/api": ["libs/frontend/organization-setups/api/index.ts"], "@mynotary/frontend/organization-setups/core": ["libs/frontend/organization-setups/core/index.ts"], "@mynotary/frontend/organization-setups/feature": ["libs/frontend/organization-setups/feature/index.ts"], "@mynotary/frontend/organization-setups/infra": ["libs/frontend/organization-setups/infra/index.ts"], "@mynotary/frontend/organizations-portalys/api": ["libs/frontend/organizations-portalys/api/index.ts"], "@mynotary/frontend/organizations-portalys/core": ["libs/frontend/organizations-portalys/core/index.ts"], "@mynotary/frontend/organizations-portalys/feature": ["libs/frontend/organizations-portalys/feature/index.ts"], "@mynotary/frontend/organizations-portalys/infra": ["libs/frontend/organizations-portalys/infra/index.ts"], "@mynotary/frontend/organizations/api": ["libs/frontend/organizations/api/index.ts"], "@mynotary/frontend/organizations/core": ["libs/frontend/organizations/core/index.ts"], "@mynotary/frontend/organizations/feature": ["libs/frontend/organizations/feature/index.ts"], "@mynotary/frontend/organizations/infra": ["libs/frontend/organizations/infra/index.ts"], "@mynotary/frontend/organizations/store": ["libs/frontend/organizations/store/index.ts"], "@mynotary/frontend/organizations/ui": ["libs/frontend/organizations/ui/index.ts"], "@mynotary/frontend/pdf/api": ["libs/frontend/pdf/api/index.ts"], "@mynotary/frontend/pdf/core": ["libs/frontend/pdf/core/index.ts"], "@mynotary/frontend/pdf/feature": ["libs/frontend/pdf/feature/index.ts"], "@mynotary/frontend/pdf/infra": ["libs/frontend/pdf/infra/index.ts"], "@mynotary/frontend/pdf/store": ["libs/frontend/pdf/store/index.ts"], "@mynotary/frontend/places/api": ["libs/frontend/places/api/index.ts"], "@mynotary/frontend/places/core": ["libs/frontend/places/core/index.ts"], "@mynotary/frontend/places/feature": ["libs/frontend/places/feature/index.ts"], "@mynotary/frontend/places/infra": ["libs/frontend/places/infra/index.ts"], "@mynotary/frontend/popins/api": ["libs/frontend/popins/api/index.ts"], "@mynotary/frontend/popins/core": ["libs/frontend/popins/core/index.ts"], "@mynotary/frontend/popins/feature": ["libs/frontend/popins/feature/index.ts"], "@mynotary/frontend/popins/store": ["libs/frontend/popins/store/index.ts"], "@mynotary/frontend/pre-etat-dates/api": ["libs/frontend/pre-etat-dates/api/index.ts"], "@mynotary/frontend/pre-etat-dates/core": ["libs/frontend/pre-etat-dates/core/index.ts"], "@mynotary/frontend/pre-etat-dates/feature": ["libs/frontend/pre-etat-dates/feature/index.ts"], "@mynotary/frontend/pre-etat-dates/store": ["libs/frontend/pre-etat-dates/store/index.ts"], "@mynotary/frontend/programs/api": ["libs/frontend/programs/api/index.ts"], "@mynotary/frontend/programs/core": ["libs/frontend/programs/core/index.ts"], "@mynotary/frontend/programs/feature": ["libs/frontend/programs/feature/index.ts"], "@mynotary/frontend/programs/infra": ["libs/frontend/programs/infra/index.ts"], "@mynotary/frontend/programs/store": ["libs/frontend/programs/store/index.ts"], "@mynotary/frontend/redactions/api": ["libs/frontend/redactions/api/index.ts"], "@mynotary/frontend/redactions/core": ["libs/frontend/redactions/core/index.ts"], "@mynotary/frontend/redactions/feature": ["libs/frontend/redactions/feature/index.ts"], "@mynotary/frontend/redactions/store": ["libs/frontend/redactions/store/index.ts"], "@mynotary/frontend/referentiels/api": ["libs/frontend/referentiels/api/index.ts"], "@mynotary/frontend/referentiels/core": ["libs/frontend/referentiels/core/index.ts"], "@mynotary/frontend/referentiels/feature": ["libs/frontend/referentiels/feature/index.ts"], "@mynotary/frontend/referentiels/infra": ["libs/frontend/referentiels/infra/index.ts"], "@mynotary/frontend/registered-letters-creation/api": ["libs/frontend/registered-letters-creation/api/index.ts"], "@mynotary/frontend/registered-letters-creation/feature": ["libs/frontend/registered-letters-creation/feature/index.ts"], "@mynotary/frontend/registered-letters/api": ["libs/frontend/registered-letters/api/index.ts"], "@mynotary/frontend/registered-letters/core": ["libs/frontend/registered-letters/core/index.ts"], "@mynotary/frontend/registered-letters/feature": ["libs/frontend/registered-letters/feature/index.ts"], "@mynotary/frontend/registered-letters/infra": ["libs/frontend/registered-letters/infra/index.ts"], "@mynotary/frontend/registered-letters/store": ["libs/frontend/registered-letters/store/index.ts"], "@mynotary/frontend/registers/api": ["libs/frontend/registers/api/index.ts"], "@mynotary/frontend/registers/core": ["libs/frontend/registers/core/index.ts"], "@mynotary/frontend/registers/feature": ["libs/frontend/registers/feature/index.ts"], "@mynotary/frontend/registers/infra": ["libs/frontend/registers/infra/index.ts"], "@mynotary/frontend/registers/store": ["libs/frontend/registers/store/index.ts"], "@mynotary/frontend/registers/ui": ["libs/frontend/registers/ui/index.ts"], "@mynotary/frontend/reservations/api": ["libs/frontend/reservations/api/index.ts"], "@mynotary/frontend/reservations/core": ["libs/frontend/reservations/core/index.ts"], "@mynotary/frontend/reservations/store": ["libs/frontend/reservations/store/index.ts"], "@mynotary/frontend/roles-portalys/api": ["libs/frontend/roles-portalys/api/index.ts"], "@mynotary/frontend/roles-portalys/core": ["libs/frontend/roles-portalys/core/index.ts"], "@mynotary/frontend/roles-portalys/feature": ["libs/frontend/roles-portalys/feature/index.ts"], "@mynotary/frontend/roles-portalys/infra": ["libs/frontend/roles-portalys/infra/index.ts"], "@mynotary/frontend/roles/api": ["libs/frontend/roles/api/index.ts"], "@mynotary/frontend/roles/core": ["libs/frontend/roles/core/index.ts"], "@mynotary/frontend/roles/infra": ["libs/frontend/roles/infra/index.ts"], "@mynotary/frontend/roles/store": ["libs/frontend/roles/store/index.ts"], "@mynotary/frontend/routes/api": ["libs/frontend/routes/api/index.ts"], "@mynotary/frontend/routes/core": ["libs/frontend/routes/core/index.ts"], "@mynotary/frontend/routes/feature": ["libs/frontend/routes/feature/index.ts"], "@mynotary/frontend/routes/ui": ["libs/frontend/routes/ui/index.ts"], "@mynotary/frontend/settings-portalys/feature": ["libs/frontend/settings-portalys/feature/index.ts"], "@mynotary/frontend/shared/axios-util": ["libs/frontend/shared/axios-util/index.ts"], "@mynotary/frontend/shared/environments-util": ["libs/frontend/shared/environments-util/index.ts"], "@mynotary/frontend/shared/features-util": ["libs/frontend/shared/features-util/index.ts"], "@mynotary/frontend/shared/firebase-infra": ["libs/frontend/shared/firebase-infra/index.ts"], "@mynotary/frontend/shared/forms-util": ["libs/frontend/shared/forms-util/index.ts"], "@mynotary/frontend/shared/injector-util": ["libs/frontend/shared/injector-util/index.ts"], "@mynotary/frontend/shared/redux-util": ["libs/frontend/shared/redux-util/index.ts"], "@mynotary/frontend/shared/static-data-util": ["libs/frontend/shared/static-data-util/index.ts"], "@mynotary/frontend/shared/ui": ["libs/frontend/shared/ui/index.ts"], "@mynotary/frontend/shared/util": ["libs/frontend/shared/util/index.ts"], "@mynotary/frontend/signature-creations/api": ["libs/frontend/signature-creations/api/index.ts"], "@mynotary/frontend/signature-creations/feature": ["libs/frontend/signature-creations/feature/index.ts"], "@mynotary/frontend/signature-creations/store": ["libs/frontend/signature-creations/store/index.ts"], "@mynotary/frontend/signatures/api": ["libs/frontend/signatures/api/index.ts"], "@mynotary/frontend/signatures/core": ["libs/frontend/signatures/core/index.ts"], "@mynotary/frontend/signatures/feature": ["libs/frontend/signatures/feature/index.ts"], "@mynotary/frontend/signatures/infra": ["libs/frontend/signatures/infra/index.ts"], "@mynotary/frontend/signatures/store": ["libs/frontend/signatures/store/index.ts"], "@mynotary/frontend/signatures/ui": ["libs/frontend/signatures/ui/index.ts"], "@mynotary/frontend/snackbars/api": ["libs/frontend/snackbars/api/index.ts"], "@mynotary/frontend/snackbars/core": ["libs/frontend/snackbars/core/index.ts"], "@mynotary/frontend/snackbars/feature": ["libs/frontend/snackbars/feature/index.ts"], "@mynotary/frontend/snackbars/store": ["libs/frontend/snackbars/store/index.ts"], "@mynotary/frontend/tasks/api": ["libs/frontend/tasks/api/index.ts"], "@mynotary/frontend/tasks/core": ["libs/frontend/tasks/core/index.ts"], "@mynotary/frontend/tasks/store": ["libs/frontend/tasks/store/index.ts"], "@mynotary/frontend/teleactes/api": ["libs/frontend/teleactes/api/index.ts"], "@mynotary/frontend/teleactes/core": ["libs/frontend/teleactes/core/index.ts"], "@mynotary/frontend/teleactes/feature": ["libs/frontend/teleactes/feature/index.ts"], "@mynotary/frontend/teleactes/infra": ["libs/frontend/teleactes/infra/index.ts"], "@mynotary/frontend/text-editor/api": ["libs/frontend/text-editor/api/index.ts"], "@mynotary/frontend/text-editor/core": ["libs/frontend/text-editor/core/index.ts"], "@mynotary/frontend/text-editor/feature": ["libs/frontend/text-editor/feature/index.ts"], "@mynotary/frontend/themes/api": ["libs/frontend/themes/api/index.ts"], "@mynotary/frontend/themes/core": ["libs/frontend/themes/core/index.ts"], "@mynotary/frontend/themes/feature": ["libs/frontend/themes/feature/index.ts"], "@mynotary/frontend/themes/infra": ["libs/frontend/themes/infra/index.ts"], "@mynotary/frontend/themes/store": ["libs/frontend/themes/store/index.ts"], "@mynotary/frontend/unis/core": ["libs/frontend/unis/core/index.ts"], "@mynotary/frontend/unis/feature": ["libs/frontend/unis/feature/index.ts"], "@mynotary/frontend/unis/infra": ["libs/frontend/unis/infra/index.ts"], "@mynotary/frontend/user-events/api": ["libs/frontend/user-events/api/index.ts"], "@mynotary/frontend/user-events/core": ["libs/frontend/user-events/core/index.ts"], "@mynotary/frontend/user-events/infra": ["libs/frontend/user-events/infra/index.ts"], "@mynotary/frontend/user-session/api": ["libs/frontend/user-session/api/index.ts"], "@mynotary/frontend/user-session/infra": ["libs/frontend/user-session/infra/index.ts"], "@mynotary/frontend/user-session/store": ["libs/frontend/user-session/store/index.ts"], "@mynotary/frontend/users-portalys/core": ["libs/frontend/users-portalys/core/index.ts"], "@mynotary/frontend/users-portalys/feature": ["libs/frontend/users-portalys/feature/index.ts"], "@mynotary/frontend/users-portalys/infra": ["libs/frontend/users-portalys/infra/index.ts"], "@mynotary/frontend/users/api": ["libs/frontend/users/api/index.ts"], "@mynotary/frontend/users/core": ["libs/frontend/users/core/index.ts"], "@mynotary/frontend/users/feature": ["libs/frontend/users/feature/index.ts"], "@mynotary/frontend/users/infra": ["libs/frontend/users/infra/index.ts"], "@mynotary/frontend/users/store": ["libs/frontend/users/store/index.ts"], "@mynotary/frontend/whitelist/api": ["libs/frontend/whitelist/api/index.ts"], "@mynotary/testing": ["libs/testing/src/index.ts"], "@mynotary/testing-node": ["libs/testing-node/src/index.ts"], "@mynotary/workspace-plugin": ["tools/workspace-plugin/src/index.ts"], "app": ["apps/front-mynotary/src/app"], "app/*": ["apps/front-mynotary/src/app/*"], "components": ["apps/front-mynotary/src/components"], "components/*": ["apps/front-mynotary/src/components/*"], "features": ["apps/front-mynotary/src/features"], "features/*": ["apps/front-mynotary/src/features/*"], "helpers": ["apps/front-mynotary/src/helpers"], "helpers/*": ["apps/front-mynotary/src/helpers/*"], "hooks": ["apps/front-mynotary/src/hooks"], "hooks/*": ["apps/front-mynotary/src/hooks/*"], "pages": ["apps/front-mynotary/src/pages"], "pages/*": ["apps/front-mynotary/src/pages/*"]}, "allowSyntheticDefaultImports": true}, "exclude": ["node_modules", "tmp"]}