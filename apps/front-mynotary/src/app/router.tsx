import { ReactElement, useEffect } from 'react';
import { Route, useLocation, Navigate, useSearchParams } from 'react-router-dom';
import loadable from '@loadable/component';
import { ApplicationPage } from 'pages/application/application';
import { OperationPage } from 'pages/operation/operation-page';
import { RecordPage } from 'pages/record/record';
import { MnLoader } from '@mynotary/frontend/shared/ui';
import { PageTemplate } from 'pages/template/pageTemplate/pageTemplate';
import { ApplicationPageTemplate } from 'pages/template/applicationPageTemplate/applicationPageTemplate';
import { Archives } from 'pages/archives/archives';
import { DataVizTableau, useFetchTableau } from '@mynotary/frontend/data-tableau/feature';
import { MnRouteSwitch } from '@mynotary/frontend/routes/api';
import { routePaths } from '@mynotary/frontend/routes/api';
import {
  SubscriptionPage,
  UnisBillingsPage,
  useFetchCredits,
  useFetchSubscription
} from '@mynotary/frontend/billings/feature';
import { SnackBar } from '@mynotary/frontend/snackbars/feature';
import { PopinManager } from '@mynotary/frontend/global-popins/feature';
import { OrganizationPage } from '@mynotary/frontend/organizations/feature';
import { UnisPage } from '@mynotary/frontend/unis/feature';
import { PublicRouter } from 'pages/public/public-router';
import { NotFoundPage } from '@mynotary/frontend/routes/ui';
import { retryImport, setBodyOverflow } from '@mynotary/frontend/shared/util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { QUERY_PARAMS_OPEN_TASKS } from '@mynotary/frontend/shared/static-data-util';
import { closeSidebar, openSidebarAction, SidebarActionType } from '@mynotary/frontend/legals/store';
import { useSelector } from 'react-redux';
import { selectIsUserSessionInitialized } from '@mynotary/frontend/user-session/store';
import { useFetchUserSession } from 'app/app/use-fetch-user-session';
import { useRefreshToken } from 'app/app/use-refresh-token';
import { useFetchFeatures } from '@mynotary/frontend/features/feature';
import { useFetchOperationAccess } from '@mynotary/frontend/operation-access/feature';
import { useFetchRegisters } from 'app/app/use-fetch-registers';
import { useFetchNotifications, useNotificationsWatcher } from '@mynotary/frontend/notifications/feature';
import { useFetchOrganizationDefaultRecords } from '@mynotary/frontend/operation-default-records/feature';

const AdminPage = loadable(() => retryImport(() => import('pages/admin/admin')), {
  fallback: <MnLoader variant='large' />
});

const ParametersPage = loadable(() => retryImport(() => import('pages/parameters/parameters')), {
  fallback: <MnLoader variant='large' />
});

export const Routes = (): ReactElement => {
  const location = useLocation();
  const dispatch = useAsyncDispatch();
  const [searchParams] = useSearchParams();
  const openTask = searchParams.has(QUERY_PARAMS_OPEN_TASKS);

  useEffect(() => {
    setBodyOverflow(true, 0);
  }, [location, dispatch]);

  useEffect(() => {
    if (openTask) {
      dispatch(openSidebarAction({ type: SidebarActionType.TASK_LIST }));
    } else {
      dispatch(closeSidebar());
    }
    window.scrollTo(0, 0);
  }, [dispatch, openTask]);

  return (
    <>
      <div id='floatingPopin' />
      <MnRouteSwitch>
        <Route element={<Navigate replace to={routePaths.application.operations.path} />} path='/' />
        <Route
          element={
            <PrivateRoute>
              <ApplicationPageTemplate>
                <ApplicationPage />
              </ApplicationPageTemplate>
            </PrivateRoute>
          }
          path={routePaths.application.relativePath}
        />
        <Route
          element={
            <PrivateRoute>
              <ApplicationPageTemplate>
                <OperationPage />
              </ApplicationPageTemplate>
            </PrivateRoute>
          }
          path={routePaths.operation.operations.subOperations.relativePath}
        />
        <Route
          element={
            <PrivateRoute>
              <ApplicationPageTemplate>
                <OperationPage />
              </ApplicationPageTemplate>
            </PrivateRoute>
          }
          path={routePaths.operation.relativePath}
        />
        <Route
          element={
            <PrivateRoute>
              <ApplicationPageTemplate>
                <RecordPage />
              </ApplicationPageTemplate>
            </PrivateRoute>
          }
          path={routePaths.person.relativePath}
        />
        <Route
          element={
            <PrivateRoute>
              <ApplicationPageTemplate>
                <RecordPage />
              </ApplicationPageTemplate>
            </PrivateRoute>
          }
          path={routePaths.property.relativePath}
        />
        <Route
          element={
            <PrivateRoute>
              <ApplicationPageTemplate>
                <Archives />
              </ApplicationPageTemplate>
            </PrivateRoute>
          }
          path={routePaths.archives.relativePath}
        />
        <Route
          element={
            <PrivateRoute>
              <ApplicationPageTemplate>
                <DataVizTableau />
              </ApplicationPageTemplate>
            </PrivateRoute>
          }
          path={routePaths.dataViz.relativePath}
        />

        <Route
          element={
            <PrivateRoute>
              <PageTemplate hasNavbar={false} support='none'>
                <SubscriptionPage />
              </PageTemplate>
            </PrivateRoute>
          }
          path={routePaths.subscriptions.relativePath}
        />
        <Route
          element={
            <PrivateRoute>
              <PageTemplate hasNavbar={false} support='none'>
                <OrganizationPage />
              </PageTemplate>
            </PrivateRoute>
          }
          path={routePaths.organization.relativePath}
        />

        <Route
          element={
            <PrivateRoute>
              <PageTemplate support='header'>
                <ParametersPage />
              </PageTemplate>
            </PrivateRoute>
          }
          path={routePaths.parameters.relativePath}
        />
        <Route
          element={
            <PrivateRoute>
              <PageTemplate support='none'>
                <AdminPage />
              </PageTemplate>
            </PrivateRoute>
          }
          path={routePaths.admin.relativePath}
        />
        <Route
          element={
            <PrivateRoute>
              <PageTemplate support='header'>
                <UnisPage />
              </PageTemplate>
            </PrivateRoute>
          }
          path={routePaths.unis.relativePath}
        />
        <Route
          element={
            <PrivateRoute>
              <PageTemplate hasNavbar={false} support='widget'>
                <UnisBillingsPage />
              </PageTemplate>
            </PrivateRoute>
          }
          path={routePaths.unis.billings.relativePath}
        />

        {PublicRouter()}
        <Route element={<NotFoundPage />} path={'*'} />
      </MnRouteSwitch>
      <SnackBar />
      <PopinManager />
    </>
  );
};

const PrivateRoute = ({ children }: { children: ReactElement }) => {
  useFetchUserSession();
  useRefreshToken();

  const isUserSessionLoaded = useSelector(selectIsUserSessionInitialized);

  if (!isUserSessionLoaded) {
    return null;
  }

  return <PrivateRouteDataFetcher>{children}</PrivateRouteDataFetcher>;
};

const PrivateRouteDataFetcher = ({ children }: { children: ReactElement }) => {
  // Global features data fetching
  useFetchFeatures();
  useFetchSubscription();
  useFetchCredits();
  useFetchOperationAccess();
  useFetchTableau();
  useFetchRegisters();
  useFetchNotifications();
  useFetchOrganizationDefaultRecords();

  // Global watchers for realtime data synchronization
  useNotificationsWatcher();
  return children;
};
