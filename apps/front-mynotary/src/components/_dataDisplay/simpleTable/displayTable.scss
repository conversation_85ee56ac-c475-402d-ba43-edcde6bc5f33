@use 'style/variables/colors' as *;
@use 'style/mixins/responsive' as *;

.mn-display-table {
  @include responsive($tablet-max) {
    width: 100%;
    padding: 0 96px;
  }

  @include responsive($mobile-max) {
    padding: 0 16px;
  }

  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 24px;

  &.empty {
    height: 100%;
  }

  .mn-display-table-responsive {
    @include responsive($small-desktop-min) {
      display: none;
    }
  }

  .mn-display-table-desktop {
    @include responsive($tablet-max) {
      display: none;
    }
  }

  &.fullscreen {
    padding-top: 32px;
    background-color: $gray50;

    .empty-result-row {
      margin-top: 80px;
    }
  }

  &:not(.fullscreen) {
    padding: 0;
  }
}
