import './downloadFiles.scss';
import React, { ReactElement, useState, useEffect } from 'react';
import { Task } from '@mynotary/frontend/legals/core';
import { TaskType } from '@mynotary/crossplatform/legals/core';
import { validateFiles } from '@mynotary/frontend/files/store';
import { openInNewTab } from '@mynotary/frontend/shared/util';

import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { MnFolderDisplay } from '@mynotary/frontend/files/feature';
import { MnButtonController } from '@mynotary/frontend/shared/ui';
import { updateTaskCompletion } from '@mynotary/frontend/legals/store';
import { Folder } from '@mynotary/crossplatform/files/core';

type DownloadFilesTaskTemplateProps = {
  task: Task;
};

const DownloadFilesTaskTemplate = ({ task }: DownloadFilesTaskTemplateProps): ReactElement | null => {
  const [isLoading, setIsLoading] = useState(false);
  const [folder, setFolder] = useState<Folder>();

  const dispatch = useAsyncDispatch();

  useEffect(() => {
    if (dispatch && (task?.type === TaskType.DOWNLOAD_FILES || task?.type === TaskType.DOWNLOAD_FILES_OPERATION)) {
      setIsLoading(true);
      dispatch(validateFiles(task.reference.folder))
        .then(setFolder)
        .finally(() => setIsLoading(false));
    }
  }, [dispatch, task]);

  if (task?.type !== TaskType.DOWNLOAD_FILES && task?.type !== TaskType.DOWNLOAD_FILES_OPERATION) {
    return null;
  }

  const onDownloadComplete = async () => {
    if (folder) {
      await dispatch(updateTaskCompletion(task?.legalComponentId, task.id, true));
    }
  };

  const handleHelpClick = (): void => {
    openInNewTab('https://support.mynotary.fr/fr/articles/8683285-vous-n-arrivez-pas-a-telecharger-un-dossier-partage');
  };

  return (
    <div className='download-files-task-template'>
      <div className='dftt-description'>
        <span className='dftt-highlight'>
          {task.creatorUser.firstname} {task.creatorUser.lastname}
        </span>{' '}
        vous demande de télécharger les documents de ce dossier
        <MnButtonController
          className='dftt-help'
          iconVariant='primary'
          label={`Vous n'arrivez pas à télécharger un dossier partagé ?`}
          leftIcon={'/assets/images/pictos/icon/question-light.svg'}
          onClick={handleHelpClick}
          underlined={true}
        />
      </div>
      {folder && <MnFolderDisplay folder={folder} isDisabled={isLoading} onDownloadComplete={onDownloadComplete} />}
    </div>
  );
};

export { DownloadFilesTaskTemplate };
