import './registeredLetterTileHeader.scss';
import { formatDate } from '@mynotary/frontend/shared/util';
import React, { ReactElement } from 'react';
import { useSelector } from 'react-redux';
import { MnActionPopover } from '@mynotary/frontend/shared/ui';
import { selectContractPermission } from '@mynotary/frontend/legals/store';
import { RegisteredLetter } from '@mynotary/frontend/registered-letters/core';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { selectRegisteredLetterContract } from '@mynotary/frontend/registered-letters/store';
import { RegisteredLetterStatus } from '@mynotary/crossplatform/registered-letters/api';

interface RegisteredLetterTileHeaderProps {
  letter: RegisteredLetter;
  onError: () => void;
  onModify: () => void;
  onReceiverUpdate: () => void;
}

const RegisteredLetterTileHeader = ({
  letter,
  onError,
  onModify,
  onReceiverUpdate
}: RegisteredLetterTileHeaderProps): ReactElement => {
  const contract = useSelector(selectRegisteredLetterContract(letter?.batch.id));
  const canCreate = useSelector(
    selectContractPermission(PermissionType.CREATE_CONTRACT_REGISTERED_LETTER, contract?.id)
  );

  const actions = [
    {
      checkCondition: () => !!canCreate,
      icon: '/assets/images/pictos/icon/reload-light.svg',
      id: 'modify',
      label: 'Modifier et renvoyer',
      onClick: onModify
    }
  ];
  const { icon, label } = getStatusInfos(letter.status);

  return (
    <div className='registered-letter-tile-header'>
      <div className='rlth-info'>
        <div className='rlth-label'>
          {`${letter.receiver.firstname} ${letter.receiver.lastname}`.toLowerCase()}
          <MnActionPopover actions={actions} />
        </div>
        {!letter.negligenceTime && !letter.refusalTime && !letter.acceptationTime && (
          <div className='rlth-time'>
            <span>Reçu le : {letter.depositTime ? formatDate(letter.depositTime) : '-'}</span>
          </div>
        )}
        {letter.depositTime && !letter.negligenceTime && !letter.refusalTime && (
          <div className='rlth-time'>
            <span>Accepté le : {letter.acceptationTime ? formatDate(letter.acceptationTime) : '-'}</span>
          </div>
        )}
        {letter.negligenceTime && (
          <div className='rlth-time'>
            <span>Négligé le : {formatDate(letter.negligenceTime)}</span>
          </div>
        )}
        {letter.refusalTime && (
          <div className='rlth-time'>
            <span>Refusé le : {formatDate(letter.refusalTime)}</span>
          </div>
        )}
        <div className='rlth-status'>
          <div className='rlth-status-icon'>{icon && <img src={icon} />}</div>
          <div className='rlth-status-label'>{label}</div>
        </div>
        {letter.status === 'BOUNCED' && (
          <div className='rlth-action error' onClick={onModify}>
            Modifier et relancer
          </div>
        )}
        {letter.status === 'AWAITING_RECIPIENT_UPDATE' && (
          <div className='rlth-action error' onClick={onReceiverUpdate}>
            En attente de validation
          </div>
        )}
        {letter.status === 'ERROR' && (
          <div className='rlth-action error' onClick={onError}>
            Contacter le support
          </div>
        )}
      </div>
    </div>
  );
};

export { RegisteredLetterTileHeader };

const getStatusInfos = (status: RegisteredLetterStatus): { icon: string; label: string } => {
  switch (status) {
    case RegisteredLetterStatus.TO_SEND:
    case RegisteredLetterStatus.SUBMITTED:
      return { icon: '/assets/images/pictos/illustrated/pending.svg', label: "En cours d'envoi" };
    case RegisteredLetterStatus.SENT:
      return { icon: '/assets/images/pictos/illustrated/pending.svg', label: 'Recommandé reçu' };
    case RegisteredLetterStatus.ACCEPTED:
      return { icon: '/assets/images/pictos/illustrated/ok.svg', label: 'Recommandé accepté' };
    case RegisteredLetterStatus.EXPIRED:
      return { icon: '/assets/images/pictos/illustrated/warning.svg', label: 'Recommandé expiré' };
    case RegisteredLetterStatus.REFUSED:
      return { icon: '/assets/images/pictos/illustrated/warning.svg', label: 'Recommandé refusé' };
    case RegisteredLetterStatus.BOUNCED:
      return { icon: '/assets/images/pictos/illustrated/error.svg', label: "Erreur d'e-mail" };
    case RegisteredLetterStatus.INVALID_EMAIL:
      return { icon: '/assets/images/pictos/illustrated/error.svg', label: "Erreur d'e-mail" };
    case RegisteredLetterStatus.ERROR:
      return { icon: '/assets/images/pictos/illustrated/error.svg', label: "Erreur d'envoi" };
    case RegisteredLetterStatus.AWAITING_RECIPIENT_UPDATE:
      return { icon: '/assets/images/pictos/illustrated/error.svg', label: "Erreur d'identification" };
  }
};
