import './customization-contract-display-table.scss';
import { Dictionary } from '@mynotary/crossplatform/shared/util';
import React, { ReactElement, ReactNode, useEffect, useState } from 'react';
import { MnButtonIcon, MnCheckbox, MnPopin, MnTooltip } from '@mynotary/frontend/shared/ui';
import { MnSimpleTable, Row } from 'components/_dataDisplay/simpleTable/simpleTable';
import { MnDropdownList } from 'components/_inputs/dropdownList/dropdownList';
import { every, filter, forEach, isEmpty, join, keyBy, map, orderBy, size, some, toArray } from 'lodash';
import { classNames, MnProps } from '@mynotary/frontend/shared/util';
import {
  CustomizationContractPreview,
  DisplayActionChoice,
  ContractsThemesActions
} from '@mynotary/frontend/themes/feature';
import { OperationContractModel } from '@mynotary/crossplatform/legal-templates/core';
import { DisplayTableEntity } from '@mynotary/crossplatform/themes/api';
import { DisplayTable } from '@mynotary/frontend/themes/core';
import { TemplateContracts } from '@mynotary/crossplatform/legal-contract-templates/core';

interface ParametersCustomizationContractDisplayTableProps extends MnProps {
  color: string;
  files: Dictionary<string | undefined>;
  onChange: (table: DisplayTable) => void;
  table: DisplayTable;
  templatesByType: Dictionary<TemplateContracts>;
}

type TableColumn = {
  content: ReactNode;
  hidden?: boolean;
  id: string;
  mainId?: keyof DisplayTableEntity;
  style?: string;
};

export type TableRow = {
  id: string;
  label: string;
  type: string;
};

/* eslint-disable sort-keys-fix/sort-keys-fix */
const columns: Record<
  keyof DisplayActionChoice | 'HEADER' | 'FLY_LEAF_SUB_COLULMN' | 'FLY_LEAF_ORGA_INFO' | 'FLY_LEAF_IMAGE' | 'ACTIONS',
  TableColumn
> = {
  HEADER: {
    id: 'HEADER',
    content: 'Nom du contrat',
    style: 'large title'
  },
  COVER: {
    id: 'COVER',
    content: 'Page de couverture',
    style: 'large centered'
  },
  FLY_LEAF: {
    id: 'FLY_LEAF',
    content: 'Page de garde',
    style: 'large centered'
  },
  FLY_LEAF_SUB_COLULMN: {
    id: 'FLY_LEAF_SUB_COLULMN',
    style: 'large centered sub-column',
    content: (
      <>
        <div className='mn-table-column large centered grayed'>Contenu de la page de garde</div>
        <div className='ccdt-sub-columns'>
          <div className='mn-table-column large centered sub-column'>Infos de l'organisation</div>
          <div className='mn-table-column large centered sub-column'>Image</div>
        </div>
      </>
    )
  },
  FLY_LEAF_ORGA_INFO: {
    id: 'FLY_LEAF_ORGA_INFO',
    mainId: 'FLY_LEAF',
    content: 'Informations',
    hidden: true
  },
  FLY_LEAF_IMAGE: {
    id: 'FLY_LEAF_IMAGE',
    mainId: 'FLY_LEAF',
    content: 'Image',
    hidden: true
  },
  BACK_COVER: {
    id: 'BACK_COVER',
    content: 'Quatrième de couverture',
    style: 'large centered'
  },
  ACTIONS: {
    id: 'ACTIONS',
    content: '',
    style: 'large left'
  }
};
/* eslint-disable sort-keys-fix/sort-keys-fix */

type PreviewPopin = {
  displayTable: DisplayTableEntity;
  label: string;
};

const CustomizationContractDisplayTable = ({
  className,
  color,
  files,
  onChange,
  table,
  templatesByType
}: ParametersCustomizationContractDisplayTableProps): ReactElement => {
  const [currentOperationType, setCurrentOperationType] = useState<string>('');
  const [previewPopin, setPreviewPopin] = useState<PreviewPopin>();

  useEffect(() => {
    if (!isEmpty(templatesByType)) {
      const firstTemplate = toArray(templatesByType)[0];
      setCurrentOperationType(`${firstTemplate.type}_${join(firstTemplate.specificTypes, '_')}`);
    }
  }, [templatesByType]);

  const props = {
    currentOperationType: currentOperationType,
    onChange: onChange,
    table: table,
    templatesByType: templatesByType
  };

  const componentRendererList = (row: TableRow) => ({
    BACK_COVER: <CheckBoxCellRenderer columnId='BACK_COVER' row={row} {...props} />,
    COVER: <CheckBoxCellRenderer columnId='COVER' row={row} {...props} />,
    FLY_LEAF: <CheckBoxCellRenderer columnId='FLY_LEAF' row={row} {...props} />,
    FLY_LEAF_IMAGE: <CheckBoxCellRenderer columnId='FLY_LEAF_IMAGE' parentColumnId='FLY_LEAF' row={row} {...props} />,
    FLY_LEAF_ORGA_INFO: (
      <CheckBoxCellRenderer columnId='FLY_LEAF_ORGA_INFO' parentColumnId='FLY_LEAF' row={row} {...props} />
    ),
    FLY_LEAF_SUB_COLULMN: <SubColumnRenderer row={row} {...props} />,
    ACTIONS:
      row.type === 'HEAD' ? (
        <div />
      ) : (
        <ContractsThemesActions {...props} operationTemplatesByTypes={templatesByType} row={row} />
      ),
    HEADER: (
      <HeaderCellRenderer
        onClick={() =>
          setPreviewPopin({
            displayTable: {
              ...table[currentOperationType]?.[row.id],
              color: table[currentOperationType]?.[row.id].color ?? color,
              cover: table[currentOperationType]?.[row.id].cover ?? files['cover'],
              flyLeafImage: table[currentOperationType]?.[row.id].flyLeafImage ?? files['fly_leaf_image'],
              logo: table[currentOperationType]?.[row.id].logo ?? files['logo'],
              logoFooter: table[currentOperationType]?.[row.id].logoFooter ?? files['logo_footer']
            },
            label: row.label
          })
        }
        row={row}
      />
    )
  });

  const withSelectAll = size(templatesByType[currentOperationType]?.contracts) > 1;

  const contracts = map(templatesByType[currentOperationType]?.contracts, (contract) => {
    return {
      ...componentRendererList({
        id: contract.id,
        label: contract.label,
        type: 'ROW'
      })
    };
  });

  const rows = filter(
    [
      withSelectAll
        ? {
            ...componentRendererList({
              id: currentOperationType,
              label: templatesByType[currentOperationType]?.label,
              type: 'HEAD'
            })
          }
        : undefined,
      ...contracts
    ],
    (row) => !!row
  ) as Row[];

  return (
    <div className={classNames('customization-contract-display-table', className)}>
      <h4 className='ccdt-title'>Affichage des pages par type de dossier</h4>
      <MnDropdownList
        className='ccdt-dropdown'
        headerLabel={templatesByType[currentOperationType]?.label}
        items={orderBy(
          map(templatesByType, (template, type) => {
            return {
              id: type,
              label: template.label,
              onClick: () => setCurrentOperationType(type)
            };
          }),
          (item) => item.label
        )}
      />
      <MnSimpleTable className='ccdt-table' headerColumns={filter(columns, (column) => !column.hidden)} rows={rows} />
      <MnPopin className='ccdt-popin' onClose={() => setPreviewPopin(undefined)} opened={!!previewPopin}>
        {previewPopin && (
          <CustomizationContractPreview
            className='ccdt-preview-popin'
            displayTable={previewPopin.displayTable}
            label={previewPopin.label}
          />
        )}
      </MnPopin>
    </div>
  );
};

export { CustomizationContractDisplayTable };

interface CheckBoxCellRendererProps {
  columnId: keyof DisplayTableEntity;
  currentOperationType: string;
  onChange: (table: DisplayTable) => void;
  parentColumnId?: keyof DisplayTableEntity;
  row: TableRow;
  table: DisplayTable;
  templatesByType: Dictionary<TemplateContracts>;
}

const CheckBoxCellRenderer = ({
  columnId,
  currentOperationType,
  onChange,
  parentColumnId,
  row,
  table,
  templatesByType
}: CheckBoxCellRendererProps) => {
  const handleValueChange = (templateType: string, contractId: string, key: string, value: boolean): void => {
    onChange(getTableUpdate(table, templateType, contractId, key, value));
  };

  const isParentChecked = parentColumnId != null ? !!table[currentOperationType]?.[row.id]?.[parentColumnId] : true;
  const isChecked = isParentChecked && !!table[currentOperationType]?.[row.id]?.[columnId];

  return row.type === 'HEAD' ? (
    <MainCheckBoxCellRenderer
      columnId={columnId}
      currentOperationType={currentOperationType}
      onChange={onChange}
      parentColumnId={parentColumnId}
      table={table}
      templatesByType={templatesByType}
    />
  ) : (
    <MnCheckbox
      className='ccdt-tr-value'
      disabled={!!parentColumnId && !table[currentOperationType]?.[row.id]?.[parentColumnId]}
      onChange={(value) => handleValueChange(currentOperationType, row.id, columnId, value)}
      position='right'
      value={isChecked}
    />
  );
};

interface MainCheckBoxCellRendererProps {
  columnId: keyof DisplayTableEntity;
  currentOperationType: string;
  onChange: (table: DisplayTable) => void;
  parentColumnId?: keyof DisplayTableEntity;
  table: DisplayTable;
  templatesByType: Dictionary<TemplateContracts>;
}

const MainCheckBoxCellRenderer = ({
  columnId,
  currentOperationType,
  onChange,
  parentColumnId,
  table,
  templatesByType
}: MainCheckBoxCellRendererProps) => {
  const handleAllValueChange = (
    contracts: Dictionary<OperationContractModel>,
    templateType: string,
    key: string,
    value: boolean
  ): void => {
    let newTable = { ...table };
    forEach(contracts, (_, id) => {
      newTable = getTableUpdate(newTable, templateType, id, key, value);
    });
    onChange(newTable);
  };

  const operationContractModel = templatesByType[currentOperationType]?.contracts;

  const contracts = parentColumnId
    ? keyBy(
        filter(operationContractModel, (contract) => {
          return !!table[currentOperationType]?.[contract.id]?.[parentColumnId];
        }),
        'id'
      )
    : operationContractModel;

  return (
    <MnCheckbox
      className='ccdt-tr-value'
      disabled={
        !!parentColumnId &&
        !some(operationContractModel, (_, contractId) => {
          return !!table[currentOperationType]?.[contractId]?.[parentColumnId];
        })
      }
      onChange={(value) => handleAllValueChange(contracts, currentOperationType, columnId, value)}
      position='right'
      value={
        !isEmpty(contracts) &&
        every(contracts, (_, contractId) => {
          return !!table[currentOperationType]?.[contractId]?.[columnId];
        })
      }
    />
  );
};

export const getTableUpdate = (
  table: DisplayTable,
  templateType: string,
  contractId: string,
  key: string,
  value: boolean
): DisplayTable => {
  return {
    ...table,
    [templateType]: {
      ...table?.[templateType],
      [contractId]: {
        ...table?.[templateType]?.[contractId],
        [key]: value
      }
    }
  };
};

interface SubColumnRendererProps {
  currentOperationType: string;
  onChange: (table: DisplayTable) => void;
  row: TableRow;
  table: DisplayTable;
  templatesByType: Dictionary<TemplateContracts>;
}

const SubColumnRenderer = ({
  currentOperationType,
  onChange,
  row,
  table,
  templatesByType
}: SubColumnRendererProps) => {
  return (
    <div className='ccdt-tr-sub-column'>
      <CheckBoxCellRenderer
        columnId='FLY_LEAF_ORGA_INFO'
        currentOperationType={currentOperationType}
        onChange={onChange}
        parentColumnId='FLY_LEAF'
        row={row}
        table={table}
        templatesByType={templatesByType}
      />
      <CheckBoxCellRenderer
        columnId='FLY_LEAF_IMAGE'
        currentOperationType={currentOperationType}
        onChange={onChange}
        parentColumnId='FLY_LEAF'
        row={row}
        table={table}
        templatesByType={templatesByType}
      />
    </div>
  );
};

interface HeaderCellRendererProps {
  onClick: () => void;
  row: TableRow;
}

const HeaderCellRenderer = ({ onClick, row }: HeaderCellRendererProps) => {
  return row.type === 'HEAD' ? (
    <div />
  ) : (
    <div className='ccdt-tr-head'>
      {row.label}
      <MnTooltip content='Prévisualiser le contrat'>
        <MnButtonIcon
          className='ccdt-tr-head-icon'
          onClick={onClick}
          path='/assets/images/pictos/icon/eye-light.svg'
          size='medium'
          variant='gray700-primary'
        />
      </MnTooltip>
    </div>
  );
};
