{"openapi": "3.0.0", "info": {"version": "1.0.0", "title": "API mynotary", "description": "Toutes les requêtes doivent être authentifiées en passant une clé dans le header x-api-key"}, "servers": [{"url": "https://api-preprod.mynotary.fr/api/v1", "description": "Staging"}, {"url": "https://api.mynotary.fr/api/v1", "description": "Production"}], "paths": {"/clients": {"post": {"tags": ["Clients"], "summary": "Ajoute un client au flux", "description": "Le token de l'agence doit être fournie par le client. Ce token est disponible dans les paramètres de l'application dans la rubrique **Interconnexion > Activer les interconnexions**", "requestBody": {"required": true, "content": {"application/json": {"example": {"apiKey": "TOKEN DE L'AGENCE R<PERSON>CUPÉRÉ DEPUIS LA PAGE PARAMÈTRE"}, "schema": {"type": "object", "properties": {"apiKey": {"type": "string", "example": "TOKEN DE L'AGENCE R<PERSON>CUPÉRÉ DEPUIS LA PAGE PARAMÈTRE"}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}}}}}}, "delete": {"tags": ["Clients"], "summary": "Supprime un client du flux", "description": "La clé d'api doit être fournie par le client. Elle est disponible dans les paramètres de l'application dans la rubrique **Interconnexion > Activer les interconnexions**", "requestBody": {"required": true, "content": {"application/json": {"example": {"apiKey": "2629a8e1-7da2-49ee-8218-40abf610dbd3"}, "schema": {"type": "object", "properties": {"apiKey": {"type": "string", "example": "2629a8e1-7da2-49ee-8218-40abf610dbd3"}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}}}}}}}, "/organizations": {"get": {"tags": ["Organisations"], "summary": "Récupère les organisations interconnectées", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Organization"}}}}}}}}, "/organizations/operations": {"get": {"tags": ["Organisations"], "summary": "Récupère la liste des dossiers et contrats autorisés pour une organisation", "parameters": [{"in": "query", "name": "organizationId", "required": true, "example": "6743", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "example": [{"id": "OPERATION__AUTRE__LIBRE", "label": "Opération libre", "contracts": [{"modelId": "AUTRE_LIBRE_VIDE", "label": "Contrat libre"}]}, {"id": "OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE", "label": "CCMI avec fourniture de plan", "contracts": [{"modelId": "IMMOBILIER_DOCUMENT_INFORMATIONS_PRECONTRACTUELLES", "label": "DIP"}, {"modelId": "IMMOBILIER_CCMI", "label": "CCMI"}]}]}}}}}}}, "/organizations/{organizationId}/users/{userId}": {"delete": {"tags": ["Utilisateurs"], "summary": "Retire un utilisateur d'une organisation", "description": "Un utilisateur sans organisation n'est pas facturé, il peut accéder a ses anciens dossiers mais ne peut plus en créer de nouveau.", "parameters": [{"in": "path", "name": "organizationId", "required": true, "example": "6743", "schema": {"type": "number"}}, {"in": "path", "name": "userId", "required": true, "example": "1234", "schema": {"type": "number"}}], "responses": {"200": {"description": "Utilisateur supprimé avec succès", "content": {"application/json": {}}}}}}, "/roles": {"get": {"tags": ["Roles"], "summary": "Récupère les roles d'une organisation", "parameters": [{"in": "query", "name": "organizationId", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number", "description": "id du role", "example": 28524}, "name": {"type": "string", "description": "Nom du role", "example": "Collaborateur"}, "isDefault": {"type": "boolean", "description": "Le role est il un rôle créee par défaut ( Ne peux pas etre modifié si oui )", "example": false}}}, "example": [{"id": 28523, "name": "Responsable", "isDefault": true}, {"id": 28524, "name": "Collaborateur", "isDefault": true}]}}}}}}}, "/users": {"get": {"tags": ["Utilisateurs"], "summary": "Récupère les informations liées à un ou plusieurs utilisateurs", "parameters": [{"in": "query", "name": "organizationId", "required": true, "example": 1075, "schema": {"type": "number"}}, {"in": "query", "name": "id", "schema": {"type": "number"}}, {"in": "query", "name": "email", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}}}, "post": {"tags": ["Utilisateurs"], "summary": "Creation d'un nouvel utilisateur dans une organisation", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["organizationId", "firstname", "lastname", "email"], "properties": {"organizationId": {"type": "number", "description": "id de l'organisation", "example": 1076}, "firstname": {"type": "string", "description": "prénom de l'utilisateur", "example": "jean"}, "lastname": {"type": "string", "description": "Nom de famille de l'utilisateur", "example": "Du<PERSON>nd"}, "civility": {"type": "string", "enum": ["homme", "femme"], "description": "Civilité de l'utilisateur", "example": "homme"}, "phone": {"type": "string", "description": "Numéro de téléphone de l'utilisateur", "example": "+336XXXXXXXX"}, "email": {"type": "string", "description": "L'adresse email de l'utilisateur", "example": "<EMAIL>"}, "roleId": {"type": "number", "description": "L'identifiant du rôle attribué à l'utilisateur dans l'organisation (collaborateur si absent)", "example": "1"}, "numeroRsac": {"type": "string", "description": "Numéro RSAC de l'agent immobilier", "example": "123456"}, "intermediaireStatus": {"type": "string", "enum": ["representant", "agent_co_salarie", "agent_co_independannt", "agent_co_portage", "agent_co_structure"], "description": "Statut de l'intermédiaire", "example": "agent_co_salarie"}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}}}, "/users/{id}": {"put": {"tags": ["Utilisateurs"], "summary": "Modifie les informations d'un utilisateur", "parameters": [{"in": "path", "name": "id", "required": true, "example": 1075, "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"firstname": {"type": "string", "example": "<PERSON>"}, "lastname": {"type": "string", "example": "<PERSON><PERSON>"}, "civility": {"type": "object", "enum": ["<PERSON>", "Madame"], "example": "<PERSON>"}, "phone": {"type": "string", "example": "+33658461588"}, "email": {"type": "string", "example": "<EMAIL>"}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {}}}}}}, "/records/types": {"get": {"tags": ["Fiches"], "summary": "Liste les types de fiches", "description": "Les fiches représentent les formulaires des biens et des personnes qui sont utiles dans un dossier ou contrat. Pour lister les types de fiches autorisées dans un dossier, utilisez la route **/operation/description**", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "example": ["RECORD__BIEN__MONOPROPRIETE_HORS_HABITATION", "RECORD__BIEN__LOT_HORS_HABITATION", "RECORD__BIEN__TERRAIN_NON_CONSTRUCTIBLE", "RECORD__BIEN__MONOPROPRIETE_HABITATION", "RECORD__BIEN__INDIVIDUEL_HORS_HABITATION", "RECORD__BIEN__TERRAIN_CONSTRUCTIBLE", "RECORD__BIEN__INDIVIDUEL_HABITATION", "RECORD__BIEN__LOT_HABITATION", "RECORD__PERSONNE__MORALE__BAILLEUR_SOCIAL", "RECORD__PERSONNE__PHYSIQUE__INTERMEDIAIRE_IMMOBILIER", "RECORD__PERSONNE__PHYSIQUE__AGENT_IMMOBILIER", "RECORD__PERSONNE__MORALE__AGENT_IMMOBILIER", "RECORD__PERSONNE__MORALE__NOTAIRE", "RECORD__PERSONNE__MORALE__CONSTRUCTEUR", "RECORD__PERSONNE__PHYSIQUE", "RECORD__PERSONNE__MORALE", "RECORD__PERSONNE__MORALE__PROMOTEUR"]}}}}}}}, "/records/description": {"get": {"tags": ["Fiches"], "summary": "Liste les questions et documents pour un type de fiche", "parameters": [{"in": "query", "name": "type", "description": "Type de fiche", "required": true, "example": "RECORD__PERSONNE__PHYSIQUE"}], "description": "Les ids des questions et documents doivent être utilisés pour enregistrer des informations sur la fiche", "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RecordDescription"}}}}}}}, "/records": {"post": {"tags": ["Fiches"], "summary": "Permet de créer une fiche", "description": "Permet de créer une fiche. Les ids des questions et documents sont disponibles en utilisant la route **/records/decription**.", "requestBody": {"required": true, "content": {"application/json": {"example": {"creatorId": 51405, "organizationid": 435, "type": "RECORD__PERSONNE__PHYSIQUE", "questions": {"nom": "DUPOND", "prenoms": "<PERSON>", "sexe": "homme", "telephone": "+33663051282", "informations_personnelles_date_naissance": 570051882000, "informations_personnelles_pays_naissance": "FR", "adresse": {"numero": "1", "rue": "rue du paradis", "codePostal": "75001", "ville": "Paris", "pays": "Paris"}}, "documents": {"carte_identite": [{"name": "carte-identite-recto.pdf", "url": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"}, {"name": "carte-identite-verso.pdf", "url": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"}]}}, "schema": {"type": "object", "required": ["creatorId", "organizationId", "type", "questions", "documents"], "properties": {"creatorId": {"type": "number", "example": 56789}, "organizationId": {"type": "number", "description": "Si non renseigné, l'organisation par defaut de l'utilisateur est utilisée", "example": 2345}, "type": {"type": "string", "example": "RECORD__PERSONNE__PHYSIQUE"}, "questions": {"type": "object"}, "documents": {"type": "object"}}}}}}, "responses": {"200": {"description": "<PERSON><PERSON><PERSON>", "content": {"application/json": {"schema": {"allOf": [{"type": "object", "properties": {"id": {"type": "number"}}}, {"$ref": "#/components/schemas/DirectLink"}]}}}}}}, "get": {"tags": ["Fiches"], "summary": "Permet d'obtenir les fiches d'une organisation", "description": "Permet de d'obtenir les fiches d'une organisation", "parameters": [{"in": "query", "name": "organizationId", "required": true, "example": 1075, "schema": {"type": "number"}}, {"in": "query", "name": "id", "example": 13256, "schema": {"type": "number"}}, {"in": "query", "name": "timestamp", "required": false, "description": "Sélectionne les fiches modifiées après ce timestamp (13 digit timestamp)", "example": 1732829175908, "schema": {"type": "number"}}, {"in": "query", "name": "type", "required": false, "description": "Type de fiche", "example": "RECORD__PERSONNE__MORALE", "schema": {"type": "string"}}, {"in": "query", "name": "operationId", "required": false, "description": "Filtre les fiches appartenant à un dossier en particulier", "example": "1", "schema": {"type": "string"}}, {"in": "query", "name": "offset", "description": "Offset sur les items", "required": false, "example": 10, "schema": {"type": "number"}}, {"in": "query", "name": "limit", "description": "Nombre d'items à afficher (tous si absent)", "required": false, "example": 100, "schema": {"type": "number"}}], "responses": {"200": {"description": "Liste des fiches", "content": {"application/json": {"schema": {"type": "object", "properties": {"totalItems": {"type": "number", "description": "nombre total d'item concerné", "example": "1223"}, "data": {"type": "array", "description": "liste des fiches disponibles", "items": {"type": "object", "properties": {"id": {"type": "number"}, "organizationId": {"type": "number"}, "type": {"type": "string"}, "questions": {"type": "object"}, "documents": {"type": "object"}, "status": {"type": "string", "description": "Statut du lot. Présent uniquement si le type est RECORD__BIEN__LOT_HABITATION ou RECORD__BIEN__LOT_HORS_HABITATION. Peut être 'RESERVED' ou 'AVAILABLE'."}, "lastUpdate": {"type": "number"}}}}}}, "example": {"totalItems": 2, "data": [{"id": 13912, "organizationId": 1, "type": "RECORD__PERSONNE__MORALE", "questions": {"personne_morale_forme_sociale": "societe_sarl", "personne_morale_denomination": "GFDG", "siren": 122223456, "personne_morale_capital": 12, "personne_morale_ville_rcs": "222AAA", "personne_morale_adresse": {"rue": "23 Rue de Sévigné, Paris, France", "numero": "23", "ville": "Paris", "codePostal": "75003"}}, "documents": {}, "lastUpdate": 1632829174807}, {"id": 18526, "organizationId": 1, "type": "RECORD__BIEN__LOT_HABITATION", "questions": {"address": {"rue": "23 Rue de Sévigné, Paris, France", "numero": "23", "ville": "Paris", "codePostal": "75003"}}, "documents": {}, "status": "RESERVED", "lastUpdate": 1632829174807}]}}}}}}}, "/records/{id}": {"put": {"tags": ["Fiches"], "summary": "Permet de modifier les questions et documents d'une fiche", "description": "Permet de modifier les questions et documents d'une fiche. En cas d'ajout de fichier sur un document, les fichiers précédemment importés seront conservés. Les ids des questions et documents sont disponibles en utilisant la route **/records/decription**.", "parameters": [{"in": "path", "name": "id", "example": "56", "required": true, "description": "Id de la fiche", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"example": {"questions": {"prenom": "<PERSON>", "sexe": "homme", "adresse": {"numero": "2", "rue": "rue du paradis", "codePostal": "75001", "ville": "Paris", "pays": "Paris"}}, "documents": {"carte_identite": [{"name": "carte identite 2", "url": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"}, {"name": "carte identite 3", "url": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"}]}}, "schema": {"type": "object", "properties": {"type": {"type": "string", "example": "PERSONNE_PHYSIQUE"}, "data": {"type": "object"}}}}}}, "responses": {"204": {"description": "The resource was updated successfully."}}}, "delete": {"tags": ["Fiches"], "summary": "Permet d'archiver une fiche", "description": "Permet d'archiver une fiche", "parameters": [{"in": "path", "name": "id", "example": "56", "required": true, "description": "Id de la fiche", "schema": {"type": "number"}}], "responses": {"204": {"description": "Archivage r<PERSON>i"}}}}, "/records/{id}/files": {"delete": {"tags": ["Fiches"], "summary": "Permet de supprimer des fichiers d'une fiche", "description": "Permet d'archiver une fiche", "parameters": [{"in": "path", "name": "id", "example": "56", "required": true, "description": "Id de la fiche", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"example": [{"id": "carte_identite", "files": ["7b562033-e261-45a1-8551-cfd3b5586a2e"]}]}}}, "responses": {"204": {"description": "Fichiers supprimés"}}}}, "/operations/types": {"get": {"tags": ["Dossier"], "summary": "Liste les types de dossier", "description": "Les dossiers regroupent des fiches et des contrats. Pour lister les types de fiches autorisées et les contrats disponibles, utilisez la route **/operation/description**", "parameters": [{"in": "query", "name": "organizationId", "required": true, "example": 1075, "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "example": ["OPERATION__AUTRE__LIBRE", "OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE", "OPERATION__IMMOBILIER__LOCATION", "OPERATION__IMMOBILIER__LOCATION_COMMERCIAL", "OPERATION__IMMOBILIER__VENTE_ANCIEN"]}}}}}}}, "/operations/description": {"get": {"tags": ["Dossier"], "summary": "Liste les informations nécessaires pour créer ou modifier un dossier", "description": "Liste les questions et documents communs a tout le dossier, les types de fiches autorisées et les types de contrats disponibles.", "parameters": [{"in": "query", "name": "type", "description": "type de dossier", "required": true, "example": "OPERATION__IMMOBILIER__VENTE_ANCIEN", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationDescription"}}}}}}}, "/operations/invitation": {"post": {"tags": ["Dossier"], "summary": "Permet d'inviter une personne dans un dossier", "description": "Permet d'inviter une personne dans un dossier. ", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["creatorId", "label", "type"], "properties": {"operationId": {"type": "number", "description": "id du dossier dans lequel on veut rajouter une personne"}, "email": {"type": "string", "description": "Email de l'utilisateur à ajouter"}, "role": {"type": "string", "description": "Nom du role de l'utilisateur à ajouter retrouvable dans la routes /roles"}}}, "example": {"operationId": 88769, "email": "<EMAIL>", "role": "Collaborateur"}}}}, "responses": {"200": {"description": "Résultat de l'invitation", "content": {"application/json": {"schema": {"type": "object", "properties": {"creatorId": {"type": "number", "description": "Id de la personne qui a invité à rejoindre le dossier ( créateur du dossier) "}, "roleId": {"type": "number", "description": "Id du role de la personne invitée"}, "operationId": {"type": "number", "description": "Id du dossier dans lequel la personne a était invitée"}}, "example": {"creatorId": 51744, "roleId": 28524, "operationId": 88769}}}}}}}}, "/operations": {"get": {"tags": ["Dossier"], "summary": "Récupère la liste des dossiers d'une organisation", "parameters": [{"required": true, "in": "query", "name": "organizationId", "description": "L'id d'une organisation", "schema": {"type": "number"}, "example": "1075"}, {"required": false, "in": "query", "name": "type", "description": "Filtre tout les dossiers par type de dossier cf. /operations/types", "schema": {"type": "string"}, "example": "OPERATION__IMMOBILIER__VENTE_ANCIEN"}, {"required": false, "in": "query", "name": "creatorId", "description": "Filtre tout les dossiers par l'identifiant du créateur", "schema": {"type": "number"}, "example": 1}, {"required": false, "in": "query", "name": "label", "description": "Filtre tout les dossiers par nom du dossier", "schema": {"type": "string"}, "example": "dossier de vente"}], "responses": {"200": {"description": "Liste des dossiers", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "number", "description": "id du dossier", "example": "789321"}, "label": {"type": "string", "description": "nom du dossier"}, "type": {"type": "string", "description": "Type d'operation cf. operation/info pour les types disponibles"}, "contracts": {"type": "array", "description": "Liste des contrats liés au dossier", "items": {"type": "object", "properties": {"id": {"type": "number", "description": "id du contrat", "example": "4567"}, "label": {"type": "string", "description": "nom du contract", "example": "<PERSON><PERSON><PERSON> de vente"}}}}, "records": {"type": "array", "description": "Dictionaire des fiches liées au dossier", "items": {"type": "object"}, "example": {"VENDEURS": [32543, 34002], "AGENCES": [33357]}}, "creationTime": {"type": "number", "description": "Date de création du dossier", "example": 1673970906550}}}}}}}}}, "post": {"tags": ["Dossier"], "summary": "Permet de créer un dossier", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["creatorId", "organizationId", "type"], "properties": {"creatorId": {"type": "number", "description": "id de l'utilisateur MyNotary qui crée le dossier"}, "organizationId": {"type": "number", "description": "Si non renseigné, l'organisation par defaut de l'utilisateur est utilisée", "example": 2345}, "label": {"type": "string", "description": "nom du dossier à créer. Si non renseigné, la nommenclature par defaut est utilisée"}, "type": {"type": "string", "description": "type du dossier. Obtenu par. **/operations/types** "}, "questions": {"type": "object", "description": "questions nécessaire au dossier"}, "documents": {"type": "object", "description": "documents nécessaire au dossier"}}}, "example": {"creatorId": 51405, "organizationId": 2345, "label": "Dossier 18 rue du jardin", "type": "OPERATION__IMMOBILIER__VENTE_ANCIEN", "questions": {"date_butoir": 570051882000, "estimation_haute": 90, "compromis_recommande_electronique": "oui", "tracfin_origine": "tracfin_faible"}, "documents": {"mention_manuscrite_substitution": [{"name": "attestation-banque.pdf", "url": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"}, {"name": "attestation-banque-page-2.pdf", "url": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"}]}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"allOf": [{"type": "object", "properties": {"id": {"type": "number", "description": "id du dossier"}, "organizationId": {"type": "number", "description": "id de l'organisation"}, "label": {"type": "string", "description": "nom du dossier"}}}, {"$ref": "#/components/schemas/DirectLink"}]}}}}}}}, "/operations/{id}/records": {"post": {"tags": ["Dossier"], "summary": "Permet d'associer des fiches a un dossier", "description": "Permet d'associer des fiches à un dossier. Les données contenues dans les fiches sont automatiquement reprises dans les contrats", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "example": {"VENDEUR": [1131], "ACQUEREUR": [2141]}}}}}, "responses": {"204": {"description": "Association réussie"}}}, "delete": {"tags": ["Dossier"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "example": {"VENDEUR": [1131], "ACQUEREUR": [2141]}}}}}, "summary": "Permet de retirer les fiches d'un dossier", "description": "Permet de retirer les fiches d'un dossier. Les fiches ne sont pas supprimées et restent utilisables pour d'autres dossiers", "responses": {"204": {"description": "Retrait réussi"}}}}, "/operations/{id}": {"get": {"tags": ["Dossier"], "summary": "Récupère les details liés à un dossier", "description": "Récupère les informations liés à un dossier (fiches, questions, documents et contrats)", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "number"}}], "responses": {"200": {"description": "Detail dossier", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Operation"}}}}}}, "put": {"tags": ["Dossier"], "summary": "Modifie les details liés à un dossier", "description": "Modifie les details liés à un dossier ( question lié au dossier, nom du dossier )", "parameters": [{"in": "path", "name": "id", "required": true, "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["creatorId", "label", "type"], "properties": {"creatorId": {"type": "number", "description": "id de l'utilisateur MyNotary qui crée le dossier"}, "label": {"type": "string", "description": "nom du dossier à créer"}, "type": {"type": "string", "description": "type du dossier. Obtenu par. **/operations/types** "}, "questions": {"type": "object", "description": "questions nécessaire au dossier"}, "documents": {"type": "object", "description": "documents nécessaire au dossier"}}}, "example": {"label": "Dossier 18 rue du jardin", "type": "OPERATION__IMMOBILIER__VENTE_ANCIEN", "questions": {"date_butoir": 570051882000, "estimation_haute": 90, "compromis_recommande_electronique": "oui", "tracfin_origine": "tracfin_faible"}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"allOf": [{"type": "object", "properties": {"id": {"type": "number", "description": "id du dossier"}, "organizationId": {"type": "number", "description": "id de l'organisation"}, "label": {"type": "string", "description": "nom du dossier"}}}]}}}}}}, "delete": {"tags": ["Dossier"], "summary": "Archive un dossier", "parameters": [{"in": "path", "name": "id", "schema": {"type": "number"}, "required": true}], "responses": {"204": {"description": "Archivage r<PERSON>i"}}}}, "/operations/{id}/files": {"delete": {"tags": ["Dossier"], "summary": "Permet de supprimer des fichiers du dossier", "parameters": [{"in": "path", "name": "id", "schema": {"type": "number"}, "required": true}], "requestBody": {"required": true, "content": {"application/json": {"example": [{"id": "mandat", "files": ["7b562033-e261-45a1-8551-cfd3b5586a2e"]}]}}}, "responses": {"204": {"description": "Fichiers supprimés"}}}}, "/operations/{id}/archive": {"put": {"tags": ["Dossier"], "summary": "Permet d'archiver ou de désarchiver un dossier", "parameters": [{"in": "path", "name": "id", "schema": {"type": "number"}, "required": true}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "required": ["archive"], "properties": {"archive": {"type": "boolean", "description": "boolean à true si on veut archiver le dossier, boolean à false si on veut désarchiver le dossier"}}}, "example": {"archive": "true"}}}}, "responses": {"204": {"description": "Dossier archivé/desarchivé"}}}}, "/contracts": {"post": {"tags": ["Contrats"], "summary": "Permet de créer un contrat", "description": "Crée un nouveau contrat et retourne un lien d'accès avec autoconnexion", "requestBody": {"required": true, "content": {"application/json": {"example": {"operationId": 1, "label": "Mandat de vente Dupond / Laporte", "type": "IMMOBILIER_VENTE_ANCIEN_MANDAT"}, "schema": {"type": "object", "properties": {"operationId": {"type": "string", "required": true}, "label": {"type": "string", "required": true}, "type": {"type": "string", "required": true}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"allOf": [{"type": "object", "properties": {"id": {"type": "number"}}}, {"$ref": "#/components/schemas/DirectLink"}]}}}}}, "security": [{"api_key": []}]}}, "/contracts/{id}": {"delete": {"tags": ["Contrats"], "summary": "Permet de supprimer un contrat", "description": "Permet de supprimer un contrat. Les contrats associés à une signature ne peuvent pas être supprimeés", "parameters": [{"in": "path", "name": "id", "example": "56", "required": true, "description": "Id du contrat", "schema": {"type": "number"}}], "responses": {"204": {"description": "Contrat supprimé"}}}, "get": {"tags": ["Contrats"], "summary": "Permet d'obtenir les information d'un contrat", "description": "Permet d'obtenir les information d'un contrat", "parameters": [{"in": "path", "name": "id", "example": "56", "required": true, "description": "Id du contrat", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Contract"}}}}}}}, "/programs/{id}/sales": {"post": {"tags": ["Programme"], "summary": "Permet de créer une vente à l'interieur d'un programme", "description": "Crée une vente dans un programme et retourne un lien d'accès avec autoconnexion", "requestBody": {"required": true, "content": {"application/json": {"example": {"creatorId": 51744, "label": "vente", "branches": {"BIEN_VENDU": [13912], "RESERVATAIRE": [13912], "COMMERCIALISATEUR": [76079]}}, "schema": {"type": "object", "properties": {"creatorId": {"type": "string", "required": true, "description": "l'id du createur de la vente"}, "label": {"type": "string", "required": true, "description": "le label qui sera affiché pour la vente"}, "branches": {"type": "string", "required": true, "description": "la liste des fiches à associer avec la vente: 'BIEN_VENDU' correspond au bien vendu, 'RESERVATAIRE' est la personne qui achète et 'COMMERCIALISATEUR' la personne qui s'occupe de la vente. Pour la branche 'BIEN_VENDU', le bien doit être disponible et présent dans le dossier du programme (voir Post /operations/{id}/records)."}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"allOf": [{"type": "object", "properties": {"id": {"type": "number", "description": "id du dossier"}, "organizationId": {"type": "number", "description": "id de l'organisation"}, "label": {"type": "string", "description": "nom du dossier"}}}, {"$ref": "#/components/schemas/DirectLink"}]}}}}}, "security": [{"api_key": []}]}}, "/programs/{id}/reservations": {"put": {"tags": ["Programme"], "summary": "Permet de reserver/rendre disponible un lot (record) dans un programme", "description": "Permet de reserver/rendre disponible un lot (record) dans un programme", "parameters": [{"in": "path", "name": "id", "example": "56", "required": true, "description": "Id du programme", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"example": {"userId": "51744", "recordId": "12223", "status": "RESERVED ou AVAILABLE"}, "schema": {"type": "object", "required": ["userId", "recordId", "status"], "properties": {"userId": {"type": "string", "description": "l'id du createur de la reservation"}, "recordId": {"type": "string", "description": "Id du record ( lot )"}, "status": {"type": "string", "description": "status du lot (RESERVED si réservé AVAILABLE si disponible)"}}}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"allOf": [{"type": "object", "properties": {"id": {"type": "number", "description": "id du dossier"}, "organizationId": {"type": "number", "description": "id de l'organisation"}, "label": {"type": "string", "description": "nom du dossier"}}}, {"$ref": "#/components/schemas/DirectLink"}]}}}}, "400": {"description": "Requête invalide dans les cas suivants :\n\n - Tentative de rendre un lot disponible (`AVAILABLE`) alors qu'il est lié à une vente avec une signature finalisée.\n  - Données d'entrée invalides ou manquantes.", "content": {"application/json": {"examples": {"Signature finalisée": {"value": {"details": "Cannot mark the lot as available because a signature has already been completed.", "error_code": "BAD_REQUEST_PARAMETERS"}}, "Requête invalide": {"value": {"details": "recordId is required", "error_code": "MISSING_INFORMATION"}}}}}}}, "security": [{"api_key": []}]}}, "/signatures/{signatureId}": {"parameters": [{"name": "signatureId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "ID de la signature"}], "get": {"summary": "Récupère une signature par ID", "tags": ["Signature"], "responses": {"200": {"description": "Su<PERSON>ès", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Signature"}}}}, "404": {"description": "Signature non trouvée"}}}}, "/login": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Récupère un lien d'auto-connexion", "description": "userId ou email sont requis", "parameters": [{"in": "query", "name": "userId", "example": 33405, "schema": {"type": "number"}}, {"in": "query", "name": "email", "example": "j.dupond@mynotary", "schema": {"type": "string"}}, {"in": "query", "name": "operationId", "example": 14205, "schema": {"type": "number"}}, {"in": "query", "name": "contractId", "example": 9805, "schema": {"type": "number"}}], "responses": {"200": {"description": "lien de connexion à ouvrir dans un nouvel onglet", "content": {"application/json": {"schema": {"type": "object", "properties": {"link": {"type": "string", "example": "https://app.mynotary.fr/connexion?authtoken=621c78d2-4f0e-4159-86d7-180efede7667&email=test%40mynotary.fr&continue=/"}}}}}}}}}}, "components": {"schemas": {"Organization": {"title": "Organization", "type": "object", "required": ["id", "name", "address"], "properties": {"id": {"type": "number", "description": "id de l'organisation", "example": 1075}, "name": {"type": "string", "description": "nom de l'organisation", "example": "Agence du soleil"}, "address": {"type": "string", "description": "Adresse de l'organisation", "example": "1 rue du paradis 75000 Paris"}}}, "User": {"title": "User", "type": "object", "required": ["firstname", "lastname", "email"], "properties": {"id": {"type": "number", "description": "id de l'utilisateur", "example": 79000}, "firstname": {"type": "string", "description": "prénom de l'utilisateur", "example": "jean"}, "lastname": {"type": "string", "description": "Nom de famille de l'utilisateur", "example": "pierre"}, "civility": {"type": "string", "enum": ["homme", "femme"], "description": "Civilité de l'utilisateur", "example": "homme"}, "email": {"type": "string", "description": "L'adresse email de l'utilisateur", "example": "<EMAIL>"}, "numeroRsac": {"type": "string", "description": "Numéro RSAC de l'agent immobilier", "example": "123456"}, "intermediaireStatus": {"type": "string", "enum": ["representant", "agent_co_salarie", "agent_co_independannt", "agent_co_portage", "agent_co_structure"], "description": "Statut de l'intermédiaire", "example": "agent_co_salarie"}}}, "RecordDescription": {"type": "object", "properties": {"type": {"type": "string", "description": "type de la fiche", "example": "RECORD__PERSONNE__PHYSIQUE"}, "questions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Id de la question. Cet id est nécessaire pour ajouter une réponse", "example": "informations_personnelles_date_naissance"}, "type": {"type": "string", "description": "Format de la réponse attendue", "example": "timestamp"}, "description": {"type": "string", "description": "Intitulé de la question", "example": "Date de naissance"}}}, "description": "questions de la fiche"}, "documents": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Id de la question. Cet id est nécessaire pour ajouter une réponse", "example": "carte_identite"}, "type": {"type": "string", "description": "nom et url du ou des fichiers", "example": "Document[]"}, "description": {"type": "string", "description": "Nom du document", "example": "carte-identité.pdf"}}}, "description": "Documents de la fiche. Les documents peuvent contenir plusieurs fichiers"}}}, "OperationDescription": {"type": "object", "properties": {"type": {"type": "string", "description": "Type de dossier", "example": "OPERATION__IMMOBILIER__VENTE_ANCIEN"}, "records": {"type": "array", "description": "Liste des types de fiches possibles à associer", "items": {"type": "object", "description": "", "properties": {"name": {"description": "nom de la fiche dans le dossier", "type": "string", "example": "VENDEUR"}, "types": {"description": "type des fiches acceptées", "type": "array", "items": {"type": "string"}, "example": ["RECORD__PERSONNE__PHYSIQUE", "RECORD__PERSONNE__MORALE"]}}}}, "questions": {"type": "array", "description": "Liste les questions du dossier", "items": {"$ref": "#/components/schemas/Question"}}, "documents": {"type": "array", "description": "Liste les documents du dossier", "items": {"$ref": "#/components/schemas/Document"}}, "availableContracts": {"type": "array", "description": "Liste des contrats disponibles dans ce dossier", "items": {"type": "object", "properties": {"id": {"type": "string", "example": "IMMOBILIER_VENTE_ANCIEN_MANDAT", "description": "id d'un contrat à utiliser pour la création"}, "label": {"type": "string", "example": "<PERSON><PERSON><PERSON> de vente", "description": "label du contrat"}}}}, "subOperationType": {"type": "array", "description": "Liste des types de sous dossiers qui peuvent être crée à l'interieur d'un dossier", "items": {"type": "string"}}}}, "Operation": {"type": "object", "properties": {"label": {"type": "string", "example": "Vente 18 rue du jardin Paris", "description": "nom du dossier"}, "type": {"type": "string", "example": "OPERATION__IMMOBILIER__VENTE_ANCIEN", "description": "Type d'operation cf. operation/info pour les types disponibles"}, "records": {"type": "array", "description": "Dictionaire des fiches liées au dossier", "items": {"type": "object"}, "example": {"VENDEUR": [32543, 34002], "ACQUEREUR": [33357]}}, "questions": {"type": "object", "description": "Liste des réponses communes à tous les contrats", "items": {"type": "object"}, "example": {"estimation_haute": 90, "compromis_purge": "purge_papier", "tracfin_origine": "tracfin_faible"}}, "contracts": {"type": "array", "description": "Liste des contrats liés au dossier", "items": {"$ref": "#/components/schemas/Contract"}}, "creationTime": {"type": "number", "description": "Date de création du dossier", "example": 1673970906550}}}, "DirectLink": {"type": "object", "properties": {"link": {"type": "string", "description": "Lien d'auto connexion vers la ressource crée", "example": "https://app.mynotary.fr/connexion?authToken=1234-ABCD-5678-EFGH&continue=/url-accès-ressource"}}}, "Question": {"type": "object", "properties": {"id": {"type": "string", "description": "id de la question", "example": "informations_personnelles_date_naissance"}, "type": {"type": "string", "description": "format de la réponse", "example": "timestamp"}, "description": {"type": "string", "description": "intitulé de la question", "example": "Date de naissance"}, "enumValues": {"type": "array", "description": "liste des valeurs possibles pour une question de type enum", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "valeur autorisée", "example": "homme"}, "description": {"type": "string", "description": "inititulé du choix", "example": "<PERSON>"}}}}}}, "Document": {"type": "object", "properties": {"name": {"required": true, "type": "string", "description": "Le nom du fichier", "example": "dummy.pdf"}, "url": {"required": true, "type": "string", "description": "Url d'accès au fichier", "example": "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf"}}}, "Contract": {"type": "object", "properties": {"id": {"type": "number", "description": "id du contrat", "example": "1"}, "model": {"type": "string", "description": "Type de contrat", "example": "IMMOBILIER_MANDAT_VENTE_SIMPLE"}, "label": {"type": "string", "description": "Nom du contrat", "example": "<PERSON><PERSON><PERSON> de vente"}, "creationTime": {"type": "number", "description": "Date de création du contrat", "example": 1673970906550}, "signatureTime": {"type": "number", "description": "Date de signature du contrat au format Unix timestamp", "example": 1673970906550}}}, "Signature": {"type": "object", "properties": {"id": {"type": "string", "description": "ID de la signature"}, "signatories": {"type": "array", "items": {"$ref": "#/components/schemas/Signatory"}, "description": "Liste des signataires"}, "signatureTime": {"type": "string", "format": "date-time", "nullable": true, "description": "Date et heure de la signature (peut être null)"}, "status": {"type": "string", "enum": ["SIGNED", "CANCELLED", "EXPIRED", "DRAFT", "PENDING"], "description": "Statut de la signature"}, "files": {"type": "array", "items": {"$ref": "#/components/schemas/SignedFile"}, "description": "Liste des fichiers signés"}}}, "Signatory": {"type": "object", "properties": {"id": {"type": "string", "description": "ID du signataire"}, "firstname": {"type": "string", "description": "Prénom du signataire"}, "lastname": {"type": "string", "description": "Nom de famille du signataire"}, "email": {"type": "string", "format": "email", "description": "Adresse e-mail du signataire"}, "phone": {"type": "string", "description": "Numéro de téléphone du signataire"}, "signatureTime": {"type": "string", "format": "date-time", "nullable": true, "description": "Date et heure de la signature du signataire (peut être null)"}, "consent": {"type": "boolean", "description": "Consentement du signataire pour utiliser ses informations"}}}, "SignedFile": {"type": "object", "properties": {"signedFileId": {"type": "string", "description": "ID du fichier signé"}}}}, "examples": {}}}