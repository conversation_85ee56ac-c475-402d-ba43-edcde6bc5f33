WITH connected_user AS (
      SELECT u.id AS "user_id", organization_id, role_id
      FROM "user" u
             LEFT JOIN organization_user ou ON u.id = ou.user_id
      WHERE u.id = 419 AND ou.organization_id = 415
    ),
    created_records AS (
      SELECT lc.id
      FROM  legal_component_record lcr
      INNER JOIN  legal_component lc ON lcr.legal_component_id = lc.id
      WHERE creator_user_id = 419 AND organization_id=415
    ),
    organization_permission AS (
      SELECT entity_type, type
      FROM connected_user cu
      INNER JOIN  role_permission rp ON rp.role_id = cu.role_id
      INNER JOIN  permission p ON p.id = rp.permission_id
      WHERE type = 'UPDATE_ORGANIZATION_RECORDS'
      AND sub_entity_type = 'NONE'
    ),
    organization_records AS (
      SELECT lc.id
      FROM  legal_component lc
      INNER JOIN connected_user cu ON lc.organization_id = cu.organization_id
      INNER JOIN organization_permission
      ON entity_type = 'ORGANIZATION' AND type = 'UPDATE_ORGANIZATION_RECORDS'
    ),
    users_allowed_records AS (
      SELECT id
      FROM created_records
      UNION
      SELECT id
      FROM organization_records
    )
   ,
    pagination AS (
      SELECT  legal_component.id, count(*) OVER() AS total
      FROM users_allowed_records uar
        INNER JOIN  legal_component ON uar.id = legal_component.id
        INNER JOIN  legal_component_template ON legal_component.template_id = legal_component_template.id
        INNER JOIN "user" u ON u.id = legal_component.creator_user_id
        INNER JOIN  legal_component_record ON legal_component.id = legal_component_record.legal_component_id
        INNER JOIN answer ON legal_component_record.answer_id = answer.id
      WHERE deleted = FALSE
        AND
            legal_component_template.type = 'RECORD' AND answer.answer::jsonb != '{}'::jsonb
             AND (
                (
                legal_component_template.specific_types[0 + 1] = 'PERSONNE'
               )
             )
      ORDER BY legal_component.creation_time  DESC
       LIMIT 20 OFFSET 0
    )
    SELECT
    legal_component.id,
    legal_component.organization_id,
    legal_component.creation_time,
    legal_component.last_update_time,
    'RECORD' AS type,
    u.id "creatorUser.id",
    u.firstname "creatorUser.firstname",
    u.lastname "creatorUser.lastname",
    u.email "creatorUser.email",
    answer.id "answer.id",
    answer.answer "answer.data",
    legal_component_template.id "template.id",
    legal_component_template.type "template.type",
    legal_component_template.specific_types "template.specific_types"
   ,
            pagination.total AS total
    FROM pagination
    INNER JOIN  legal_component ON pagination.id = legal_component.id
    INNER JOIN  legal_component_template ON legal_component.template_id = legal_component_template.id
    INNER JOIN "user" u ON u.id = legal_component.creator_user_id
    INNER JOIN  legal_component_record ON legal_component.id =
        legal_component_record.legal_component_id
    INNER JOIN answer ON legal_component_record.answer_id = answer.id
    ORDER BY legal_component.creation_time  DESC;


WITH pagination AS (SELECT legal_component.id, count(*) OVER () AS total
                    FROM legal_component
                             INNER JOIN legal_component_template
                                        ON legal_component.template_id = legal_component_template.id
                             INNER JOIN "user" u ON u.id = legal_component.creator_user_id
                             INNER JOIN legal_component_record
                                        ON legal_component.id = legal_component_record.legal_component_id
                             INNER JOIN answer ON legal_component_record.answer_id = answer.id
                    WHERE deleted = FALSE

                      AND (organization_id = 415 OR legal_component.creator_user_id = 419)


                      AND legal_component_template.type = 'RECORD'
                      AND answer.answer::jsonb != '{}'::jsonb
                      AND (
                        (
                            unaccent(answer.answer -> 'nom' ->> 'value') ILIKE '%' || unaccent('') || '%'
                                OR
                            unaccent(answer.answer -> 'prenoms' ->> 'value') ILIKE '%' || unaccent('') || '%'
                                OR
                            unaccent(answer.answer -> 'email' ->> 'value') ILIKE '%' || unaccent('') || '%'
                                OR
                            unaccent(answer.answer -> 'telephone' ->> 'value') ILIKE '%' || unaccent('') || '%'
                                OR
                            unaccent(answer.answer -> 'personne_morale_forme_sociale' ->> 'value') ILIKE '%' || unaccent('') || '%'
                                OR
                            unaccent(answer.answer -> 'personne_morale_denomination_commerciale' ->> 'value') ILIKE '%' || unaccent('') || '%'
                                OR
                            unaccent(answer.answer -> 'personne_morale_forme_sociale_autre' ->> 'value') ILIKE '%' || unaccent('') || '%'
                                OR
                            unaccent(answer.answer -> 'siren' ->> 'value') ILIKE '%' || unaccent('') || '%'
                                OR
                            unaccent(answer.answer -> 'personne_morale_denomination' ->> 'value') ILIKE '%' || unaccent('') || '%'
                                OR
                            unaccent(answer.answer -> 'etude_nom' ->> 'value') ILIKE '%' || unaccent('') || '%'
                            )
                        )
                      AND ((legal_component_template.specific_types[0 + 1] = 'PERSONNE'))

                    ORDER BY legal_component.creation_time DESC
                    LIMIT 20 OFFSET 0)
SELECT legal_component.id,
       legal_component.organization_id,
       legal_component.creation_time,
       legal_component.last_update_time,
       'RECORD' AS                             type,
       u.id                                    "creatorUser.id",
       u.firstname                             "creatorUser.firstname",
       u.lastname                              "creatorUser.lastname",
       u.email                                 "creatorUser.email",
       answer.id                               "answer.id",
       answer.answer                           "answer.data",
       legal_component_template.id             "template.id",
       legal_component_template.type           "template.type",
       legal_component_template.specific_types "template.specific_types"
        ,
       total
FROM pagination
         INNER JOIN legal_component ON pagination.id = legal_component.id
         INNER JOIN legal_component_template ON legal_component.template_id = legal_component_template.id
         INNER JOIN "user" u ON u.id = legal_component.creator_user_id
         INNER JOIN legal_component_record ON legal_component.id =
                                              legal_component_record.legal_component_id
         INNER JOIN answer ON legal_component_record.answer_id = answer.id
ORDER BY legal_component.creation_time DESC;
