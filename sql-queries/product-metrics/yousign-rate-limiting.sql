
WITH daily_signatory AS (SELECT DATE(creation_time) AS day,
                                COUNT(*)            AS row_count
                         FROM signature_signatory ss
                                  INNER JOIN signature s ON ss.signature_id = s.id
                         WHERE creation_time >= NOW() - INTERVAL '30 days'
                           AND EXTRACT(DOW FROM creation_time) NOT IN (0, 6)
                         GROUP BY day),
     max_signatory AS (SELECT MAX(row_count) AS max_rows_per_day
                       FROM daily_signatory),
     daily_document AS (SELECT DATE(creation_time) AS day,
                               COUNT(*)            AS row_count
                        FROM signature_file ss
                                 INNER JOIN signature s ON ss.signature_id = s.id
                        WHERE creation_time >= NOW() - INTERVAL '30 days'
                          AND EXTRACT(DOW FROM creation_time) NOT IN (0, 6)
                        GROUP BY day),
     max_document AS (SELECT MAX(row_count) AS max_rows_per_day
                      FROM daily_document),
     daily_signature AS (SELECT DATE(creation_time) AS day,
                                COUNT(*)            AS row_count
                         FROM signature
                         WHERE creation_time >= NOW() - INTERVAL '30 days'
                           AND EXTRACT(DOW FROM creation_time) NOT IN (0, 6)
                         GROUP BY day),
     max_signature AS (SELECT MAX(row_count) AS max_rows_per_day
                       FROM daily_signature)
SELECT *
FROM max_document,
     max_signatory,
     max_signature;

-- 2272 + 2272 + 2272 + 3572 + 3572 + 1424 + 1424 =


WITH daily_signatory AS (SELECT DATE_TRUNC('minute', creation_time) AS minute,
                                COUNT(*)                            AS row_count
                         FROM signature_signatory ss
                                  INNER JOIN signature s ON ss.signature_id = s.id
                         WHERE creation_time >= NOW() - INTERVAL '30 days'
                           AND EXTRACT(DOW FROM creation_time) NOT IN (0, 6)
                         GROUP BY minute),
     max_signatory AS (SELECT MAX(row_count) AS max_rows_per_day
                       FROM daily_signatory),
     daily_document AS (SELECT DATE_TRUNC('minute', creation_time) AS minute,
                               COUNT(*)            AS row_count
                        FROM signature_file ss
                                 INNER JOIN signature s ON ss.signature_id = s.id
                        WHERE creation_time >= NOW() - INTERVAL '30 days'
                          AND EXTRACT(DOW FROM creation_time) NOT IN (0, 6)
                        GROUP BY minute),
     max_document AS (SELECT MAX(row_count) AS max_rows_per_day
                      FROM daily_document),
     daily_signature AS (SELECT DATE_TRUNC('minute', creation_time) AS minute,
                                COUNT(*)            AS row_count
                         FROM signature
                         WHERE creation_time >= NOW() - INTERVAL '30 days'
                           AND EXTRACT(DOW FROM creation_time) NOT IN (0, 6)
                         GROUP BY minute),
     max_signature AS (SELECT MAX(row_count) AS max_rows_per_day
                       FROM daily_signature)
SELECT *
FROM max_document,
     max_signatory,
     max_signature;

WITH daily_signatory AS (SELECT DATE_TRUNC('minute', creation_time) AS minute,
                                COUNT(*)                            AS row_count
                         FROM signature_signatory ss
                                  INNER JOIN signature s ON ss.signature_id = s.id
                         WHERE creation_time >= NOW() - INTERVAL '30 days'
                           AND EXTRACT(DOW FROM creation_time) NOT IN (0, 6)
                         GROUP BY minute),
     avg_signatory AS (SELECT AVG(row_count) AS avg_rows_per_day
                       FROM daily_signatory),
     daily_document AS (SELECT DATE_TRUNC('minute', creation_time) AS minute,
                               COUNT(*)                            AS row_count
                        FROM signature_file ss
                                 INNER JOIN signature s ON ss.signature_id = s.id
                        WHERE creation_time >= NOW() - INTERVAL '30 days'
                          AND EXTRACT(DOW FROM creation_time) NOT IN (0, 6)
                        GROUP BY minute),
     avg_document AS (SELECT AVG(row_count) AS avg_rows_per_day
                      FROM daily_document)
SELECT *
FROM avg_document,
     avg_signatory;
