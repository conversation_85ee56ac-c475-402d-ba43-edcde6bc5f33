-- Uniq user and uniq operation used in drive
SELECT COUNT(DISTINCT u.email) user_uniq_count, COUNT(DISTINCT drive_file.operation_id) operation_uniq_count  FROM drive_file
INNER JOIN public."user" u on u.id = drive_file.user_id
WHERE email NOT LIKE '%@mynotary.fr'
AND drive_file.creation_time >= NOW() - INTERVAL '30 days';

-- Uniq user and uniq operation used for document request
SELECT COUNT(DISTINCT u.email) user_uniq_count, COUNT(DISTINCT task.legal_component_id) operation_uniq_count FROM task
INNER JOIN "user" u on u.id = task.creator_user_id
WHERE email NOT LIKE '%@mynotary.fr'
AND type='DOCUMENT_REQUEST'
AND task.creation_time >= NOW() - INTERVAL '30 days';

-- Uniq user and uniq operation
SELECT count(DISTINCT u.email) user_uniq_count, count(DISTINCT lc.id) operation_uniq_count  FROM legal_component lc
                INNER JOIN "user" u on u.id = lc.creator_user_id
                               INNER JOIN legal_component_template lct ON lc.template_id = lct.id
WHERE email NOT LIKE '%@mynotary.fr'
AND lct.type='OPERATION'
AND lc.creation_time >= NOW() - INTERVAL '30 days'
AND deleted IS FALSE;

-- Uniq user authenticated or connected to MyNotary
SELECT count(DISTINCT email) FROM "user"
WHERE last_authentication_time >= NOW() - INTERVAL '30 days'
OR last_connection_time >= NOW() - INTERVAL '30 days';