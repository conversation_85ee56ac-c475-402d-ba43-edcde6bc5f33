CREATE or REPLACE view tableau_admin_house as
WITH target_houses AS (
    SELECT
        lc.id                                                                                  AS house_id,
        COALESCE((a.answer -> 'nature_bien'::text) ->> 'value'::text,
                 (a.answer -> 'nature_bien_leizee'::text) ->> 'value'::text)                   AS house_type,
        (a.answer -> 'nature_bien_autre'::text) ->> 'value'::text                              AS house_type_other,
        COALESCE((a.answer -> 'mesurage_carrez_superficie'::text) ->> 'value'::text,
                 (a.answer -> 'surface_habitable'::text) ->> 'value'::text,
                 (a.answer -> 'programme_superficie'::text) ->> 'value'::text)                 AS surface,
        (a.answer -> 'programme_terrain_superficie'::text) ->> 'value'::text                   AS surface_field,
        (a.answer -> 'dpe_consommation_energetique'::text) ->> 'value'::text                   AS dpe,
        (a.answer -> 'dpe_emission_gaz'::text) ->> 'value'::text                               AS gaz,
        (a.answer -> 'construction'::text) -> 'value'::text                                    AS construction_date,
        (a.answer -> 'denomination'::text) -> 'value'::text                                    AS denomination,
        ((a.answer -> 'adresse'::text) -> 'value'::text) ->> 'formattedAddress'::text          AS formatted_address,
        (((a.answer -> 'adresse'::text) -> 'value'::text) -> 'location'::text) ->> 'lat'::text AS lat,
        (((a.answer -> 'adresse'::text) -> 'value'::text) -> 'location'::text) ->> 'lng'::text AS lng,
        ((a.answer -> 'adresse'::text) -> 'value'::text) ->> 'city'::text                      AS city,
        ((a.answer -> 'adresse'::text) -> 'value'::text) ->> 'zip'::text                       AS zip,
        lct.label                                                                              AS record_type,
        lc.creation_time,
        u.email                                                                                AS creator_email,
        (u.firstname || ' '::text) || u.lastname                                               AS creator_name,
        ('https://app.mynotary.fr/biens/'::text || lc.id) || '/informations'::text             AS house_link,
        ('https://app.mynotary.fr/biens/'::text || lc.id) || '/operations'::text               AS operation_link,
        o.id                                                                                   AS organization_id,
        o.name                                                                                 AS organization_name,
        oo.parent_organization_id                                                              AS holding_id,
        (
            SELECT
                organization.name
            FROM organization
            WHERE organization.id = oo.parent_organization_id
            )                                                                                  AS holding_name
    FROM legal_component lc
             JOIN organization o ON lc.organization_id = o.id
             JOIN legal_component_template lct ON lc.template_id = lct.id
             JOIN legal_component_record lcr ON lc.id = lcr.legal_component_id
             JOIN answer a ON a.id = lcr.answer_id
             JOIN "user" u ON lc.creator_user_id = u.id
             LEFT JOIN organization_organization oo ON lc.organization_id = oo.sub_organization_id
    WHERE lct.type = 'RECORD'::text
      AND (lct.origin_template ~~ 'RECORD__BIEN%'::text OR lct.origin_template = 'RECORD__STRUCTURE__COPROPRIETE'::text)
      AND lc.deleted IS FALSE
    )
SELECT
    target_houses.house_id,
    target_houses.surface,
    target_houses.surface_field,
    target_houses.dpe,
    target_houses.gaz,
    target_houses.construction_date,
    target_houses.denomination,
    target_houses.lat,
    target_houses.lng,
    target_houses.city,
    target_houses.zip,
    target_houses.formatted_address,
    target_houses.record_type,
    target_houses.creation_time,
    target_houses.creator_email,
    target_houses.creator_name,
    target_houses.house_link,
    target_houses.operation_link,
    CASE
        WHEN target_houses.house_type = 'nature_bien_autre'::text THEN target_houses.house_type_other
        WHEN target_houses.house_type = 'nature_bien_appartement'::text THEN 'Appartement'::text
        WHEN target_houses.house_type = 'nature_bien_duplex'::text THEN 'Duplex'::text
        WHEN target_houses.house_type = 'nature_bien_triplex'::text THEN 'Triplex'::text
        WHEN target_houses.house_type = 'nature_bien_maison'::text THEN 'Maison en copropriété'::text
        WHEN target_houses.house_type = 'leizee_appartement'::text THEN 'Appartement'::text
        WHEN target_houses.house_type = 'leizee_batiment'::text THEN 'Bâtiment'::text
        WHEN target_houses.house_type = 'leizee_chalet'::text THEN 'Chalet'::text
        WHEN target_houses.house_type = 'leizee_chambre'::text THEN 'Chambre'::text
        WHEN target_houses.house_type = 'leizee_chambre_double_medicalisee'::text THEN 'Chambre double médicalisée'::text
        WHEN target_houses.house_type = 'leizee_chambre_etudiante'::text THEN 'Chambre étudiante'::text
        WHEN target_houses.house_type = 'leizee_chambre_medicalisee'::text THEN 'Chambre médicalisée'::text
        WHEN target_houses.house_type = 'leizee_duplex'::text THEN 'Duplex'::text
        WHEN target_houses.house_type = 'leizee_loft'::text THEN 'Loft'::text
        WHEN target_houses.house_type = 'leizee_maison'::text THEN 'Maison'::text
        WHEN target_houses.house_type = 'leizee_terrain'::text THEN 'Terrain'::text
        WHEN target_houses.house_type = 'leizee_triplex'::text THEN 'Triplex'::text
        WHEN target_houses.house_type = 'nature_maison'::text THEN 'Maison individuelle'::text
        WHEN target_houses.house_type = 'nature_mitoyen'::text THEN 'Maison mitoyenne'::text
        WHEN target_houses.house_type = 'nature_immeuble'::text THEN 'Immeuble entier'::text
        WHEN target_houses.house_type = 'nature_bien_immeuble'::text THEN 'Immeuble Entier'::text
        WHEN target_houses.house_type = 'nature_bien_commercial'::text THEN 'Bien commercial ou professionnel'::text
        WHEN target_houses.house_type = 'nature_garage'::text THEN 'Garage'::text
        WHEN target_houses.house_type = 'nature_bien_cave'::text THEN 'Cave'::text
        WHEN target_houses.house_type = 'nature_bien_local'::text THEN 'Local commercial ou professionnel'::text
        WHEN target_houses.house_type = 'nature_bien_parking'::text THEN 'Parking'::text
        WHEN target_houses.house_type = 'leizee_batiment'::text THEN 'Bâtiment'::text
        WHEN target_houses.house_type = 'leizee_bureau'::text THEN 'Parking'::text
        WHEN target_houses.house_type = 'nature_bien_parking'::text THEN 'Bureau'::text
        WHEN target_houses.house_type = 'leizee_casier_ski'::text THEN 'Casier à ski'::text
        WHEN target_houses.house_type = 'leizee_cave'::text THEN 'Cave'::text
        WHEN target_houses.house_type = 'leizee_commerce'::text THEN 'Commerce'::text
        WHEN target_houses.house_type = 'leizee_local_velo'::text THEN 'Local à vélo'::text
        WHEN target_houses.house_type = 'leizee_stationnement'::text THEN 'Stationnement'::text
        ELSE target_houses.house_type_other
        END AS house_type,
    target_houses.organization_name,
    target_houses.organization_id,
    target_houses.holding_id,
    target_houses.holding_name
FROM target_houses
WHERE target_houses.creator_email::text !~~ '%mynotary.fr'::text