CREATE or REPLACE view tableau_admin_registered_letter as
SELECT
    ra.registered_letter_batch_id,
    batch.creator_user_id                                             AS registered_letter_creator_id,
    batch.label                                                       AS registered_letter_label,
    batch.creation_time                                               AS registered_letter_creation_time,
    batch.cancelation_time,
    (
        SELECT
            COUNT(rl.id) AS count
        FROM registered_letter rl
        WHERE rl.batch_id = ra.registered_letter_batch_id
        )                                                             AS recipient_count,
    CASE
        WHEN batch.cancelation_time IS NOT NULL THEN 'Annulée'::text
        ELSE 'Envoyée'::text
        END                                                           AS status,
    ra.contract_id,
    (
        SELECT
            ocm.label
        FROM operation_contract oc
                 JOIN operation_contract_model ocm ON oc.model_id = ocm.id
        WHERE oc.id = ra.contract_id
        )                                                             AS contract_type_label,
    u.email                                                           AS registered_letter_creator_email,
    o.name                                                            AS organization_name,
    o.id                                                              AS organization_id,
    (
        SELECT
            organization_organization.parent_organization_id
        FROM organization_organization
        WHERE organization_organization.sub_organization_id = o.id
        )                                                             AS holding_id,
    (
        SELECT
            o2.name
        FROM organization_organization
                 JOIN organization o2 ON o2.id = organization_organization.parent_organization_id
        WHERE organization_organization.sub_organization_id = o.id
        )                                                             AS holding_name,
    s.plan_type AS plan,
    lco.legal_component_id                                            AS operation_id,
    lco.label                                                         AS operation_label
FROM registered_letter_batch_association_legal_component ra
         JOIN legal_component_operation lco ON ra.legal_component_id = lco.legal_component_id
         JOIN legal_component lc ON lco.legal_component_id = lc.id
         JOIN registered_letter_batch batch ON ra.registered_letter_batch_id = batch.id
         JOIN "user" u ON batch.creator_user_id = u.id
         LEFT JOIN organization o ON lc.organization_id = o.id
         LEFT JOIN subscription s ON o.subscription_id = s.id
WHERE u.email::text !~~ '%@mynotary.fr'::text