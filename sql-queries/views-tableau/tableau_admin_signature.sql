CREATE or REPLACE view tableau_admin_signature as
SELECT
    s.id                              AS signature_id,
    s.creator_user_id                 AS signature_creator_id,
    s.creation_time                   AS signature_creation_time,
    s.document_name                   AS signature_label,
    s.expiration_time,
    s.cancelation_time,
    s.signature_time,
    (
        SELECT
            COUNT(ss.id) AS count
        FROM signature_signatory ss
        WHERE ss.signature_id = s.id
        )                             AS signatory_count,
    (
        SELECT
            COUNT(ss.id) FILTER (WHERE ss.signature_time IS NOT NULL) AS count
        FROM signature_signatory ss
        WHERE ss.signature_id = s.id
        )                             AS signatory_signed_count,
    CASE
        WHEN s.provider_type::text = 'MYNOTARY_PAPER'::text THEN 'Papier'::text
        WHEN s.method = 'SIMPLE'::text THEN 'Simple'::text
        WHEN s.method = 'ADVANCED'::text THEN 'Avancée'::text
        ELSE '-'::text
        END                           AS signature_type,
    CASE
        WHEN s.expiration_time < NOW() THEN 'Expirée'::text
        WHEN s.cancelation_time IS NOT NULL THEN 'Annulée'::text
        WHEN s.signature_time IS NOT NULL THEN 'Finalisée'::text
        ELSE 'En cours'::text
        END                           AS signature_status,
    u.email                           AS signature_creator_email,
    o.name                            AS organization_name,
    o.id                              AS organization_id,
    oo.parent_organization_id         AS holding_id,
    parent_orga.name                  AS holding_name,
    subscription.plan_type            AS plan,
    operation_meta.legal_component_id AS operation_id,
    operation_meta.label              AS operation_label,
    program_meta.legal_component_id   AS program_operation_id,
    program_meta.label                AS program_operation_label
FROM signature_association_legal_component sa
         JOIN legal_component operation ON sa.legal_component_id = operation.id
         JOIN legal_component_operation operation_meta ON operation.id = operation_meta.legal_component_id
         JOIN signature s ON sa.signature_id = s.id
         LEFT JOIN legal_component program ON operation_meta.parent_operation_id = program.id
         LEFT JOIN legal_component_operation program_meta ON program.id = program_meta.legal_component_id
         LEFT JOIN "user" u ON s.creator_user_id = u.id
         LEFT JOIN organization o ON o.id = COALESCE(program.organization_id, operation.organization_id)
         LEFT JOIN organization_organization oo ON operation.organization_id = oo.sub_organization_id
         LEFT JOIN organization parent_orga ON oo.parent_organization_id = parent_orga.id
         LEFT JOIN subscription ON o.subscription_id = subscription.id
WHERE u.email::text !~~ '%@mynotary.fr'::text