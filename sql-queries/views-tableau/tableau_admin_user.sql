CREATE or REPLACE view tableau_admin_user as
WITH register_users AS (
    SELECT
        u.email,
        u.id                                                             AS user_id,
        u.lastname,
        u.firstname,
        u.creation_time,
        u.last_authentication_time,
        o.name                                                           AS organization_name,
        o.id                                                             AS organization_id,
        (
            SELECT
                organization_organization.parent_organization_id
            FROM organization_organization
            WHERE organization_organization.sub_organization_id = o.id
            )                                                            AS holding_id,
        s.plan_type AS plan,
        r.name                                                           AS role
    FROM "user" u
             JOIN organization_user ou ON u.id = ou.user_id
             JOIN organization o ON ou.organization_id = o.id
             JOIN mynotary.public.subscription s ON o.subscription_id = s.id
             JOIN role r ON ou.role_id = r.id
    ),
     invited_users AS (
         SELECT
             ou.email,
             ou.user_id,
             ou.creation_time,
             NULL::timestamp WITHOUT TIME ZONE                                AS last_authentication_time,
             o.name                                                           AS organization_name,
             ou.organization_id,
             (
                 SELECT
                     organization_organization.parent_organization_id
                 FROM organization_organization
                 WHERE organization_organization.sub_organization_id = o.id
                 )                                                            AS holding_id,
             s.plan_type                                                      AS plan,
             r.name                                                           AS role
         FROM organization_user ou
                  JOIN organization o ON ou.organization_id = o.id
                  JOIN mynotary.public.subscription s ON o.subscription_id = s.id
                  JOIN role r ON ou.role_id = r.id
         WHERE ou.user_id IS NULL
         ),
     users AS (
         SELECT
             register_users.email,
             register_users.lastname,
             register_users.firstname,
             register_users.user_id,
             register_users.creation_time,
             register_users.last_authentication_time,
             register_users.organization_name,
             register_users.organization_id,
             register_users.holding_id,
             register_users.plan,
             register_users.role
         FROM register_users
         UNION ALL
         SELECT
             invited_users.email,
             NULL::text AS lastname,
             NULL::text AS firstname,
             invited_users.user_id,
             invited_users.creation_time,
             invited_users.last_authentication_time,
             invited_users.organization_name,
             invited_users.organization_id,
             invited_users.holding_id,
             invited_users.plan,
             invited_users.role
         FROM invited_users
         )
SELECT
    users.user_id,
    users.email,
    users.firstname,
    users.lastname,
    users.creation_time,
    users.last_authentication_time,
    users.organization_name,
    users.organization_id,
    users.holding_id,
    (
        SELECT
            organization.name
        FROM organization
        WHERE organization.id = users.holding_id
        ) AS holding_name,
    users.plan,
    users.role
FROM users