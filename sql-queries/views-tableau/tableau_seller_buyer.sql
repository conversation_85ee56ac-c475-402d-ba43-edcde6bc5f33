CREATE or REPLACE view tableau_seller_buyer as
WITH target_operations AS (
    SELECT
        lc.id              AS operation_id,
        lc.creation_time   AS operation_creation_time,
        lc.creator_user_id AS operation_creator_user_id,
        lc.organization_id,
        o.name             AS organization_name
    FROM legal_component lc
             JOIN legal_component_template lct ON lc.template_id = lct.id
             JOIN "user" u_1 ON lc.creator_user_id = u_1.id
             JOIN organization o ON lc.organization_id = o.id
    WHERE lct.origin_template = 'OPERATION__IMMOBILIER__VENTE_ANCIEN'::text
      AND lc.deleted IS FALSE
      AND u_1.email::text !~~ '%mynotary.fr'::text
    ),
     target_records AS (
         SELECT
             target_operations_1.operation_id,
             lcb_1.to_id   AS target_record_id,
             lct.label     AS record_label,
             lcb_1.type    AS record_type,
             answer.answer AS target_record_answer
         FROM legal_component_branch lcb_1
                  JOIN target_operations target_operations_1 ON lcb_1.from_id = target_operations_1.operation_id
                  JOIN legal_component lc ON lcb_1.to_id = lc.id
                  JOIN legal_component_template lct ON lc.template_id = lct.id
                  JOIN legal_component_record lcr_1 ON lcb_1.to_id = lcr_1.legal_component_id
                  JOIN answer ON lcr_1.answer_id = answer.id
         WHERE (lcb_1.type = ANY (ARRAY ['VENDEUR'::text, 'ACQUEREUR'::text]))
           AND lcb_1.to_id IS NOT NULL
         ),
     target_links AS (
         SELECT
             target_records_1.target_record_id,
             lct.label AS marital_status
         FROM target_records target_records_1
                  JOIN legal_component_branch lcb_1 ON target_records_1.target_record_id = lcb_1.from_id
                  JOIN legal_component lc ON lcb_1.link_id = lc.id
                  JOIN legal_component_template lct ON lc.template_id = lct.id
         WHERE lct.specific_types @> '{SITUATION_MARITALE}'::text[]
         )
SELECT
    target_records.target_record_id,
    target_records.record_type,
    TO_TIMESTAMP(((((target_records.target_record_answer -> 'informations_personnelles_date_naissance'::text) ->>
                    'value'::text)::bigint) / 1000)::double precision)::date AS date_of_birth,
    target_links.marital_status,
    (a.answer -> 'prix_vente_total'::text) -> 'value'::text                  AS price,
    target_operations.organization_id,
    target_operations.organization_name
FROM target_operations
         JOIN target_records ON target_operations.operation_id = target_records.operation_id
         JOIN target_links ON target_records.target_record_id = target_links.target_record_id
         JOIN "user" u ON target_operations.operation_creator_user_id = u.id
         JOIN legal_component_branch lcb ON target_operations.operation_id = lcb.from_id
         JOIN legal_component_record lcr ON lcb.to_id = lcr.legal_component_id
         JOIN answer a ON lcr.answer_id = a.id
WHERE lcb.type = 'CONDITIONS_GENERALES'::text