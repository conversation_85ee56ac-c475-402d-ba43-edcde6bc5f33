CREATE or REPLACE view tableau_admin_contract as
WITH contracts AS (
    SELECT
        oc.id,
        oc.creation_time,
        oc.creator_user_id,
        oc.operation_id,
        oc.validation_time,
        oc.label                   AS contract_label,
        ocm.label,
        ocm.origin_model_id,
        lct.label                  AS operation_type,
        operation.label            AS operation_label,
        lc.organization_id         AS operation_organization_id,
        oc.status,
        program.legal_component_id AS program_id,
        program.label              AS program_label,
        (
            SELECT
                sa.signature_id
            FROM signature_association_legal_component sa
            WHERE sa.contract_id = oc.id
            ORDER BY sa.signature_id DESC
            LIMIT 1
            )                      AS last_signature_id
    FROM operation_contract oc
             JOIN operation_contract_model ocm ON oc.model_id = ocm.id
             JOIN legal_component_operation operation ON oc.operation_id = operation.legal_component_id
             JOIN legal_component lc ON oc.operation_id = lc.id
             JOIN legal_component_template lct ON lc.template_id = lct.id
             LEFT JOIN legal_component_operation program ON operation.parent_operation_id = program.legal_component_id
    WHERE oc.deletion_time IS NULL
      AND lc.deleted IS FALSE
    ),
     last_signatures AS (
         SELECT
             ci.id        AS contract_id,
             signature.creation_time,
             signature.signature_time,
             signature.provider_type,
             signature.id AS signature_id
         FROM contracts ci
                  JOIN signature ON ci.last_signature_id = signature.id
         ),
     creators AS (
         SELECT
             contracts.id                                                      AS contract_id,
             (u.firstname || ' '::text) || u.lastname                          AS contract_creator_name,
             u.email                                                           AS contract_creator_email,
             o.name                                                            AS organization_name,
             o.id                                                              AS organization_id,
             oo.parent_organization_id                                         AS holding_id,
             holding.name                                                      AS holding_name,
             s.plan_type AS plan,
             (
                 SELECT
                     r.name
                 FROM organization_user ou
                          JOIN role r ON ou.role_id = r.id
                 WHERE ou.organization_id = o.id
                   AND ou.user_id = u.id
                 )                                                             AS contract_creator_role
         FROM contracts
                  JOIN "user" u ON contracts.creator_user_id = u.id
                  JOIN organization o ON contracts.operation_organization_id = o.id
                  LEFT JOIN organization_organization oo ON contracts.operation_organization_id = oo.sub_organization_id
                  LEFT JOIN organization holding ON oo.parent_organization_id = holding.id
                  LEFT JOIN subscription s ON o.subscription_id = s.id
         ),
     mandat AS (
         SELECT DISTINCT ON (c_1.id)
             c_1.id                                                                            AS contract_id,
             (a.answer -> 'mandat_vente_calcul'::text) ->> 'value'::text                       AS fee_type,
             (a.answer -> 'mandat_pourcentage_honoraires_vente_total'::text) ->> 'value'::text AS fee_value_percent,
             (a.answer -> 'mandat_honoraires_montant'::text) ->> 'value'::text                 AS fee_value_fixed,
             (a.answer -> 'mandat_type'::text) ->> 'value'::text                               AS mandat_type
         FROM contracts c_1
                  JOIN legal_component_branch lcb ON c_1.operation_id = lcb.from_id
                  JOIN legal_component_record lcr ON lcb.to_id = lcr.legal_component_id
                  JOIN answer a ON lcr.answer_id = a.id
         WHERE lcb.type = 'MANDAT'::text
           AND lcb.specific_contract_id IS NULL
         ),
     price AS (
         SELECT
             c_1.id                                                             AS contract_id,
             COALESCE((a.answer -> 'nouveau_prix_de_vente'::text) ->> 'value'::text,
                      (a.answer -> 'prix_vente_total'::text) ->> 'value'::text) AS price
         FROM contracts c_1
                  JOIN legal_component_branch lcb ON c_1.operation_id = lcb.from_id
                  JOIN legal_component_record lcr ON lcb.to_id = lcr.legal_component_id
                  JOIN answer a ON a.id = lcr.answer_id
         WHERE lcb.type = 'CONDITIONS_GENERALES'::text
           AND lcb.specific_contract_id IS NULL
         ),
     bien AS (
         SELECT DISTINCT ON (c_1.id)
             c_1.id                                                                                 AS contract_id,
             (a.answer -> 'dpe_consommation_energetique'::text) ->> 'value'::text                   AS dpe,
             (a.answer -> 'dpe_emission_gaz'::text) ->> 'value'::text                               AS gaz,
             (a.answer -> 'mesurage_carrez_superficie'::text) ->> 'value'::text                     AS surface,
             (((a.answer -> 'adresse'::text) -> 'value'::text) -> 'location'::text) ->> 'lat'::text AS lat,
             (((a.answer -> 'adresse'::text) -> 'value'::text) -> 'location'::text) ->> 'lng'::text AS lng,
             ((a.answer -> 'adresse'::text) -> 'value'::text) ->> 'city'::text                      AS city,
             ((a.answer -> 'adresse'::text) -> 'value'::text) ->> 'zip'::text                       AS zip,
             lct.label                                                                              AS record_type
         FROM contracts c_1
                  JOIN legal_component_branch lcb ON c_1.operation_id = lcb.from_id
                  JOIN legal_component_record lcr ON lcb.to_id = lcr.legal_component_id
                  JOIN legal_component lc ON lcr.legal_component_id = lc.id
                  JOIN legal_component_template lct ON lc.template_id = lct.id
                  JOIN answer a ON lcr.answer_id = a.id
         WHERE lcb.type = 'BIEN_VENDU'::text
           AND lcb.specific_contract_id IS NULL
         )
SELECT
    co.id              AS contract_id,
    co.creation_time   AS contract_creation_time,
    co.contract_label,
    co.label           AS contract_type_label,
    co.origin_model_id AS contract_type,
    co.operation_type,
    co.creator_user_id AS contract_creator_id,
    co.operation_label,
    co.operation_id,
    co.status          AS contract_status,
    co.program_id,
    co.program_label,
    co.validation_time,
    ls.creation_time   AS signature_creation_time,
    ls.signature_time,
    c.contract_creator_name,
    c.contract_creator_email,
    c.organization_name,
    c.organization_id,
    c.holding_id,
    c.holding_name,
    c.plan,
    c.contract_creator_role,
    (
        SELECT
            o.name
        FROM legal_component lc2
                 JOIN organization o ON lc2.organization_id = o.id
        WHERE lc2.id = co.program_id
        )              AS program_organization_name,
    p.price,
    m.fee_type,
    m.fee_value_percent,
    m.fee_value_fixed,
    m.mandat_type,
    CASE
        WHEN m.fee_type = 'recherche_pourcentage'::text AND m.fee_value_percent IS NOT NULL AND p.price IS NOT NULL AND
             m.fee_value_percent::double precision > 0::double precision THEN
                    p.price::double precision / 100::double precision * m.fee_value_percent::double precision
        ELSE m.fee_value_fixed::double precision
        END            AS fee,
    b.city,
    b.zip,
    b.lat,
    b.lng,
    b.surface,
    b.dpe,
    b.gaz,
    b.record_type
FROM contracts co
         LEFT JOIN mandat m ON co.id = m.contract_id
         LEFT JOIN price p ON co.id = p.contract_id
         LEFT JOIN bien b ON co.id = b.contract_id
         LEFT JOIN last_signatures ls ON co.id = ls.contract_id
         LEFT JOIN creators c ON co.id = c.contract_id
WHERE c.contract_creator_email::text !~~ '%mynotary.fr'::text