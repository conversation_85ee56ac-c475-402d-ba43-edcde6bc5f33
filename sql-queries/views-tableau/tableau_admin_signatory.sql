CREATE or REPLACE view tableau_admin_signatory as
SELECT
    s.id                                                              AS signature_id,
    s.creation_time                                                   AS signature_creation_time,
    s.document_name                                                   AS signature_label,
    CASE
        WHEN s.provider_type::text = 'MYNOTARY_PAPER'::text THEN 'Papier'::text
        WHEN s.method = 'SIMPLE'::text THEN 'Simple'::text
        WHEN s.method = 'ADVANCED'::text THEN 'Avancée'::text
        ELSE '-'::text
        END                                                           AS signature_type,
    s.creator_user_id                                                 AS signature_creator_id,
    u.email                                                           AS signature_creator_email,
    o.name                                                            AS organization_name,
    o.id                                                              AS organization_id,
    oo.parent_organization_id                                         AS holding_id,
    parent_orga.name                                                  AS holding_name,
    subscription.plan_type AS plan,
    operation_meta.legal_component_id                                 AS operation_id,
    operation_meta.label                                              AS operation_label,
    program_meta.legal_component_id                                   AS program_operation_id,
    program_meta.label                                                AS program_operation_label,
    ss.firstname                                                      AS signatory_firstname,
    ss.lastname                                                       AS signatory_lastname,
    ss.email                                                          AS signatory_email,
    ss.signature_time                                                 AS signatory_signature_time
FROM signature_association_legal_component sa
         JOIN legal_component operation ON sa.legal_component_id = operation.id
         JOIN legal_component_operation operation_meta ON operation.id = operation_meta.legal_component_id
         JOIN signature s ON sa.signature_id = s.id
         JOIN signature_signatory ss ON sa.signature_id = ss.signature_id
         LEFT JOIN legal_component program ON operation_meta.parent_operation_id = program.id
         LEFT JOIN legal_component_operation program_meta ON program.id = program_meta.legal_component_id
         LEFT JOIN "user" u ON s.creator_user_id = u.id
         LEFT JOIN organization o ON o.id = COALESCE(program.organization_id, operation.organization_id)
         LEFT JOIN organization_organization oo ON operation.organization_id = oo.sub_organization_id
         LEFT JOIN organization parent_orga ON oo.parent_organization_id = parent_orga.id
         LEFT JOIN subscription ON o.subscription_id = subscription.id