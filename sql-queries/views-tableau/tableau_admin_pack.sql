CREATE or REPLACE view tableau_admin_pack as
SELECT
    o.id                                                                AS organization_id,
    o.name                                                              AS organization_name,
    (
        SELECT
            of.feature_state['registeredLetterCredits'::text]['number'::text] AS feature_state
        FROM organization o2
                 JOIN organization_feature of ON o.id = of.organization_id
        WHERE of.feature_id::text = 'REGISTERED_LETTER_CREDITS'::text
          AND of.enabled
          AND o2.id = o.id
        )                                                               AS registered_letter_credits,
    (
        SELECT
            of.feature_state['signatureCredits'::text]['number'::text] AS feature_state
        FROM organization o2
                 JOIN organization_feature of ON o.id = of.organization_id
        WHERE of.feature_id::text = 'SIGNATURE_CREDITS'::text
          AND of.enabled
          AND o2.id = o.id
        )                                                               AS signature_credits,
    (
        SELECT
            of.feature_state['advancedSignatureCredits'::text]['number'::text] AS feature_state
        FROM organization o2
                 JOIN organization_feature of ON o.id = of.organization_id
        WHERE of.feature_id::text = 'ADVANCED_SIGNATURE_CREDITS'::text
          AND of.enabled
          AND o2.id = o.id
        )                                                               AS advanced_signature_credits,
    s.plan_type AS plan
FROM organization o
         LEFT JOIN subscription s ON o.subscription_id = s.id