CREATE or REPLACE view tableau_admin_organization as
WITH orga_holdings AS (
    SELECT
        sub.id      AS sub_id,
        sub.name    AS sub_name,
        parent.id   AS holding_id,
        parent.name AS holding_name
    FROM organization_organization oo
             JOIN organization sub ON sub.id = oo.sub_organization_id
             JOIN organization parent ON parent.id = oo.parent_organization_id
    )
SELECT
    o.id,
    o.name,
    oh.holding_id,
    oh.holding_name,
    s.plan_type   AS plan,
    s.status      AS plan_status,
    s.update_time AS plan_last_update_time,
    o.creation_time
FROM organization o
         LEFT JOIN orga_holdings oh ON o.id = oh.sub_id
         LEFT JOIN subscription s ON o.subscription_id = s.id
WHERE o.deletion_time IS NULL
  AND o.name IS NOT NULL