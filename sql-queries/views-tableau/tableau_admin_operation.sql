CREATE or REPLACE view tableau_admin_operation as
SELECT
    lc.id,
    lc.organization_id,
    o.name                                                           AS organization_name,
    lco.label,
    lct.label                                                        AS type,
    lc.creation_time,
    lc.deleted,
    (
        SELECT
            (EXISTS (
                SELECT
                    TRUE AS bool
                FROM operation_contract oc
                WHERE oc.operation_id = lco.legal_component_id
                )) AS "exists"
        )                                                            AS has_contract,
    (
        SELECT
            (EXISTS (
                SELECT
                    TRUE AS bool
                FROM operation_contract oc
                WHERE oc.contract_file_id IS NOT NULL
                  AND oc.operation_id = lco.legal_component_id
                )) AS "exists"
        )                                                            AS has_validated_contract,
    (
        SELECT
            (EXISTS (
                SELECT
                    TRUE AS bool
                FROM signature_association_legal_component sa
                WHERE sa.legal_component_id = lco.legal_component_id
                )) AS "exists"
        )                                                            AS has_signature,
    (
        SELECT
            (EXISTS (
                SELECT
                    TRUE AS bool
                FROM registered_letter_batch_association_legal_component ra
                WHERE ra.legal_component_id = lco.legal_component_id
                )) AS "exists"
        )                                                            AS has_registered_letter,
    lco2.legal_component_id                                          AS program_id,
    lco2.label                                                       AS program_label,
    (
        SELECT
            organization_organization.parent_organization_id
        FROM organization_organization
        WHERE organization_organization.sub_organization_id = lc.organization_id
        )                                                            AS holding_id,
    s.plan_type AS plan,
    (
        SELECT
            o_1.name
        FROM legal_component lc2
                 JOIN organization o_1 ON lc2.organization_id = o_1.id
        WHERE lc2.id = lco2.legal_component_id
        )                                                            AS program_organization_name
FROM legal_component_operation lco
         JOIN legal_component lc ON lco.legal_component_id = lc.id
         JOIN legal_component_template lct ON lc.template_id = lct.id
         LEFT JOIN legal_component_operation lco2 ON lco.parent_operation_id = lco2.legal_component_id
         LEFT JOIN organization o ON lc.organization_id = o.id
         LEFT JOIN subscription s ON o.subscription_id = s.id