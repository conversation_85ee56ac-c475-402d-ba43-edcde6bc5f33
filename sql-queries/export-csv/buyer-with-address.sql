/**
Export all data related to the current seller, including the property address linked to the operation.

Note: Make sure to fill in the specific organization IDs in the organizations CTE for accurate data extraction.

**/
WITH target_organizations (id) AS (
    SELECT
        id,
        name
    FROM organization
    WHERE id IN (1)
)
   , buyer AS (
    SELECT
        legal_component.id                AS operation_id,
        target_organizations.id           AS organization_id,
        answer -> 'nom' ->> 'value'       AS nom,
        answer -> 'prenoms' ->> 'value'   AS prenom,
        answer -> 'email' ->> 'value'     AS email,
        answer -> 'telephone' ->> 'value' AS telephone
    FROM target_organizations
             INNER JOIN legal_component ON target_organizations.id = legal_component.organization_id
             INNER JOIN legal_component_branch ON legal_component.id = legal_component_branch.from_id
             INNER JOIN legal_component_record lcr ON legal_component_branch.to_id = lcr.legal_component_id
             INNER JOIN answer a ON a.id = lcr.answer_id
    WHERE type = 'ACQUEREUR'
)
   , property AS (
    SELECT
        legal_component.id                           AS operation_id,
        target_organizations.id                      AS organization_id,
        answer -> 'adresse' -> 'value' ->> 'address' AS adresse
    FROM target_organizations
             INNER JOIN legal_component ON target_organizations.id = legal_component.organization_id
             INNER JOIN legal_component_branch ON legal_component.id = legal_component_branch.from_id
             INNER JOIN legal_component_record lcr ON legal_component_branch.to_id = lcr.legal_component_id
             INNER JOIN answer a ON a.id = lcr.answer_id
    WHERE type = 'BIEN_VENDU'
)
SELECT
    target_organizations.id   AS organization_id,
    target_organizations.name AS organization_name,
    nom,
    prenom,
    email,
    telephone,
    adresse
FROM target_organizations
         INNER JOIN buyer ON target_organizations.id = buyer.organization_id
         INNER JOIN property ON property.operation_id = buyer.operation_id
ORDER BY property.organization_id;
