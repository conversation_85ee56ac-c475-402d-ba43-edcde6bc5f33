
-- Select all Orpi organization and their associations id
SELECT orpi_agency_id."from"                  as mynotary_organization_id,
       orpi_agency_id."to"                    AS "orpi_agence_id",
       (SELECT "to"
        FROM internal_api_association
        WHERE type = 'ORPI_ORGANIZATION_CODE'
          AND "from" = orpi_agency_id."from") AS orpi_agence_code,
       (SELECT "to"
        FROM internal_api_association
        WHERE type = 'ORPI_ORGANIZATION_SWEEPBRIGHT'
          AND "from" = orpi_agency_id."from") AS id_entity_sweepbright
from internal_api_association orpi_agency_id
WHERE orpi_agency_id.from::int IN (SELECT sub_organization_id
                                   FROM organization_organization
                                   WHERE parent_organization_id = 7671)
  AND type = 'ORPI_ORGANIZATION';



-- Select all Orpi users
WITH target_orga AS (SELECT sub_organization_id
                     FROM organization_organization
                     WHERE parent_organization_id = 7671),
     target_member AS (SELECT user_id
                       FROM organization_user ou
                                INNER JOIN target_orga ON organization_id = sub_organization_id)
SELECT DISTINCT ON (email) email, firstname, lastname
FROM "user"
WHERE id IN (SELECT user_id FROM target_member)
ORDER BY email;


-- Select all Orpi member with role and agency name

WITH target_orga AS (SELECT sub_organization_id
                     FROM organization_organization
                     WHERE parent_organization_id = 7671)
SELECT o.name                                       agence,
       u.email,
       firstname,
       lastname,
       r.name,
       o.id                                         mynotary_organization_id,
       (SELECT "to"
        FROM internal_api_association
        WHERE type = 'ORPI_ORGANIZATION_CODE'
          AND "from" = ou.organization_id::text) AS orpi_agence_code,
       (SELECT "to"
        FROM internal_api_association
        WHERE type = 'ORPI_ORGANIZATION_SWEEPBRIGHT'
          AND "from" = ou.organization_id::text) AS id_entity_sweepbright

FROM "user" u
         INNER JOIN organization_user ou ON u.id = ou.user_id
         INNER JOIN role r ON ou.role_id = r.id
         INNER JOIN organization o ON ou.organization_id = o.id
    AND ou.organization_id IN (SELECT * FROM target_orga);
