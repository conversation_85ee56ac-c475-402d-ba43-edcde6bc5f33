
-- Toutes les orga avec la meme feature 2 fois
-- https://stackoverflow.com/a/32265524/5272932
WITH tmp AS (
    SELECT organization_id
    FROM organization
             INNER JOIN organization_feature ON organization.id = organization_feature.organization_id
    WHERE enabled
      AND feature_id = 'SIGNATURE_CREDITS'
)
SELECT *
FROM (
         SELECT organization_id,
                ROW_NUMBER() OVER (PARTITION BY organization_id ORDER BY organization_id) AS Row
         FROM tmp
     ) dups
WHERE dups.Row > 1