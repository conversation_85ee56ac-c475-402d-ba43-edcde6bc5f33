-- duplicate an answer value in another answer id in a REPEAT
-- find the repeat ID "key" that is not randomly created
-- ID here is reglement_list_reglementXXXX_date duplicated in reglement_list_reglementXXXX_date_texte
-- with operation specific type and record
-- specific types example : {<PERSON><PERSON>,IMMOBILIER,PRO<PERSON>AM<PERSON>}
-- lcb.type example : COPROPRIETE


UPDATE answer
SET answer = answer::jsonb || (
    SELECT jsonb_object_agg(
        key || '_texte',
        json_build_object('value', answer->key->>'value')
    )
    FROM jsonb_object_keys(answer) key
    WHERE key LIKE 'reglement_list_reglement%_date'
)::jsonb
WHERE EXISTS (
    SELECT 1
    FROM jsonb_object_keys(answer) key
    WHERE key LIKE 'reglement_list_reglement%_date'
)
AND id IN (
    SELECT DISTINCT a.id
    FROM legal_component_template lct
         INNER JOIN legal_component lc ON lct.id = lc.template_id
         INNER JOIN legal_component_branch lcb ON lc.id = lcb.from_id
         INNER JOIN legal_component_record lcr ON lcb.to_id = lcr.legal_component_id
         INNER JOIN answer a ON a.id = lcr.answer_id
    WHERE specific_types = '{}'
      AND lcb.type = 'BRANCH'
      AND to_id IS NOT NULL
);