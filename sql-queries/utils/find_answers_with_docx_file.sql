CREATE OR REPLACE FUNCTION find_answers_with_docx_file()
RETURNS TABLE(file_id TEXT, answer_id INT, answer JSONB) AS
$$
DECLARE
    file_row RECORD;
BEGIN
    -- <PERSON><PERSON><PERSON> sur chaque ligne de la table "file"
    FOR file_row IN
        SELECT id, upload_time FROM file
        WHERE name ILIKE '%.doc' OR name ILIKE '%.docx'
    LOOP
        -- Sélectionner les résultats de la table "answer"
        RETURN QUERY
        SELECT
            file_row.id AS file_id,
            a.id AS answer_id,
            a.answer
        FROM answer a
        INNER JOIN public.legal_component_record lcr on a.id = lcr.answer_id
        INNER JOIN legal_component lc ON lcr.legal_component_id = lc.id
        WHERE lc.last_update_time BETWEEN file_row.upload_time
                                  AND file_row.upload_time + INTERVAL '1 minute'
        AND a.answer::text ILIKE '%'|| file_row.id ||'%';
    END LOOP;
END
$$ LANGUAGE plpgsql;