-- converting a timestamp value to a string value (passing from Mynotary Date Format to Text Format)
-- possible with a value in a repeat

UPDATE answer a
SET answer = (
    SELECT a.answer::jsonb || 
    jsonb_object_agg(
        key,
        jsonb_build_object(
            'value',
            to_char(
                to_timestamp((a.answer->key->>'value')::bigint/1000),
                'DD/MM/YYYY'
            )
        )
    )
    FROM jsonb_object_keys(a.answer::jsonb) as key
    WHERE key LIKE 'reglement_list_reglement%_date_texte'
    AND a.answer->key->>'value' IS NOT NULL
)
WHERE EXISTS (
    SELECT 1
    FROM legal_component_template lct
    INNER JOIN legal_component lc ON lct.id = lc.template_id
    INNER JOIN legal_component_branch lcb ON lc.id = lcb.from_id
    INNER JOIN legal_component_record lcr ON lcb.to_id = lcr.legal_component_id
    WHERE a.id = lcr.answer_id
        AND specific_types = '{CDC,IMMOBILIER,PROGRAMME}'
        AND lcb.type = 'COPROPRIETE'
        AND to_id IS NOT NULL
)