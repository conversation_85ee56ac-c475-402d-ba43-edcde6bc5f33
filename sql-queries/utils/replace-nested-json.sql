-- 1) Generate the matching for the numeric id and str id value for the link replacement
SELECT 'WHEN ''' || id || ''' THEN ''' || id_str || ''''
FROM legal_component_template
WHERE type = 'OPERATION'
ORDER BY id;

-- 2) Update the case with the value created step 1 for preprod and prod
create or replace function update_operation_custom_views() returns void
    language plpgsql
as
$$
DECLARE
    row_record    RECORD;
    key_record    RECORD;
    new_key_value jsonb;
    new_value     jsonb;
BEGIN
    -- Select targeted rows
    FOR row_record IN
        SELECT *
        FROM custom_view
        WHERE is_default IS FALSE
          AND filters -> 'TEMPLATE_IDS' IS NOT NULL
        LOOP
            -- Loop through keys in JSONB column
            FOR key_record IN
                SELECT key,
                       value   AS current_value,
                       CASE key
                           WHEN '8' THEN 'OPERATION__IMMOBILIER__VENTE_ANCIEN'
                           WHEN '9' THEN 'OPERATION__IMMOBILIER__VENTE_NEUF'
                           WHEN '10' THEN 'OPERATION__IMMOBILIER__VENTES_PROGRAMME'
                           WHEN '38' THEN 'OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE'
                           WHEN '48' THEN 'OPERATION__IMMOBILIER__LOCATION'
                           WHEN '56' THEN 'OPERATION__IMMOBILIER__LOCATION_VISITE'
                           WHEN '273' THEN 'OPERATION__AUTRE__LIBRE'
                           WHEN '322' THEN 'OPERATION__RECRUTEMENT__AGENT'
                           WHEN '387' THEN 'OPERATION__MYNOTARY__CONTRAT_SAAS'
                           WHEN '391' THEN 'OPERATION__KELLER_WILLIAMS__IMMOBILIER'
                           WHEN '398' THEN 'OPERATION__IMMOBILIER__PSLA_PROGRAMME'
                           WHEN '399' THEN 'OPERATION__IMMOBILIER__PSLA_PRELIMINAIRE'
                           WHEN '412' THEN 'OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME'
                           WHEN '413' THEN 'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION'
                           WHEN '414' THEN 'OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME'
                           WHEN '415' THEN 'OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE'
                           WHEN '499' THEN 'OPERATION__IMMOBILIER__SOCIAL_PROGRAMME'
                           WHEN '500' THEN 'OPERATION__IMMOBILIER__SOCIAL_VENTE'
                           WHEN '509' THEN 'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL'
                           WHEN '515' THEN 'OPERATION__COLDWELL_BANKER__IMMOBILIER'
                           WHEN '516' THEN 'OPERATION__ALLOWA__IMMOBILIER'
                           WHEN '526' THEN 'OPERATION__OZANAM__IMMOBILIER__PSLA_PROGRAMME'
                           WHEN '527' THEN 'OPERATION__OZANAM__IMMOBILIER__PSLA_PRELIMINAIRE'
                           WHEN '563' THEN 'OPERATION__MLS__IMMOBILIER'
                           WHEN '573' THEN 'OPERATION__COLDWELL_BANKER__CONTRAT_NEGOCIATEUR'
                           WHEN '578' THEN 'OPERATION__PODELIHA__IMMOBILIER__PROGRAMME'
                           WHEN '579' THEN 'OPERATION__PODELIHA__IMMOBILIER__VENTE'
                           WHEN '591' THEN 'OPERATION__DUVAL__IMMOBILIER__PROGRAMME_VEFA'
                           WHEN '592' THEN 'OPERATION__DUVAL__IMMOBILIER__VEFA_RESERVATION'
                           WHEN '595' THEN 'OPERATION__IMMOBILIER__VENTE_BIEN_PROFESSIONNEL'
                           WHEN '597' THEN 'OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL'
                           WHEN '598' THEN 'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME'
                           WHEN '599' THEN 'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE'
                           WHEN '611' THEN 'OPERATION__FOLLIOT__IMMOBILIER'
                           WHEN '612' THEN 'OPERATION__PIERRE_REVENTE__IMMOBILIER'
                           WHEN '645' THEN 'OPERATION__KELLER_WILLIAMS__LOCATION_COMMERCIAL'
                           WHEN '678' THEN 'OPERATION__DOMOFRANCE__IMMOBILIER__PROGRAMME'
                           WHEN '679' THEN 'OPERATION__DOMOFRANCE__IMMOBILIER__VENTE'
                           WHEN '689' THEN 'OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER'
                           WHEN '690' THEN 'OPERATION__IMMOBILIER__VENTE_VIAGER'
                           WHEN '691' THEN 'OPERATION__LFEUR__IMMOBILIER__PROGRAMME'
                           WHEN '692' THEN 'OPERATION__LFEUR__IMMOBILIER__VENTE'
                           WHEN '702' THEN 'OPERATION__AGENCE_DIRECTE__IMMOBILIER'
                           WHEN '706' THEN 'OPERATION__C2I__CONTRAT_RECRUTEMENT'
                           WHEN '707' THEN 'OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME'
                           WHEN '708' THEN 'OPERATION__SD_ACCESS__IMMOBILIER__VENTE'
                           WHEN '718' THEN 'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME'
                           WHEN '719' THEN 'OPERATION__CLESENCE__IMMOBILIER__VENTE'
                           WHEN '731' THEN 'OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME'
                           WHEN '732' THEN 'OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__VENTE'
                           WHEN '743' THEN 'OPERATION__RECRUTEMENT__KW_EXPAND__AGENT'
                           WHEN '744' THEN 'OPERATION__PROPRIETES_PRIVEES__IMMOBILIER'
                           WHEN '746' THEN 'OPERATION__PROPRIETES_PRIVEES__IMMORESEAU'
                           WHEN '747' THEN 'OPERATION__PROPRIETES_PRIVEES__BUSINESS'
                           WHEN '748' THEN 'OPERATION__PROPRIETES_PRIVEES__MIZAPRI'
                           WHEN '749' THEN 'OPERATION__PROPRIETES_PRIVEES__REZOXIMO'
                           WHEN '750' THEN 'OPERATION__PROPRIETES_PRIVEES__VIAGER'
                           WHEN '752' THEN 'OPERATION__DESIMO__IMMOBILIER__PROGRAMME'
                           WHEN '753' THEN 'OPERATION__DESIMO__IMMOBILIER__VENTE'
                           WHEN '756' THEN 'OPERATION__BLOT__IMMOBILIER'
                           WHEN '757' THEN 'OPERATION__IDEAL__IMMOBILIER__PROGRAMME'
                           WHEN '758' THEN 'OPERATION__IDEAL__IMMOBILIER__VENTE'
                           WHEN '761' THEN 'OPERATION__C2I__FORMATION'
                           WHEN '796' THEN 'OPERATION__ORPI__IMMOBILIER__VENTE'
                           WHEN '797' THEN 'OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME'
                           WHEN '798' THEN 'OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__VENTE'
                           WHEN '810' THEN 'OPERATION__LGM__IMMOBILIER'
                           WHEN '811' THEN 'OPERATION__KW_ABONDANCE__IMMOBILIER'
                           WHEN '813' THEN 'OPERATION__EFFICITY__IMMOBILIER'
                           WHEN '814' THEN 'OPERATION__VIAGER_CONSULTING__IMMOBILIER'
                           WHEN '816' THEN 'OPERATION__RECRUTEMENT__MYNOTARY__AGENT'
                           WHEN '862' THEN 'OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME'
                           WHEN '863' THEN 'OPERATION__PVCI_SENS__IMMOBILIER__VENTE'
                           WHEN '866' THEN 'OPERATION__EFFICITY__IMMOBILIER__LOCATION__HABITATION'
                           WHEN '867' THEN 'OPERATION__EFFICITY__IMMOBILIER__LOCATION__COMMERCIAL'
                           WHEN '868' THEN 'OPERATION__WHITEBIRD__LOCATION_HABITATION'
                           WHEN '872' THEN 'OPERATION__SEXTANT__IMMOBILIER'
                           WHEN '873' THEN 'OPERATION__PRELLO__IMMOBILIER__LOCATION'
                           WHEN '875' THEN 'OPERATION__PVCI_SENS__IMMOBILIER__VENTE_ACHEVE'
                           WHEN '876' THEN 'OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE'
                           WHEN '878' THEN 'OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION'
                           WHEN '879' THEN 'OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE'
                           WHEN '880' THEN 'OPERATION__I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL'
                           WHEN '881' THEN 'OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE'
                           WHEN '884' THEN 'OPERATION__FOLLIOT__IMMOBILIER_PRO'
                           WHEN '886' THEN 'OPERATION__I_PARTICULIERS__IMMOBILIER__ESPAGNE'
                           WHEN '895' THEN 'OPERATION__PROPRIETES_PRIVEES__IMMOBILIER_LOCATION'
                           WHEN '896' THEN 'OPERATION__RECRUTEMENT__KW_POLYNESIE__AGENT'
                           WHEN '897' THEN 'OPERATION__IMMOBILIER__POLYNESIE__VENTE'
                           WHEN '898' THEN 'OPERATION__WHITEBIRD__GENERAL_PROGRAMME'
                           WHEN '899' THEN 'OPERATION__WHITEBIRD__GENERAL_LOCATION'
                           WHEN '911' THEN 'OPERATION__SYNDIC__GENERAL'
                           WHEN '917' THEN 'OPERATION__ISM__IMMOBILIER'
                           WHEN '918' THEN 'OPERATION__SEXTANT__IMMOBILIER_COMMERCIAL'
                           WHEN '919' THEN 'OPERATION__VALLOIRE__IMMOBILIER_NEUF__PROGRAMME'
                           WHEN '920' THEN 'OPERATION__VALLOIRE__IMMOBILIER_NEUF__VENTE'
                           WHEN '922' THEN 'OPERATION__ORGANISATION_INTERNE'
                           WHEN '923' THEN 'OPERATION__BENEDIC__IMMOBILIER'
                           WHEN '927' THEN 'OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE'
                           WHEN '928' THEN 'OPERATION__UNIS__SOCIAL'
                           WHEN '931' THEN 'OPERATION__UNIS__ORGANISATION_INTERNE'
                           WHEN '932' THEN 'OPERATION__UNIS__COPROPRIETE'
                           WHEN '934' THEN 'OPERATION__UNIS__IMMOBILIER__HABITATION'
                           WHEN '935' THEN 'OPERATION__UNIS__IMMOBILIER__COMMERCIAL'
                           WHEN '936' THEN 'OPERATION__UNIS__LOCATION__HABITATION'
                           WHEN '937' THEN 'OPERATION__UNIS__LOCATION__COMMERCIAL'
                           WHEN '941' THEN 'OPERATION__GIBOIRE__IMMOBILIER__VENTE'
                           WHEN '944' THEN 'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE'
                           WHEN '945' THEN 'OPERATION__PROPRIETES_PRIVEES__IMMORESEAU_BUSINESS'
                           WHEN '946' THEN 'OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME'
                           WHEN '947' THEN 'OPERATION__IMMOBILIER__LOTISSEMENT_VENTE'
                           WHEN '962' THEN 'OPERATION__ERA__IMMOBILIER__VENTE'
                           WHEN '963' THEN 'OPERATION__IMMOBILIER__BRS_PROGRAMME_HYBRIDE'
                           WHEN '964' THEN 'OPERATION__IMMOBILIER__BRS_VENTE_HYBRIDE'
                           WHEN '965' THEN 'OPERATION__SD_ACCESS__IMMOBILIER_HYBRIDE__PROGRAMME'
                           WHEN '966' THEN 'OPERATION__SD_ACCESS__IMMOBILIER_HYBRIDE__VENTE'
                           WHEN '968' THEN 'OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE'
                           WHEN '969' THEN 'OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE'
                           WHEN '970' THEN 'OPERATION__PROPRIETES_PRIVEES__IMMORESEAU_LOCATION'
                           WHEN '971' THEN 'OPERATION__KELLER_WILLIAMS__IMMOBILIER_LOCATION_HABITATION'
                           WHEN '972' THEN 'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION'
                           WHEN '973' THEN 'OPERATION__HERMES__IMMOBILIER__TRANSACTION_PRO'
                           WHEN '974' THEN 'OPERATION__HERMES__IMMOBILIER__LOCATION_PRO'
                           WHEN '975' THEN 'OPERATION__C2I__FORMATION_MAESTRO'
                           WHEN '977' THEN 'OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION'
                           WHEN '978' THEN 'OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION'
                           ELSE key::text
                           END as new_key
                FROM jsonb_each(row_record.filters -> 'TEMPLATE_IDS' -> 'values')
                LOOP
                    new_key_value := jsonb_set(key_record.current_value, '{id}', to_jsonb(key_record.new_key), true);

                    new_value := jsonb_set(
                            row_record.filters,
                            ARRAY ['TEMPLATE_IDS', 'values', key_record.new_key],
                            new_key_value
                                 );


                    UPDATE custom_view
                    SET filters = jsonb_set(
                            filters,
                            ARRAY ['TEMPLATE_IDS', 'values', key_record.new_key],
                            new_key_value
                                  )
                    WHERE id = row_record.id;
                    IF key_record.key ~ '^[0-9.]+$' THEN
                        PERFORM print('Deleting ' || key_record.key);
                        new_value := (SELECT filters #- ARRAY ['TEMPLATE_IDS', 'values', key_record.key]
                                      FROM custom_view
                                      WHERE id = row_record.id);
                        PERFORM print('New value ' || new_value::text);
                        UPDATE custom_view
                        SET filters = new_value
                        WHERE id = row_record.id;
                    END IF;
                END LOOP;
        END LOOP;
END ;
$$;
SELECT update_operation_custom_views();


-- -------------------------------------------- CONTRACT_FUNCTION -------------------------------

create or replace function update_contract_custom_views() returns void
    language plpgsql
as
$$
DECLARE
    row_record    RECORD;
    key_record    RECORD;
    new_value     jsonb;
BEGIN
    -- Select targeted rows
    FOR row_record IN
        SELECT *
        FROM custom_view
        WHERE is_default IS FALSE
          AND type = 'OPERATION_CONTRACT'
          AND filters -> 'CONTRACTS_TYPES' IS NOT NULL
    LOOP
        -- Loop through keys in JSONB column
        FOR key_record IN
            SELECT key,
                   value   AS current_value,
                   CASE key
                       WHEN '8' THEN 'OPERATION__IMMOBILIER__VENTE_ANCIEN'
                       WHEN '9' THEN 'OPERATION__IMMOBILIER__VENTE_NEUF'
                       WHEN '10' THEN 'OPERATION__IMMOBILIER__VENTES_PROGRAMME'
                       WHEN '38' THEN 'OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE'
                       WHEN '48' THEN 'OPERATION__IMMOBILIER__LOCATION'
                       WHEN '56' THEN 'OPERATION__IMMOBILIER__LOCATION_VISITE'
                       WHEN '273' THEN 'OPERATION__AUTRE__LIBRE'
                       WHEN '322' THEN 'OPERATION__RECRUTEMENT__AGENT'
                       WHEN '387' THEN 'OPERATION__MYNOTARY__CONTRAT_SAAS'
                       WHEN '391' THEN 'OPERATION__KELLER_WILLIAMS__IMMOBILIER'
                       WHEN '398' THEN 'OPERATION__IMMOBILIER__PSLA_PROGRAMME'
                       WHEN '399' THEN 'OPERATION__IMMOBILIER__PSLA_PRELIMINAIRE'
                       WHEN '412' THEN 'OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME'
                       WHEN '413' THEN 'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION'
                       WHEN '414' THEN 'OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME'
                       WHEN '415' THEN 'OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE'
                       WHEN '499' THEN 'OPERATION__IMMOBILIER__SOCIAL_PROGRAMME'
                       WHEN '500' THEN 'OPERATION__IMMOBILIER__SOCIAL_VENTE'
                       WHEN '509' THEN 'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL'
                       WHEN '515' THEN 'OPERATION__COLDWELL_BANKER__IMMOBILIER'
                       WHEN '516' THEN 'OPERATION__ALLOWA__IMMOBILIER'
                       WHEN '526' THEN 'OPERATION__OZANAM__IMMOBILIER__PSLA_PROGRAMME'
                       WHEN '527' THEN 'OPERATION__OZANAM__IMMOBILIER__PSLA_PRELIMINAIRE'
                       WHEN '563' THEN 'OPERATION__MLS__IMMOBILIER'
                       WHEN '573' THEN 'OPERATION__COLDWELL_BANKER__CONTRAT_NEGOCIATEUR'
                       WHEN '578' THEN 'OPERATION__PODELIHA__IMMOBILIER__PROGRAMME'
                       WHEN '579' THEN 'OPERATION__PODELIHA__IMMOBILIER__VENTE'
                       WHEN '591' THEN 'OPERATION__DUVAL__IMMOBILIER__PROGRAMME_VEFA'
                       WHEN '592' THEN 'OPERATION__DUVAL__IMMOBILIER__VEFA_RESERVATION'
                       WHEN '595' THEN 'OPERATION__IMMOBILIER__VENTE_BIEN_PROFESSIONNEL'
                       WHEN '597' THEN 'OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL'
                       WHEN '598' THEN 'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME'
                       WHEN '599' THEN 'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE'
                       WHEN '611' THEN 'OPERATION__FOLLIOT__IMMOBILIER'
                       WHEN '612' THEN 'OPERATION__PIERRE_REVENTE__IMMOBILIER'
                       WHEN '645' THEN 'OPERATION__KELLER_WILLIAMS__LOCATION_COMMERCIAL'
                       WHEN '678' THEN 'OPERATION__DOMOFRANCE__IMMOBILIER__PROGRAMME'
                       WHEN '679' THEN 'OPERATION__DOMOFRANCE__IMMOBILIER__VENTE'
                       WHEN '689' THEN 'OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER'
                       WHEN '690' THEN 'OPERATION__IMMOBILIER__VENTE_VIAGER'
                       WHEN '691' THEN 'OPERATION__LFEUR__IMMOBILIER__PROGRAMME'
                       WHEN '692' THEN 'OPERATION__LFEUR__IMMOBILIER__VENTE'
                       WHEN '702' THEN 'OPERATION__AGENCE_DIRECTE__IMMOBILIER'
                       WHEN '706' THEN 'OPERATION__C2I__CONTRAT_RECRUTEMENT'
                       WHEN '707' THEN 'OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME'
                       WHEN '708' THEN 'OPERATION__SD_ACCESS__IMMOBILIER__VENTE'
                       WHEN '718' THEN 'OPERATION__CLESENCE__IMMOBILIER__PROGRAMME'
                       WHEN '719' THEN 'OPERATION__CLESENCE__IMMOBILIER__VENTE'
                       WHEN '731' THEN 'OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME'
                       WHEN '732' THEN 'OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__VENTE'
                       WHEN '743' THEN 'OPERATION__RECRUTEMENT__KW_EXPAND__AGENT'
                       WHEN '744' THEN 'OPERATION__PROPRIETES_PRIVEES__IMMOBILIER'
                       WHEN '746' THEN 'OPERATION__PROPRIETES_PRIVEES__IMMORESEAU'
                       WHEN '747' THEN 'OPERATION__PROPRIETES_PRIVEES__BUSINESS'
                       WHEN '748' THEN 'OPERATION__PROPRIETES_PRIVEES__MIZAPRI'
                       WHEN '749' THEN 'OPERATION__PROPRIETES_PRIVEES__REZOXIMO'
                       WHEN '750' THEN 'OPERATION__PROPRIETES_PRIVEES__VIAGER'
                       WHEN '752' THEN 'OPERATION__DESIMO__IMMOBILIER__PROGRAMME'
                       WHEN '753' THEN 'OPERATION__DESIMO__IMMOBILIER__VENTE'
                       WHEN '756' THEN 'OPERATION__BLOT__IMMOBILIER'
                       WHEN '757' THEN 'OPERATION__IDEAL__IMMOBILIER__PROGRAMME'
                       WHEN '758' THEN 'OPERATION__IDEAL__IMMOBILIER__VENTE'
                       WHEN '761' THEN 'OPERATION__C2I__FORMATION'
                       WHEN '796' THEN 'OPERATION__ORPI__IMMOBILIER__VENTE'
                       WHEN '797' THEN 'OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME'
                       WHEN '798' THEN 'OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__VENTE'
                       WHEN '810' THEN 'OPERATION__LGM__IMMOBILIER'
                       WHEN '811' THEN 'OPERATION__KW_ABONDANCE__IMMOBILIER'
                       WHEN '813' THEN 'OPERATION__EFFICITY__IMMOBILIER'
                       WHEN '814' THEN 'OPERATION__VIAGER_CONSULTING__IMMOBILIER'
                       WHEN '816' THEN 'OPERATION__RECRUTEMENT__MYNOTARY__AGENT'
                       WHEN '862' THEN 'OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME'
                       WHEN '863' THEN 'OPERATION__PVCI_SENS__IMMOBILIER__VENTE'
                       WHEN '866' THEN 'OPERATION__EFFICITY__IMMOBILIER__LOCATION__HABITATION'
                       WHEN '867' THEN 'OPERATION__EFFICITY__IMMOBILIER__LOCATION__COMMERCIAL'
                       WHEN '868' THEN 'OPERATION__WHITEBIRD__LOCATION_HABITATION'
                       WHEN '872' THEN 'OPERATION__SEXTANT__IMMOBILIER'
                       WHEN '873' THEN 'OPERATION__PRELLO__IMMOBILIER__LOCATION'
                       WHEN '875' THEN 'OPERATION__PVCI_SENS__IMMOBILIER__VENTE_ACHEVE'
                       WHEN '876' THEN 'OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE'
                       WHEN '878' THEN 'OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION'
                       WHEN '879' THEN 'OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE'
                       WHEN '880' THEN 'OPERATION__I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL'
                       WHEN '881' THEN 'OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE'
                       WHEN '884' THEN 'OPERATION__FOLLIOT__IMMOBILIER_PRO'
                       WHEN '886' THEN 'OPERATION__I_PARTICULIERS__IMMOBILIER__ESPAGNE'
                       WHEN '895' THEN 'OPERATION__PROPRIETES_PRIVEES__IMMOBILIER_LOCATION'
                       WHEN '896' THEN 'OPERATION__RECRUTEMENT__KW_POLYNESIE__AGENT'
                       WHEN '897' THEN 'OPERATION__IMMOBILIER__POLYNESIE__VENTE'
                       WHEN '898' THEN 'OPERATION__WHITEBIRD__GENERAL_PROGRAMME'
                       WHEN '899' THEN 'OPERATION__WHITEBIRD__GENERAL_LOCATION'
                       WHEN '911' THEN 'OPERATION__SYNDIC__GENERAL'
                       WHEN '917' THEN 'OPERATION__ISM__IMMOBILIER'
                       WHEN '918' THEN 'OPERATION__SEXTANT__IMMOBILIER_COMMERCIAL'
                       WHEN '919' THEN 'OPERATION__VALLOIRE__IMMOBILIER_NEUF__PROGRAMME'
                       WHEN '920' THEN 'OPERATION__VALLOIRE__IMMOBILIER_NEUF__VENTE'
                       WHEN '922' THEN 'OPERATION__ORGANISATION_INTERNE'
                       WHEN '923' THEN 'OPERATION__BENEDIC__IMMOBILIER'
                       WHEN '927' THEN 'OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE'
                       WHEN '928' THEN 'OPERATION__UNIS__SOCIAL'
                       WHEN '931' THEN 'OPERATION__UNIS__ORGANISATION_INTERNE'
                       WHEN '932' THEN 'OPERATION__UNIS__COPROPRIETE'
                       WHEN '934' THEN 'OPERATION__UNIS__IMMOBILIER__HABITATION'
                       WHEN '935' THEN 'OPERATION__UNIS__IMMOBILIER__COMMERCIAL'
                       WHEN '936' THEN 'OPERATION__UNIS__LOCATION__HABITATION'
                       WHEN '937' THEN 'OPERATION__UNIS__LOCATION__COMMERCIAL'
                       WHEN '941' THEN 'OPERATION__GIBOIRE__IMMOBILIER__VENTE'
                       WHEN '944' THEN 'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE'
                       WHEN '945' THEN 'OPERATION__PROPRIETES_PRIVEES__IMMORESEAU_BUSINESS'
                       WHEN '946' THEN 'OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME'
                       WHEN '947' THEN 'OPERATION__IMMOBILIER__LOTISSEMENT_VENTE'
                       WHEN '962' THEN 'OPERATION__ERA__IMMOBILIER__VENTE'
                       WHEN '963' THEN 'OPERATION__IMMOBILIER__BRS_PROGRAMME_HYBRIDE'
                       WHEN '964' THEN 'OPERATION__IMMOBILIER__BRS_VENTE_HYBRIDE'
                       WHEN '965' THEN 'OPERATION__SD_ACCESS__IMMOBILIER_HYBRIDE__PROGRAMME'
                       WHEN '966' THEN 'OPERATION__SD_ACCESS__IMMOBILIER_HYBRIDE__VENTE'
                       WHEN '968' THEN 'OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE'
                       WHEN '969' THEN 'OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE'
                       WHEN '970' THEN 'OPERATION__PROPRIETES_PRIVEES__IMMORESEAU_LOCATION'
                       WHEN '971' THEN 'OPERATION__KELLER_WILLIAMS__IMMOBILIER_LOCATION_HABITATION'
                       WHEN '972' THEN 'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION'
                       WHEN '973' THEN 'OPERATION__HERMES__IMMOBILIER__TRANSACTION_PRO'
                       WHEN '974' THEN 'OPERATION__HERMES__IMMOBILIER__LOCATION_PRO'
                       WHEN '975' THEN 'OPERATION__C2I__FORMATION_MAESTRO'
                       WHEN '977' THEN 'OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION'
                       WHEN '978' THEN 'OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION'
                       ELSE key::text
                       END as new_key
            FROM jsonb_each(row_record.filters -> 'CONTRACTS_TYPES' -> 'values')
            LOOP

                UPDATE custom_view
                SET filters = jsonb_set(
                        filters,
                        ARRAY ['CONTRACTS_TYPES', 'values', key_record.new_key],
                        regexp_replace(key_record.current_value::text, key_record.key, key_record.new_key, 'g')::jsonb
                              )
                WHERE id = row_record.id;
                IF key_record.key ~ '^[0-9.]+$' THEN
                    PERFORM print('Deleting ' || key_record.key);
                    new_value := (SELECT filters #- ARRAY ['CONTRACTS_TYPES', 'values', key_record.key]
                                  FROM custom_view
                                  WHERE id = row_record.id);
                    UPDATE custom_view
                    SET filters = new_value
                    WHERE id = row_record.id;
                END IF;
            END LOOP;
    END LOOP;
END ;
$$;
SELECT update_contract_custom_views();