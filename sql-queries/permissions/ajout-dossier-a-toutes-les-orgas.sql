/***
  * Il faut mettre a jour :
  * - %"RECRUTEMENT", "MYNOTARY", "AGENT"%
  * - RECRUTEMENT__MYNOTARY__AGENT
  * - ["RECRUTEMENT", "MYNOTARY", "AGENT"]
  * Pour les différentes section, on récupère les organisations concernées en partant de la feature V3 et de l'abo
  * 1) On récupère les filiales et on donne les droits a tous les admins
  * 2) On récupère les holdings et on donne les droits a tous les admins
  * 3) On ajoute la feature
 */
WITH holdings AS (
    SELECT DISTINCT
        of.id,
        of.organization_id
    FROM organization_feature of
             INNER JOIN organization o ON of.organization_id = o.id
             INNER JOIN organization_billing_coordinate obc ON o.billing_coordinate_id = obc.id
    WHERE feature_id = 'V3_ACCESS'
      AND enabled
      AND (feature_parameters -> 'v3Access' -> 'operations')::text NOT LIKE '%"RECRUTEMENT", "MYNOTARY", "AGENT"%'
      AND obc.zoho_subscription_data -> 'plan' ->> 'planCode' NOT ILIKE '%freemium%'
      AND obc.zoho_subscription_data -> 'plan' ->> 'planCode' NOT ILIKE '%Institutionnel%'
      AND o.type NOT IN ('DEVELOPER', 'HOUSE_BUILDER', 'NOTARY_OFFICE')
      AND o.deletion_time IS NULL
    ),
     sub_orga_with_v3 AS (
         SELECT
             sub_organization_id
         FROM organization_organization oo
                  INNER JOIN organization_feature of ON oo.sub_organization_id = of.organization_id
         WHERE feature_id = 'V3_ACCESS'
           AND enabled IS TRUE
         ),
     role_ids AS (
         SELECT
             r.id AS role_id
         FROM holdings h
                  INNER JOIN organization_organization oo ON h.organization_id = oo.parent_organization_id
                  INNER JOIN organization o ON oo.sub_organization_id = o.id
                  INNER JOIN role r ON oo.sub_organization_id = r.organization_id
         WHERE sub_organization_id NOT IN (
             SELECT
                 sub_organization_id
             FROM sub_orga_with_v3
             )
           AND o.deletion_time IS NULL
           AND r.is_admin IS TRUE
         ),
     new_perms AS (
         SELECT
             id AS permission_id
         FROM permission
         WHERE entity_type = 'RECRUTEMENT__MYNOTARY__AGENT'
         )
INSERT
INTO role_permission (role_id, permission_id)
SELECT
    role_id,
    permission_id
FROM role_ids
         JOIN new_perms ON TRUE
ON CONFLICT DO NOTHING;

-- Ajoute les permissions aux admins
WITH role_ids AS (
    SELECT DISTINCT
        r.id role_id
    FROM organization_feature of
             INNER JOIN organization o ON of.organization_id = o.id
             INNER JOIN organization_billing_coordinate obc ON o.billing_coordinate_id = obc.id
             INNER JOIN role r ON o.id = r.organization_id
    WHERE feature_id = 'V3_ACCESS'
      AND enabled
      AND (feature_parameters -> 'v3Access' -> 'operations')::text NOT LIKE '%"RECRUTEMENT", "MYNOTARY", "AGENT"%'
      AND obc.zoho_subscription_data -> 'plan' ->> 'planCode' NOT ILIKE '%freemium%'
      AND obc.zoho_subscription_data -> 'plan' ->> 'planCode' NOT ILIKE '%Institutionnel%'
      AND o.type NOT IN ('DEVELOPER', 'HOUSE_BUILDER', 'NOTARY_OFFICE')
      AND r.is_admin IS TRUE
    ),
     new_perms AS (
         SELECT
             id AS permission_id
         FROM permission
         WHERE entity_type = 'RECRUTEMENT__MYNOTARY__AGENT'
         )
INSERT
INTO role_permission (role_id, permission_id)
SELECT
    role_id,
    permission_id
FROM role_ids
         JOIN new_perms ON TRUE
ON CONFLICT DO NOTHING;


-- Ajoute le dossier aux orgas qui ont la feature
WITH target_features AS (
    SELECT DISTINCT
        of.id
    FROM organization_feature of
             INNER JOIN organization o ON of.organization_id = o.id
             INNER JOIN organization_billing_coordinate obc ON o.billing_coordinate_id = obc.id
    WHERE feature_id = 'V3_ACCESS'
      AND enabled
      AND (feature_parameters -> 'v3Access' -> 'operations')::text NOT LIKE '%"RECRUTEMENT", "MYNOTARY", "AGENT"%'
      AND obc.zoho_subscription_data -> 'plan' ->> 'planCode' NOT ILIKE '%freemium%'
      AND obc.zoho_subscription_data -> 'plan' ->> 'planCode' NOT ILIKE '%Institutionnel%'
      AND o.type NOT IN ('DEVELOPER', 'HOUSE_BUILDER', 'NOTARY_OFFICE')
    )
UPDATE organization_feature
SET feature_parameters=jsonb_insert(
        feature_parameters::jsonb,
        '{v3Access, operations, 0}',
        '["RECRUTEMENT", "MYNOTARY", "AGENT"]'::jsonb
    )
WHERE id IN (
    SELECT
        id
    FROM target_features
    );





/**
  Retire un dossier
 */
WITH target_orga AS (
    SELECT DISTINCT
        of.id
    FROM organization_feature of
             INNER JOIN organization o ON of.organization_id = o.id
             INNER JOIN organization_billing_coordinate obc ON o.billing_coordinate_id = obc.id
    WHERE feature_id = 'V3_ACCESS'
      AND enabled
      AND (feature_parameters -> 'v3Access' -> 'operations')::text LIKE '%"RECRUTEMENT", "GENERAL"%'
      AND obc.zoho_subscription_data -> 'plan' ->> 'planCode' NOT ILIKE '%freemium%'
      AND obc.zoho_subscription_data -> 'plan' ->> 'planCode' NOT ILIKE '%Institutionnel%'
      AND o.type NOT IN ('DEVELOPER', 'HOUSE_BUILDER', 'NOTARY_OFFICE')
      AND o.deletion_time IS NULL
    )
UPDATE organization_feature of
SET feature_parameters =
        JSONB_SET(
                feature_parameters::jsonb,
                '{v3Access}',
                REGEXP_REPLACE((feature_parameters -> 'v3Access')::text, '\["RECRUTEMENT", "GENERAL"],', '')::jsonb
            )
FROM target_orga o
WHERE of.id = o.id;



