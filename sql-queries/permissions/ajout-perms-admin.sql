/**
  Ajoute des permissions manquante aux admins.
  Utile après ajout d'un nouveau contrat dans un dossier
 */
WITH role_ids AS (
    SELECT DISTINCT role_id
    FROM role_permission
        INNER JOIN role ON role_permission.role_id = role.id
             INNER JOIN permission p ON p.id = role_permission.permission_id
    WHERE entity_type = 'TYPE_DE_DOSSIER'
    AND is_admin IS TRUE
),
     new_perms AS (
         SELECT id AS permission_id
         FROM permission
         WHERE entity_type='TYPE_DE_DOSSIER'
     )
INSERT INTO role_permission (role_id, permission_id)
SELECT role_id, permission_id FROM role_ids JOIN new_perms ON TRUE
ON CONFLICT DO NOTHING;


/**
  Ajoute une permission par contrat à tous les admins concernés.
  Il faut retrouver la liste des dossiers actifs pour chaque role et ajouter la permission.
  Il faut penser a mettre a jour le permission.type et la liste des roles impactées
 */
WITH target_permission AS (SELECT id as target_permission, entity_type, sub_entity_type
                           FROM permission
                           WHERE type = 'permission.type'),
     target_role AS (SELECT DISTINCT ON (role_id, entity_type, sub_entity_type) role_id, entity_type, sub_entity_type
                     FROM role_permission
                              INNER JOIN role ON role_permission.role_id = role.id
                              INNER JOIN permission p ON p.id = role_permission.permission_id
                     WHERE role.is_default IS TRUE
                       AND sub_entity_type NOT IN ('NONE')
                     AND name IN ('Admin')),
     target AS (SELECT role_id, target_permission
                FROM target_role tr
                         LEFT JOIN target_permission tp
                                   ON tp.entity_type = tr.entity_type AND tp.sub_entity_type = tr.sub_entity_type)
INSERT
INTO role_permission (role_id, permission_id)
SElECT role_id, target_permission FROM target
                                  WHERE target.target_permission IS NOT NULL
ON CONFLICT DO NOTHING;

/**
  Copy toutes les permissions d'un entity type vers un autre entity type.
  Utile quand on veut transférer un type de dossier et que tous les roles conservent les mêmes permissions que le dossier transféré
 */
with target_orgas AS (SELECT sub_organization_id orga_id
                      FROM organization_organization
                      WHERE parent_organization_id = 8412
                      UNION
                      SELECT 8412 as orga_id),
     target_perms AS (SELECT p.id                                           src_perm,
                             rp.role_id,
                             (SELECT id
                              FROM permission tp
                              WHERE tp.type = p.type
                                AND tp.entity_type = 'THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER'
                                AND tp.sub_entity_type = p.sub_entity_type) target_perm
                      FROM role r
                               INNER JOIN role_permission rp ON r.id = rp.role_id
                               INNER JOIN permission p ON rp.permission_id = p.id
                      WHERE organization_id IN (SELECT * FROM target_orgas)
                        AND entity_type = 'IMMOBILIER__VENTE_ANCIEN')
INSERT
INTO role_permission (role_id, permission_id)
SELECT role_id, target_perm
FROM target_perms
ON CONFLICT DO NOTHING;