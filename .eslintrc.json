{"root": true, "ignorePatterns": ["**/*", "*-dto.ts"], "plugins": ["@nx", "unused-imports", "typescript-sort-keys", "sort-destructure-keys", "sort-keys-fix", "react"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"jsx-a11y/role-has-required-aria-props": "off", "jsx-a11y/accessible-emoji": "off", "jsx-a11y/alt-text": "off", "jsx-a11y/anchor-is-valid": "off", "jsx-a11y/iframe-has-title": "off", "sort-keys-fix/sort-keys-fix": "warn", "sort-destructure-keys/sort-destructure-keys": "warn", "typescript-sort-keys/interface": "warn", "typescript-sort-keys/string-enum": "warn", "react/self-closing-comp": "error", "@nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "platform:frontend", "onlyDependOnLibsWithTags": ["platform:frontend", "platform:crossplatform"]}, {"sourceTag": "platform:backend", "onlyDependOnLibsWithTags": ["platform:backend", "platform:crossplatform"]}, {"sourceTag": "platform:crossplatform", "onlyDependOnLibsWithTags": ["platform:crossplatform"]}, {"sourceTag": "type:api", "onlyDependOnLibsWithTags": ["type:api", "type:authorization", "type:core", "type:feature", "type:store", "type:ui", "type:util"]}, {"sourceTag": "type:app", "onlyDependOnLibsWithTags": ["type:core", "type:infra", "type:feature", "type:providers", "type:util", "type:store", "type:ui"]}, {"sourceTag": "type:authorization", "onlyDependOnLibsWithTags": ["type:api", "type:core", "type:util"]}, {"sourceTag": "type:core", "onlyDependOnLibsWithTags": ["type:api", "type:core", "type:util"]}, {"allSourceTags": ["platform:backend", "type:feature"], "onlyDependOnLibsWithTags": ["type:api", "type:authorization", "type:core", "type:openapi", "type:util"]}, {"allSourceTags": ["platform:frontend", "type:feature"], "onlyDependOnLibsWithTags": ["type:api", "type:core", "type:store", "type:ui", "type:util"]}, {"sourceTag": "type:infra", "onlyDependOnLibsWithTags": ["type:api", "type:core", "type:infra", "type:openapi", "type:util"]}, {"sourceTag": "type:openapi", "onlyDependOnLibsWithTags": []}, {"sourceTag": "type:providers", "onlyDependOnLibsWithTags": ["type:api", "type:core", "type:infra"]}, {"sourceTag": "type:store", "onlyDependOnLibsWithTags": ["type:api", "type:core", "type:infra", "type:openapi", "type:store", "type:util"]}, {"sourceTag": "type:test", "onlyDependOnLibsWithTags": ["*"]}, {"sourceTag": "type:ui", "onlyDependOnLibsWithTags": ["type:api", "type:core", "type:ui", "type:util"]}, {"sourceTag": "type:util", "onlyDependOnLibsWithTags": ["type:api", "type:core", "type:util"]}, {"onlyDependOnLibsWithTags": ["scope:api-adsn", "scope:shared", "type:api", "type:feature", "type:providers"], "sourceTag": "scope:api-adsn"}, {"onlyDependOnLibsWithTags": ["scope:api-auth", "scope:shared", "type:feature", "type:providers"], "sourceTag": "scope:api-auth"}, {"onlyDependOnLibsWithTags": ["scope:api-etatscivils", "scope:shared", "type:api", "type:feature", "type:providers"], "sourceTag": "scope:api-etatscivils"}, {"onlyDependOnLibsWithTags": ["scope:api-files", "scope:shared", "type:feature", "type:providers"], "sourceTag": "scope:api-files"}, {"onlyDependOnLibsWithTags": ["scope:api-mynotary", "scope:shared", "type:feature", "type:providers"], "sourceTag": "scope:api-mynotary"}, {"onlyDependOnLibsWithTags": ["scope:api-signatures", "scope:shared", "type:feature", "type:providers"], "sourceTag": "scope:api-signatures"}, {"onlyDependOnLibsWithTags": ["scope:api-teleactes", "scope:shared", "type:api", "type:feature", "type:providers"], "sourceTag": "scope:api-teleactes"}, {"onlyDependOnLibsWithTags": ["scope:back-portalys-companion", "scope:shared", "type:api", "type:core", "type:feature", "type:providers"], "sourceTag": "scope:back-portalys-companion"}, {"onlyDependOnLibsWithTags": ["scope:bff-portalys", "scope:shared", "type:api", "type:feature", "type:providers"], "sourceTag": "scope:bff-portalys"}, {"onlyDependOnLibsWithTags": ["*"], "sourceTag": "scope:front-mynotary"}, {"onlyDependOnLibsWithTags": ["*"], "sourceTag": "scope:front-portalys"}, {"onlyDependOnLibsWithTags": ["*"], "sourceTag": "scope:front-portalys-companion"}, {"onlyDependOnLibsWithTags": ["*"], "sourceTag": "scope:jobs"}, {"onlyDependOnLibsWithTags": ["*"], "sourceTag": "scope:jobs"}, {"onlyDependOnLibsWithTags": ["*"], "sourceTag": "scope:portalys-companion"}, {"onlyDependOnLibsWithTags": ["*"], "sourceTag": "scope:tool-legal-templates"}, {"onlyDependOnLibsWithTags": ["*"], "sourceTag": "scope:tools"}, {"onlyDependOnLibsWithTags": ["scope:adsn-client", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:adsn-client"}, {"onlyDependOnLibsWithTags": ["scope:anf", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:anf"}, {"onlyDependOnLibsWithTags": ["scope:anf-companion", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:anf-companion"}, {"onlyDependOnLibsWithTags": ["scope:apps", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:apps"}, {"onlyDependOnLibsWithTags": ["scope:archives", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:archives"}, {"onlyDependOnLibsWithTags": ["scope:assets", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:assets"}, {"onlyDependOnLibsWithTags": ["scope:async-tasks", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:async-tasks"}, {"onlyDependOnLibsWithTags": ["scope:auth", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:auth"}, {"onlyDependOnLibsWithTags": ["scope:authentications", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:authentications"}, {"onlyDependOnLibsWithTags": ["scope:authorizations", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:authorizations"}, {"onlyDependOnLibsWithTags": ["scope:back-portalys-companion", "scope:shared", "type:feature", "type:providers"], "sourceTag": "scope:back-portalys-companion"}, {"onlyDependOnLibsWithTags": ["scope:background-jobs", "scope:shared", "type:feature", "type:providers"], "sourceTag": "scope:background-jobs"}, {"onlyDependOnLibsWithTags": ["scope:billings", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:billings"}, {"onlyDependOnLibsWithTags": ["scope:cardreader", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:cardreader"}, {"onlyDependOnLibsWithTags": ["scope:companies", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:companies"}, {"onlyDependOnLibsWithTags": ["scope:contract-validators", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:contract-validators"}, {"onlyDependOnLibsWithTags": ["scope:contract-views", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:contract-views"}, {"onlyDependOnLibsWithTags": ["scope:contract-validations", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:contract-validations"}, {"onlyDependOnLibsWithTags": ["scope:contracts-dashboard", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:contracts-dashboard"}, {"onlyDependOnLibsWithTags": ["scope:coproprietes", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:coproprietes"}, {"onlyDependOnLibsWithTags": ["scope:csv", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:csv"}, {"onlyDependOnLibsWithTags": ["scope:csv-legal-records", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:csv-legal-records"}, {"onlyDependOnLibsWithTags": ["scope:current-operation", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:current-operation"}, {"onlyDependOnLibsWithTags": ["scope:custom-views", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:custom-views"}, {"onlyDependOnLibsWithTags": ["scope:customer-support", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:customer-support"}, {"onlyDependOnLibsWithTags": ["scope:dashboard-filters", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:dashboard-filters"}, {"onlyDependOnLibsWithTags": ["scope:dashboards-portalys", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:dashboards-portalys"}, {"onlyDependOnLibsWithTags": ["scope:data-analytics", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:data-analytics"}, {"onlyDependOnLibsWithTags": ["scope:data-sync", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:data-sync"}, {"onlyDependOnLibsWithTags": ["scope:data-tableau", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:data-tableau"}, {"onlyDependOnLibsWithTags": ["scope:default-records", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:default-records"}, {"onlyDependOnLibsWithTags": ["scope:demande-copie-document", "scope:teleactes", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:demande-copie-document"}, {"onlyDependOnLibsWithTags": ["scope:document-requests", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:document-requests"}, {"onlyDependOnLibsWithTags": ["scope:drives", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:drives"}, {"onlyDependOnLibsWithTags": ["scope:email-editor", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:email-editor"}, {"onlyDependOnLibsWithTags": ["scope:emails", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:emails"}, {"onlyDependOnLibsWithTags": ["scope:etatscivils", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:etatscivils"}, {"onlyDependOnLibsWithTags": ["scope:etatscivils-bff", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:etatscivils-bff"}, {"onlyDependOnLibsWithTags": ["scope:etatscivils-client", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:etatscivils-client"}, {"onlyDependOnLibsWithTags": ["scope:events", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:events"}, {"onlyDependOnLibsWithTags": ["scope:external-apps", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:external-apps"}, {"onlyDependOnLibsWithTags": ["scope:feature-organizations", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:feature-organizations"}, {"onlyDependOnLibsWithTags": ["scope:features", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:features"}, {"onlyDependOnLibsWithTags": ["scope:files", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:files"}, {"onlyDependOnLibsWithTags": ["scope:files-client", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:files-client"}, {"onlyDependOnLibsWithTags": ["scope:front-mynotary-header", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:front-mynotary-header"}, {"onlyDependOnLibsWithTags": ["scope:gel-avoirs", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:gel-avoirs"}, {"onlyDependOnLibsWithTags": ["scope:global-popins", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:global-popins"}, {"onlyDependOnLibsWithTags": ["scope:inquiries", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:inquiries"}, {"onlyDependOnLibsWithTags": ["scope:inquiries-history", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:inquiries-history"}, {"onlyDependOnLibsWithTags": ["scope:interop-compta", "scope:teleactes", "scope:teleactes-client", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:interop-compta"}, {"onlyDependOnLibsWithTags": ["scope:interop-compta-companion", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:interop-compta-companion"}, {"onlyDependOnLibsWithTags": ["scope:invoices", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:invoices"}, {"onlyDependOnLibsWithTags": ["scope:learn-worlds", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:learn-worlds"}, {"onlyDependOnLibsWithTags": ["scope:legacy-java", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legacy-java"}, {"onlyDependOnLibsWithTags": ["scope:legal-branches", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legal-branches"}, {"onlyDependOnLibsWithTags": ["scope:legal-contract-creations", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legal-contract-creations"}, {"onlyDependOnLibsWithTags": ["scope:legal-contract-templates", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legal-contract-templates"}, {"onlyDependOnLibsWithTags": ["scope:legal-link-creations", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legal-link-creations"}, {"onlyDependOnLibsWithTags": ["scope:legal-link-templates", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legal-link-templates"}, {"onlyDependOnLibsWithTags": ["scope:legal-links", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legal-links"}, {"onlyDependOnLibsWithTags": ["scope:legal-operation-creations", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legal-operation-creations"}, {"onlyDependOnLibsWithTags": ["scope:legal-operation-templates", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legal-operation-templates"}, {"onlyDependOnLibsWithTags": ["scope:legal-operations-fetch", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legal-operations-fetch"}, {"onlyDependOnLibsWithTags": ["scope:legal-record-creations", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legal-record-creations"}, {"onlyDependOnLibsWithTags": ["scope:legal-record-exports", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legal-record-exports"}, {"onlyDependOnLibsWithTags": ["scope:legal-record-forms", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legal-record-forms"}, {"onlyDependOnLibsWithTags": ["scope:legal-record-templates", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legal-record-templates"}, {"onlyDependOnLibsWithTags": ["scope:legal-templates", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legal-templates"}, {"onlyDependOnLibsWithTags": ["scope:legals", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:legals"}, {"onlyDependOnLibsWithTags": ["scope:loader", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:loader"}, {"onlyDependOnLibsWithTags": ["scope:localqueues", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:localqueues"}, {"onlyDependOnLibsWithTags": ["scope:member-setups", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:member-setups"}, {"onlyDependOnLibsWithTags": ["scope:members", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:members"}, {"onlyDependOnLibsWithTags": ["scope:members-bff", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:members-bff"}, {"onlyDependOnLibsWithTags": ["scope:members-portalys", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:members-portalys"}, {"onlyDependOnLibsWithTags": ["scope:mnapi-client", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:mnapi-client"}, {"onlyDependOnLibsWithTags": ["scope:notifications", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:notifications"}, {"onlyDependOnLibsWithTags": ["scope:operation-access", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:operation-access"}, {"onlyDependOnLibsWithTags": ["scope:operation-access-update", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:operation-access-update"}, {"onlyDependOnLibsWithTags": ["scope:operation-default-records", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:operation-default-records"}, {"onlyDependOnLibsWithTags": ["scope:operation-files", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:operation-files"}, {"onlyDependOnLibsWithTags": ["scope:operation-invitations", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:operation-invitations"}, {"onlyDependOnLibsWithTags": ["scope:operation-status", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:operation-status"}, {"onlyDependOnLibsWithTags": ["scope:operation-views", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:operation-views"}, {"onlyDependOnLibsWithTags": ["scope:operations-bff", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:operations-bff"}, {"onlyDependOnLibsWithTags": ["scope:operations-dashboard", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:operations-dashboard"}, {"onlyDependOnLibsWithTags": ["scope:operations-portalys", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:operations-portalys"}, {"onlyDependOnLibsWithTags": ["scope:orders", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:orders"}, {"onlyDependOnLibsWithTags": ["scope:organization-data-transfers", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:organization-data-transfers"}, {"onlyDependOnLibsWithTags": ["scope:organization-holdings", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:organization-holdings"}, {"onlyDependOnLibsWithTags": ["scope:organization-setups", "scope:shared", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:organization-setups"}, {"onlyDependOnLibsWithTags": ["scope:organizations", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:organizations"}, {"onlyDependOnLibsWithTags": ["scope:organizations-bff", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:organizations-bff"}, {"onlyDependOnLibsWithTags": ["scope:organizations-portalys", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:organizations-portalys"}, {"onlyDependOnLibsWithTags": ["scope:orpi", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:orpi"}, {"onlyDependOnLibsWithTags": ["scope:pdf", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:pdf"}, {"onlyDependOnLibsWithTags": ["scope:pdf-client", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:pdf-client"}, {"onlyDependOnLibsWithTags": ["scope:places", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:places"}, {"onlyDependOnLibsWithTags": ["scope:planete", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:planete"}, {"onlyDependOnLibsWithTags": ["scope:planete-companion", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:planete-companion"}, {"onlyDependOnLibsWithTags": ["scope:planete-dispatcher", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:planete-dispatcher"}, {"onlyDependOnLibsWithTags": ["scope:popins", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:popins"}, {"onlyDependOnLibsWithTags": ["scope:pre-etat-dates", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:pre-etat-dates"}, {"onlyDependOnLibsWithTags": ["scope:programs", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:programs"}, {"onlyDependOnLibsWithTags": ["scope:records", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:records"}, {"onlyDependOnLibsWithTags": ["scope:redactions", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:redactions"}, {"onlyDependOnLibsWithTags": ["scope:referentiels", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:referentiels"}, {"onlyDependOnLibsWithTags": ["scope:referentiels-bff", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:referentiels-bff"}, {"onlyDependOnLibsWithTags": ["scope:referentiels-companion", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:referentiels-companion"}, {"onlyDependOnLibsWithTags": ["scope:registered-letters", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:registered-letters"}, {"onlyDependOnLibsWithTags": ["scope:registered-letters-creation", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:registered-letters-creation"}, {"onlyDependOnLibsWithTags": ["scope:registers", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:registers"}, {"onlyDependOnLibsWithTags": ["scope:requisitions", "scope:teleactes", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:requisitions"}, {"onlyDependOnLibsWithTags": ["scope:reservations", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:reservations"}, {"onlyDependOnLibsWithTags": ["scope:roles", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:roles"}, {"onlyDependOnLibsWithTags": ["scope:roles-bff", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:roles-bff"}, {"onlyDependOnLibsWithTags": ["scope:roles-portalys", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:roles-portalys"}, {"onlyDependOnLibsWithTags": ["scope:routes", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:routes"}, {"onlyDependOnLibsWithTags": ["scope:scrapping", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:scrapping"}, {"onlyDependOnLibsWithTags": ["scope:secrets", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:secrets"}, {"onlyDependOnLibsWithTags": ["scope:settings-portalys", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:settings-portalys"}, {"onlyDependOnLibsWithTags": ["scope:signature-creations", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:signature-creations"}, {"onlyDependOnLibsWithTags": ["scope:signatures", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:signatures"}, {"onlyDependOnLibsWithTags": ["scope:snackbars", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:snackbars"}, {"onlyDependOnLibsWithTags": ["scope:teleactes", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:teleactes"}, {"onlyDependOnLibsWithTags": ["scope:teleactes-bff", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:teleactes-bff"}, {"onlyDependOnLibsWithTags": ["scope:teleactes-client", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:teleactes-client"}, {"onlyDependOnLibsWithTags": ["scope:teleactes-dispatcher", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:teleactes-dispatcher"}, {"onlyDependOnLibsWithTags": ["scope:text-editor", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:text-editor"}, {"onlyDependOnLibsWithTags": ["scope:themes", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:themes"}, {"onlyDependOnLibsWithTags": ["scope:unis", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:unis"}, {"onlyDependOnLibsWithTags": ["scope:user-events", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:user-events"}, {"onlyDependOnLibsWithTags": ["scope:user-events-bff", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:user-events-bff"}, {"onlyDependOnLibsWithTags": ["scope:user-session", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:user-session"}, {"onlyDependOnLibsWithTags": ["scope:users", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:users"}, {"onlyDependOnLibsWithTags": ["scope:users-bff", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:users-bff"}, {"onlyDependOnLibsWithTags": ["scope:users-portalys", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:users-portalys"}, {"onlyDependOnLibsWithTags": ["scope:whitelist", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:whitelist"}, {"onlyDependOnLibsWithTags": ["scope:shared", "scope:shared", "type:api", "type:openapi", "type:providers"], "sourceTag": "scope:shared"}, {"onlyDependOnLibsWithTags": ["scope:feature-flags", "scope:shared", "type:api", "type:openapi", "type:providers"], "sourceTag": "scope:feature-flags"}, {"onlyDependOnLibsWithTags": ["scope:internal-notifications", "scope:shared", "type:api", "type:openapi", "type:providers"], "sourceTag": "scope:internal-notifications"}, {"onlyDependOnLibsWithTags": ["scope:contract-reviews", "scope:shared", "type:api", "type:openapi", "type:test"], "sourceTag": "scope:contract-reviews"}]}]}}, {"files": ["*.ts", "*.tsx"], "extends": ["plugin:@nx/typescript"], "rules": {"@typescript-eslint/no-non-null-assertion": "error", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/ban-ts-comment": "off", "no-unused-vars": "off", "unused-imports/no-unused-imports": "warn", "unused-imports/no-unused-vars": ["warn", {"vars": "all", "varsIgnorePattern": "^__", "args": "after-used", "argsIgnorePattern": "^__"}]}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nx/javascript"], "rules": {"@typescript-eslint/no-require-imports": "off"}}, {"files": ["*.tsx"], "extends": [], "rules": {"react/jsx-sort-props": "warn"}}, {"files": "*.json", "parser": "jsonc-eslint-parser", "rules": {}}, {"files": ["*.slice.ts"], "rules": {"no-param-reassign": ["error", {"props": false}]}}]}